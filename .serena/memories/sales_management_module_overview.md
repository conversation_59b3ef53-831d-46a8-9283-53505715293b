# 销售管理模块 概述

## 基本信息
- **文档**: PRD-M1: 销售管理模块 产品需求文档 V2.1
- **版本**: 2.1 架构优化版
- **日期**: 2025-08-02
- **状态**: 架构优化完成
- **定位**: 核心业务层模块

## 核心价值主张

### 模块定位
**销售管理模块通过整合V2.0的销售管理子系统和CRM子系统，实现客户全生命周期的一体化管理。**

### 优化目标
整合V2.0的销售管理子系统和CRM子系统，解决客户管理与销售业务分离导致的信息割裂问题，实现客户全生命周期的一体化管理。

### 商业价值量化
- **销售转化率提升**: 一体化管理使销售转化率从15%提升至30%，销售业绩提升100%
- **客户响应速度**: 报价响应时间从24小时缩短至30分钟，客户满意度提升80%
- **订单处理效率**: 复杂订单录入时间从2小时缩短至10分钟，效率提升92%
- **客户资产沉淀**: 客户信息完整性从40%提升至95%，避免客户流失

## 整合架构设计

### 功能整合映射
| V2.1功能模块 | 整合的V2.0功能 | 整合优势 |
|-------------|---------------|----------|
| **客户档案管理** | CRM客户管理 + 销售客户关联 | 统一客户信息源，避免重复维护 |
| **销售机会管理** | CRM销售机会 + 销售跟进 | 商机与销售业务无缝衔接 |
| **报价订单管理** | 销售报价单 + 销售订单 | 报价到订单一体化流程 |
| **客户服务管理** | CRM售后服务 + 销售客服 | 售前售后服务统一管理 |

### 模块架构
```
销售管理模块
├── 客户关系管理
│   ├── 客户档案管理
│   ├── 销售机会管理
│   ├── 客户活动跟踪
│   └── 售后服务管理
├── 销售业务管理
│   ├── 报价单管理
│   ├── 销售订单管理
│   ├── 价格策略管理
│   └── 销售分析报表
└── 共享服务调用
    ├── PDM服务
    └── 基础服务
```

## 核心功能设计

### 1. 客户档案管理
**功能描述**: 提供统一的客户信息管理，整合客户基本信息、联系人、销售关系、服务历史等全方位客户档案。

**核心功能**:
- **客户主数据管理**: 客户基本信息、分类标签、信用评级
- **联系人管理**: 多联系人信息、角色关系、沟通偏好
- **销售关系管理**: 销售负责人分配、团队协作、权限控制
- **客户360度视图**: 销售历史、服务记录、商机状态统一展示

**业务规则**:
- 客户编码自动生成，格式：CUS-XXXXXXXX
- 一个客户只能分配一个主要销售负责人
- 客户信息变更需要审批流程
- 支持客户查重和合并功能

### 2. 销售机会管理
**功能描述**: 管理销售机会的全生命周期，从线索发现到成交转化。

**核心功能**:
- **销售漏斗管理**: 阶段化销售过程，可视化进度跟踪
- **商机评估**: 商机价值评估、成交概率分析
- **竞争对手分析**: 竞争信息收集、优劣势对比
- **销售预测**: 基于历史数据的销售预测分析

**销售阶段流程**:
```
线索 → 初步接触 → 需求确认 → 方案提交 → 商务谈判 → 成交
```

**业务规则**:
- 每个阶段必须有明确的完成标准
- 商机超过30天未更新自动提醒
- 支持商机转移和团队协作

### 3. 报价订单管理
**功能描述**: 整合报价和订单管理，实现从报价到订单的一体化流程。

**核心功能**:
- **智能报价**: 参数化产品配置、自动成本计算、价格策略匹配
- **报价版本管理**: 多版本报价、变更追踪、客户反馈记录
- **订单转换**: 报价单一键转订单、信息自动继承
- **批量处理**: Excel导入、批量报价、批量订单处理

**业务规则**:
- 报价单有效期默认30天，过期自动提醒
- 报价转订单时自动触发BOM固化流程
- 大额订单需要销售经理审批
- 支持订单分批交货和部分发货

### 4. 客户服务管理
**功能描述**: 提供售前售后一体化的客户服务管理。

**核心功能**:
- **服务工单管理**: 工单创建、分派、跟踪、关闭全流程
- **客户满意度**: 满意度调查、评价分析、改进建议
- **服务知识库**: 常见问题、解决方案、最佳实践
- **服务质量监控**: 响应时间、解决率、客户反馈统计

**业务规则**:
- 服务工单必须在2小时内响应
- 客户投诉工单优先级最高
- 服务完成后必须进行满意度调查
- 服务记录与客户档案自动关联

## 服务集成设计

### PDM服务集成
**产品配置服务**:
```javascript
// 调用PDM服务进行产品配置
const productConfig = await fetch('/api/pdm/configure', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    template_id: 'TEMP-001',
    parameters: {
      length: 2000,
      width: 1500,
      thickness: 6
    }
  })
});
```

**BOM固化服务**:
```javascript
// 订单确认后调用BOM固化
const bomSolidify = await fetch('/api/pdm/boms/solidify', {
  method: 'POST',
  body: JSON.stringify({
    order_id: 'SO-20250802-001',
    bom_snapshot: bomData,
    adjustments: []
  })
});
```

### 基础服务集成
**用户权限控制**:
- 销售代表：查看分配客户、创建报价订单
- 销售经理：管理团队客户、审批大额订单
- 客服专员：处理服务工单、客户满意度调查
- 销售总监：查看所有数据、制定价格策略

**数据字典集成**:
- 客户类型：重点客户/普通客户/潜在客户
- 销售阶段：线索/接触/需求/方案/谈判/成交
- 服务类型：售前咨询/技术支持/投诉处理/维修服务

## 核心业务流程

### 客户开发到成交流程
```
1. 销售代表创建客户档案
2. 创建销售机会
3. 创建报价单 (调用PDM服务)
4. 发送报价给客户
5. 更新商机状态
6. 报价转订单
7. 触发BOM固化 (调用PDM服务)
```

### 售后服务流程
```
1. 客户反映问题
2. 客服专员创建服务工单
3. 分派工单给工程师
4. 工程师联系客户处理
5. 更新处理进展
6. 关闭工单
7. 满意度调查
8. 更新客户服务记录
```

## 数据模型设计

### 客户数据模型
```sql
CREATE TABLE customers (
    customer_id VARCHAR(12) PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL,
    customer_type VARCHAR(20) NOT NULL,
    industry VARCHAR(50),
    credit_rating VARCHAR(10),
    sales_rep_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW()
);
```

### 销售机会数据模型
```sql
CREATE TABLE sales_opportunities (
    opportunity_id VARCHAR(20) PRIMARY KEY,
    customer_id VARCHAR(12) NOT NULL,
    opportunity_name VARCHAR(100) NOT NULL,
    stage VARCHAR(20) NOT NULL,
    estimated_value DECIMAL(15,2),
    probability DECIMAL(5,2),
    expected_close_date DATE,
    sales_rep_id VARCHAR(20),
    created_date DATETIME DEFAULT NOW()
);
```

### 报价订单数据模型
```sql
CREATE TABLE quotations (
    quotation_id VARCHAR(20) PRIMARY KEY,
    customer_id VARCHAR(12) NOT NULL,
    opportunity_id VARCHAR(20),
    quotation_no VARCHAR(30) UNIQUE NOT NULL,
    total_amount DECIMAL(15,2),
    valid_until DATE,
    status VARCHAR(20) DEFAULT 'DRAFT',
    created_date DATETIME DEFAULT NOW()
);

CREATE TABLE sales_orders (
    order_id VARCHAR(20) PRIMARY KEY,
    quotation_id VARCHAR(20),
    customer_id VARCHAR(12) NOT NULL,
    order_no VARCHAR(30) UNIQUE NOT NULL,
    total_amount DECIMAL(15,2),
    delivery_date DATE,
    status VARCHAR(20) DEFAULT 'PENDING',
    created_date DATETIME DEFAULT NOW()
);
```

## 性能和可靠性要求

### 性能指标
- **页面加载时间**: ≤ 3秒
- **报价计算响应时间**: ≤ 2秒
- **Excel导入处理时间**: 500行 ≤ 30秒
- **并发处理能力**: ≥ 200用户同时在线
- **系统可用性**: ≥ 99.5%

### 业务指标
- **销售转化率**: ≥ 30%
- **客户响应时间**: ≤ 30分钟
- **订单录入准确率**: ≥ 98%
- **客户满意度**: ≥ 90%
- **服务响应时间**: ≤ 2小时

## 用户界面设计

### 客户360度视图
- **客户基本信息**: 左侧面板显示客户详情
- **销售机会**: 中间区域显示商机列表和漏斗
- **交易历史**: 右侧显示报价订单历史
- **服务记录**: 底部显示服务工单和满意度

### 销售工作台
- **我的客户**: 显示分配的客户列表
- **我的商机**: 显示负责的销售机会
- **待办事项**: 显示需要跟进的任务
- **业绩统计**: 显示个人销售业绩

### 报价订单界面
- **产品配置器**: 参数化产品配置界面
- **价格计算**: 实时成本和价格计算
- **Excel导入**: 批量订单导入向导
- **PDF生成**: 专业报价单格式

## 与其他模块的关联

### 向下游传递
- **向生产模块**: 传递生产需求和订单信息
- **向财务模块**: 传递应收信息和收款计划
- **向库存模块**: 传递发货需求和客户信息

### 从服务获取
- **从PDM服务**: 获取产品配置和BOM信息
- **从基础服务**: 获取用户权限和数据字典

### 与支持模块协作
- **与项目模块**: 项目型销售的协作管理
- **与质量模块**: 客户质量要求的传递

---

**总结**: 销售管理模块作为V2.1架构中的核心业务模块，通过整合CRM和销售管理功能，实现了客户全生命周期的一体化管理，为企业销售业务提供了完整的数字化解决方案。该模块与PDM服务和基础服务深度集成，为后续的生产、库存、财务等模块提供了重要的业务驱动。