# PDM服务 概述

## 基本信息
- **文档**: PRD-S1: PDM服务 产品需求文档 V2.1
- **版本**: 2.1 架构优化版
- **日期**: 2025-08-02
- **状态**: 架构优化完成
- **定位**: 共享服务层核心组件

## 核心价值主张

### 服务定位
**PDM服务作为企业级产品数据管理服务中心，为所有业务模块提供统一的产品数据管理能力。**

### 优化目标
将V2.0的PDM子系统重构为共享服务，解决各业务模块重复实现产品数据管理功能的问题，实现产品数据的统一管理和服务化提供。

### 商业价值量化
- **开发效率提升**: 服务化设计使各模块开发效率提升50%，避免重复开发
- **数据一致性**: 统一的产品数据管理使数据一致性从85%提升至99%
- **维护成本降低**: 集中式服务管理使维护成本降低60%
- **业务响应速度**: 标准化API使业务响应速度提升40%

## 服务架构

### 服务能力矩阵
| 服务能力 | API接口 | 调用模块 | 核心功能 |
|----------|---------|----------|----------|
| 物料主数据管理 | `/api/pdm/materials` | 所有模块 | 统一的物料信息管理 |
| 参数化BOM设计 | `/api/pdm/boms` | 销售、生产模块 | 动态BOM生成和配置 |
| BOM固化服务 | `/api/pdm/boms/solidify` | 销售、生产模块 | 订单BOM固化处理 |
| 工艺路线管理 | `/api/pdm/routes` | 生产、质量模块 | 工艺流程定义和管理 |
| 产品配置服务 | `/api/pdm/configure` | 销售模块 | 参数化产品配置 |
| 技术文档管理 | `/api/pdm/documents` | 所有模块 | 技术文档存储和关联 |
| 版本管理服务 | `/api/pdm/versions` | 所有模块 | 数据版本控制 |

### 模块调用关系
```
销售管理模块 ──┐
生产管理模块 ──┤
库存管理模块 ──┼──► PDM服务 ──► 产品数据库
财务管理模块 ──┤              ├──► 文档存储
项目管理模块 ──┤              └──► 版本管理
质量管理模块 ──┘
```

## 核心服务功能

### 1. 物料主数据管理服务
**功能描述**: 为所有业务模块提供统一的物料主数据管理能力

**核心API**:
- `GET /api/pdm/materials` - 物料查询服务
- `POST /api/pdm/materials` - 物料创建服务
- `GET/POST /api/pdm/materials/{id}/variants` - 物料变体管理

**业务规则**:
- 物料编码自动生成: MAT-XXXXXXXX
- 支持玻璃行业特殊属性: 长、宽、厚度、颜色、品级
- 变体物料与主物料关联管理
- 物料状态控制: ACTIVE/INACTIVE

### 2. 参数化BOM设计服务
**功能描述**: 提供参数化BOM设计和动态计算能力

**核心API**:
- `GET/POST/PUT /api/pdm/bom-templates` - BOM模板管理
- `POST /api/pdm/boms/calculate` - 参数化计算服务
- `GET/POST /api/pdm/boms/{id}/versions` - BOM版本管理

**业务规则**:
- 支持多层级BOM结构设计
- 参数化公式计算物料用量
- BOM版本状态控制: 草稿/激活/归档
- 支持物料BOM和工艺BOM分离

### 3. BOM固化服务
**功能描述**: 将参数化BOM转换为固化的生产BOM

**核心API**:
- `POST /api/pdm/boms/solidify` - BOM固化处理
- `GET /api/pdm/boms/solidified/{order_id}` - 固化BOM查询

**业务规则**:
- 销售订单确认后自动触发固化流程
- 支持工艺工程师微调: 物料替换、用量调整
- 固化后BOM与原模板解除关联
- 固化BOM自动传递给生产和采购系统

### 4. 工艺路线管理服务
**功能描述**: 提供工艺路线设计、工序管理、外协管理

**核心API**:
- `GET/POST/PUT /api/pdm/routes` - 工艺路线设计
- `GET/POST/PUT /api/pdm/operations` - 工序管理服务
- `GET/POST/PUT /api/pdm/operations/{id}/outsourcing` - 外协工序管理

**业务规则**:
- 工艺路线与BOM关联设计
- 支持参数化工艺路线配置
- 内制外协灵活切换
- 工艺参数与工序关联

## 关键技术特性

### 性能要求
- **API响应时间**: ≤ 2秒
- **并发处理能力**: ≥ 500请求/秒
- **数据查询性能**: 复杂BOM计算 ≤ 5秒
- **服务可用性**: ≥ 99.9%

### 可靠性保障
- **数据备份**: 实时数据备份机制
- **故障恢复**: 自动故障检测和恢复
- **降级处理**: 服务异常时的降级策略
- **监控告警**: 7×24小时服务监控

### 扩展性设计
- **水平扩展**: 支持多实例部署
- **缓存机制**: Redis缓存热点数据
- **数据库优化**: 读写分离和分库分表
- **微服务架构**: 支持独立部署和升级

## 安全和权限控制

### 数据安全
- **数据加密**: 敏感数据加密存储
- **访问控制**: 基于角色的数据访问控制
- **审计日志**: 完整的操作审计记录
- **数据脱敏**: 非生产环境数据脱敏

### API安全
- **身份认证**: JWT Token认证机制
- **权限校验**: 细粒度权限控制
- **请求限流**: API调用频率限制
- **SQL注入防护**: 参数化查询防护

## 与AUTH-001的关联

### 认证集成
PDM服务完全依赖基础服务的认证和授权:
- 所有API调用必须通过基础服务的认证
- 使用JWT Token进行身份验证
- 基于RBAC的权限控制

### 权限控制
PDM服务的权限控制与AUTH-001认证系统紧密相关:
- 物料数据的创建、修改、删除权限
- BOM设计和固化的审批权限
- 工艺路线的查看和修改权限
- 技术文档的访问权限

### 数据权限
支持细粒度的数据权限控制:
- 部门级别的物料数据访问权限
- 项目级别的BOM数据权限
- 角色级别的工艺数据权限

## 数据模型核心

### 物料主数据
```sql
CREATE TABLE materials (
    material_code VARCHAR(12) PRIMARY KEY,
    material_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(20) NOT NULL,
    specification VARCHAR(200),
    unit_of_measure VARCHAR(10) NOT NULL,
    category_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE'
);
```

### BOM结构数据
```sql
CREATE TABLE bom_structures (
    bom_id VARCHAR(20) PRIMARY KEY,
    parent_material VARCHAR(12),
    child_material VARCHAR(12),
    quantity DECIMAL(15,3) NOT NULL,
    unit VARCHAR(10) NOT NULL,
    level INT NOT NULL,
    sequence INT
);
```

## 服务调用示例

### 产品配置服务调用
```javascript
const productConfig = await fetch('/api/pdm/configure', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    template_id: 'TEMP-001',
    parameters: {
      length: 2000,
      width: 1500,
      thickness: 6,
      color: '透明'
    }
  })
});
```

### BOM固化服务调用
```javascript
const bomData = await fetch(`/api/pdm/boms/solidified/${orderId}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

## 监控和运维

### 服务监控
- **性能监控**: API响应时间、吞吐量监控
- **错误监控**: 错误率、异常监控
- **资源监控**: CPU、内存、磁盘使用率
- **业务监控**: 关键业务指标监控

### 日志管理
- **访问日志**: API调用日志记录
- **错误日志**: 异常和错误日志
- **业务日志**: 关键业务操作日志
- **审计日志**: 数据变更审计记录

---

**总结**: PDM服务是V2.1架构中的核心共享服务，通过服务化设计实现了产品数据的统一管理，为所有业务模块提供了一致可靠的产品数据服务，是实现数据一致性和业务协同的关键基础设施。