# 基础服务 概述

## 基本信息
- **文档**: PRD-S2: 基础服务 产品需求文档 V2.1
- **版本**: 2.1 架构优化版
- **日期**: 2025-08-02
- **状态**: 架构优化完成
- **定位**: 共享服务层核心组件

## 核心价值主张

### 服务定位
**基础服务作为企业级基础服务中心，为所有业务模块提供统一的用户认证、权限管理、组织架构、数据字典等基础服务。**

### 优化目标
将V2.0的基础管理子系统重构为共享服务，解决各业务模块重复实现用户认证、权限管理、组织架构等基础功能的问题，实现"一次登录，全系统访问"的用户体验。

### 商业价值量化
- **安全风险降低**: 统一的权限管理使数据泄露风险降低90%
- **管理效率提升**: 新员工入职配置时间从30分钟缩短至5分钟
- **运维成本降低**: 统一服务管理减少重复配置工作60%
- **开发效率提升**: 服务化设计使各模块开发效率提升40%

## 服务架构

### 服务能力矩阵
| 服务能力 | API接口 | 调用模块 | 核心功能 |
|----------|---------|----------|----------|
| 用户认证服务 | `/api/auth/*` | 所有模块 | 统一的身份认证和会话管理 |
| 权限管理服务 | `/api/permissions/*` | 所有模块 | RBAC权限控制和数据权限 |
| 组织架构服务 | `/api/organizations/*` | 所有模块 | 企业组织结构管理 |
| 数据字典服务 | `/api/dictionaries/*` | 所有模块 | 基础数据字典维护 |
| 用户管理服务 | `/api/users/*` | 所有模块 | 用户生命周期管理 |
| 角色管理服务 | `/api/roles/*` | 所有模块 | 角色定义和权限分配 |

### 内部服务架构
```
基础服务
├── 用户认证服务 ──► 用户数据库
├── 权限管理服务 ──► 权限数据库
├── 组织架构服务 ──► 组织数据库
└── 数据字典服务 ──► 字典数据库
```

## 核心服务功能

### 1. 用户认证服务
**功能描述**: 为所有业务模块提供统一的用户身份认证和会话管理能力

**核心API**:
- `POST /api/auth/login` - 用户登录服务
- `GET /api/auth/verify` - Token验证服务
- `GET /api/auth/sso` - 单点登录服务
- `POST /api/auth/logout` - 会话管理服务

**业务规则**:
- 支持用户名密码、手机验证码等多种认证方式
- JWT Token有效期可配置，默认8小时
- 支持记住登录状态，最长30天
- 异常登录自动锁定账户

**安全特性**:
- BCrypt密码加密存储
- JWT签名验证
- 防暴力破解机制
- 完整的操作审计记录

### 2. 权限管理服务
**功能描述**: 提供基于RBAC的权限管理能力

**核心API**:
- `POST /api/permissions/check` - 权限校验服务
- `GET /api/permissions/user/{user_id}` - 用户权限查询
- `GET/POST/PUT /api/roles/{role_id}/permissions` - 角色权限管理
- `POST /api/permissions/data-scope` - 数据权限控制

**权限模型**: RBAC (用户-角色-权限)
- **菜单权限**: 控制用户可访问的功能模块
- **操作权限**: 控制用户可执行的具体操作
- **数据权限**: 控制用户可访问的数据范围

**数据权限层级**:
- 本人数据
- 本部门数据
- 本部门及下级数据
- 全部数据

### 3. 组织架构服务
**功能描述**: 提供企业组织架构的统一管理能力

**核心API**:
- `GET /api/organizations/tree` - 组织架构查询
- `GET/POST/PUT/DELETE /api/organizations/{org_id}` - 组织节点管理
- `GET /api/organizations/users/{user_id}` - 用户组织关系
- `PUT /api/organizations/users/{user_id}/transfer` - 用户组织调动

**架构特性**:
- 支持无限层级的树状组织结构
- 组织编码全局唯一
- 支持拖拽调整组织关系
- 组织变更自动更新用户权限

### 4. 数据字典服务
**功能描述**: 提供统一的数据字典管理能力

**核心API**:
- `GET /api/dictionaries/{dict_type}` - 字典数据查询
- `GET/POST/PUT/DELETE /api/dictionaries` - 字典管理服务
- `GET /api/dictionaries/form-options/{form_field}` - 业务表单集成

**管理特性**:
- 支持分类管理和层级结构
- 字典值支持排序和启用/禁用
- 被引用的字典值限制删除
- 业务表单自动关联字典

## 与AUTH-001开发的直接关联

### 核心关联性
基础服务与AUTH-001认证系统开发高度相关，是AUTH-001的核心实现载体:

1. **用户认证服务** = AUTH-001的核心功能实现
2. **权限管理服务** = AUTH-001的权限控制实现
3. **组织架构服务** = AUTH-001的组织权限基础
4. **数据字典服务** = AUTH-001的配置数据支撑

### 开发优先级
**Phase 1 - 基础认证 (与AUTH-001同步)**:
- 用户认证服务 (登录/登出/Token验证)
- 基础权限管理服务
- 用户管理服务

**Phase 2 - 权限扩展**:
- 完整的RBAC权限体系
- 数据权限控制
- 权限缓存和优化

**Phase 3 - 组织和字典**:
- 组织架构服务
- 数据字典服务
- 系统配置服务

### API集成规范
```javascript
// 用户登录 - AUTH-001核心接口
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: 'password123',
    captcha: 'ABCD'
  })
});

// 权限校验 - AUTH-001权限验证
const permissionCheck = await fetch('/api/permissions/check', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    resource: 'sales_order',
    action: 'create'
  })
});
```

## 性能和可靠性

### 性能指标
- **登录响应时间**: ≤ 500ms
- **权限校验响应时间**: ≤ 200ms
- **API响应时间**: ≤ 1秒
- **并发处理能力**: ≥ 1000请求/秒
- **服务可用性**: ≥ 99.9%

### 可靠性保障
- **会话管理**: 分布式会话存储
- **权限缓存**: Redis缓存权限数据
- **故障恢复**: 自动故障检测和恢复
- **降级处理**: 服务异常时的降级策略

### 安全保障
- **密码安全**: BCrypt加密存储
- **Token安全**: JWT签名验证
- **防暴力破解**: 登录失败锁定机制
- **审计日志**: 完整的操作审计记录

## 核心数据模型

### 用户数据模型
```sql
CREATE TABLE users (
    user_id VARCHAR(20) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    employee_id VARCHAR(20) UNIQUE,
    name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    department_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW(),
    updated_date DATETIME DEFAULT NOW()
);
```

### 角色权限模型
```sql
CREATE TABLE roles (
    role_id VARCHAR(20) PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_code VARCHAR(20) UNIQUE NOT NULL,
    description VARCHAR(200),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW()
);

CREATE TABLE permissions (
    permission_id VARCHAR(20) PRIMARY KEY,
    permission_name VARCHAR(50) NOT NULL,
    permission_code VARCHAR(50) UNIQUE NOT NULL,
    resource_type VARCHAR(20),
    action VARCHAR(20),
    parent_id VARCHAR(20),
    level INT DEFAULT 1
);
```

### 组织架构模型
```sql
CREATE TABLE organizations (
    org_id VARCHAR(20) PRIMARY KEY,
    org_code VARCHAR(20) UNIQUE NOT NULL,
    org_name VARCHAR(100) NOT NULL,
    parent_id VARCHAR(20),
    level INT DEFAULT 1,
    sort_order INT DEFAULT 0,
    manager_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW()
);
```

## 监控和运维

### 服务监控
- **登录监控**: 登录成功率、失败率、响应时间
- **权限监控**: 权限校验次数、失败次数
- **会话监控**: 在线用户数、会话时长
- **API监控**: 接口调用量、响应时间、错误率

### 安全监控
- **异常登录**: 异常IP、异常时间登录告警
- **权限异常**: 权限提升、异常访问告警
- **暴力破解**: 密码尝试次数监控
- **数据访问**: 敏感数据访问监控

### 日志管理
- **访问日志**: 用户登录、API调用日志
- **操作日志**: 权限变更、组织调整日志
- **错误日志**: 系统异常、错误日志
- **审计日志**: 关键操作审计记录

## 部署和扩展

### 部署架构
- **负载均衡**: Nginx负载均衡
- **应用集群**: 多实例部署
- **数据库**: 主从复制，读写分离
- **缓存**: Redis集群

### 扩展性设计
- **水平扩展**: 支持多实例部署
- **垂直扩展**: 支持资源动态调整
- **微服务**: 支持服务拆分和独立部署
- **容器化**: 支持Docker容器部署

---

**总结**: 基础服务是V2.1架构中的基础设施层，直接对应AUTH-001认证系统的核心实现。通过服务化设计实现了统一的认证授权管理，为所有业务模块提供了安全可靠的基础服务支撑，是整个ERP系统的安全基石。