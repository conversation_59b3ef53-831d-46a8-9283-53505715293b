# 业务规则与数据标准 概述

## 基本信息
- **文档**: 核心业务规则库 V2.1 + 数据字典 V2.1
- **版本**: 2.1 架构优化版
- **日期**: 2025-08-02
- **状态**: 架构优化完成
- **定位**: 系统规范和约束的统一标准

## 核心价值定位

### 业务规则库的作用
为所有PRD文档提供统一的业务规则定义，确保系统的一致性和完整性。V2.1版本重点更新了跨模块协作规则，以反映分层架构的设计思想。

### 数据字典的作用
为所有PRD文档提供统一的数据定义和标准，所有模块必须严格遵循数据格式、约束条件和业务规则。V2.1版本重点建立了跨模块的统一数据标准。

## V2.1架构业务规则

### AR-001: 分层架构调用规则
**核心原则**: 系统各层之间的调用必须遵循分层架构原则

**具体规则**:
- 上层可以调用下层服务，下层不能直接调用上层
- 同层模块之间不能直接调用，必须通过共享服务层
- 跨层调用必须通过标准API接口
- 禁止跨层直接访问数据库

**适用模块**: 所有模块
**违规处理**: 系统拒绝调用，记录错误日志

### AR-002: 模块边界规则
**核心原则**: 各业务模块必须明确职责边界，避免功能重复

**具体规则**:
- 产品配置功能统一由PDM服务提供
- 用户认证和权限管理统一由基础服务提供
- 数据字典维护统一由基础服务提供
- 各模块不得重复实现共享服务的功能

**适用模块**: 所有业务模块
**违规处理**: 设计评审阶段拒绝通过

### AR-003: 服务调用规则
**核心原则**: 业务模块调用共享服务必须遵循标准规范

**具体规则**:
- 必须使用RESTful API进行服务调用
- 调用超时时间不得超过5秒
- 必须实现重试机制和降级处理
- 关键业务数据必须进行本地缓存

**适用模块**: 所有业务模块
**违规处理**: 系统自动降级，告警通知

## 数据管理业务规则

### DR-001: 主数据管理规则
**核心原则**: 企业主数据必须统一管理，确保数据一致性

**具体规则**:
- 客户、供应商、物料等主数据由PDM服务统一管理
- 各业务模块不得维护重复的主数据
- 主数据变更必须通过PDM服务进行
- 主数据删除前必须检查引用关系

### DR-002: 数据编码规则
**核心原则**: 所有业务数据必须遵循统一的编码规范

**关键编码规范**:
- 订单编号格式：SO-YYYYMMDD-XXX
- 生产订单号格式：MO-YYYYMMDD-XXX
- 物料编码格式：MAT-XXXXXXXX
- 客户编码格式：CUS-XXXXXXXX
- 供应商编码格式：SUP-XXXXXXXX

### DR-003: 数据同步规则
**核心原则**: 跨模块数据同步必须保证一致性和及时性

**具体规则**:
- 关键业务数据必须实时同步
- 同步失败必须自动重试，最多重试3次
- 同步延迟不得超过30秒
- 同步异常必须记录日志并告警

## 核心业务流程规则

### BR-001: 订单到生产流程规则
**流程描述**: 从销售订单到生产执行的端到端流程规则

**关键规则**:
- 销售订单确认后自动触发BOM固化流程
- BOM固化完成后自动生成生产订单
- 生产订单下达前必须完成物料可用性检查
- 生产开始前必须完成工艺路线确认

**适用模块**: 销售管理、生产管理、PDM服务

### BR-002: BOM固化规则
**流程描述**: 产品BOM从参数化到固化的业务规则

**关键规则**:
- 销售订单确认后，参数化BOM必须固化为生产BOM
- BOM固化后不允许修改，如需变更必须走变更流程
- 固化BOM必须包含完整的物料清单和工艺路线
- BOM版本必须与订单关联，确保可追溯

**适用模块**: 销售管理、生产管理、PDM服务

### BR-003: 切割优化规则
**流程描述**: 玻璃切割优化的业务规则

**关键规则**:
- 切割优化必须基于实际库存的原片规格
- 优化目标：最大化原片利用率，最小化废料
- 切割方案确定后自动更新物料需求
- 切割异常必须记录并影响后续优化算法

**适用模块**: 生产管理、库存管理、PDM服务

## 统一数据标准

### 编码规范标准

#### 主数据编码规范
| 数据类型 | 编码格式 | 长度 | 示例 | 说明 |
|----------|----------|------|------|------|
| 客户编码 | CUS-XXXXXXXX | 12位 | CUS-00000001 | 客户唯一标识 |
| 供应商编码 | SUP-XXXXXXXX | 12位 | SUP-00000001 | 供应商唯一标识 |
| 物料编码 | MAT-XXXXXXXX | 12位 | MAT-00000001 | 物料唯一标识 |
| 员工编码 | EMP-XXXXXXXX | 12位 | EMP-00000001 | 员工唯一标识 |
| 设备编码 | EQU-XXXXXXXX | 12位 | EQU-00000001 | 设备唯一标识 |

#### 业务单据编码规范
| 单据类型 | 编码格式 | 长度 | 示例 | 说明 |
|----------|----------|------|------|------|
| 销售订单 | SO-YYYYMMDD-XXX | 14位 | SO-20250802-001 | 销售订单编号 |
| 生产订单 | MO-YYYYMMDD-XXX | 14位 | MO-20250802-001 | 生产订单编号 |
| 采购订单 | PO-YYYYMMDD-XXX | 14位 | PO-20250802-001 | 采购订单编号 |
| 入库单 | IN-YYYYMMDD-XXX | 14位 | IN-20250802-001 | 入库单编号 |
| 出库单 | OUT-YYYYMMDD-XXX | 15位 | OUT-20250802-001 | 出库单编号 |
| 报价单 | QT-YYYYMMDD-XXX | 14位 | QT-20250802-001 | 报价单编号 |

### 核心数据实体定义

#### 客户主数据标准
```sql
CREATE TABLE customers (
    customer_code VARCHAR(12) PRIMARY KEY,     -- 客户编码
    customer_name VARCHAR(100) NOT NULL,       -- 客户名称
    customer_type VARCHAR(20) NOT NULL,        -- 客户类型：直销/经销商/工程商
    contact_person VARCHAR(50),                -- 联系人
    contact_phone VARCHAR(20),                 -- 联系电话
    contact_email VARCHAR(100),                -- 联系邮箱
    address VARCHAR(200),                      -- 客户地址
    credit_limit DECIMAL(15,2) DEFAULT 0.00,   -- 信用额度
    payment_terms VARCHAR(50),                 -- 付款条件
    status VARCHAR(10) DEFAULT 'ACTIVE',       -- 状态：ACTIVE/INACTIVE
    created_date DATETIME DEFAULT NOW(),       -- 创建时间
    updated_date DATETIME DEFAULT NOW()        -- 更新时间
);
```

#### 物料主数据标准
```sql
CREATE TABLE materials (
    material_code VARCHAR(12) PRIMARY KEY,     -- 物料编码
    material_name VARCHAR(100) NOT NULL,       -- 物料名称
    material_type VARCHAR(20) NOT NULL,        -- 物料类型：原材料/半成品/成品
    specification VARCHAR(200),               -- 规格描述
    unit_of_measure VARCHAR(10) NOT NULL,     -- 计量单位
    standard_cost DECIMAL(15,4) DEFAULT 0,    -- 标准成本
    safety_stock DECIMAL(15,3) DEFAULT 0,     -- 安全库存
    lead_time INT DEFAULT 0,                  -- 采购提前期（天）
    is_variant BOOLEAN DEFAULT FALSE,         -- 是否变体物料
    parent_material VARCHAR(12),              -- 父物料编码
    status VARCHAR(10) DEFAULT 'ACTIVE',      -- 状态：ACTIVE/INACTIVE
    created_date DATETIME DEFAULT NOW(),      -- 创建时间
    updated_date DATETIME DEFAULT NOW()       -- 更新时间
);
```

### 状态枚举定义

#### 订单状态标准
| 状态代码 | 状态名称 | 说明 | 允许操作 |
|----------|----------|------|----------|
| DRAFT | 草稿 | 订单创建但未确认 | 编辑、删除、确认 |
| CONFIRMED | 已确认 | 订单已确认，等待生产 | 取消、修改、生产 |
| IN_PRODUCTION | 生产中 | 订单正在生产 | 查看、暂停 |
| COMPLETED | 已完成 | 订单生产完成 | 查看、发货 |
| SHIPPED | 已发货 | 订单已发货 | 查看、收款 |
| CANCELLED | 已取消 | 订单已取消 | 查看 |

#### 生产状态标准
| 状态代码 | 状态名称 | 说明 | 允许操作 |
|----------|----------|------|----------|
| PLANNED | 已计划 | 生产订单已创建 | 编辑、删除、下达 |
| RELEASED | 已下达 | 生产订单已下达 | 开始、暂停 |
| IN_PROGRESS | 进行中 | 生产正在进行 | 报工、暂停、完成 |
| COMPLETED | 已完成 | 生产已完成 | 查看、入库 |
| CANCELLED | 已取消 | 生产已取消 | 查看 |

## 财务管理业务规则

### FR-001: 成本核算规则
**关键规则**:
- 成本核算必须基于实际消耗的物料和工时
- 成本归集必须按照成本中心进行
- 间接费用按照预设比例分摊
- 成本核算周期为月度，月末自动结算

### FR-002: 计件薪酬规则
**关键规则**:
- 计件薪酬基于实际完成的合格产品数量
- 不合格产品不计入计件数量
- 计件单价按照工序和难度系数确定
- 薪酬计算结果必须经过审核确认

### FR-003: 应收应付管理规则
**关键规则**:
- 应收款项必须与销售订单关联
- 应付款项必须与采购订单关联
- 超期应收必须自动提醒和催收
- 付款必须经过审批流程

## 库存管理业务规则

### IR-001: 库存控制规则
**关键规则**:
- 库存数量不得为负数
- 出库必须先进行库存可用性检查
- 安全库存低于预警线时自动提醒
- 呆滞库存超过6个月自动标记

### IR-002: 批次管理规则
**关键规则**:
- 所有物料必须按批次管理
- 出库遵循先进先出原则
- 批次信息必须完整记录
- 问题批次必须能够快速定位和隔离

## 权限管理业务规则

### PR-001: 用户权限规则
**关键规则**:
- 用户权限基于角色分配
- 权限变更必须经过审批
- 敏感操作必须记录审计日志
- 用户离职后权限立即回收

### PR-002: 数据权限规则
**关键规则**:
- 数据权限基于组织架构控制
- 用户只能访问权限范围内的数据
- 跨部门数据访问需要特殊授权
- 数据导出必须记录和审批

## 系统运行规则

### SR-001: 性能要求规则
**关键指标**:
- 页面响应时间不得超过3秒
- API接口响应时间不得超过2秒
- 系统可用性不得低于99.9%
- 并发用户数不得低于300

### SR-002: 数据备份规则
**关键规则**:
- 关键业务数据必须实时备份
- 完整备份周期不得超过24小时
- 备份数据必须定期验证可用性
- 灾难恢复时间不得超过4小时

## 数据约束规则

### 数值约束
| 字段类型 | 最小值 | 最大值 | 精度 | 说明 |
|----------|--------|--------|------|------|
| 数量字段 | 0 | 999999999.999 | 3位小数 | 不允许负数 |
| 金额字段 | 0 | 999999999999.99 | 2位小数 | 不允许负数 |
| 单价字段 | 0 | 999999.9999 | 4位小数 | 不允许负数 |
| 百分比字段 | 0 | 100 | 2位小数 | 百分比值 |

### 日期约束
| 字段类型 | 最小值 | 最大值 | 格式 | 说明 |
|----------|--------|--------|------|------|
| 日期字段 | 1900-01-01 | 2099-12-31 | YYYY-MM-DD | 标准日期格式 |
| 时间字段 | - | - | YYYY-MM-DD HH:MM:SS | 标准时间格式 |
| 交货日期 | 当前日期 | 当前日期+365天 | YYYY-MM-DD | 不能早于当前日期 |

### 文本约束
| 字段类型 | 最小长度 | 最大长度 | 字符集 | 说明 |
|----------|----------|----------|--------|------|
| 编码字段 | 8 | 16 | 字母数字 | 不允许特殊字符 |
| 名称字段 | 1 | 100 | UTF-8 | 支持中英文 |
| 描述字段 | 0 | 500 | UTF-8 | 可选字段 |
| 备注字段 | 0 | 2000 | UTF-8 | 长文本字段 |

## 与AUTH-001的关联

### 权限管理规则集成
业务规则库中的权限管理规则直接指导AUTH-001的实现:

1. **用户权限规则 (PR-001)**: 定义了基于角色的权限分配机制
2. **数据权限规则 (PR-002)**: 定义了基于组织架构的数据访问控制
3. **模块边界规则 (AR-002)**: 明确了认证和权限管理由基础服务统一提供

### 数据标准应用
数据字典中的标准直接应用于AUTH-001:

1. **用户数据模型**: 定义了用户表的标准结构
2. **角色权限模型**: 定义了RBAC的数据结构
3. **组织架构模型**: 定义了组织数据的标准格式
4. **编码规范**: 用户编码、角色编码等的标准格式

### 业务流程约束
业务规则对AUTH-001开发的具体约束:

1. **服务调用规则**: 所有模块必须通过标准API调用认证服务
2. **数据同步规则**: 权限变更必须实时同步到所有模块
3. **性能要求**: 权限校验响应时间≤200ms
4. **安全规则**: 敏感操作必须记录审计日志

## 维护和更新机制

### 业务规则维护
1. **新增规则**: 必须经过产品团队和技术团队联合评审
2. **修改规则**: 需要评估对现有系统的影响
3. **废弃规则**: 必须提供替代方案和迁移计划
4. **定期审查**: 每季度进行一次业务规则审查和更新

### 数据字典维护
1. **新增字段**: 必须经过数据架构师评审
2. **修改字段**: 需要评估对现有数据的影响
3. **删除字段**: 必须确保无业务依赖
4. **数据迁移**: 字段变更需要提供数据迁移脚本

---

**总结**: 业务规则库和数据字典构成了Glass ERP V2.1的规范基础，为所有模块开发提供了统一的标准和约束。这些规则和标准特别是对AUTH-001认证系统的开发具有直接的指导意义，确保了系统的一致性、安全性和可维护性。