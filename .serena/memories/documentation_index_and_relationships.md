# Glass ERP V2.1 文档索引与关联关系

## 文档概览

### 文档总量统计
- **PRD V2.1文档**: 17个核心文档
- **PRD V2.0文档**: 70+个详细模块文档 (参考基础)
- **核心指导文档**: 5个
- **参考文档**: 2个
- **技术实施指导**: 5个

## 文档分类索引

### 🏗️ 架构设计文档

#### 1. 系统总览 (`prd_v2.1/README.md`)
- **核心内容**: V2.1分层架构设计、模块整合说明、验收标准
- **关键价值**: 开发效率提升60%，维护成本降低50%，用户学习成本降低70%
- **架构模式**: 5层架构 (用户界面层→核心业务层→支持业务层→共享服务层→数据存储层)
- **依赖关系**: 所有其他文档的基础和指导原则

#### 2. 变更对比报告 (`prd_v2.1/V2.0_to_V2.1_Change_Report.md`)
- **核心内容**: V2.0到V2.1的详细变更对比、优化效果量化
- **关键指标**: 系统数量从11个减少到7个，接口数量减少62%，业务流程效率提升80%
- **实施指导**: 分阶段迁移策略、风险控制、成功标准
- **参考价值**: 理解架构优化的具体改进和预期效果

### 🔧 共享服务文档

#### 3. PDM服务 (`prd_v2.1/PDM_Service.md`)
- **服务定位**: 企业产品数据管理服务中心，所有模块的产品数据唯一可信源
- **核心API**: 物料管理、参数化BOM、BOM固化、工艺路线、产品配置
- **业务价值**: 数据一致性从85%提升至99%，开发效率提升50%
- **调用模块**: 销售、生产、库存、财务、项目、质量模块
- **与AUTH-001关联**: 需要完整的认证授权和数据权限控制

#### 4. 基础服务 (`prd_v2.1/Basic_Service.md`)
- **服务定位**: 企业级基础服务中心，提供认证、权限、组织、字典服务
- **核心API**: 用户认证、权限管理、组织架构、数据字典、用户管理、角色管理
- **业务价值**: 安全风险降低90%，管理效率提升，运维成本降低60%
- **直接关联**: 与AUTH-001开发高度重合，是AUTH-001的核心实现载体
- **开发优先级**: Phase 1与AUTH-001同步开发

### 📋 核心业务模块文档

#### 5. 销售管理模块 (`prd_v2.1/Sales_Management_Module.md`)
- **整合范围**: 销售管理子系统 + CRM子系统
- **核心功能**: 客户档案、销售机会、报价订单、客户服务
- **业务价值**: 销售转化率从15%提升至30%，响应时间从24小时缩短至30分钟
- **服务依赖**: 调用PDM服务(产品配置)、基础服务(认证权限)
- **下游传递**: 向生产模块传递生产需求，向财务模块传递应收信息

#### 6. 生产管理模块 (`prd_v2.1/Production_Management_Module.md`)
- **整合范围**: 生产管理子系统 + 设备管理子系统
- **核心功能**: APS智能排程、切割优化、工序执行、设备监控
- **业务价值**: 生产设备协调效率提升40%，切割优化提升原料利用率
- **服务依赖**: 接收销售模块需求，调用PDM服务获取工艺路线
- **行业特性**: 玻璃切割优化算法，变体生产管理

#### 7. 库存管理模块 (`prd_v2.1/Inventory_Management_Module.md`)
- **整合范围**: 仓储管理子系统 + 采购管理子系统
- **核心功能**: MRP需求计算、采购管理、出入库管理、库存控制
- **业务价值**: 库存准确率提升至99.5%，采购库存流程效率提升83%
- **服务依赖**: 接收生产模块物料需求，向财务模块传递应付信息
- **管理特性**: 批次管理、先进先出、变体库存管理

#### 8. 财务管理模块 (`prd_v2.1/Financial_Management_Module.md`)
- **整合范围**: 财务管理子系统 + 人事管理子系统
- **核心功能**: 财务核算、成本管理、薪酬计算、应收应付
- **业务价值**: 成本核算准确率提升至99%，自动成本归集效率提升85%
- **数据来源**: 接收销售、库存、生产模块的财务数据
- **管理特性**: 计件薪酬计算、成本中心核算

### 📖 规范和标准文档

#### 9. 业务规则库 (`prd_v2.1/Business_Rules.md`)
- **核心内容**: V2.1架构业务规则、数据管理规则、核心业务流程规则
- **关键规则**: 分层架构调用规则、模块边界规则、BOM固化规则、权限管理规则
- **适用范围**: 所有模块必须严格遵循
- **与AUTH-001关联**: 权限管理规则直接指导AUTH-001实现

#### 10. 数据字典 (`prd_v2.1/Data_Dictionary.md`)
- **核心内容**: 统一编码规范、数据实体定义、状态枚举、约束规则
- **编码标准**: 客户CUS-、物料MAT-、订单SO-、生产订单MO-等统一格式
- **数据模型**: 客户、物料、订单等核心实体的标准结构
- **与AUTH-001关联**: 用户、角色、权限数据模型的标准定义

#### 11. 功能边界定义 (`prd_v2.1/Function_Boundary_Definition.md`)
- **核心内容**: 明确各模块的职责边界，避免功能重复
- **边界规则**: PDM服务负责产品数据，基础服务负责认证权限
- **协作机制**: 定义模块间的协作接口和数据传递规范

#### 12. 业务流程优化 (`prd_v2.1/Business_Process_Optimization.md`)
- **核心内容**: 端到端业务流程的优化设计
- **关键流程**: 客户询价→产品配置→报价→订单→生产→交付→收款
- **优化效果**: 关键业务流程效率提升80%以上

#### 13. 术语表 (`prd_v2.1/Glossary.md`)
- **核心内容**: 全局统一的业务术语定义
- **应用范围**: 所有文档和模块开发的术语标准
- **维护机制**: 与项目发展同步更新

### 🛠️ 技术实施指导

#### 14-18. 技术指导文档 (`prd_v2.1/technical_implementation/`)
- **销售管理技术指导**: API设计、数据库结构、集成规范
- **生产管理技术指导**: 切割算法、设备集成、实时数据采集
- **库存管理技术指导**: 批次追溯、库存同步、MRP算法
- **财务管理技术指导**: 成本核算算法、薪酬计算引擎
- **共享服务技术指导**: 服务化架构、API网关、缓存策略

## 文档关联关系图

### 核心依赖关系
```
系统总览(README.md)
    ├── 指导 → 所有模块文档
    └── 定义 → 架构和标准

业务规则库 + 数据字典
    ├── 约束 → 所有模块实现
    └── 标准 → 数据结构和业务逻辑

基础服务
    ├── 支撑 → 所有业务模块
    └── 实现 → AUTH-001认证系统

PDM服务
    ├── 服务 → 销售、生产、库存、财务模块
    └── 管理 → 企业产品数据

销售管理模块
    ├── 调用 → PDM服务、基础服务
    ├── 驱动 → 生产管理模块
    └── 传递 → 财务管理模块

生产管理模块
    ├── 接收 → 销售模块需求
    ├── 调用 → PDM服务、基础服务
    └── 协作 → 库存管理模块

库存管理模块
    ├── 响应 → 生产模块需求
    ├── 调用 → 基础服务
    └── 传递 → 财务管理模块

财务管理模块
    ├── 汇总 → 销售、生产、库存数据
    └── 调用 → 基础服务
```

### 业务流程关联
```
客户询价(销售) → 产品配置(PDM) → 报价生成(销售) → 订单确认(销售)
    ↓
BOM固化(PDM) → 生产计划(生产) → 物料需求(库存) → 采购执行(库存)
    ↓
生产执行(生产) → 质量检验(质量) → 成品入库(库存) → 发货交付(销售)
    ↓
成本核算(财务) → 应收管理(财务) → 收款结算(财务)
```

## 与AUTH-001开发的文档指导

### 直接相关文档（优先级最高）
1. **基础服务文档**: AUTH-001的核心实现依据
2. **业务规则库**: 权限管理规则的具体定义
3. **数据字典**: 用户、角色、权限数据模型标准
4. **系统总览**: 分层架构中基础服务的定位

### 间接相关文档（参考价值）
1. **所有业务模块文档**: 了解认证服务的调用场景
2. **技术实施指导**: 服务化架构的技术实现
3. **功能边界定义**: 认证服务的职责范围

### 开发路径指导
```
Phase 1: 基础认证功能
├── 参考: 基础服务文档 + 业务规则库
├── 实现: 用户登录、Token验证、基础权限
└── 标准: 数据字典中的用户数据模型

Phase 2: 权限管理扩展
├── 参考: 业务规则库权限部分 + 数据字典
├── 实现: RBAC权限体系、数据权限控制
└── 集成: 各业务模块的权限需求

Phase 3: 组织和配置
├── 参考: 数据字典组织模型 + 系统总览
├── 实现: 组织架构服务、数据字典服务
└── 优化: 性能优化和扩展性设计
```

## 文档使用建议

### 开发团队使用指南
1. **架构师**: 重点关注系统总览、变更报告、功能边界定义
2. **后端开发**: 重点关注共享服务文档、技术实施指导、业务规则库
3. **前端开发**: 重点关注业务模块文档、数据字典、用户界面规范
4. **测试团队**: 重点关注业务规则库、数据字典、验收标准
5. **运维团队**: 重点关注技术实施指导、性能要求、监控规范

### 文档维护机制
1. **版本同步**: 所有文档版本与项目版本保持一致
2. **定期审查**: 每季度进行一次文档完整性和准确性审查
3. **变更管理**: 重大变更需要更新相关的所有文档
4. **知识传承**: 通过文档系统确保项目知识的有效传承

---

**总结**: 本文档索引为Glass ERP V2.1项目提供了完整的文档导航和关联关系图，特别是为AUTH-001认证系统的开发提供了明确的文档指导路径。通过系统化的文档组织和关联关系梳理，确保开发团队能够高效利用现有文档资源，推进项目的顺利实施。