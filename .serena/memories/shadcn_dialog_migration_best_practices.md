# Shadcn-ui Dialog Migration Best Practices for Vue.js

## Overview
在 Glass ERP 项目中，我们已成功将模态框从自定义组件迁移到 shadcn-ui Dialog 组件。以下是标准化的最佳实践模式。

## 核心迁移模式

### 1. 组件导入结构
```typescript
// 导入 shadcn-ui Dialog 组件
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'

// 导入内容组件
import FormComponent from '@/components/module/FormComponent.vue'
```

### 2. 标准化的 Dialog 结构

#### 表单对话框模式
```vue
<Dialog :open="showFormDialog" @update:open="showFormDialog = $event">
  <DialogContent class="max-w-2xl">
    <DialogHeader>
      <DialogTitle>{{ editingItem ? '编辑项目' : '新增项目' }}</DialogTitle>
      <DialogDescription>
        {{ editingItem ? '修改项目信息和设置' : '创建新的项目' }}
      </DialogDescription>
    </DialogHeader>
    <FormComponent
      :item="editingItem"
      @success="handleFormSuccess"
      @cancel="showFormDialog = false"
    />
  </DialogContent>
</Dialog>
```

#### 详情对话框模式
```vue
<Dialog :open="showDetailDialog" @update:open="showDetailDialog = $event">
  <DialogContent class="max-w-4xl">
    <DialogHeader>
      <DialogTitle>项目详情</DialogTitle>
      <DialogDescription>
        查看项目的详细信息、关联数据和操作记录
      </DialogDescription>
    </DialogHeader>
    <DetailComponent
      :item-id="viewingItemId"
      @edit="handleEditFromDetail"
      @close="showDetailDialog = false"
    />
  </DialogContent>
</Dialog>
```

#### 确认对话框模式
```vue
<Dialog :open="showDeleteDialog" @update:open="showDeleteDialog = $event">
  <DialogContent>
    <DialogHeader>
      <DialogTitle>确认删除</DialogTitle>
      <DialogDescription>
        此操作将永久删除数据，请谨慎操作
      </DialogDescription>
    </DialogHeader>
    
    <div class="py-4">
      <p class="text-sm text-gray-600">
        确定要删除 <span class="font-semibold text-gray-900">{{ deletingItem?.name }}</span> 吗？
      </p>
      <ul class="mt-3 text-sm text-red-600 space-y-1">
        <li>• 此操作不可撤销</li>
        <li>• 相关数据将被永久删除</li>
      </ul>
    </div>
    
    <DialogFooter>
      <DialogClose as-child>
        <button class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
          取消
        </button>
      </DialogClose>
      <button 
        @click="confirmDelete"
        :disabled="store.submitting"
        class="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
      >
        {{ store.submitting ? '删除中...' : '删除' }}
      </button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### 3. 状态管理模式

#### 响应式状态声明
```typescript
// 对话框状态
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const showDeleteDialog = ref(false)

// 数据状态
const editingItem = ref<ItemType | null>(null)
const viewingItemId = ref<string>('')
const deletingItem = ref<ItemType | null>(null)
```

#### 事件处理函数
```typescript
// 打开编辑对话框
const handleEdit = (item: ItemType) => {
  editingItem.value = item
  showFormDialog.value = true
}

// 表单成功处理
const handleFormSuccess = async () => {
  showFormDialog.value = false
  editingItem.value = null
  toast({ title: '操作成功', variant: 'success' })
  await store.fetchData()
}

// 对话框关闭时重置状态
watch(showFormDialog, (newVal) => {
  if (!newVal) {
    editingItem.value = null
  }
})
```

### 4. 样式和响应式设计

#### 对话框尺寸规范
- **小型对话框**: 默认 `max-w-lg` (确认对话框)
- **中型对话框**: `max-w-2xl` (表单对话框)
- **大型对话框**: `max-w-4xl` (详情对话框)
- **超大对话框**: `max-w-6xl` (复杂数据展示)

#### 可访问性要求
```vue
<DialogHeader>
  <!-- DialogTitle 是必需的，用于屏幕阅读器 -->
  <DialogTitle>对话框标题</DialogTitle>
  <!-- DialogDescription 为可选，提供额外的上下文 -->
  <DialogDescription>
    对话框的详细描述信息
  </DialogDescription>
</DialogHeader>
```

### 5. 从旧模式到新模式的迁移步骤

#### 迁移前（旧模式）
```vue
<CustomDialog
  v-model:visible="showDialog"
  :data="itemData"
  @success="handleSuccess"
/>
```

#### 迁移后（新模式）
```vue
<Dialog :open="showDialog" @update:open="showDialog = $event">
  <DialogContent>
    <DialogHeader>
      <DialogTitle>标题</DialogTitle>
      <DialogDescription>描述</DialogDescription>
    </DialogHeader>
    <CustomDialog
      :data="itemData"
      @success="handleSuccess"
      @cancel="showDialog = false"
    />
  </DialogContent>
</Dialog>
```

### 6. 优势和好处

#### 一致性
- 所有对话框使用相同的结构和API
- 统一的打开/关闭动画效果
- 一致的键盘导航和焦点管理

#### 可访问性
- 自动的 ARIA 标签
- 焦点陷阱和键盘导航
- 屏幕阅读器支持

#### 开发效率
- 标准化的组件结构
- 更好的类型安全性
- 易于维护和扩展

## 应用场景

### 已迁移的组件
- ✅ RoleManagement.vue - 角色管理页面
- ✅ user-management.vue - 用户管理页面

### 适用的对话框类型
1. **CRUD 操作对话框** - 创建、编辑、删除确认
2. **详情查看对话框** - 数据详情展示
3. **配置设置对话框** - 系统配置和设置
4. **数据导入导出对话框** - 文件处理相关

## 注意事项

1. **状态管理**: 确保对话框关闭时重置相关状态
2. **错误处理**: 在对话框内容组件中处理错误，避免意外关闭
3. **数据验证**: 表单验证失败时不应关闭对话框
4. **用户体验**: 使用适当的加载状态和反馈信息

## 后续改进

1. 考虑创建通用的对话框 Composable 函数
2. 实现对话框状态的全局管理
3. 添加更多的动画和交互效果
4. 支持多层对话框的场景处理