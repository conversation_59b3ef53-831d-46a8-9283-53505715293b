import{aQ as l,q as c}from"./index-DMDdU1SS.js";function x(s,o){const r=l({}),u=l({}),i=t=>{const n=s[t],e=o[t];if(!e)return r[t]="",!0;if(e.required&&(!n||typeof n=="string"&&!n.trim()))return r[t]=`${String(t)}是必填项`,!1;if(e.minLength&&n&&n.length<e.minLength)return r[t]=`${String(t)}至少需要${e.minLength}个字符`,!1;if(e.maxLength&&n&&n.length>e.maxLength)return r[t]=`${String(t)}不能超过${e.maxLength}个字符`,!1;if(e.pattern&&n&&!e.pattern.test(n))return r[t]=`${String(t)}格式不正确`,!1;if(e.custom){const a=e.custom(n);if(a)return r[t]=a,!1}return r[t]="",!0},g=()=>{let t=!0;for(const n in o)i(n)||(t=!1),u[n]=!0;return t},h=t=>{r[t]=""},f=()=>{for(const t in r)r[t]=""},m=t=>{u[t]=!0},L=c(()=>Object.values(r).every(t=>!t)),d=c(()=>Object.values(r).some(t=>!!t)),p=c(()=>Object.values(r).filter(t=>!!t).length);return{errors:r,touched:u,validateField:i,validateAll:g,clearError:h,clearAllErrors:f,setTouched:m,isValid:L,hasErrors:d,errorCount:p}}const V={username:{required:!0,minLength:3,maxLength:20,custom:s=>s&&!/^[a-zA-Z0-9_@.-]+$/.test(s)?"用户名只能包含字母、数字、下划线、@、点和减号":null},password:{required:!0,minLength:6,maxLength:50,custom:s=>{if(s&&s.length>=6){const o=/[a-zA-Z]/.test(s),r=/\d/.test(s);if(!o||!r)return"密码应包含字母和数字"}return null}}};export{V as c,x as u};
