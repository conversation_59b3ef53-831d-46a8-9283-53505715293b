import{u as m,aK as c,v as e,bR as N,bS as $e,aS as h,q as B,M as le,aQ as we,bC as se,t as V,I as C,bi as l,b9 as x,bM as Ce,bJ as F,H as K,by as W,at as q,bx as fe,as as be,F as X,aV as Y,s as ke,aX as Re,G as Pe,aC as Se}from"./index-DMDdU1SS.js";import{d as De,u as Ve,r as Te,a as je,b as Me,_ as he}from"./ConfirmDialog.vue_vue_type_script_setup_true_lang-CWUz7rH-.js";import{u as Ae}from"./useFormValidation-BtbC7VZE.js";import{r as re}from"./ShieldCheckIcon-BQ5F360L.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as _e}from"./UsersIcon-DhyLUKGz.js";import"./createLucideIcon-B64IBOqu.js";import"./circle-check-big-BRAv-ZO3.js";function Ee(p,t){return c(),m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})])}function Ie(p,t){return c(),m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Be(p,t){return c(),m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function te(p,t){return c(),m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"})])}function ve(p,t){return c(),m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 18 18 6M6 6l12 12"})])}const Ne=p=>N.Get("/roles/query",{params:p}),Le=p=>N.Get(`/roles/get/${p}`),Oe=p=>N.Post("/roles/create",p),Ue=(p,t)=>N.Put(`/roles/update/${p}`,t),Fe=p=>N.Delete(`/roles/delete/${p}`),qe=p=>N.Post("/roles/batch-operation",p),ze=p=>N.Get(`/roles/${p}/permissions`),Qe=(p,t)=>N.Post(`/roles/${p}/assign-permissions`,t),Ge=(p,t)=>N.Delete(`/roles/${p}/remove-permission/${t}`),He=()=>N.Get("/permissions/tree"),Ze=()=>N.Get("/permissions/flat"),Je=()=>N.Get("/roles/statistics"),Ke=p=>N.Get(`/roles/${p}/usage`),We=(p,t)=>N.Post("/roles/validate-name",{role_name:p,exclude_role_id:t}),Xe=(p,t)=>N.Post("/roles/validate-code",{role_code:p,exclude_role_id:t}),ae=$e("role",()=>{const p=h([]),t=h(null),o=h([]),R=h([]),T=h([]),S=h(null),n=h(null),w=h(1),b=h(20),$=h(0),j=h(!1),A=h(!1),E=h(!1),M=h(!1),i=h({offset:0,limit:20}),a=B(()=>Math.ceil($.value/b.value)),D=B(()=>o.value.length>0),P=B(()=>o.value.length>0),_=B(()=>o.value.length>0),I=B(()=>o.value.length>0),oe=B(()=>({canCreate:!0,canEdit:!0,canDelete:!0,canAssignPermission:!0,canViewList:!0,canViewDetail:!0,canBatchOperation:!0})),Z=B(()=>{const r=(u,v=0)=>{const L=[];for(const H of u)L.push({...H,level:v}),H.children&&H.children.length>0&&L.push(...r(H.children,v+1));return L};return r(R.value)}),ee=async r=>{A.value=!0;try{const u={...i.value,...r,offset:(w.value-1)*b.value,limit:b.value},v=await Ne(u);v.code===200&&(p.value=v.data.items,$.value=v.data.total,j.value=v.data.has_more,Object.assign(i.value,u))}catch(u){throw console.error("获取角色列表失败:",u),u}finally{A.value=!1}},z=async r=>{E.value=!0;try{const u=await Le(r);return u.code===200&&(t.value=u.data),u.data}catch(u){throw console.error("获取角色详情失败:",u),u}finally{E.value=!1}},f=async r=>{M.value=!0;try{const u=await Oe(r);return u.code===200&&await ee(),u}catch(u){throw console.error("创建角色失败:",u),u}finally{M.value=!1}},y=async(r,u)=>{M.value=!0;try{const v=await Ue(r,u);if(v.code===200){const L=p.value.findIndex(H=>H.role_id===r);L!==-1&&Object.assign(p.value[L],v.data),t.value?.role_id===r&&Object.assign(t.value,v.data)}return v}catch(v){throw console.error("更新角色失败:",v),v}finally{M.value=!1}},k=async r=>{try{const u=await Fe(r);if(u.code===200){const v=p.value.findIndex(H=>H.role_id===r);v!==-1&&(p.value.splice(v,1),$.value--);const L=o.value.indexOf(r);L!==-1&&o.value.splice(L,1)}return u}catch(u){throw console.error("删除角色失败:",u),u}},O=async r=>{if(o.value.length!==0)try{const u=await qe({user_ids:o.value,action:r});if(u.code===200){if(r==="delete")p.value=p.value.filter(v=>!o.value.includes(v.role_id)),$.value-=o.value.length;else{const v=r==="activate"?"ACTIVE":"INACTIVE";p.value.forEach(L=>{o.value.includes(L.role_id)&&(L.status=v)})}o.value=[]}return u}catch(u){throw console.error("批量操作失败:",u),u}},Q=async r=>{try{return(await ze(r)).data}catch(u){throw console.error("获取角色权限失败:",u),u}},ie=async(r,u)=>{try{const v=await Qe(r,{permission_ids:u});return v.code===200&&t.value?.role_id===r&&await z(r),v}catch(v){throw console.error("分配权限失败:",v),v}},de=async(r,u)=>{try{const v=await Ge(r,u);return v.code===200&&t.value?.role_id===r&&(t.value.permissions=t.value.permissions.filter(L=>L.permission_id!==u)),v}catch(v){throw console.error("移除权限失败:",v),v}},ce=async()=>{try{const r=await He();return r.code===200&&(R.value=r.data),r.data}catch(r){throw console.error("获取可用权限失败:",r),r}},ue=async()=>{try{const r=await Ze();return r.code===200&&(T.value=r.data),r.data}catch(r){throw console.error("获取权限列表失败:",r),r}},me=async()=>{try{const r=await Je();return r.code===200&&(S.value=r.data),r.data}catch(r){throw console.error("获取统计信息失败:",r),r}},pe=async r=>{try{const u=await Ke(r);return u.code===200&&(n.value=u.data),u.data}catch(u){throw console.error("获取角色使用情况失败:",u),u}},xe=async(r,u)=>{try{return(await We(r,u)).data.available}catch(v){return console.error("验证角色名称失败:",v),!1}},ge=async(r,u)=>{try{return(await Xe(r,u)).data.available}catch(v){return console.error("验证角色编码失败:",v),!1}},g=r=>{const u=o.value.indexOf(r);u===-1?o.value.push(r):o.value.splice(u,1)},s=()=>{o.value=p.value.map(r=>r.role_id)},d=()=>{o.value=[]},U=r=>{w.value=r},G=r=>{b.value=r,w.value=1},J=r=>{Object.assign(i.value,r),w.value=1},ye=()=>{i.value={offset:0,limit:b.value},w.value=1};return{roles:p,roleDetail:t,selectedRoleIds:o,availablePermissions:R,permissionsFlat:T,statistics:S,roleUsage:n,currentPage:w,pageSize:b,total:$,hasMore:j,loading:A,detailLoading:E,submitting:M,queryParams:i,totalPages:a,hasSelectedRoles:D,canBatchDelete:P,canBatchActivate:_,canBatchDeactivate:I,permissions:oe,flatPermissions:Z,fetchRoles:ee,fetchRoleDetail:z,addRole:f,editRole:y,removeRole:k,batchOperation:O,fetchRolePermissions:Q,assignPermissions:ie,removePermission:de,fetchAvailablePermissions:ce,fetchPermissionsFlat:ue,fetchStatistics:me,fetchRoleUsage:pe,checkRoleNameAvailable:xe,checkRoleCodeAvailable:ge,toggleRoleSelection:g,selectAllRoles:s,clearSelection:d,setPage:U,setPageSize:G,setQueryParams:J,resetQueryParams:ye,resetState:()=>{p.value=[],t.value=null,o.value=[],w.value=1,$.value=0,j.value=!1,A.value=!1,E.value=!1,M.value=!1,ye()}}}),Ye={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},et={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},tt={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},st={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},ot={class:"sm:flex sm:items-start"},lt={class:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10"},nt={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full"},rt={class:"text-lg leading-6 font-medium text-gray-900",id:"modal-title"},at={class:"mt-4"},it={key:0,class:"mt-1 text-sm text-red-600"},dt={key:0,class:"mt-1 text-sm text-red-600"},ct={class:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},ut=["disabled"],mt={key:0,class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},pt=le({__name:"RoleFormDialog",props:{visible:{type:Boolean},role:{default:null}},emits:["update:visible","success"],setup(p,{emit:t}){const o=p,R=t,T=ae(),S=h(!1),n=we({role_name:"",role_code:"",description:"",status:"ACTIVE"}),w={role_name:{required:!0,minLength:2,maxLength:50},role_code:{required:!0,minLength:2,maxLength:20,pattern:/^[A-Z_]+$/}},{errors:b,validateField:$}=Ae(n,w),j=B(()=>!!o.role),A=B(()=>n.role_name&&n.role_code&&!b.role_name&&!b.role_code),E=async()=>{const P=$("role_name");if(P&&n.role_name)try{if(!await T.checkRoleNameAvailable(n.role_name,o.role?.role_id))return b.role_name="角色名称已存在",!1}catch(_){console.error("验证角色名称失败:",_)}return P},M=async()=>{const P=$("role_code");if(P&&n.role_code)try{if(!await T.checkRoleCodeAvailable(n.role_code,o.role?.role_id))return b.role_code="角色编码已存在",!1}catch(_){console.error("验证角色编码失败:",_)}return P},i=async()=>{const P=await E(),_=await M();if(!(!P||!_)){S.value=!0;try{if(j.value&&o.role){const I={role_name:n.role_name,role_code:n.role_code,description:n.description||void 0,status:n.status};await T.editRole(o.role.role_id,I)}else{const I={role_name:n.role_name,role_code:n.role_code,description:n.description||void 0,status:n.status};await T.addRole(I)}R("success")}catch(I){console.error("保存角色失败:",I)}finally{S.value=!1}}},a=()=>{R("update:visible",!1)},D=()=>{o.role?(n.role_name=o.role.role_name,n.role_code=o.role.role_code,n.description=o.role.description||"",n.status=o.role.status):(n.role_name="",n.role_code="",n.description="",n.status="ACTIVE"),b.role_name="",b.role_code=""};return se(()=>o.visible,P=>{P&&be(()=>{D()})}),se(()=>o.role,()=>{o.visible&&D()}),(P,_)=>P.visible?(c(),m("div",Ye,[e("div",et,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:a}),e("div",tt,[e("div",st,[e("div",ot,[e("div",lt,[C(l(re),{class:"h-6 w-6 text-blue-600"})]),e("div",nt,[e("h3",rt,x(j.value?"编辑角色":"新增角色"),1),e("div",at,[e("form",{onSubmit:Ce(i,["prevent"]),class:"space-y-4"},[e("div",null,[_[4]||(_[4]=e("label",{for:"role-name",class:"block text-sm font-medium text-gray-700"},[K(" 角色名称 "),e("span",{class:"text-red-500"},"*")],-1)),F(e("input",{id:"role-name","onUpdate:modelValue":_[0]||(_[0]=I=>n.role_name=I),type:"text",required:"",class:q(["mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",l(b).role_name?"border-red-300":"border-gray-300"]),placeholder:"请输入角色名称",onBlur:E},null,34),[[W,n.role_name]]),l(b).role_name?(c(),m("p",it,x(l(b).role_name),1)):V("",!0)]),e("div",null,[_[5]||(_[5]=e("label",{for:"role-code",class:"block text-sm font-medium text-gray-700"},[K(" 角色编码 "),e("span",{class:"text-red-500"},"*")],-1)),F(e("input",{id:"role-code","onUpdate:modelValue":_[1]||(_[1]=I=>n.role_code=I),type:"text",required:"",class:q(["mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",l(b).role_code?"border-red-300":"border-gray-300"]),placeholder:"请输入角色编码，如：ADMIN, USER",onBlur:M},null,34),[[W,n.role_code]]),l(b).role_code?(c(),m("p",dt,x(l(b).role_code),1)):V("",!0),_[6]||(_[6]=e("p",{class:"mt-1 text-xs text-gray-500"},"角色编码用于系统识别，建议使用大写字母和下划线",-1))]),e("div",null,[_[7]||(_[7]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700"}," 描述 ",-1)),F(e("textarea",{id:"description","onUpdate:modelValue":_[2]||(_[2]=I=>n.description=I),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"请输入角色描述"},null,512),[[W,n.description]])]),e("div",null,[_[9]||(_[9]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700"}," 状态 ",-1)),F(e("select",{id:"status","onUpdate:modelValue":_[3]||(_[3]=I=>n.status=I),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},_[8]||(_[8]=[e("option",{value:"ACTIVE"},"启用",-1),e("option",{value:"INACTIVE"},"禁用",-1)]),512),[[fe,n.status]])])],32)])])])]),e("div",ct,[e("button",{type:"button",disabled:S.value||!A.value,onClick:i,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"},[S.value?(c(),m("div",mt)):V("",!0),K(" "+x(j.value?"更新":"创建"),1)],8,ut),e("button",{type:"button",onClick:a,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," 取消 ")])])])])):V("",!0)}}),xt=ne(pt,[["__scopeId","data-v-184b83a6"]]),gt={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},vt={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},ft={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"},bt={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},yt={class:"flex items-center justify-between mb-6"},ht={class:"flex items-center space-x-3"},_t={class:"flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-blue-100"},wt={key:0,class:"flex items-center justify-center py-12"},kt={key:1,class:"space-y-6"},$t={class:"bg-gray-50 rounded-lg p-4"},Ct={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Rt={class:"mt-1 text-sm text-gray-900"},Pt={class:"mt-1 text-sm text-gray-900"},St={class:"mt-1 text-sm text-gray-900"},Dt={class:"md:col-span-2"},Vt={class:"mt-1 text-sm text-gray-900"},Tt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},jt={class:"bg-blue-50 rounded-lg p-4"},Mt={class:"flex items-center"},At={class:"flex-shrink-0"},Et={class:"ml-4"},It={class:"text-2xl font-bold text-blue-600"},Bt={class:"bg-green-50 rounded-lg p-4"},Nt={class:"flex items-center"},Lt={class:"flex-shrink-0"},Ot={class:"ml-4"},Ut={class:"text-2xl font-bold text-green-600"},Ft={class:"bg-purple-50 rounded-lg p-4"},qt={class:"flex items-center"},zt={class:"flex-shrink-0"},Qt={class:"ml-4"},Gt={class:"text-sm font-bold text-purple-600"},Ht={class:"flex items-center justify-between mb-4"},Zt={key:0,class:"bg-white border rounded-lg overflow-hidden"},Jt={class:"overflow-x-auto"},Kt={class:"min-w-full divide-y divide-gray-200"},Wt={class:"bg-white divide-y divide-gray-200"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},es={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ts={class:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate"},ss={key:1,class:"text-center py-8 bg-gray-50 rounded-lg"},os={class:"mt-6"},ls={key:0,class:"bg-white border rounded-lg overflow-hidden"},ns={class:"overflow-x-auto"},rs={class:"min-w-full divide-y divide-gray-200"},as={class:"bg-white divide-y divide-gray-200"},is={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},ds={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},cs={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},us={class:"px-6 py-4 whitespace-nowrap"},ms={key:1,class:"text-center py-8 bg-gray-50 rounded-lg"},ps={key:2,class:"text-center py-12"},xs={class:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},gs=le({__name:"RoleDetailDialog",props:{visible:{type:Boolean},roleId:{}},emits:["update:visible","edit-role","manage-permissions"],setup(p,{emit:t}){const o=p,R=t,T=ae(),S=h(!1),n=h(null),w=i=>({menu:"菜单",action:"操作",data:"数据",api:"接口",page:"页面"})[i]||i,b=i=>({menu:"bg-blue-100 text-blue-800",action:"bg-green-100 text-green-800",data:"bg-yellow-100 text-yellow-800",api:"bg-purple-100 text-purple-800",page:"bg-indigo-100 text-indigo-800"})[i]||"bg-gray-100 text-gray-800",$=async()=>{if(o.roleId){S.value=!0;try{n.value=await T.fetchRoleDetail(o.roleId)}catch(i){console.error("加载角色详情失败:",i),n.value=null}finally{S.value=!1}}},j=()=>{R("update:visible",!1)},A=()=>{n.value&&R("edit-role",n.value)},E=()=>{o.roleId&&R("manage-permissions",o.roleId)},M=i=>new Date(i).toLocaleString("zh-CN");return se(()=>o.visible,i=>{i?be(()=>{$()}):n.value=null}),se(()=>o.roleId,()=>{o.visible&&o.roleId&&$()}),(i,a)=>i.visible?(c(),m("div",gt,[e("div",vt,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:j}),e("div",ft,[e("div",bt,[e("div",yt,[e("div",ht,[e("div",_t,[C(l(re),{class:"h-6 w-6 text-blue-600"})]),a[0]||(a[0]=e("div",null,[e("h3",{class:"text-lg leading-6 font-medium text-gray-900",id:"modal-title"}," 角色详情 "),e("p",{class:"text-sm text-gray-500"},"查看角色信息和权限分配")],-1))]),e("button",{onClick:j,class:"text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"},[C(l(ve),{class:"h-6 w-6"})])]),S.value?(c(),m("div",wt,a[1]||(a[1]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"ml-2 text-gray-600"},"加载中...",-1)]))):n.value?(c(),m("div",kt,[e("div",$t,[a[7]||(a[7]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"基本信息",-1)),e("div",Ct,[e("div",null,[a[2]||(a[2]=e("label",{class:"block text-sm font-medium text-gray-700"},"角色名称",-1)),e("p",Rt,x(n.value.role_name),1)]),e("div",null,[a[3]||(a[3]=e("label",{class:"block text-sm font-medium text-gray-700"},"角色编码",-1)),e("p",Pt,x(n.value.role_code),1)]),e("div",null,[a[4]||(a[4]=e("label",{class:"block text-sm font-medium text-gray-700"},"状态",-1)),e("span",{class:q(n.value.status==="ACTIVE"?"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800")},x(n.value.status==="ACTIVE"?"启用":"禁用"),3)]),e("div",null,[a[5]||(a[5]=e("label",{class:"block text-sm font-medium text-gray-700"},"创建时间",-1)),e("p",St,x(M(n.value.created_date)),1)]),e("div",Dt,[a[6]||(a[6]=e("label",{class:"block text-sm font-medium text-gray-700"},"描述",-1)),e("p",Vt,x(n.value.description||"暂无描述"),1)])])]),e("div",Tt,[e("div",jt,[e("div",Mt,[e("div",At,[C(l(_e),{class:"h-8 w-8 text-blue-600"})]),e("div",Et,[a[8]||(a[8]=e("p",{class:"text-sm font-medium text-blue-900"},"分配用户",-1)),e("p",It,x(n.value.users?.length||0),1)])])]),e("div",Bt,[e("div",Nt,[e("div",Lt,[C(l(te),{class:"h-8 w-8 text-green-600"})]),e("div",Ot,[a[9]||(a[9]=e("p",{class:"text-sm font-medium text-green-900"},"权限数量",-1)),e("p",Ut,x(n.value.permissions?.length||0),1)])])]),e("div",Ft,[e("div",qt,[e("div",zt,[C(l(Ie),{class:"h-8 w-8 text-purple-600"})]),e("div",Qt,[a[10]||(a[10]=e("p",{class:"text-sm font-medium text-purple-900"},"最后更新",-1)),e("p",Gt,x(M(n.value.updated_date)),1)])])])]),e("div",null,[e("div",Ht,[a[12]||(a[12]=e("h4",{class:"text-lg font-medium text-gray-900"},"权限列表",-1)),e("button",{onClick:E,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},[C(l(te),{class:"h-4 w-4 mr-1"}),a[11]||(a[11]=K(" 管理权限 "))])]),n.value.permissions&&n.value.permissions.length>0?(c(),m("div",Zt,[e("div",Jt,[e("table",Kt,[a[13]||(a[13]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"权限名称"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"权限编码"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"资源类型"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"描述")])],-1)),e("tbody",Wt,[(c(!0),m(X,null,Y(n.value.permissions,D=>(c(),m("tr",{key:D.permission_id,class:"hover:bg-gray-50"},[e("td",Xt,x(D.permission_name),1),e("td",Yt,x(D.permission_code),1),e("td",es,[e("span",{class:q(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",b(D.resource_type)])},x(w(D.resource_type)),3)]),e("td",ts,x(D.description||"-"),1)]))),128))])])])])):(c(),m("div",ss,[C(l(te),{class:"mx-auto h-12 w-12 text-gray-400"}),a[15]||(a[15]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无权限",-1)),a[16]||(a[16]=e("p",{class:"mt-1 text-sm text-gray-500"},"该角色尚未分配任何权限",-1)),e("div",os,[e("button",{onClick:E,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"},[C(l(te),{class:"h-4 w-4 mr-2"}),a[14]||(a[14]=K(" 分配权限 "))])])]))]),e("div",null,[a[20]||(a[20]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"使用该角色的用户",-1)),n.value.users&&n.value.users.length>0?(c(),m("div",ls,[e("div",ns,[e("table",rs,[a[17]||(a[17]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"用户名"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"姓名"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"部门"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"状态")])],-1)),e("tbody",as,[(c(!0),m(X,null,Y(n.value.users,D=>(c(),m("tr",{key:D.user_id,class:"hover:bg-gray-50"},[e("td",is,x(D.username),1),e("td",ds,x(D.name),1),e("td",cs,x(D.department_name||"-"),1),e("td",us,[e("span",{class:q(D.status==="ACTIVE"?"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800")},x(D.status==="ACTIVE"?"活跃":"禁用"),3)])]))),128))])])])])):(c(),m("div",ms,[C(l(_e),{class:"mx-auto h-12 w-12 text-gray-400"}),a[18]||(a[18]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无用户",-1)),a[19]||(a[19]=e("p",{class:"mt-1 text-sm text-gray-500"},"该角色尚未分配给任何用户",-1))]))])])):(c(),m("div",ps,[C(l(Be),{class:"mx-auto h-12 w-12 text-red-400"}),a[21]||(a[21]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"加载失败",-1)),a[22]||(a[22]=e("p",{class:"mt-1 text-sm text-gray-500"},"无法加载角色详情信息",-1))]))]),e("div",xs,[n.value?(c(),m("button",{key:0,onClick:A,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"}," 编辑角色 ")):V("",!0),e("button",{type:"button",onClick:j,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," 关闭 ")])])])])):V("",!0)}}),vs=ne(gs,[["__scopeId","data-v-d4c4a1bb"]]),fs={class:"flex items-center space-x-3 flex-1"},bs={key:1,class:"w-4"},ys=["checked"],hs={class:"flex-1 min-w-0"},_s={class:"flex items-center space-x-2"},ws={class:"text-sm font-medium text-gray-900 truncate"},ks={class:"flex items-center space-x-4 mt-1"},$s={class:"text-xs text-gray-500"},Cs={key:0,class:"text-xs text-gray-500 truncate"},Rs={key:0},Ps=le({__name:"PermissionTreeNode",props:{permission:{},selectedPermissions:{},expandedNodes:{},searchQuery:{},level:{default:0}},emits:["toggle-selection","toggle-expand"],setup(p,{emit:t}){const o=p,R=t,T=B(()=>o.selectedPermissions.includes(o.permission.permission_id)),S=B(()=>o.expandedNodes.has(o.permission.permission_id)),n=B(()=>o.permission.children&&o.permission.children.length>0),w=B(()=>{if(!o.searchQuery)return!1;const i=o.searchQuery.toLowerCase();return o.permission.permission_name.toLowerCase().includes(i)||o.permission.permission_code.toLowerCase().includes(i)||o.permission.description&&o.permission.description.toLowerCase().includes(i)}),b=i=>({menu:"bg-blue-100 text-blue-800",action:"bg-green-100 text-green-800",data:"bg-yellow-100 text-yellow-800",api:"bg-purple-100 text-purple-800",page:"bg-indigo-100 text-indigo-800"})[i]||"bg-gray-100 text-gray-800",$=i=>({menu:"菜单",action:"操作",data:"数据",api:"接口",page:"页面"})[i]||i,j=i=>{const a=i.target.checked;R("toggle-selection",o.permission.permission_id,a)},A=()=>{n.value&&R("toggle-expand",o.permission.permission_id)},E=(i,a)=>{R("toggle-selection",i,a)},M=i=>{R("toggle-expand",i)};return(i,a)=>{const D=Re("PermissionTreeNode",!0);return c(),m("div",null,[e("div",{class:q(["flex items-center p-3 hover:bg-gray-50",w.value?"bg-yellow-50":"",i.level>0?"ml-6":""])},[e("div",fs,[n.value?(c(),m("button",{key:0,onClick:A,class:"text-gray-400 hover:text-gray-600"},[C(l(Ee),{class:q(["h-4 w-4 transition-transform",S.value?"rotate-90":""])},null,8,["class"])])):(c(),m("div",bs)),e("input",{type:"checkbox",checked:T.value,onChange:j,class:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"},null,40,ys),e("div",hs,[e("div",_s,[e("p",ws,x(i.permission.permission_name),1),e("span",{class:q(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",b(i.permission.resource_type)])},x($(i.permission.resource_type)),3)]),e("div",ks,[e("p",$s,x(i.permission.permission_code),1),i.permission.description?(c(),m("p",Cs,x(i.permission.description),1)):V("",!0)])])])],2),n.value&&S.value?(c(),m("div",Rs,[(c(!0),m(X,null,Y(i.permission.children,P=>(c(),ke(D,{key:P.permission_id,permission:P,"selected-permissions":i.selectedPermissions,"expanded-nodes":i.expandedNodes,"search-query":i.searchQuery,level:i.level+1,onToggleSelection:E,onToggleExpand:M},null,8,["permission","selected-permissions","expanded-nodes","search-query","level"]))),128))])):V("",!0)])}}}),Ss=ne(Ps,[["__scopeId","data-v-fd9747fe"]]),Ds={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Vs={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Ts={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"},js={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Ms={class:"flex items-center justify-between mb-6"},As={class:"flex items-center space-x-3"},Es={class:"flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-green-100"},Is={class:"text-sm text-gray-500"},Bs={key:0,class:"flex items-center justify-center py-12"},Ns={key:1,class:"space-y-6"},Ls={class:"flex items-center justify-between p-4 bg-gray-50 rounded-lg"},Os={class:"flex items-center space-x-4"},Us={class:"text-sm text-gray-600"},Fs={class:"flex items-center space-x-4"},qs={class:"flex-1"},zs={class:"border rounded-lg overflow-hidden"},Qs={class:"max-h-96 overflow-y-auto"},Gs={key:0,class:"text-center py-8 text-gray-500"},Hs={key:1,class:"divide-y divide-gray-200"},Zs={key:0,class:"bg-blue-50 rounded-lg p-4"},Js={class:"text-sm font-medium text-blue-900 mb-3"},Ks={class:"flex flex-wrap gap-2"},Ws=["onClick"],Xs={class:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Ys=["disabled"],eo={key:0,class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},to=le({__name:"RolePermissionDialog",props:{visible:{type:Boolean},role:{default:null},permissions:{}},emits:["update:visible","success"],setup(p,{emit:t}){const o=p,R=t,T=ae(),S=h(!1),n=h(!1),w=h([]),b=h(new Set),$=h(""),j=h(""),A=h([]),E=B(()=>{let f=o.permissions;if(j.value&&(f=f.filter(y=>y.resource_type===j.value)),$.value){const y=$.value.toLowerCase();f=f.filter(k=>k.permission_name.toLowerCase().includes(y)||k.permission_code.toLowerCase().includes(y)||k.description&&k.description.toLowerCase().includes(y))}return f}),M=B(()=>{const f=new Map,y=k=>{k.forEach(O=>{f.set(O.permission_id,O),O.children&&y(O.children)})};return y(o.permissions),f}),i=f=>M.value.get(f)?.permission_name||f,a=async()=>{if(o.role){S.value=!0;try{const f=await T.fetchRolePermissions(o.role.role_id);A.value=f.map(y=>y.permission_id),w.value=[...A.value]}catch(f){console.error("加载角色权限失败:",f)}finally{S.value=!1}}},D=(f,y)=>{if(y)w.value.includes(f)||w.value.push(f);else{const k=w.value.indexOf(f);k!==-1&&w.value.splice(k,1)}},P=f=>{b.value.has(f)?b.value.delete(f):b.value.add(f)},_=f=>{const y=w.value.indexOf(f);y!==-1&&w.value.splice(y,1)},I=()=>{w.value=[]},oe=()=>{const f=k=>{const O=[];return k.forEach(Q=>{O.push(Q.permission_id),Q.children&&Q.children.length>0&&O.push(...f(Q.children))}),O},y=f(o.permissions);b.value=new Set(y)},Z=()=>{b.value.clear()},ee=async()=>{if(o.role){n.value=!0;try{await T.assignPermissions(o.role.role_id,w.value),R("success")}catch(f){console.error("保存权限失败:",f)}finally{n.value=!1}}},z=()=>{R("update:visible",!1)};return se(()=>o.visible,f=>{f?be(()=>{a();const y=o.permissions.map(k=>k.permission_id);b.value=new Set(y)}):(w.value=[],A.value=[],b.value.clear(),$.value="",j.value="")}),(f,y)=>f.visible?(c(),m("div",Ds,[e("div",Vs,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:z}),e("div",Ts,[e("div",js,[e("div",Ms,[e("div",As,[e("div",Es,[C(l(te),{class:"h-6 w-6 text-green-600"})]),e("div",null,[y[2]||(y[2]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900",id:"modal-title"}," 权限管理 ",-1)),e("p",Is,' 为角色 "'+x(f.role?.role_name)+'" 分配权限 ',1)])]),e("button",{onClick:z,class:"text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"},[C(l(ve),{class:"h-6 w-6"})])]),S.value?(c(),m("div",Bs,y[3]||(y[3]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"},null,-1),e("span",{class:"ml-2 text-gray-600"},"加载权限数据...",-1)]))):(c(),m("div",Ns,[e("div",Ls,[e("div",Os,[e("div",Us," 已选择 "+x(w.value.length)+" 个权限 ",1),e("button",{onClick:I,class:"text-sm text-blue-600 hover:text-blue-700"}," 清空选择 ")]),e("div",{class:"flex items-center space-x-2"},[e("button",{onClick:oe,class:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"}," 全部展开 "),e("button",{onClick:Z,class:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"}," 全部收起 ")])]),e("div",Fs,[e("div",qs,[F(e("input",{"onUpdate:modelValue":y[0]||(y[0]=k=>$.value=k),type:"text",placeholder:"搜索权限名称或编码...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"},null,512),[[W,$.value]])]),F(e("select",{"onUpdate:modelValue":y[1]||(y[1]=k=>j.value=k),class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"},y[4]||(y[4]=[Pe('<option value="" data-v-fd4ccc07>全部类型</option><option value="menu" data-v-fd4ccc07>菜单</option><option value="action" data-v-fd4ccc07>操作</option><option value="data" data-v-fd4ccc07>数据</option><option value="api" data-v-fd4ccc07>接口</option><option value="page" data-v-fd4ccc07>页面</option>',6)]),512),[[fe,j.value]])]),e("div",zs,[e("div",Qs,[E.value.length===0?(c(),m("div",Gs," 暂无匹配的权限 ")):(c(),m("div",Hs,[(c(!0),m(X,null,Y(E.value,k=>(c(),ke(Ss,{key:k.permission_id,permission:k,"selected-permissions":w.value,"expanded-nodes":b.value,"search-query":$.value,onToggleSelection:D,onToggleExpand:P},null,8,["permission","selected-permissions","expanded-nodes","search-query"]))),128))]))])]),w.value.length>0?(c(),m("div",Zs,[e("h4",Js,"已选择的权限 ("+x(w.value.length)+")",1),e("div",Ks,[(c(!0),m(X,null,Y(w.value,k=>(c(),m("span",{key:k,class:"inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"},[K(x(i(k))+" ",1),e("button",{onClick:O=>_(k),class:"ml-1 text-blue-600 hover:text-blue-800"},[C(l(ve),{class:"h-3 w-3"})],8,Ws)]))),128))])])):V("",!0)]))]),e("div",Xs,[e("button",{type:"button",disabled:n.value,onClick:ee,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"},[n.value?(c(),m("div",eo)):V("",!0),y[5]||(y[5]=K(" 保存权限 "))],8,Ys),e("button",{type:"button",onClick:z,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," 取消 ")])])])])):V("",!0)}}),so=ne(to,[["__scopeId","data-v-fd4ccc07"]]),oo={class:"role-management p-6 space-y-6"},lo={class:"flex items-center justify-between"},no={class:"flex items-center space-x-3"},ro={key:0,class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ao={class:"bg-white rounded-lg border p-4"},io={class:"flex items-center space-x-3"},co={class:"p-2 bg-blue-100 rounded-lg"},uo={class:"text-2xl font-bold"},mo={class:"bg-white rounded-lg border p-4"},po={class:"flex items-center space-x-3"},xo={class:"p-2 bg-green-100 rounded-lg"},go={class:"text-2xl font-bold"},vo={class:"bg-white rounded-lg border p-4"},fo={class:"flex items-center space-x-3"},bo={class:"p-2 bg-red-100 rounded-lg"},yo={class:"text-2xl font-bold"},ho={class:"bg-white rounded-lg border p-4"},_o={class:"flex items-center space-x-3"},wo={class:"p-2 bg-purple-100 rounded-lg"},ko={class:"text-2xl font-bold"},$o={class:"bg-white rounded-lg border p-4"},Co={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Ro={class:"flex items-center justify-between mt-4"},Po={class:"text-sm text-gray-600"},So={key:1,class:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg"},Do={class:"flex items-center space-x-4"},Vo={class:"text-sm text-blue-700"},To={class:"flex items-center space-x-2"},jo=["disabled"],Mo=["disabled"],Ao=["disabled"],Eo={class:"bg-white rounded-lg border"},Io={class:"relative"},Bo={key:0,class:"absolute top-0 left-0 right-0 bottom-0 bg-white bg-opacity-50 flex items-center justify-center z-10"},No={class:"overflow-x-auto"},Lo={class:"min-w-full divide-y divide-gray-200"},Oo={class:"bg-gray-50"},Uo={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},Fo=["checked"],qo={class:"bg-white divide-y divide-gray-200"},zo={class:"px-6 py-4 whitespace-nowrap"},Qo=["checked","onChange"],Go={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Ho={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Zo={class:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate"},Jo={class:"px-6 py-4 whitespace-nowrap"},Ko={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Wo={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Xo={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Yo={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},el={class:"flex items-center justify-end space-x-2"},tl=["onClick"],sl=["onClick"],ol=["onClick"],ll=["onClick"],nl={key:0,class:"text-center py-12"},rl={class:"mt-6"},al={key:2,class:"flex items-center justify-between"},il={class:"text-sm text-gray-600"},dl={class:"flex items-center space-x-2"},cl=["value"],ul={class:"flex items-center space-x-1"},ml=["disabled"],pl={class:"flex items-center space-x-1"},xl=["onClick"],gl=["disabled"],vl=le({__name:"RoleManagement",setup(p){const t=ae(),{toast:o}=Ve(),R=h(null),T=h(!1),S=h(!1),n=h(!1),w=h(!1),b=h(!1),$=h(null),j=h(""),A=h(null),E=h(null),M=h("create"),i=we({role_name:"",role_code:"",status:"",description:""}),a=B(()=>t.roles.length>0&&t.roles.every(g=>t.selectedRoleIds.includes(g.role_id))),D=B(()=>{const g=t.currentPage,s=t.totalPages,d=2,U=[],G=[];for(let J=Math.max(2,g-d);J<=Math.min(s-1,g+d);J++)U.push(J);return g-d>2?G.push(1,"..."):G.push(1),G.push(...U),g+d<s-1?G.push("...",s):s>1&&G.push(s),G.filter(J=>J!==1||s===1)}),P=De(()=>{_()},500),_=async()=>{const g=Object.fromEntries(Object.entries(i).filter(([s,d])=>d!==""));t.setQueryParams(g),await t.fetchRoles()},I=async()=>{Object.assign(i,{role_name:"",role_code:"",status:"",description:""}),t.resetQueryParams(),await t.fetchRoles()},oe=g=>{g.target.checked?t.selectAllRoles():t.clearSelection()},Z=async g=>{t.setPage(g),await t.fetchRoles()},ee=async g=>{const s=g.target.value;t.setPageSize(parseInt(s)),await t.fetchRoles()},z=g=>{j.value=g.role_id,S.value=!0},f=g=>{$.value=g,M.value="edit",T.value=!0},y=g=>{A.value=g,w.value=!0},k=g=>{E.value=g,n.value=!0},O=async()=>{try{await t.batchOperation("activate"),o({title:"操作成功",description:"角色已批量启用"})}catch{o({title:"操作失败",description:"批量启用角色失败",variant:"destructive"})}},Q=async()=>{try{await t.batchOperation("deactivate"),o({title:"操作成功",description:"角色已批量禁用"})}catch{o({title:"操作失败",description:"批量禁用角色失败",variant:"destructive"})}},ie=()=>{b.value=!0},de=async()=>{if(A.value)try{await t.removeRole(A.value.role_id),w.value=!1,A.value=null,o({title:"删除成功",description:"角色已删除",variant:"success"})}catch{o({title:"删除失败",description:"删除角色失败",variant:"destructive"})}},ce=async()=>{try{await t.batchOperation("delete"),b.value=!1,o({title:"删除成功",description:"角色已批量删除",variant:"success"})}catch{o({title:"删除失败",description:"批量删除角色失败",variant:"destructive"})}},ue=async()=>{const g=M.value==="edit";T.value=!1,$.value=null,M.value="create",o({title:"操作成功",description:g?"角色更新成功":"角色创建成功",variant:"success"}),await t.fetchRoles()},me=()=>{n.value=!1,E.value=null,o({title:"操作成功",description:"权限分配已更新",variant:"success"})},pe=g=>{S.value=!1,$.value=g,M.value="edit",T.value=!0},xe=g=>{const s=t.roles.find(d=>d.role_id===g);s&&(S.value=!1,E.value=s,n.value=!0)},ge=g=>new Date(g).toLocaleString("zh-CN");return se(T,g=>{g||($.value=null,M.value="create")}),Se(async()=>{try{await Promise.all([t.fetchRoles(),t.fetchAvailablePermissions(),t.fetchStatistics().then(g=>R.value=g)])}catch(g){console.error("初始化角色管理页面失败:",g),o({title:"加载失败",description:"初始化页面数据失败",variant:"destructive"})}}),(g,s)=>(c(),m("div",oo,[e("div",lo,[s[18]||(s[18]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"角色管理"),e("p",{class:"text-gray-600"},"管理系统角色和权限分配")],-1)),e("div",no,[l(t).permissions.canCreate?(c(),m("button",{key:0,onClick:s[0]||(s[0]=d=>T.value=!0),class:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},[C(l(Te),{class:"h-4 w-4"}),s[17]||(s[17]=e("span",null,"新增角色",-1))])):V("",!0)])]),R.value?(c(),m("div",ro,[e("div",ao,[e("div",io,[e("div",co,[C(l(re),{class:"h-6 w-6 text-blue-600"})]),e("div",null,[s[19]||(s[19]=e("p",{class:"text-sm text-gray-600"},"总角色数",-1)),e("p",uo,x(R.value.total_roles),1)])])]),e("div",mo,[e("div",po,[e("div",xo,[C(l(je),{class:"h-6 w-6 text-green-600"})]),e("div",null,[s[20]||(s[20]=e("p",{class:"text-sm text-gray-600"},"启用角色",-1)),e("p",go,x(R.value.active_roles),1)])])]),e("div",vo,[e("div",fo,[e("div",bo,[C(l(Me),{class:"h-6 w-6 text-red-600"})]),e("div",null,[s[21]||(s[21]=e("p",{class:"text-sm text-gray-600"},"禁用角色",-1)),e("p",yo,x(R.value.inactive_roles),1)])])]),e("div",ho,[e("div",_o,[e("div",wo,[C(l(te),{class:"h-6 w-6 text-purple-600"})]),e("div",null,[s[22]||(s[22]=e("p",{class:"text-sm text-gray-600"},"权限类型",-1)),e("p",ko,x(R.value.roles_by_type?.length||0),1)])])])])):V("",!0),e("div",$o,[e("div",Co,[e("div",null,[s[23]||(s[23]=e("label",{for:"search-role-name",class:"block text-sm font-medium text-gray-700"},"角色名称",-1)),F(e("input",{id:"search-role-name","onUpdate:modelValue":s[1]||(s[1]=d=>i.role_name=d),placeholder:"请输入角色名称",onInput:s[2]||(s[2]=(...d)=>l(P)&&l(P)(...d)),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,544),[[W,i.role_name]])]),e("div",null,[s[24]||(s[24]=e("label",{for:"search-role-code",class:"block text-sm font-medium text-gray-700"},"角色编码",-1)),F(e("input",{id:"search-role-code","onUpdate:modelValue":s[3]||(s[3]=d=>i.role_code=d),placeholder:"请输入角色编码",onInput:s[4]||(s[4]=(...d)=>l(P)&&l(P)(...d)),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,544),[[W,i.role_code]])]),e("div",null,[s[26]||(s[26]=e("label",{for:"search-status",class:"block text-sm font-medium text-gray-700"},"状态",-1)),F(e("select",{"onUpdate:modelValue":s[5]||(s[5]=d=>i.status=d),onChange:_,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},s[25]||(s[25]=[e("option",{value:""},"全部状态",-1),e("option",{value:"ACTIVE"},"启用",-1),e("option",{value:"INACTIVE"},"禁用",-1)]),544),[[fe,i.status]])]),e("div",null,[s[27]||(s[27]=e("label",{for:"search-description",class:"block text-sm font-medium text-gray-700"},"描述",-1)),F(e("input",{id:"search-description","onUpdate:modelValue":s[6]||(s[6]=d=>i.description=d),placeholder:"请输入描述关键词",onInput:s[7]||(s[7]=(...d)=>l(P)&&l(P)(...d)),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,544),[[W,i.description]])])]),e("div",Ro,[e("div",{class:"flex items-center space-x-2"},[e("button",{onClick:I,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"}," 重置 ")]),e("div",Po," 共 "+x(l(t).total)+" 条记录 ",1)])]),l(t).hasSelectedRoles?(c(),m("div",So,[e("div",Do,[e("span",Vo," 已选择 "+x(l(t).selectedRoleIds.length)+" 个角色 ",1),e("button",{onClick:s[8]||(s[8]=(...d)=>l(t).clearSelection&&l(t).clearSelection(...d)),class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"}," 取消选择 ")]),e("div",To,[l(t).permissions.canBatchOperation?(c(),m("button",{key:0,onClick:O,disabled:!l(t).canBatchActivate,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"}," 批量启用 ",8,jo)):V("",!0),l(t).permissions.canBatchOperation?(c(),m("button",{key:1,onClick:Q,disabled:!l(t).canBatchDeactivate,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"}," 批量禁用 ",8,Mo)):V("",!0),l(t).permissions.canDelete?(c(),m("button",{key:2,onClick:ie,disabled:!l(t).canBatchDelete,class:"px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"}," 批量删除 ",8,Ao)):V("",!0)])])):V("",!0),e("div",Eo,[e("div",Io,[l(t).loading?(c(),m("div",Bo,s[28]||(s[28]=[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),e("span",{class:"text-gray-600"},"加载中...")],-1)]))):V("",!0),e("div",No,[e("table",Lo,[e("thead",Oo,[e("tr",null,[e("th",Uo,[e("input",{type:"checkbox",checked:a.value,onChange:oe,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,Fo)]),s[29]||(s[29]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"角色名称",-1)),s[30]||(s[30]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"角色编码",-1)),s[31]||(s[31]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"描述",-1)),s[32]||(s[32]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"状态",-1)),s[33]||(s[33]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"用户数",-1)),s[34]||(s[34]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"权限数",-1)),s[35]||(s[35]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"创建时间",-1)),s[36]||(s[36]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"操作",-1))])]),e("tbody",qo,[(c(!0),m(X,null,Y(l(t).roles,d=>(c(),m("tr",{key:d.role_id,class:"hover:bg-gray-50"},[e("td",zo,[e("input",{type:"checkbox",checked:l(t).selectedRoleIds.includes(d.role_id),onChange:U=>l(t).toggleRoleSelection(d.role_id),class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,Qo)]),e("td",Go,x(d.role_name),1),e("td",Ho,x(d.role_code),1),e("td",Zo,x(d.description||"-"),1),e("td",Jo,[e("span",{class:q(d.status==="ACTIVE"?"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800")},x(d.status==="ACTIVE"?"启用":"禁用"),3)]),e("td",Ko,x(d.user_count||0),1),e("td",Wo,x(d.permission_count||0),1),e("td",Xo,x(ge(d.created_date)),1),e("td",Yo,[e("div",el,[l(t).permissions.canViewDetail?(c(),m("button",{key:0,onClick:U=>z(d),class:"text-blue-600 hover:text-blue-900 text-sm"}," 查看 ",8,tl)):V("",!0),l(t).permissions.canEdit?(c(),m("button",{key:1,onClick:U=>f(d),class:"text-blue-600 hover:text-blue-900 text-sm"}," 编辑 ",8,sl)):V("",!0),l(t).permissions.canAssignPermission?(c(),m("button",{key:2,onClick:U=>k(d),class:"text-green-600 hover:text-green-900 text-sm"}," 权限 ",8,ol)):V("",!0),l(t).permissions.canDelete?(c(),m("button",{key:3,onClick:U=>y(d),class:"text-red-600 hover:text-red-900 text-sm"}," 删除 ",8,ll)):V("",!0)])])]))),128))])]),!l(t).loading&&l(t).roles.length===0?(c(),m("div",nl,[C(l(re),{class:"mx-auto h-12 w-12 text-gray-400"}),s[37]||(s[37]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无角色",-1)),s[38]||(s[38]=e("p",{class:"mt-1 text-sm text-gray-500"},"开始创建第一个角色吧",-1)),e("div",rl,[e("button",{onClick:s[9]||(s[9]=d=>T.value=!0),class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},"新增角色")])])):V("",!0)])])]),l(t).total>0?(c(),m("div",al,[e("div",il," 显示第 "+x((l(t).currentPage-1)*l(t).pageSize+1)+" - "+x(Math.min(l(t).currentPage*l(t).pageSize,l(t).total))+" 条， 共 "+x(l(t).total)+" 条记录 ",1),e("div",dl,[e("select",{value:l(t).pageSize.toString(),onChange:ee,class:"px-3 py-2 border border-gray-300 rounded-md text-sm"},s[39]||(s[39]=[e("option",{value:"10"},"10",-1),e("option",{value:"20"},"20",-1),e("option",{value:"50"},"50",-1),e("option",{value:"100"},"100",-1)]),40,cl),e("div",ul,[e("button",{disabled:l(t).currentPage<=1,onClick:s[10]||(s[10]=d=>Z(l(t).currentPage-1)),class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,ml),e("div",pl,[(c(!0),m(X,null,Y(D.value,d=>(c(),m("button",{key:d,class:q(d===l(t).currentPage?"px-3 py-2 text-sm bg-blue-600 text-white rounded-md":"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"),onClick:U=>Z(Number(d))},x(d),11,xl))),128))]),e("button",{disabled:l(t).currentPage>=l(t).totalPages,onClick:s[11]||(s[11]=d=>Z(l(t).currentPage+1)),class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,gl)])])])):V("",!0),C(xt,{visible:T.value,"onUpdate:visible":s[12]||(s[12]=d=>T.value=d),role:$.value,onSuccess:ue},null,8,["visible","role"]),C(vs,{visible:S.value,"onUpdate:visible":s[13]||(s[13]=d=>S.value=d),"role-id":j.value,onEditRole:pe,onManagePermissions:xe},null,8,["visible","role-id"]),C(so,{visible:n.value,"onUpdate:visible":s[14]||(s[14]=d=>n.value=d),role:E.value,permissions:l(t).availablePermissions,onSuccess:me},null,8,["visible","role","permissions"]),C(he,{visible:w.value,"onUpdate:visible":s[15]||(s[15]=d=>w.value=d),type:"danger",title:"确认删除角色",message:`确定要删除角色 ${A.value?.role_name} 吗？`,details:["此操作不可撤销","角色的所有权限关联将被清除"],"confirm-text":"删除",loading:l(t).submitting,onConfirm:de},null,8,["visible","message","loading"]),C(he,{visible:b.value,"onUpdate:visible":s[16]||(s[16]=d=>b.value=d),type:"danger",title:"确认批量删除",message:`确定要删除选中的 ${l(t).selectedRoleIds.length} 个角色吗？`,details:["此操作不可撤销","所有选中角色的权限关联将被清除"],"confirm-text":"批量删除",loading:l(t).submitting,onConfirm:ce},null,8,["visible","message","loading"])]))}}),Rl=ne(vl,[["__scopeId","data-v-907080a8"]]);export{Rl as default};
