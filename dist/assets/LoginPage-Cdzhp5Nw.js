import{B as ze,a as Ue,C as Le,D as Ke,E as qe,b as He,c as Ge,F as Se,K as We,R as Je,S as Ye,d as Qe,T as Xe,e as Ze,f as et,g as tt,h as at,i as nt,V as st,j as rt,k as ot,l as lt,m as ue,n as it,o as ut,p as dt,q as _,r as ct,s as O,t as D,u as T,v as p,w as ft,x as mt,y as pt,z as vt,A as bt,G as ht,H as P,I as S,J as yt,L as gt,M as I,N as wt,O as xt,P as $t,Q as _t,U as Et,W as St,X as Ct,Y as At,Z as kt,_ as It,$ as Ot,a0 as z,a1 as Vt,a2 as Mt,a3 as Rt,a4 as Tt,a5 as W,a6 as Pt,a7 as Nt,a8 as Bt,a9 as jt,aa as Ft,ab as Dt,ac as zt,ad as Ut,ae as Lt,af as de,ag as Kt,ah as qt,ai as Ht,aj as Gt,ak as L,al as Wt,am as Jt,an as Yt,ao as Ce,ap as Qt,aq as Xt,ar as B,as as q,at as K,au as Zt,av as ea,aw as ta,ax as aa,ay as na,az as sa,aA as ra,aB as oa,aC as ce,aD as la,aE as ia,aF as ua,aG as da,aH as fe,aI as ca,aJ as fa,aK as x,aL as ma,aM as me,aN as pa,aO as va,aP as ba,aQ as J,aR as ha,aS as C,aT as ya,aU as ga,aV as Ae,aW as j,aX as wa,aY as xa,aZ as ke,a_ as $a,a$ as _a,b0 as Ea,b1 as Sa,b2 as Ca,b3 as Aa,b4 as ka,b5 as Ia,b6 as Oa,b7 as Va,b8 as Ma,b9 as U,ba as Ie,bb as Ra,bc as Ta,bd as Oe,be as Y,bf as Z,bg as Pa,bh as Na,bi as l,bj as Ba,bk as ja,bl as Fa,bm as Da,bn as Ve,bo as za,bp as Ua,bq as La,br as Ka,bs as qa,bt as Ha,bu as Ga,bv as Wa,bw as Ja,bx as Ya,by as Me,bz as Qa,bA as Xa,bB as Za,bC as N,bD as Re,bE as en,bF as tn,bG as an,bH as k,bI as nn,bJ as Te,bK as Pe,bL as sn,bM as Q,bN as rn,bO as on,bP as ln,bQ as un}from"./index-DMDdU1SS.js";import{u as dn,c as he}from"./useFormValidation-BtbC7VZE.js";import{P as H,r as cn,S as ye,c as ee,_ as fn}from"./index-MhxZY9Rm.js";import{C as mn,a as se,E as pn,b as vn}from"./eye-DygF8KSj.js";import{c as te}from"./createLucideIcon-B64IBOqu.js";import{C as bn}from"./circle-check-big-BRAv-ZO3.js";import{_ as hn}from"./_plugin-vue_export-helper-DlAUqK2U.js";/**
* vue v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const yn=()=>{},gn=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:ze,BaseTransitionPropsValidators:Ue,Comment:Le,DeprecationTypes:Ke,EffectScope:qe,ErrorCodes:He,ErrorTypeStrings:Ge,Fragment:Se,KeepAlive:We,ReactiveEffect:Je,Static:Ye,Suspense:Qe,Teleport:Xe,Text:Ze,TrackOpTypes:et,Transition:tt,TransitionGroup:at,TriggerOpTypes:nt,VueElement:st,assertNumber:rt,callWithAsyncErrorHandling:ot,callWithErrorHandling:lt,camelize:ue,capitalize:it,cloneVNode:ut,compatUtils:dt,compile:yn,computed:_,createApp:ct,createBlock:O,createCommentVNode:D,createElementBlock:T,createElementVNode:p,createHydrationRenderer:ft,createPropsRestProxy:mt,createRenderer:pt,createSSRApp:vt,createSlots:bt,createStaticVNode:ht,createTextVNode:P,createVNode:S,customRef:yt,defineAsyncComponent:gt,defineComponent:I,defineCustomElement:wt,defineEmits:xt,defineExpose:$t,defineModel:_t,defineOptions:Et,defineProps:St,defineSSRCustomElement:Ct,defineSlots:At,devtools:kt,effect:It,effectScope:Ot,getCurrentInstance:z,getCurrentScope:Vt,getCurrentWatcher:Mt,getTransitionRawChildren:Rt,guardReactiveProps:Tt,h:W,handleError:Pt,hasInjectionContext:Nt,hydrate:Bt,hydrateOnIdle:jt,hydrateOnInteraction:Ft,hydrateOnMediaQuery:Dt,hydrateOnVisible:zt,initCustomFormatter:Ut,initDirectivesForSSR:Lt,inject:de,isMemoSame:Kt,isProxy:qt,isReactive:Ht,isReadonly:Gt,isRef:L,isRuntimeOnly:Wt,isShallow:Jt,isVNode:Yt,markRaw:Ce,mergeDefaults:Qt,mergeModels:Xt,mergeProps:B,nextTick:q,normalizeClass:K,normalizeProps:Zt,normalizeStyle:ea,onActivated:ta,onBeforeMount:aa,onBeforeUnmount:na,onBeforeUpdate:sa,onDeactivated:ra,onErrorCaptured:oa,onMounted:ce,onRenderTracked:la,onRenderTriggered:ia,onScopeDispose:ua,onServerPrefetch:da,onUnmounted:fe,onUpdated:ca,onWatcherCleanup:fa,openBlock:x,popScopeId:ma,provide:me,proxyRefs:pa,pushScopeId:va,queuePostFlushCb:ba,reactive:J,readonly:ha,ref:C,registerRuntimeCompiler:ya,render:ga,renderList:Ae,renderSlot:j,resolveComponent:wa,resolveDirective:xa,resolveDynamicComponent:ke,resolveFilter:$a,resolveTransitionHooks:_a,setBlockTracking:Ea,setDevtoolsHook:Sa,setTransitionHooks:Ca,shallowReactive:Aa,shallowReadonly:ka,shallowRef:Ia,ssrContextKey:Oa,ssrUtils:Va,stop:Ma,toDisplayString:U,toHandlerKey:Ie,toHandlers:Ra,toRaw:Ta,toRef:Oe,toRefs:Y,toValue:Z,transformVNodeArgs:Pa,triggerRef:Na,unref:l,useAttrs:Ba,useCssModule:ja,useCssVars:Fa,useHost:Da,useId:Ve,useModel:za,useSSRContext:Ua,useShadowRoot:La,useSlots:Ka,useTemplateRef:qa,useTransitionState:Ha,vModelCheckbox:Ga,vModelDynamic:Wa,vModelRadio:Ja,vModelSelect:Ya,vModelText:Me,vShow:Qa,version:Xa,warn:Za,watch:N,watchEffect:Re,watchPostEffect:en,watchSyncEffect:tn,withAsyncContext:an,withCtx:k,withDefaults:nn,withDirectives:Te,withKeys:Pe,withMemo:sn,withModifiers:Q,withScopeId:rn},Symbol.toStringTag,{value:"Module"})),wn=I({__name:"VisuallyHidden",props:{feature:{default:"focusable"},asChild:{type:Boolean},as:{default:"span"}},setup(a){return(t,e)=>(x(),O(l(H),{as:t.as,"as-child":t.asChild,"aria-hidden":t.feature==="focusable"?"true":void 0,"data-hidden":t.feature==="fully-hidden"?"":void 0,tabindex:t.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:k(()=>[j(t.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}}),Ne=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const xn=a=>typeof a<"u",$n=Z,_n=Ne?window:void 0;function ae(a){var t;const e=Z(a);return(t=e?.$el)!=null?t:e}function En(a){return JSON.parse(JSON.stringify(a))}function Sn(a,t,e,s={}){var n,o,r;const{clone:u=!1,passive:g=!1,eventName:c,deep:d=!1,defaultValue:f,shouldEmit:v}=s,i=z(),$=e||i?.emit||((n=i?.$emit)==null?void 0:n.bind(i))||((r=(o=i?.proxy)==null?void 0:o.$emit)==null?void 0:r.bind(i?.proxy));let h=c;h=h||`update:${t.toString()}`;const m=y=>u?typeof u=="function"?u(y):En(y):y,E=()=>xn(a[t])?m(a[t]):f,M=y=>{v?v(y)&&$(h,y):$(h,y)};if(g){const y=E(),A=C(y);let V=!1;return N(()=>a[t],R=>{V||(V=!0,A.value=m(R),q(()=>V=!1))}),N(A,R=>{!V&&(R!==a[t]||d)&&M(R)},{deep:d}),A}else return _({get(){return E()},set(y){M(y)}})}function ne(a,t){const e=typeof a=="string"&&!t?`${a}Context`:t,s=Symbol(e);return[r=>{const u=de(s,r);if(u||u===null)return u;throw new Error(`Injection \`${s.toString()}\` not found. Component must be used within ${Array.isArray(a)?`one of the following components: ${a.join(", ")}`:`\`${a}\``}`)},r=>(me(s,r),r)]}function ge(a){return typeof a=="string"?`'${a}'`:new Cn().serialize(a)}const Cn=function(){class a{#e=new Map;compare(e,s){const n=typeof e,o=typeof s;return n==="string"&&o==="string"?e.localeCompare(s):n==="number"&&o==="number"?e-s:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(s,!0))}serialize(e,s){if(e===null)return"null";switch(typeof e){case"string":return s?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const s=Object.prototype.toString.call(e);if(s!=="[object Object]")return this.serializeBuiltInType(s.length<10?`unknown:${s}`:s.slice(8,-1),e);const n=e.constructor,o=n===Object||n===void 0?"":n.name;if(o!==""&&globalThis[o]===n)return this.serializeBuiltInType(o,e);if(typeof e.toJSON=="function"){const r=e.toJSON();return o+(r!==null&&typeof r=="object"?this.$object(r):`(${this.serialize(r)})`)}return this.serializeObjectEntries(o,Object.entries(e))}serializeBuiltInType(e,s){const n=this["$"+e];if(n)return n.call(this,s);if(typeof s?.entries=="function")return this.serializeObjectEntries(e,s.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,s){const n=Array.from(s).sort((r,u)=>this.compare(r[0],u[0]));let o=`${e}{`;for(let r=0;r<n.length;r++){const[u,g]=n[r];o+=`${this.serialize(u,!0)}:${this.serialize(g)}`,r<n.length-1&&(o+=",")}return o+"}"}$object(e){let s=this.#e.get(e);return s===void 0&&(this.#e.set(e,`#${this.#e.size}`),s=this.serializeObject(e),this.#e.set(e,s)),s}$function(e){const s=Function.prototype.toString.call(e);return s.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${s.replace(/\s*\n\s*/g,"")}`}$Array(e){let s="[";for(let n=0;n<e.length;n++)s+=this.serialize(e[n]),n<e.length-1&&(s+=",");return s+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((s,n)=>this.compare(s,n)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}for(const t of["Error","RegExp","URL"])a.prototype["$"+t]=function(e){return`${t}(${e})`};for(const t of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])a.prototype["$"+t]=function(e){return`${t}[${e.join(",")}]`};for(const t of["BigInt64Array","BigUint64Array"])a.prototype["$"+t]=function(e){return`${t}[${e.join("n,")}${e.length>0?"n":""}]`};return a}();function oe(a,t){return a===t||ge(a)===ge(t)}function le(a){return a==null}function we(a,t){return le(a)?!1:Array.isArray(a)?a.some(e=>oe(e,t)):oe(a,t)}const[An,Ds]=ne("ConfigProvider");function pe(){const a=z(),t=C(),e=_(()=>["#text","#comment"].includes(t.value?.$el.nodeName)?t.value?.$el.nextElementSibling:ae(t)),s=Object.assign({},a.exposed),n={};for(const r in a.props)Object.defineProperty(n,r,{enumerable:!0,configurable:!0,get:()=>a.props[r]});if(Object.keys(s).length>0)for(const r in s)Object.defineProperty(n,r,{enumerable:!0,configurable:!0,get:()=>s[r]});Object.defineProperty(n,"$el",{enumerable:!0,configurable:!0,get:()=>a.vnode.el}),a.exposed=n;function o(r){t.value=r,r&&(Object.defineProperty(n,"$el",{enumerable:!0,configurable:!0,get:()=>r instanceof Element?r:r.$el}),a.exposed=n)}return{forwardRef:o,currentRef:t,currentElement:e}}let kn=0;function In(a,t="reka"){const e=An({useId:void 0});return Object.hasOwn(gn,"useId")?`${t}-${Ve?.()}`:e.useId?`${t}-${e.useId()}`:`${t}-${++kn}`}function On(a,t){const e=C(a);function s(o){return t[e.value][o]??e.value}return{state:e,dispatch:o=>{e.value=s(o)}}}function Vn(a,t){const e=C({}),s=C("none"),n=C(a),o=a.value?"mounted":"unmounted";let r;const u=t.value?.ownerDocument.defaultView??_n,{state:g,dispatch:c}=On(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),d=m=>{if(Ne){const E=new CustomEvent(m,{bubbles:!1,cancelable:!1});t.value?.dispatchEvent(E)}};N(a,async(m,E)=>{const M=E!==m;if(await q(),M){const y=s.value,A=G(t.value);m?(c("MOUNT"),d("enter"),A==="none"&&d("after-enter")):A==="none"||A==="undefined"||e.value?.display==="none"?(c("UNMOUNT"),d("leave"),d("after-leave")):E&&y!==A?(c("ANIMATION_OUT"),d("leave")):(c("UNMOUNT"),d("after-leave"))}},{immediate:!0});const f=m=>{const E=G(t.value),M=E.includes(m.animationName),y=g.value==="mounted"?"enter":"leave";if(m.target===t.value&&M&&(d(`after-${y}`),c("ANIMATION_END"),!n.value)){const A=t.value.style.animationFillMode;t.value.style.animationFillMode="forwards",r=u?.setTimeout(()=>{t.value?.style.animationFillMode==="forwards"&&(t.value.style.animationFillMode=A)})}m.target===t.value&&E==="none"&&c("ANIMATION_END")},v=m=>{m.target===t.value&&(s.value=G(t.value))},i=N(t,(m,E)=>{m?(e.value=getComputedStyle(m),m.addEventListener("animationstart",v),m.addEventListener("animationcancel",f),m.addEventListener("animationend",f)):(c("ANIMATION_END"),r!==void 0&&u?.clearTimeout(r),E?.removeEventListener("animationstart",v),E?.removeEventListener("animationcancel",f),E?.removeEventListener("animationend",f))},{immediate:!0}),$=N(g,()=>{const m=G(t.value);s.value=g.value==="mounted"?m:"none"});return fe(()=>{i(),$()}),{isPresent:_(()=>["mounted","unmountSuspended"].includes(g.value))}}function G(a){return a&&getComputedStyle(a).animationName||"none"}const Mn=I({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(a,{slots:t,expose:e}){const{present:s,forceMount:n}=Y(a),o=C(),{isPresent:r}=Vn(s,o);e({present:r});let u=t.default({present:r.value});u=cn(u||[]);const g=z();if(u&&u?.length>1){const c=g?.parent?.type.name?`<${g.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${c}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(d=>`  - ${d}`).join(`
`)].join(`
`))}return()=>n.value||s.value||r.value?W(t.default({present:r.value})[0],{ref:c=>{const d=ae(c);return typeof d?.hasAttribute>"u"||(d?.hasAttribute("data-reka-popper-content-wrapper")?o.value=d.firstElementChild:o.value=d),d}}):null}});function Rn(a){const t=z(),e=t?.type.emits,s={};return e?.length||console.warn(`No emitted event found. Please check component: ${t?.type.__name}`),e?.forEach(n=>{s[Ie(ue(n))]=(...o)=>a(n,...o)}),s}function xe(){let a=document.activeElement;if(a==null)return null;for(;a!=null&&a.shadowRoot!=null&&a.shadowRoot.activeElement!=null;)a=a.shadowRoot.activeElement;return a}function Tn(a){const t=z(),e=Object.keys(t?.type.props??{}).reduce((n,o)=>{const r=(t?.type.props[o]).default;return r!==void 0&&(n[o]=r),n},{}),s=Oe(a);return _(()=>{const n={},o=t?.vnode.props??{};return Object.keys(o).forEach(r=>{n[ue(r)]=o[r]}),Object.keys({...e,...n}).reduce((r,u)=>(s.value[u]!==void 0&&(r[u]=s.value[u]),r),{})})}function Pn(a,t){const e=Tn(a),s=t?Rn(t):{};return _(()=>({...e.value,...s}))}function ie(){const a=C(),t=_(()=>["#text","#comment"].includes(a.value?.$el.nodeName)?a.value?.$el.nextElementSibling:ae(a));return{primitiveElement:a,currentElement:t}}function Nn(a){return _(()=>$n(a)?!!ae(a)?.closest("form"):!0)}const $e="data-reka-collection-item";function Bn(a={}){const{key:t="",isProvider:e=!1}=a,s=`${t}CollectionProvider`;let n;if(e){const d=C(new Map);n={collectionRef:C(),itemMap:d},me(s,n)}else n=de(s);const o=(d=!1)=>{const f=n.collectionRef.value;if(!f)return[];const v=Array.from(f.querySelectorAll(`[${$e}]`)),$=Array.from(n.itemMap.value.values()).sort((h,m)=>v.indexOf(h.ref)-v.indexOf(m.ref));return d?$:$.filter(h=>h.ref.dataset.disabled!=="")},r=I({name:"CollectionSlot",setup(d,{slots:f}){const{primitiveElement:v,currentElement:i}=ie();return N(i,()=>{n.collectionRef.value=i.value}),()=>W(ye,{ref:v},f)}}),u=I({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(d,{slots:f,attrs:v}){const{primitiveElement:i,currentElement:$}=ie();return Re(h=>{if($.value){const m=Ce($.value);n.itemMap.value.set(m,{ref:$.value,value:d.value}),h(()=>n.itemMap.value.delete(m))}}),()=>W(ye,{...v,[$e]:"",ref:i},f)}}),g=_(()=>Array.from(n.itemMap.value.values())),c=_(()=>n.itemMap.value.size);return{getItems:o,reactiveItems:g,itemMapSize:c,CollectionSlot:r,CollectionItem:u}}const jn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Fn(a,t){return t!=="rtl"?a:a==="ArrowLeft"?"ArrowRight":a==="ArrowRight"?"ArrowLeft":a}function Dn(a,t,e){const s=Fn(a.key,e);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(s))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(s)))return jn[s]}function zn(a,t=!1){const e=xe();for(const s of a)if(s===e||(s.focus({preventScroll:t}),xe()!==e))return}function Un(a,t){return a.map((e,s)=>a[(t+s)%a.length])}const[Ln,zs]=ne("RovingFocusGroup"),_e=I({inheritAttrs:!1,__name:"VisuallyHiddenInputBubble",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(a){const t=a,{primitiveElement:e,currentElement:s}=ie(),n=_(()=>t.checked??t.value);return N(n,(o,r)=>{if(!s.value)return;const u=s.value,g=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(g,"value").set;if(d&&o!==r){const f=new Event("input",{bubbles:!0}),v=new Event("change",{bubbles:!0});d.call(u,o),u.dispatchEvent(f),u.dispatchEvent(v)}}),(o,r)=>(x(),O(wn,B({ref_key:"primitiveElement",ref:e},{...t,...o.$attrs},{as:"input"}),null,16))}}),Kn=I({inheritAttrs:!1,__name:"VisuallyHiddenInput",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(a){const t=a,e=_(()=>typeof t.value=="object"&&Array.isArray(t.value)&&t.value.length===0&&t.required),s=_(()=>typeof t.value=="string"||typeof t.value=="number"||typeof t.value=="boolean"?[{name:t.name,value:t.value}]:typeof t.value=="object"&&Array.isArray(t.value)?t.value.flatMap((n,o)=>typeof n=="object"?Object.entries(n).map(([r,u])=>({name:`[${t.name}][${o}][${r}]`,value:u})):{name:`[${t.name}][${o}]`,value:n}):t.value!==null&&typeof t.value=="object"&&!Array.isArray(t.value)?Object.entries(t.value).map(([n,o])=>({name:`[${t.name}][${n}]`,value:o})):[]);return(n,o)=>e.value?(x(),O(_e,B({key:n.name},{...t,...n.$attrs},{name:n.name,value:n.value}),null,16,["name","value"])):(x(!0),T(Se,{key:1},Ae(s.value,r=>(x(),O(_e,B({key:r.name,ref_for:!0},{...t,...n.$attrs},{name:r.name,value:r.value}),null,16,["name","value"]))),128))}}),[qn,Us]=ne("CheckboxGroupRoot");function X(a){return a==="indeterminate"}function Be(a){return X(a)?"indeterminate":a?"checked":"unchecked"}const Hn=I({__name:"RovingFocusItem",props:{tabStopId:{},focusable:{type:Boolean,default:!0},active:{type:Boolean},allowShiftKey:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(a){const t=a,e=Ln(),s=In(),n=_(()=>t.tabStopId||s),o=_(()=>e.currentTabStopId.value===n.value),{getItems:r,CollectionItem:u}=Bn();ce(()=>{t.focusable&&e.onFocusableItemAdd()}),fe(()=>{t.focusable&&e.onFocusableItemRemove()});function g(c){if(c.key==="Tab"&&c.shiftKey){e.onItemShiftTab();return}if(c.target!==c.currentTarget)return;const d=Dn(c,e.orientation.value,e.dir.value);if(d!==void 0){if(c.metaKey||c.ctrlKey||c.altKey||!t.allowShiftKey&&c.shiftKey)return;c.preventDefault();let f=[...r().map(v=>v.ref).filter(v=>v.dataset.disabled!=="")];if(d==="last")f.reverse();else if(d==="prev"||d==="next"){d==="prev"&&f.reverse();const v=f.indexOf(c.currentTarget);f=e.loop.value?Un(f,v+1):f.slice(v+1)}q(()=>zn(f))}}return(c,d)=>(x(),O(l(u),null,{default:k(()=>[S(l(H),{tabindex:o.value?0:-1,"data-orientation":l(e).orientation.value,"data-active":c.active?"":void 0,"data-disabled":c.focusable?void 0:"",as:c.as,"as-child":c.asChild,onMousedown:d[0]||(d[0]=f=>{c.focusable?l(e).onItemFocus(n.value):f.preventDefault()}),onFocus:d[1]||(d[1]=f=>l(e).onItemFocus(n.value)),onKeydown:g},{default:k(()=>[j(c.$slots,"default")]),_:3},8,["tabindex","data-orientation","data-active","data-disabled","as","as-child"])]),_:3}))}}),[Gn,Wn]=ne("CheckboxRoot"),Jn=I({inheritAttrs:!1,__name:"CheckboxRoot",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null],default:void 0},disabled:{type:Boolean},value:{default:"on"},id:{},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:modelValue"],setup(a,{emit:t}){const e=a,s=t,{forwardRef:n,currentElement:o}=pe(),r=qn(null),u=Sn(e,"modelValue",s,{defaultValue:e.defaultValue,passive:e.modelValue===void 0}),g=_(()=>r?.disabled.value||e.disabled),c=_(()=>le(r?.modelValue.value)?u.value==="indeterminate"?"indeterminate":u.value:we(r.modelValue.value,e.value));function d(){if(le(r?.modelValue.value))u.value=X(u.value)?!0:!u.value;else{const i=[...r.modelValue.value||[]];if(we(i,e.value)){const $=i.findIndex(h=>oe(h,e.value));i.splice($,1)}else i.push(e.value);r.modelValue.value=i}}const f=Nn(o),v=_(()=>e.id&&o.value?document.querySelector(`[for="${e.id}"]`)?.innerText:void 0);return Wn({disabled:g,state:c}),(i,$)=>(x(),O(ke(l(r)?.rovingFocus.value?l(Hn):l(H)),B(i.$attrs,{id:i.id,ref:l(n),role:"checkbox","as-child":i.asChild,as:i.as,type:i.as==="button"?"button":void 0,"aria-checked":l(X)(c.value)?"mixed":c.value,"aria-required":i.required,"aria-label":i.$attrs["aria-label"]||v.value,"data-state":l(Be)(c.value),"data-disabled":g.value?"":void 0,disabled:g.value,focusable:l(r)?.rovingFocus.value?!g.value:void 0,onKeydown:Pe(Q(()=>{},["prevent"]),["enter"]),onClick:d}),{default:k(()=>[j(i.$slots,"default",{modelValue:l(u),state:c.value}),l(f)&&i.name&&!l(r)?(x(),O(l(Kn),{key:0,type:"checkbox",checked:!!c.value,name:i.name,value:i.value,disabled:g.value,required:i.required},null,8,["checked","name","value","disabled","required"])):D("",!0)]),_:3},16,["id","as-child","as","type","aria-checked","aria-required","aria-label","data-state","data-disabled","disabled","focusable","onKeydown"]))}}),Yn=I({__name:"CheckboxIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(a){const{forwardRef:t}=pe(),e=Gn();return(s,n)=>(x(),O(l(Mn),{present:s.forceMount||l(X)(l(e).state.value)||l(e).state.value===!0},{default:k(()=>[S(l(H),B({ref:l(t),"data-state":l(Be)(l(e).state.value),"data-disabled":l(e).disabled.value?"":void 0,style:{pointerEvents:"none"},"as-child":s.asChild,as:s.as},s.$attrs),{default:k(()=>[j(s.$slots,"default")]),_:3},16,["data-state","data-disabled","as-child","as"])]),_:3},8,["present"]))}}),Qn=I({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(a){const t=a;return pe(),(e,s)=>(x(),O(l(H),B(t,{onMousedown:s[0]||(s[0]=n=>{!n.defaultPrevented&&n.detail>1&&n.preventDefault()})}),{default:k(()=>[j(e.$slots,"default")]),_:3},16))}});function Xn(a){if(!L(a))return J(a);const t=new Proxy({},{get(e,s,n){return l(Reflect.get(a.value,s,n))},set(e,s,n){return L(a.value[s])&&!L(n)?a.value[s].value=n:a.value[s]=n,!0},deleteProperty(e,s){return Reflect.deleteProperty(a.value,s)},has(e,s){return Reflect.has(a.value,s)},ownKeys(){return Object.keys(a.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return J(t)}function Zn(a){return Xn(_(a))}function je(a,...t){const e=t.flat(),s=e[0];return Zn(()=>Object.fromEntries(typeof s=="function"?Object.entries(Y(a)).filter(([n,o])=>!s(Z(o),n)):Object.entries(Y(a)).filter(n=>!e.includes(n[0]))))}typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const es=a=>typeof a<"u";function ts(a){return JSON.parse(JSON.stringify(a))}function as(a,t,e,s={}){var n,o,r;const{clone:u=!1,passive:g=!1,eventName:c,deep:d=!1,defaultValue:f,shouldEmit:v}=s,i=z(),$=e||i?.emit||((n=i?.$emit)==null?void 0:n.bind(i))||((r=(o=i?.proxy)==null?void 0:o.$emit)==null?void 0:r.bind(i?.proxy));let h=c;h=h||`update:${t.toString()}`;const m=y=>u?typeof u=="function"?u(y):ts(y):y,E=()=>es(a[t])?m(a[t]):f,M=y=>{v?v(y)&&$(h,y):$(h,y)};if(g){const y=E(),A=C(y);let V=!1;return N(()=>a[t],R=>{V||(V=!0,A.value=m(R),q(()=>V=!1))}),N(A,R=>{!V&&(R!==a[t]||d)&&M(R)},{deep:d}),A}else return _({get(){return E()},set(y){M(y)}})}const Ee=I({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(a,{emit:t}){const e=a,n=as(e,"modelValue",t,{passive:!0,defaultValue:e.defaultValue});return(o,r)=>Te((x(),T("input",{"onUpdate:modelValue":r[0]||(r[0]=u=>L(n)?n.value=u:null),"data-slot":"input",class:K(l(ee)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e.class))},null,2)),[[Me,l(n)]])}}),re=I({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(a){const t=a,e=je(t,"class");return(s,n)=>(x(),O(l(Qn),B({"data-slot":"label"},l(e),{class:l(ee)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t.class)}),{default:k(()=>[j(s.$slots,"default")]),_:3},16,["class"]))}});/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=te("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=te("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=te("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const os=te("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),ls=I({__name:"Checkbox",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null]},disabled:{type:Boolean},value:{},id:{},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},emits:["update:modelValue"],setup(a,{emit:t}){const e=a,s=t,n=je(e,"class"),o=Pn(n,s);return(r,u)=>(x(),O(l(Jn),B({"data-slot":"checkbox"},l(o),{class:l(ee)("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e.class)}),{default:k(()=>[S(l(Yn),{"data-slot":"checkbox-indicator",class:"flex items-center justify-center text-current transition-none"},{default:k(()=>[j(r.$slots,"default",{},()=>[S(l(mn),{class:"size-3.5"})])]),_:3})]),_:3},16,["class"]))}}),is=I({__name:"Card",props:{class:{}},setup(a){const t=a;return(e,s)=>(x(),T("div",{"data-slot":"card",class:K(l(ee)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t.class))},[j(e.$slots,"default")],2))}}),us={class:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4"},ds={class:"relative w-full max-w-md"},cs={class:"space-y-2"},fs={class:"relative"},ms={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},ps={key:0,class:"text-xs text-red-600 flex items-center gap-1"},vs={class:"space-y-2"},bs={class:"relative"},hs={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},ys=["disabled"],gs={key:0,class:"text-xs text-red-600 flex items-center gap-1"},ws={class:"flex items-center justify-between"},xs={class:"flex items-center space-x-2"},$s={key:0,class:"p-4 bg-orange-50 border border-orange-200 rounded-lg"},_s={class:"flex items-start gap-3"},Es={class:"text-sm text-orange-700"},Ss={class:"font-medium"},Cs={key:1,class:"p-3 bg-red-50 border border-red-200 rounded-lg"},As={class:"text-sm text-red-700 flex items-center gap-2"},ks={key:2,class:"p-3 bg-green-50 border border-green-200 rounded-lg"},Is={class:"text-sm text-green-700 flex items-center gap-2"},Os={key:1},Vs={key:2},Ms=I({__name:"LoginPage",setup(a){const t=un(),e=ln(),s=on(),n=J({username:"admin",password:"admin123",remember:!1}),{errors:o,validateField:r,validateAll:u,isValid:g,clearError:c}=dn(n,{username:{...he.username,custom:w=>!w||!w.trim()?"请输入用户名或邮箱":w.length<2?"用户名或邮箱至少2个字符":null},password:{...he.password,custom:w=>!w||!w.trim()?"请输入密码":w.length<6?"密码至少6个字符":null}}),d=C(!1),f=C(!1),v=C(""),i=C(""),$=C(0),h=C(!1),m=C(null),E=_(()=>n.username.trim()&&n.password.trim()&&n.password.length>=6&&g.value&&!h.value),M=_(()=>e.query.redirect||"/"),y=_(()=>{if(!m.value)return 0;const w=Math.max(0,m.value.getTime()-Date.now());return Math.ceil(w/1e3)}),A=w=>{r(w)},V=w=>{c(w),v.value=""},R=()=>{$.value++,$.value>=5?(h.value=!0,m.value=new Date(Date.now()+15*60*1e3),setTimeout(()=>{h.value=!1,$.value=0,m.value=null},15*60*1e3),v.value="登录失败次数过多，账户已被锁定15分钟"):v.value=`登录失败，还可以尝试 ${5-$.value} 次`},ve=async()=>{if(v.value="",i.value="",h.value){v.value=`账户已被锁定，请在 ${y.value} 秒后重试`;return}if(u())try{f.value=!0,(await s.login({username:n.username.trim(),password:n.password})).success?($.value=0,i.value="登录成功，正在跳转...",n.remember?localStorage.setItem("remembered_username",n.username.trim()):localStorage.removeItem("remembered_username"),setTimeout(()=>{t.push(M.value)},800)):R()}catch(w){console.error("Login error:",w),R()}finally{f.value=!1}},Fe=()=>{d.value=!d.value},be=w=>{w.key==="Enter"&&E.value&&!f.value&&ve()};ce(()=>{s.isAuthenticated&&t.push(M.value);const w=localStorage.getItem("remembered_username");w&&(n.username=w,n.remember=!0)});const De=w=>{n.remember=w,w||localStorage.removeItem("remembered_username")};return typeof window<"u"&&setInterval(()=>{h.value&&y.value<=0&&(h.value=!1,$.value=0,m.value=null)},1e3),(w,b)=>(x(),T("div",us,[b[16]||(b[16]=p("div",{class:"absolute inset-0 overflow-hidden pointer-events-none"},[p("div",{class:"absolute -top-40 -right-40 w-80 h-80 bg-blue-400/20 rounded-full blur-3xl"}),p("div",{class:"absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400/20 rounded-full blur-3xl"})],-1)),p("div",ds,[S(l(is),{class:"px-8 py-10 backdrop-blur-sm bg-white/80 shadow-2xl border-0"},{default:k(()=>[b[13]||(b[13]=p("div",{class:"text-center mb-8"},[p("div",{class:"mx-auto mb-6 w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg"},[p("svg",{class:"w-8 h-8 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[p("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z","clip-rule":"evenodd"})])]),p("h1",{class:"text-2xl font-bold text-gray-900 mb-2"}," Glass ERP V2.1 "),p("p",{class:"text-gray-600 text-sm"}," 玻璃生产管理系统 ")],-1)),p("form",{onSubmit:Q(ve,["prevent"]),class:"space-y-6"},[p("div",cs,[S(l(re),{for:"username",class:"text-sm font-medium text-gray-700"},{default:k(()=>b[7]||(b[7]=[P(" 用户名 ")])),_:1,__:[7]}),p("div",fs,[p("div",ms,[S(l(os),{class:"h-5 w-5 text-gray-400"})]),S(l(Ee),{id:"username",modelValue:n.username,"onUpdate:modelValue":b[0]||(b[0]=F=>n.username=F),type:"text",placeholder:"请输入用户名或邮箱",autocomplete:"username",disabled:h.value,class:K(["pl-10 transition-all duration-200",l(o).username?"border-red-300 focus:border-red-500 focus:ring-red-500/20":"focus:border-blue-500 focus:ring-blue-500/20",h.value?"bg-gray-100 cursor-not-allowed":""]),onInput:b[1]||(b[1]=F=>V("username")),onBlur:b[2]||(b[2]=F=>A("username")),onKeydown:be},null,8,["modelValue","disabled","class"])]),l(o).username?(x(),T("p",ps,[S(l(se),{class:"h-3 w-3"}),P(" "+U(l(o).username),1)])):D("",!0)]),p("div",vs,[S(l(re),{for:"password",class:"text-sm font-medium text-gray-700"},{default:k(()=>b[8]||(b[8]=[P(" 密码 ")])),_:1,__:[8]}),p("div",bs,[p("div",hs,[S(l(ss),{class:"h-5 w-5 text-gray-400"})]),S(l(Ee),{id:"password",modelValue:n.password,"onUpdate:modelValue":b[3]||(b[3]=F=>n.password=F),type:d.value?"text":"password",placeholder:"请输入密码",autocomplete:"current-password",disabled:h.value,class:K(["pl-10 pr-10 transition-all duration-200",l(o).password?"border-red-300 focus:border-red-500 focus:ring-red-500/20":"focus:border-blue-500 focus:ring-blue-500/20",h.value?"bg-gray-100 cursor-not-allowed":""]),onInput:b[4]||(b[4]=F=>V("password")),onBlur:b[5]||(b[5]=F=>A("password")),onKeydown:be},null,8,["modelValue","type","disabled","class"]),p("button",{type:"button",disabled:h.value,class:"absolute inset-y-0 right-0 pr-3 flex items-center transition-colors duration-200 disabled:cursor-not-allowed",onClick:Fe},[d.value?(x(),O(l(vn),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(x(),O(l(pn),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))],8,ys)]),l(o).password?(x(),T("p",gs,[S(l(se),{class:"h-3 w-3"}),P(" "+U(l(o).password),1)])):D("",!0)]),p("div",ws,[p("div",xs,[S(l(ls),{id:"remember",checked:n.remember,"onUpdate:checked":De},null,8,["checked"]),S(l(re),{for:"remember",class:"text-sm text-gray-600 cursor-pointer select-none"},{default:k(()=>b[9]||(b[9]=[P(" 记住我 ")])),_:1,__:[9]})]),p("a",{href:"#",class:"text-sm text-blue-600 hover:text-blue-800 transition-colors duration-200",onClick:b[6]||(b[6]=Q(F=>w.$router.push("/forgot-password"),["prevent"]))}," 忘记密码？ ")]),h.value?(x(),T("div",$s,[p("div",_s,[S(l(rs),{class:"h-5 w-5 text-orange-600 flex-shrink-0 mt-0.5"}),p("div",null,[b[12]||(b[12]=p("h4",{class:"text-sm font-medium text-orange-800 mb-1"}," 账户安全保护 ",-1)),p("p",Es,[b[10]||(b[10]=P(" 由于多次登录失败，您的账户已被临时锁定。 ")),b[11]||(b[11]=p("br",null,null,-1)),p("span",Ss,U(y.value)+" 秒后自动解锁",1)])])])])):D("",!0),v.value&&!h.value?(x(),T("div",Cs,[p("p",As,[S(l(se),{class:"h-4 w-4 flex-shrink-0"}),P(" "+U(v.value),1)])])):D("",!0),i.value?(x(),T("div",ks,[p("p",Is,[S(l(bn),{class:"h-4 w-4 flex-shrink-0"}),P(" "+U(i.value),1)])])):D("",!0),S(l(fn),{type:"submit",disabled:!E.value||f.value,class:"w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"},{default:k(()=>[f.value?(x(),O(l(ns),{key:0,class:"h-4 w-4 mr-2 animate-spin"})):D("",!0),f.value?(x(),T("span",Os,"登录中...")):(x(),T("span",Vs,"登录"))]),_:1},8,["disabled"])],32),b[14]||(b[14]=p("div",{class:"mt-8 text-center"},[p("p",{class:"text-xs text-gray-500"}," © 2024 Glass ERP V2.1. 保留所有权利. ")],-1))]),_:1,__:[13,14]}),b[15]||(b[15]=p("div",{class:"mt-6 text-center"},[p("p",{class:"text-sm text-gray-600"},[P(" 需要帮助？请联系 "),p("a",{href:"mailto:<EMAIL>",class:"text-blue-600 hover:text-blue-800 transition-colors duration-200"}," 技术支持 ")])],-1))])]))}}),Ls=hn(Ms,[["__scopeId","data-v-f494eb1c"]]);export{Ls as default};
