import{u as y,v as s,aK as d,aS as P,M as U,q as I,t as B,at as S,b9 as w,I as D,bi as p,s as M,H as Z,F as K,aV as J,bM as Q}from"./index-DMDdU1SS.js";import{c as j}from"./createLucideIcon-B64IBOqu.js";import{C as Y}from"./circle-check-big-BRAv-ZO3.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=j("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=j("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ne=j("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=j("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=j("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function qe(e,t){return d(),y("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function He(e,t){return d(),y("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}function Re(e,t){return d(),y("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}var ae=typeof global=="object"&&global&&global.Object===Object&&global,oe=typeof self=="object"&&self&&self.Object===Object&&self,V=ae||oe||Function("return this")(),O=V.Symbol,q=Object.prototype,se=q.hasOwnProperty,le=q.toString,k=O?O.toStringTag:void 0;function ce(e){var t=se.call(e,k),i=e[k];try{e[k]=void 0;var o=!0}catch{}var n=le.call(e);return o&&(t?e[k]=i:delete e[k]),n}var de=Object.prototype,ue=de.toString;function fe(e){return ue.call(e)}var ge="[object Null]",me="[object Undefined]",W=O?O.toStringTag:void 0;function be(e){return e==null?e===void 0?me:ge:W&&W in Object(e)?ce(e):fe(e)}function ye(e){return e!=null&&typeof e=="object"}var he="[object Symbol]";function pe(e){return typeof e=="symbol"||ye(e)&&be(e)==he}var ve=/\s/;function xe(e){for(var t=e.length;t--&&ve.test(e.charAt(t)););return t}var we=/^\s+/;function ke(e){return e&&e.slice(0,xe(e)+1).replace(we,"")}function E(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var z=NaN,Te=/^[-+]0x[0-9a-f]+$/i,je=/^0b[01]+$/i,Ce=/^0o[0-7]+$/i,Se=parseInt;function F(e){if(typeof e=="number")return e;if(pe(e))return z;if(E(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=E(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ke(e);var i=je.test(e);return i||Ce.test(e)?Se(e.slice(2),i?2:8):Te.test(e)?z:+e}var $=function(){return V.Date.now()},Me="Expected a function",Oe=Math.max,_e=Math.min;function Xe(e,t,i){var o,n,c,m,l,f,g=0,a=!1,u=!1,h=!0;if(typeof e!="function")throw new TypeError(Me);t=F(t)||0,E(i)&&(a=!!i.leading,u="maxWait"in i,c=u?Oe(F(i.maxWait)||0,t):c,h="trailing"in i?!!i.trailing:h);function v(r){var b=o,x=n;return o=n=void 0,g=r,m=e.apply(x,b),m}function H(r){return g=r,l=setTimeout(C,t),a?v(r):m}function R(r){var b=r-f,x=r-g,A=t-b;return u?_e(A,c-x):A}function L(r){var b=r-f,x=r-g;return f===void 0||b>=t||b<0||u&&x>=c}function C(){var r=$();if(L(r))return N(r);l=setTimeout(C,R(r))}function N(r){return l=void 0,h&&o?v(r):(o=n=void 0,m)}function X(){l!==void 0&&clearTimeout(l),g=0,o=f=n=l=void 0}function G(){return l===void 0?m:N($())}function _(){var r=$(),b=L(r);if(o=arguments,n=this,f=r,b){if(l===void 0)return H(f);if(u)return clearTimeout(l),l=setTimeout(C,t),v(f)}return l===void 0&&(l=setTimeout(C,t)),m}return _.cancel=X,_.flush=G,_}const T=P([]);function Ge(){const e=o=>{const n=Date.now().toString(),c={id:n,timestamp:Date.now(),duration:5e3,variant:"default",...o};return T.value.push(c),setTimeout(()=>{t(n)},c.duration),n},t=o=>{const n=T.value.findIndex(c=>c.id===o);n>-1&&T.value.splice(n,1)};return{toast:e,toasts:T,removeToast:t,clearAllToasts:()=>{T.value=[]}}}const Ie={class:"flex items-center justify-between p-6 border-b border-gray-200"},Be={class:"p-6"},$e={class:"flex items-start space-x-4"},Ee={class:"flex-1 min-w-0"},Le=["innerHTML"],Ne={key:0,class:"mt-4"},Ae={key:0,class:"mt-3 p-3 bg-gray-50 rounded-md"},De={class:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50"},We=["disabled"],ze=["disabled"],Ue=U({__name:"ConfirmDialog",props:{visible:{type:Boolean},title:{default:"确认操作"},message:{},details:{},type:{default:"info"},confirmText:{default:"确认"},cancelText:{default:"取消"},loadingText:{default:"处理中..."},loading:{type:Boolean,default:!1}},emits:["update:visible","confirm","cancel"],setup(e,{emit:t}){const i=e,o=t,n=P(!1),c=I(()=>{switch(i.type){case"warning":return"text-yellow-600";case"danger":return"text-red-600";case"success":return"text-green-600";default:return"text-blue-600"}}),m=I(()=>{switch(i.type){case"warning":return"bg-yellow-100 text-yellow-600";case"danger":return"bg-red-100 text-red-600";case"success":return"bg-green-100 text-green-600";default:return"bg-blue-100 text-blue-600"}}),l=I(()=>{switch(i.type){case"warning":return"bg-yellow-600 text-white border-yellow-600 hover:bg-yellow-700";case"danger":return"bg-red-600 text-white border-red-600 hover:bg-red-700";case"success":return"bg-green-600 text-white border-green-600 hover:bg-green-700";default:return"bg-blue-600 text-white border-blue-600 hover:bg-blue-700"}});function f(){o("confirm")}function g(){i.loading||(o("update:visible",!1),o("cancel"))}return(a,u)=>a.visible?(d(),y("div",{key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",onClick:g},[s("div",{class:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",onClick:u[1]||(u[1]=Q(()=>{},["stop"]))},[s("div",Ie,[s("h3",{class:S(["text-lg font-semibold text-gray-900",c.value])},w(a.title),3),s("button",{class:"p-2 text-gray-400 hover:text-gray-600 transition-colors",onClick:g},[D(p(ie),{class:"w-5 h-5"})])]),s("div",Be,[s("div",$e,[s("div",{class:S(["flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center",m.value])},[a.type==="warning"?(d(),M(p(re),{key:0,class:"w-6 h-6"})):a.type==="danger"?(d(),M(p(te),{key:1,class:"w-6 h-6"})):a.type==="info"?(d(),M(p(ne),{key:2,class:"w-6 h-6"})):(d(),M(p(Y),{key:3,class:"w-6 h-6"}))],2),s("div",Ee,[s("p",{class:"text-sm text-gray-900",innerHTML:a.message},null,8,Le),a.details?(d(),y("div",Ne,[s("button",{class:"flex items-center text-sm text-blue-600 hover:text-blue-700",onClick:u[0]||(u[0]=h=>n.value=!n.value)},[Z(w(n.value?"隐藏详情":"显示详情")+" ",1),D(p(ee),{class:S(["w-4 h-4 ml-1 transition-transform",{"rotate-180":n.value}])},null,8,["class"])]),n.value?(d(),y("div",Ae,[(d(!0),y(K,null,J(a.details,(h,v)=>(d(),y("p",{key:v,class:"text-sm text-gray-600 mb-1 last:mb-0"},w(h),1))),128))])):B("",!0)])):B("",!0)])])]),s("div",De,[s("button",{class:"inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50",onClick:g,disabled:a.loading},w(a.cancelText),9,We),s("button",{class:S(["inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",l.value]),onClick:f,disabled:a.loading},w(a.loading?a.loadingText:a.confirmText),11,ze)])])])):B("",!0)}});export{re as T,ie as X,Ue as _,qe as a,Re as b,Xe as d,He as r,Ge as u};
