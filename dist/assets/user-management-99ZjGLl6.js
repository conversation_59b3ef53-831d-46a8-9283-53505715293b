import{u as v,v as e,aK as n,bR as O,bS as Pe,aS as k,q as I,M as te,aQ as se,bC as Z,t as D,b9 as u,I as V,bi as l,bM as W,bJ as B,H as F,by as G,at as T,bv as xe,s as q,bx as re,F as K,aV as X,as as De,av as Se,aC as Ve}from"./index-DMDdU1SS.js";import{X as Q,T as Re,d as Ie,u as Ae,r as Ee,a as ze,b as je,_ as Ce}from"./ConfirmDialog.vue_vue_type_script_setup_true_lang-CWUz7rH-.js";import{E as ke,b as $e,a as Me,C as le}from"./eye-DygF8KSj.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as J}from"./createLucideIcon-B64IBOqu.js";import{r as Ue}from"./UsersIcon-DhyLUKGz.js";import"./circle-check-big-BRAv-ZO3.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=J("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=J("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=J("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=J("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=J("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=J("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=J("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);function He(g,a){return n(),v("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"})])}const Ge=g=>O.Get("/users/query",{params:g}),Qe=g=>O.Get(`/users/get/${g}`),Ze=g=>O.Post("/users/create",g),Ke=(g,a)=>O.Put(`/users/update/${g}`,a),Xe=g=>O.Delete(`/users/delete/${g}`),Je=(g,a)=>O.Post(`/users/${g}/reset-password`,a),We=g=>O.Post("/users/batch-operation",g),Ye=g=>O.Get(`/users/${g}/roles`),es=(g,a)=>O.Post(`/users/${g}/assign-role`,a),ss=(g,a)=>O.Delete(`/users/${g}/remove-role/${a}`),ts=()=>O.Get("/roles/available"),as=()=>O.Get("/organizations/tree"),os=()=>O.Get("/users/statistics"),ls=(g,a)=>O.Post("/users/validate-username",{username:g,exclude_user_id:a}),rs=(g,a)=>O.Post("/users/validate-employee-id",{employee_id:g,exclude_user_id:a}),ns=(g,a)=>O.Post("/users/validate-email",{email:g,exclude_user_id:a}),ae=Pe("user",()=>{const g=k([]),a=k(null),c=k([]),A=k([]),P=k([]),f=k(null),$=k(1),U=k(20),o=k(0),i=k(!1),r=k(!1),S=k(!1),E=k(!1),z=k({offset:0,limit:20}),x=I(()=>Math.ceil(o.value/U.value)),b=I(()=>c.value.length>0),w=I(()=>c.value.length>0),j=I(()=>c.value.length>0),h=I(()=>c.value.length>0),p=I(()=>({canCreate:!0,canEdit:!0,canDelete:!0,canResetPassword:!0,canAssignRole:!0,canViewList:!0,canViewDetail:!0,canBatchOperation:!0})),C=async s=>{r.value=!0;try{const d={...z.value,...s,offset:($.value-1)*U.value,limit:U.value},m=await Ge(d);m.code===200&&(g.value=m.data.items,o.value=m.data.total,i.value=m.data.has_more,Object.assign(z.value,d))}catch(d){throw console.error("获取用户列表失败:",d),d}finally{r.value=!1}},L=async s=>{S.value=!0;try{const d=await Qe(s);return d.code===200&&(a.value=d.data),d.data}catch(d){throw console.error("获取用户详情失败:",d),d}finally{S.value=!1}},Y=async s=>{E.value=!0;try{const d=await Ze(s);return d.code===200&&await C(),d}catch(d){throw console.error("创建用户失败:",d),d}finally{E.value=!1}},ee=async(s,d)=>{E.value=!0;try{const m=await Ke(s,d);if(m.code===200){const M=g.value.findIndex(we=>we.user_id===s);M!==-1&&Object.assign(g.value[M],m.data),a.value?.user_id===s&&Object.assign(a.value,m.data)}return m}catch(m){throw console.error("更新用户失败:",m),m}finally{E.value=!1}},N=async s=>{try{const d=await Xe(s);if(d.code===200){const m=g.value.findIndex(we=>we.user_id===s);m!==-1&&(g.value.splice(m,1),o.value--);const M=c.value.indexOf(s);M!==-1&&c.value.splice(M,1)}return d}catch(d){throw console.error("删除用户失败:",d),d}},R=async(s,d)=>{try{return await Je(s,{new_password:d})}catch(m){throw console.error("重置密码失败:",m),m}},_=async s=>{if(c.value.length!==0)try{const d=await We({user_ids:c.value,action:s});if(d.code===200){if(s==="delete")g.value=g.value.filter(m=>!c.value.includes(m.user_id)),o.value-=c.value.length;else{const m=s==="activate"?"ACTIVE":"INACTIVE";g.value.forEach(M=>{c.value.includes(M.user_id)&&(M.status=m)})}c.value=[]}return d}catch(d){throw console.error("批量操作失败:",d),d}},H=async s=>{try{return(await Ye(s)).data}catch(d){throw console.error("获取用户角色失败:",d),d}},ie=async(s,d)=>{try{const m=await es(s,{role_id:d});return m.code===200&&a.value?.user_id===s&&await L(s),m}catch(m){throw console.error("分配角色失败:",m),m}},de=async(s,d)=>{try{const m=await ss(s,d);return m.code===200&&a.value?.user_id===s&&(a.value.roles=a.value.roles.filter(M=>M.role_id!==d)),m}catch(m){throw console.error("移除角色失败:",m),m}},ue=async()=>{try{const s=await ts();return s.code===200&&(A.value=s.data),s.data}catch(s){throw console.error("获取可用角色失败:",s),s}},ce=async()=>{try{const s=await as();return s.code===200&&(P.value=s.data),s.data}catch(s){throw console.error("获取组织架构失败:",s),s}},ve=async()=>{try{const s=await os();return s.code===200&&(f.value=s.data),s.data}catch(s){throw console.error("获取统计信息失败:",s),s}},pe=async(s,d)=>{try{return(await ls(s,d)).data.available}catch(m){return console.error("验证用户名失败:",m),!1}},me=async(s,d)=>{try{return(await rs(s,d)).data.available}catch(m){return console.error("验证员工编号失败:",m),!1}},fe=async(s,d)=>{try{return(await ns(s,d)).data.available}catch(m){return console.error("验证邮箱失败:",m),!1}},ye=s=>{const d=c.value.indexOf(s);d===-1?c.value.push(s):c.value.splice(d,1)},ge=()=>{c.value=g.value.map(s=>s.user_id)},be=()=>{c.value=[]},he=s=>{$.value=s},_e=s=>{U.value=s,$.value=1},oe=s=>{Object.assign(z.value,s),$.value=1},y=()=>{z.value={offset:0,limit:U.value},$.value=1};return{users:g,userDetail:a,selectedUserIds:c,availableRoles:A,organizationTree:P,statistics:f,currentPage:$,pageSize:U,total:o,hasMore:i,loading:r,detailLoading:S,submitting:E,queryParams:z,totalPages:x,hasSelectedUsers:b,canBatchDelete:w,canBatchActivate:j,canBatchDeactivate:h,permissions:p,fetchUsers:C,fetchUserDetail:L,addUser:Y,editUser:ee,removeUser:N,resetPassword:R,batchOperation:_,fetchUserRoles:H,assignRole:ie,removeRole:de,fetchAvailableRoles:ue,fetchOrganizationTree:ce,fetchStatistics:ve,checkUsernameAvailable:pe,checkEmployeeIdAvailable:me,checkEmailAvailable:fe,toggleUserSelection:ye,selectAllUsers:ge,clearSelection:be,setPage:he,setPageSize:_e,setQueryParams:oe,resetQueryParams:y,resetState:()=>{g.value=[],a.value=null,c.value=[],$.value=1,o.value=0,i.value=!1,r.value=!1,S.value=!1,E.value=!1,y()}}}),is={class:"dialog-header"},ds={class:"dialog-title"},us={class:"form-grid"},cs={class:"form-section"},vs={class:"form-field"},ps=["disabled"],ms={key:0,class:"form-error"},fs={key:1,class:"form-help"},ys={key:0,class:"form-field"},gs={class:"password-input"},bs=["type"],hs={key:0,class:"form-error"},_s={class:"form-field"},ws={key:0,class:"form-error"},xs={class:"form-field"},ks={class:"form-section"},$s={class:"form-field"},Cs={key:0,class:"form-error"},Us={class:"form-field"},Ds={key:0,class:"form-error"},Ps={class:"form-field"},Ss=["value"],Vs={class:"form-field"},Rs={class:"dialog-footer"},Is=["disabled"],As=te({__name:"UserFormDialog",props:{visible:{type:Boolean},user:{default:null},organizations:{}},emits:["update:visible","success"],setup(g,{emit:a}){const c=g,A=a,P=ae(),f=k(!1),$=k(!1),U=k(!1),o=se({username:"",password:"",name:"",employee_id:"",email:"",phone:"",department_id:"",status:"ACTIVE"}),i=se({username:"",password:"",name:"",email:"",phone:""}),r=I(()=>!!c.user),S=I(()=>{const h=o.username&&o.name&&(r.value||o.password),p=!Object.values(i).some(C=>C);return h&&p});Z(()=>c.visible,h=>{h&&(E(),c.user&&z(c.user),De(()=>{document.querySelector(".form-input")?.focus()}))}),Z(()=>o.username,()=>{i.username&&(i.username="")});function E(){Object.assign(o,{username:"",password:"",name:"",employee_id:"",email:"",phone:"",department_id:"",status:"ACTIVE"}),Object.assign(i,{username:"",password:"",name:"",email:"",phone:""})}function z(h){Object.assign(o,{username:h.username,password:"",name:h.name,employee_id:h.employee_id||"",email:h.email||"",phone:h.phone||"",department_id:h.department_id||"",status:h.status})}async function x(){if(!(!o.username||r.value)){if(o.username.length<3){i.username="用户名长度至少3位";return}if(!/^[a-zA-Z0-9_]+$/.test(o.username)){i.username="用户名只能包含字母、数字和下划线";return}try{U.value=!0,await P.checkUsernameAvailable(o.username)||(i.username="用户名已存在")}catch(h){console.error("检查用户名失败:",h)}finally{U.value=!1}}}function b(){let h=!0;return o.username?o.username.length<3?(i.username="用户名长度至少3位",h=!1):/^[a-zA-Z0-9_]+$/.test(o.username)||(i.username="用户名只能包含字母、数字和下划线",h=!1):(i.username="请输入用户名",h=!1),r.value||(o.password?o.password.length<8?(i.password="密码长度至少8位",h=!1):/(?=.*[a-zA-Z])(?=.*\d)/.test(o.password)||(i.password="密码必须包含字母和数字",h=!1):(i.password="请输入密码",h=!1)),o.name?o.name.length<2&&(i.name="姓名长度至少2位",h=!1):(i.name="请输入姓名",h=!1),o.email&&(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.email)||(i.email="请输入有效的邮箱地址",h=!1)),o.phone&&(/^1[3-9]\d{9}$/.test(o.phone)||(i.phone="请输入有效的手机号",h=!1)),h}async function w(){if(b())try{if(f.value=!0,r.value&&c.user){const h={name:o.name,employee_id:o.employee_id||void 0,email:o.email||void 0,phone:o.phone||void 0,department_id:o.department_id||void 0,status:o.status};await P.editUser(c.user.user_id,h)}else{const h={username:o.username,password:o.password,name:o.name,employee_id:o.employee_id||void 0,email:o.email||void 0,phone:o.phone||void 0,department_id:o.department_id||void 0,status:o.status};await P.addUser(h)}A("success")}catch(h){console.error("保存用户失败:",h)}finally{f.value=!1}}function j(){A("update:visible",!1)}return(h,p)=>h.visible?(n(),v("div",{key:0,class:"dialog-overlay",onClick:j},[e("div",{class:"dialog",onClick:p[9]||(p[9]=W(()=>{},["stop"]))},[e("div",is,[e("h3",ds,u(r.value?"编辑用户":"新增用户"),1),e("button",{class:"dialog-close",onClick:j},[V(l(Q),{class:"w-5 h-5"})])]),e("form",{onSubmit:W(w,["prevent"]),class:"dialog-body"},[e("div",us,[e("div",cs,[p[15]||(p[15]=e("h4",{class:"section-title"},"基本信息",-1)),e("div",vs,[p[10]||(p[10]=e("label",{class:"form-label",for:"username"},[F(" 用户名 "),e("span",{class:"required"},"*")],-1)),B(e("input",{id:"username","onUpdate:modelValue":p[0]||(p[0]=C=>o.username=C),type:"text",class:T(["form-input",{error:i.username}]),disabled:r.value,placeholder:"请输入用户名",onBlur:x},null,42,ps),[[G,o.username]]),i.username?(n(),v("div",ms,u(i.username),1)):D("",!0),U.value?(n(),v("div",fs," 正在检查用户名... ")):D("",!0)]),r.value?D("",!0):(n(),v("div",ys,[p[11]||(p[11]=e("label",{class:"form-label",for:"password"},[F(" 密码 "),e("span",{class:"required"},"*")],-1)),e("div",gs,[B(e("input",{id:"password","onUpdate:modelValue":p[1]||(p[1]=C=>o.password=C),type:$.value?"text":"password",class:T(["form-input",{error:i.password}]),placeholder:"请输入密码"},null,10,bs),[[xe,o.password]]),e("button",{type:"button",class:"password-toggle",onClick:p[2]||(p[2]=C=>$.value=!$.value)},[$.value?(n(),q(l($e),{key:1,class:"w-4 h-4"})):(n(),q(l(ke),{key:0,class:"w-4 h-4"}))])]),i.password?(n(),v("div",hs,u(i.password),1)):D("",!0),p[12]||(p[12]=e("div",{class:"form-help"}," 密码长度至少8位，包含字母和数字 ",-1))])),e("div",_s,[p[13]||(p[13]=e("label",{class:"form-label",for:"name"},[F(" 姓名 "),e("span",{class:"required"},"*")],-1)),B(e("input",{id:"name","onUpdate:modelValue":p[3]||(p[3]=C=>o.name=C),type:"text",class:T(["form-input",{error:i.name}]),placeholder:"请输入真实姓名"},null,2),[[G,o.name]]),i.name?(n(),v("div",ws,u(i.name),1)):D("",!0)]),e("div",xs,[p[14]||(p[14]=e("label",{class:"form-label",for:"employee_id"}," 员工编号 ",-1)),B(e("input",{id:"employee_id","onUpdate:modelValue":p[4]||(p[4]=C=>o.employee_id=C),type:"text",class:"form-input",placeholder:"请输入员工编号"},null,512),[[G,o.employee_id]])])]),e("div",ks,[p[22]||(p[22]=e("h4",{class:"section-title"},"联系信息",-1)),e("div",$s,[p[16]||(p[16]=e("label",{class:"form-label",for:"email"}," 邮箱 ",-1)),B(e("input",{id:"email","onUpdate:modelValue":p[5]||(p[5]=C=>o.email=C),type:"email",class:T(["form-input",{error:i.email}]),placeholder:"请输入邮箱地址"},null,2),[[G,o.email]]),i.email?(n(),v("div",Cs,u(i.email),1)):D("",!0)]),e("div",Us,[p[17]||(p[17]=e("label",{class:"form-label",for:"phone"}," 手机号 ",-1)),B(e("input",{id:"phone","onUpdate:modelValue":p[6]||(p[6]=C=>o.phone=C),type:"tel",class:T(["form-input",{error:i.phone}]),placeholder:"请输入手机号"},null,2),[[G,o.phone]]),i.phone?(n(),v("div",Ds,u(i.phone),1)):D("",!0)]),e("div",Ps,[p[19]||(p[19]=e("label",{class:"form-label",for:"department"}," 所属部门 ",-1)),B(e("select",{id:"department","onUpdate:modelValue":p[7]||(p[7]=C=>o.department_id=C),class:"form-select"},[p[18]||(p[18]=e("option",{value:""},"请选择部门",-1)),(n(!0),v(K,null,X(h.organizations,C=>(n(),v("option",{key:C.org_id,value:C.org_id},u("　".repeat(C.level))+u(C.org_name),9,Ss))),128))],512),[[re,o.department_id]])]),e("div",Vs,[p[21]||(p[21]=e("label",{class:"form-label",for:"status"}," 状态 ",-1)),B(e("select",{id:"status","onUpdate:modelValue":p[8]||(p[8]=C=>o.status=C),class:"form-select"},p[20]||(p[20]=[e("option",{value:"ACTIVE"},"启用",-1),e("option",{value:"INACTIVE"},"禁用",-1)]),512),[[re,o.status]])])])]),e("div",Rs,[e("button",{type:"button",class:"btn btn-outline",onClick:j}," 取消 "),e("button",{type:"submit",class:"btn btn-primary",disabled:f.value||!S.value},u(f.value?"保存中...":r.value?"保存":"创建"),9,Is)])],32)])])):D("",!0)}}),Es=ne(As,[["__scopeId","data-v-972bebd4"]]),zs={class:"dialog-header"},js={class:"dialog-body"},Ms={key:0,class:"loading-container"},Ts={key:1,class:"user-detail"},Os={class:"detail-section"},Bs={class:"detail-grid"},qs={class:"detail-item"},Fs={class:"detail-value"},Ls={class:"detail-item"},Ns={class:"detail-value"},Hs={class:"detail-item"},Gs={class:"detail-value"},Qs={class:"detail-item"},Zs={class:"detail-value"},Ks={class:"detail-section"},Xs={class:"detail-grid"},Js={class:"detail-item"},Ws={class:"detail-value"},Ys={class:"detail-item"},et={class:"detail-value"},st={class:"detail-item"},tt={class:"detail-value"},at={class:"detail-section"},ot={class:"section-header"},lt={key:0,class:"roles-list"},rt={class:"role-info"},nt={class:"role-name"},it={class:"role-code"},dt={class:"role-date"},ut={key:1,class:"empty-roles"},ct={class:"detail-section"},vt={class:"detail-grid"},pt={class:"detail-item"},mt={class:"detail-value"},ft={class:"detail-item"},yt={class:"detail-value"},gt={class:"detail-item"},bt={class:"detail-value"},ht={key:2,class:"error-container"},_t={class:"dialog-footer"},wt={class:"action-buttons"},xt=te({__name:"UserDetailDialog",props:{visible:{type:Boolean},userId:{}},emits:["update:visible","edit-user","reset-password","manage-roles"],setup(g,{emit:a}){const c=g,A=a,P=ae(),f=k(null),$=I(()=>f.value?f.value.status==="ACTIVE"?"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800":"");Z(()=>c.visible,async i=>{if(i&&c.userId)try{f.value=await P.fetchUserDetail(c.userId)}catch(r){console.error("获取用户详情失败:",r),f.value=null}});function U(i){return new Date(i).toLocaleString("zh-CN")}function o(){A("update:visible",!1),f.value=null}return(i,r)=>i.visible?(n(),v("div",{key:0,class:"dialog-overlay",onClick:o},[e("div",{class:"dialog",onClick:r[3]||(r[3]=W(()=>{},["stop"]))},[e("div",zs,[r[4]||(r[4]=e("h3",{class:"dialog-title"}," 用户详情 ",-1)),e("button",{class:"dialog-close",onClick:o},[V(l(Q),{class:"w-5 h-5"})])]),e("div",js,[l(P).detailLoading?(n(),v("div",Ms,r[5]||(r[5]=[e("div",{class:"loading-spinner"},null,-1),e("p",{class:"loading-text"},"加载中...",-1)]))):f.value?(n(),v("div",Ts,[e("div",Os,[r[10]||(r[10]=e("h4",{class:"section-title"},"基本信息",-1)),e("div",Bs,[e("div",qs,[r[6]||(r[6]=e("label",{class:"detail-label"},"用户名",-1)),e("div",Fs,u(f.value.username),1)]),e("div",Ls,[r[7]||(r[7]=e("label",{class:"detail-label"},"姓名",-1)),e("div",Ns,u(f.value.name),1)]),e("div",Hs,[r[8]||(r[8]=e("label",{class:"detail-label"},"员工编号",-1)),e("div",Gs,u(f.value.employee_id||"-"),1)]),e("div",Qs,[r[9]||(r[9]=e("label",{class:"detail-label"},"状态",-1)),e("div",Zs,[e("span",{class:T($.value)},u(f.value.status==="ACTIVE"?"活跃":"禁用"),3)])])])]),e("div",Ks,[r[14]||(r[14]=e("h4",{class:"section-title"},"联系信息",-1)),e("div",Xs,[e("div",Js,[r[11]||(r[11]=e("label",{class:"detail-label"},"邮箱",-1)),e("div",Ws,u(f.value.email||"-"),1)]),e("div",Ys,[r[12]||(r[12]=e("label",{class:"detail-label"},"手机号",-1)),e("div",et,u(f.value.phone||"-"),1)]),e("div",st,[r[13]||(r[13]=e("label",{class:"detail-label"},"部门",-1)),e("div",tt,u(f.value.department_name||"-"),1)])])]),e("div",at,[e("div",ot,[r[16]||(r[16]=e("h4",{class:"section-title"},"角色信息",-1)),l(P).permissions.canAssignRole?(n(),v("button",{key:0,onClick:r[0]||(r[0]=S=>i.$emit("manage-roles",f.value.user_id)),class:"btn btn-sm btn-outline"},[V(l(Le),{class:"w-4 h-4"}),r[15]||(r[15]=F(" 管理角色 "))])):D("",!0)]),f.value.roles&&f.value.roles.length>0?(n(),v("div",lt,[(n(!0),v(K,null,X(f.value.roles,S=>(n(),v("div",{key:S.role_id,class:"role-item"},[e("div",rt,[e("div",nt,u(S.role_name),1),e("div",it,u(S.role_code),1)]),e("div",dt," 分配时间: "+u(U(S.assigned_date)),1)]))),128))])):(n(),v("div",ut,[V(l(Ne),{class:"w-12 h-12 text-gray-400"}),r[17]||(r[17]=e("p",{class:"empty-text"},"暂无分配角色",-1))]))]),e("div",ct,[r[21]||(r[21]=e("h4",{class:"section-title"},"系统信息",-1)),e("div",vt,[e("div",pt,[r[18]||(r[18]=e("label",{class:"detail-label"},"最后登录",-1)),e("div",mt,u(f.value.last_login_date?U(f.value.last_login_date):"从未登录"),1)]),e("div",ft,[r[19]||(r[19]=e("label",{class:"detail-label"},"创建时间",-1)),e("div",yt,u(U(f.value.created_date)),1)]),e("div",gt,[r[20]||(r[20]=e("label",{class:"detail-label"},"更新时间",-1)),e("div",bt,u(U(f.value.updated_date)),1)])])])])):(n(),v("div",ht,[V(l(Me),{class:"w-12 h-12 text-red-400"}),r[22]||(r[22]=e("p",{class:"error-text"},"加载用户详情失败",-1))]))]),e("div",_t,[e("button",{class:"btn btn-outline",onClick:o}," 关闭 "),e("div",wt,[l(P).permissions.canEdit?(n(),v("button",{key:0,onClick:r[1]||(r[1]=S=>i.$emit("edit-user",f.value)),class:"btn btn-primary"},[V(l(Oe),{class:"w-4 h-4"}),r[23]||(r[23]=F(" 编辑 "))])):D("",!0),l(P).permissions.canResetPassword?(n(),v("button",{key:1,onClick:r[2]||(r[2]=S=>i.$emit("reset-password",f.value)),class:"btn btn-outline"},[V(l(Te),{class:"w-4 h-4"}),r[24]||(r[24]=F(" 重置密码 "))])):D("",!0)])])])])):D("",!0)}}),kt=ne(xt,[["__scopeId","data-v-c5630e38"]]),$t={class:"dialog-header"},Ct={class:"dialog-title"},Ut={class:"password-reset-form"},Dt={class:"user-info"},Pt={class:"info-item"},St={class:"info-value"},Vt={class:"info-item"},Rt={class:"info-value"},It={key:0,class:"info-item"},At={class:"info-value"},Et={class:"password-section"},zt={class:"form-field"},jt={class:"password-input"},Mt=["type"],Tt={key:0,class:"form-error"},Ot={class:"form-field"},Bt={class:"password-input"},qt=["type"],Ft={key:0,class:"form-error"},Lt={class:"password-strength"},Nt={class:"strength-bar"},Ht={class:"password-requirements"},Gt={class:"requirements-list"},Qt={class:"warning-section"},Zt={class:"warning-box"},Kt={class:"dialog-footer"},Xt=["disabled"],Jt=te({__name:"ResetPasswordDialog",props:{visible:{type:Boolean},user:{default:null}},emits:["update:visible","success"],setup(g,{emit:a}){const c=g,A=a,P=ae(),f=k(!1),$=k(!1),U=k(!1),o=se({new_password:"",confirm_password:""}),i=se({new_password:"",confirm_password:""}),r=I(()=>o.new_password.length>=8),S=I(()=>/[a-zA-Z]/.test(o.new_password)),E=I(()=>/\d/.test(o.new_password)),z=I(()=>/[!@#$%^&*(),.?":{}|<>]/.test(o.new_password)),x=I(()=>{let R=0;return r.value&&(R+=25),S.value&&(R+=25),E.value&&(R+=25),z.value&&(R+=25),R}),b=I(()=>x.value),w=I(()=>x.value>=75?"strong":x.value>=50?"medium":x.value>=25?"weak":"very-weak"),j=I(()=>x.value>=75?"强":x.value>=50?"中等":x.value>=25?"弱":"很弱"),h=I(()=>o.new_password&&o.confirm_password&&o.new_password===o.confirm_password&&r.value&&S.value&&E.value&&!i.new_password&&!i.confirm_password);Z(()=>c.visible,R=>{R&&(p(),De(()=>{document.querySelector("#new_password")?.focus()}))}),Z(()=>o.new_password,()=>{C(),o.confirm_password&&L()}),Z(()=>o.confirm_password,()=>{L()});function p(){o.new_password="",o.confirm_password="",i.new_password="",i.confirm_password=""}function C(){return o.new_password?o.new_password.length<8?(i.new_password="密码长度至少8位",!1):S.value?E.value?(i.new_password="",!0):(i.new_password="密码必须包含数字",!1):(i.new_password="密码必须包含字母",!1):(i.new_password="请输入新密码",!1)}function L(){return o.confirm_password?o.new_password!==o.confirm_password?(i.confirm_password="两次输入的密码不一致",!1):(i.confirm_password="",!0):(i.confirm_password="请确认密码",!1)}function Y(){const R=C(),_=L();return R&&_}async function ee(){if(!(!Y()||!c.user))try{f.value=!0,await P.resetPassword(c.user.user_id,o.new_password),A("success"),N()}catch(R){console.error("重置密码失败:",R)}finally{f.value=!1}}function N(){A("update:visible",!1)}return(R,_)=>R.visible?(n(),v("div",{key:0,class:"dialog-overlay",onClick:N},[e("div",{class:"dialog",onClick:_[4]||(_[4]=W(()=>{},["stop"]))},[e("div",$t,[e("h3",Ct," 重置密码 - "+u(R.user?.name)+" ("+u(R.user?.username)+") ",1),e("button",{class:"dialog-close",onClick:N},[V(l(Q),{class:"w-5 h-5"})])]),e("form",{onSubmit:W(ee,["prevent"]),class:"dialog-body"},[e("div",Ut,[e("div",Dt,[e("div",Pt,[_[5]||(_[5]=e("span",{class:"info-label"},"用户名:",-1)),e("span",St,u(R.user?.username),1)]),e("div",Vt,[_[6]||(_[6]=e("span",{class:"info-label"},"姓名:",-1)),e("span",Rt,u(R.user?.name),1)]),R.user?.department_name?(n(),v("div",It,[_[7]||(_[7]=e("span",{class:"info-label"},"部门:",-1)),e("span",At,u(R.user.department_name),1)])):D("",!0)]),e("div",Et,[e("div",zt,[_[8]||(_[8]=e("label",{class:"form-label",for:"new_password"},[F(" 新密码 "),e("span",{class:"required"},"*")],-1)),e("div",jt,[B(e("input",{id:"new_password","onUpdate:modelValue":_[0]||(_[0]=H=>o.new_password=H),type:$.value?"text":"password",class:T(["form-input",{error:i.new_password}]),placeholder:"请输入新密码",autocomplete:"new-password"},null,10,Mt),[[xe,o.new_password]]),e("button",{type:"button",class:"password-toggle",onClick:_[1]||(_[1]=H=>$.value=!$.value)},[$.value?(n(),q(l($e),{key:1,class:"w-4 h-4"})):(n(),q(l(ke),{key:0,class:"w-4 h-4"}))])]),i.new_password?(n(),v("div",Tt,u(i.new_password),1)):D("",!0)]),e("div",Ot,[_[9]||(_[9]=e("label",{class:"form-label",for:"confirm_password"},[F(" 确认密码 "),e("span",{class:"required"},"*")],-1)),e("div",Bt,[B(e("input",{id:"confirm_password","onUpdate:modelValue":_[2]||(_[2]=H=>o.confirm_password=H),type:U.value?"text":"password",class:T(["form-input",{error:i.confirm_password}]),placeholder:"请再次输入新密码",autocomplete:"new-password"},null,10,qt),[[xe,o.confirm_password]]),e("button",{type:"button",class:"password-toggle",onClick:_[3]||(_[3]=H=>U.value=!U.value)},[U.value?(n(),q(l($e),{key:1,class:"w-4 h-4"})):(n(),q(l(ke),{key:0,class:"w-4 h-4"}))])]),i.confirm_password?(n(),v("div",Ft,u(i.confirm_password),1)):D("",!0)]),e("div",Lt,[_[10]||(_[10]=e("div",{class:"strength-label"},"密码强度:",-1)),e("div",Nt,[e("div",{class:T(["strength-fill",w.value]),style:Se({width:b.value+"%"})},null,6)]),e("div",{class:T(["strength-text",w.value])},u(j.value),3)]),e("div",Ht,[_[15]||(_[15]=e("div",{class:"requirements-title"},"密码要求:",-1)),e("ul",Gt,[e("li",{class:T({valid:r.value})},[r.value?(n(),q(l(le),{key:0,class:"w-4 h-4"})):(n(),q(l(Q),{key:1,class:"w-4 h-4"})),_[11]||(_[11]=F(" 至少8个字符 "))],2),e("li",{class:T({valid:S.value})},[S.value?(n(),q(l(le),{key:0,class:"w-4 h-4"})):(n(),q(l(Q),{key:1,class:"w-4 h-4"})),_[12]||(_[12]=F(" 包含字母 "))],2),e("li",{class:T({valid:E.value})},[E.value?(n(),q(l(le),{key:0,class:"w-4 h-4"})):(n(),q(l(Q),{key:1,class:"w-4 h-4"})),_[13]||(_[13]=F(" 包含数字 "))],2),e("li",{class:T({valid:z.value})},[z.value?(n(),q(l(le),{key:0,class:"w-4 h-4"})):(n(),q(l(Q),{key:1,class:"w-4 h-4"})),_[14]||(_[14]=F(" 包含特殊字符(推荐) "))],2)])])]),e("div",Qt,[e("div",Zt,[V(l(Re),{class:"warning-icon"}),_[16]||(_[16]=e("div",{class:"warning-content"},[e("div",{class:"warning-title"},"重要提醒"),e("div",{class:"warning-text"}," 重置密码后，用户需要使用新密码重新登录。请确保及时通知用户新密码。 ")],-1))])])]),e("div",Kt,[e("button",{type:"button",class:"btn btn-outline",onClick:N}," 取消 "),e("button",{type:"submit",class:"btn btn-primary",disabled:f.value||!h.value},u(f.value?"重置中...":"确认重置"),9,Xt)])],32)])])):D("",!0)}}),Wt=ne(Jt,[["__scopeId","data-v-0105ed31"]]),Yt={class:"flex items-center justify-between p-6 border-b border-gray-200"},ea={class:"text-lg font-semibold text-gray-900"},sa={class:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]"},ta={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},aa={class:"space-y-4"},oa={class:"flex items-center justify-between"},la={class:"text-sm text-gray-500"},ra={key:0,class:"space-y-3 max-h-96 overflow-y-auto"},na={class:"flex-1 space-y-1"},ia={class:"font-medium text-gray-900"},da={class:"text-sm text-blue-600 font-mono"},ua={key:0,class:"text-sm text-gray-600"},ca=["onClick","disabled"],va={key:1,class:"text-center py-8"},pa={class:"space-y-4"},ma={class:"flex items-center justify-between"},fa={class:"relative"},ya={key:0,class:"space-y-3 max-h-96 overflow-y-auto"},ga={class:"flex-1 space-y-1"},ba={class:"font-medium text-gray-900"},ha={class:"text-sm text-blue-600 font-mono"},_a={key:0,class:"text-sm text-gray-600"},wa=["onClick","disabled"],xa={key:1,class:"text-center py-8"},ka={key:2,class:"text-center py-8"},$a=te({__name:"UserRoleDialog",props:{visible:{type:Boolean},user:{default:null},roles:{}},emits:["update:visible","success"],setup(g,{emit:a}){const c=g,A=a,P=ae(),f=k(!1),$=k([]),U=k(""),o=I(()=>{const x=$.value.map(b=>b.role_id);return c.roles.filter(b=>!x.includes(b.role_id)&&b.status==="ACTIVE")}),i=I(()=>{if(!U.value)return o.value;const x=U.value.toLowerCase();return o.value.filter(b=>b.role_name.toLowerCase().includes(x)||b.role_code.toLowerCase().includes(x)||b.description?.toLowerCase().includes(x))});Z(()=>c.visible,async x=>{x&&c.user&&await r()});async function r(){if(c.user)try{f.value=!0;const x=await P.fetchUserRoles(c.user.user_id);$.value=x.map(b=>c.roles.find(j=>j.role_id===b.role_id)||{role_id:b.role_id,role_name:b.role_name,role_code:b.role_code,description:"",status:"ACTIVE",created_date:"",updated_date:""})}catch(x){console.error("获取用户角色失败:",x),$.value=[]}finally{f.value=!1}}async function S(x){if(c.user)try{f.value=!0,await P.assignRole(c.user.user_id,x.role_id),$.value.push(x),A("success")}catch(b){console.error("分配角色失败:",b)}finally{f.value=!1}}async function E(x){if(!(!c.user||!confirm(`确定要移除角色"${x.role_name}"吗？`)))try{f.value=!0,await P.removeRole(c.user.user_id,x.role_id);const w=$.value.findIndex(j=>j.role_id===x.role_id);w!==-1&&$.value.splice(w,1),A("success")}catch(w){console.error("移除角色失败:",w)}finally{f.value=!1}}function z(){A("update:visible",!1),U.value=""}return(x,b)=>x.visible?(n(),v("div",{key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",onClick:z},[e("div",{class:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden",onClick:b[1]||(b[1]=W(()=>{},["stop"]))},[e("div",Yt,[e("h3",ea," 角色管理 - "+u(x.user?.name)+" ("+u(x.user?.username)+") ",1),e("button",{class:"p-2 text-gray-400 hover:text-gray-600 transition-colors",onClick:z},[V(l(Q),{class:"w-5 h-5"})])]),e("div",sa,[e("div",ta,[e("div",aa,[e("div",oa,[b[2]||(b[2]=e("h4",{class:"text-base font-medium text-gray-900"},"当前角色",-1)),e("div",la,u($.value.length)+" 个角色 ",1)]),$.value.length>0?(n(),v("div",ra,[(n(!0),v(K,null,X($.value,w=>(n(),v("div",{key:w.role_id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"},[e("div",na,[e("div",ia,u(w.role_name),1),e("div",da,u(w.role_code),1),w.description?(n(),v("div",ua,u(w.description),1)):D("",!0)]),e("button",{class:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",onClick:j=>E(w),disabled:f.value,title:"移除角色"},[V(l(Fe),{class:"w-4 h-4"})],8,ca)]))),128))])):(n(),v("div",va,b[3]||(b[3]=[e("div",{class:"text-4xl mb-3"},"👤",-1),e("div",{class:"text-gray-500"},"该用户暂未分配任何角色",-1)])))]),e("div",pa,[e("div",ma,[b[4]||(b[4]=e("h4",{class:"text-base font-medium text-gray-900"},"可分配角色",-1)),e("div",fa,[V(l(qe),{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),B(e("input",{type:"text",placeholder:"搜索角色...","onUpdate:modelValue":b[0]||(b[0]=w=>U.value=w),class:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-48"},null,512),[[G,U.value]])])]),o.value.length>0?(n(),v("div",ya,[(n(!0),v(K,null,X(i.value,w=>(n(),v("div",{key:w.role_id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"},[e("div",ga,[e("div",ba,u(w.role_name),1),e("div",ha,u(w.role_code),1),w.description?(n(),v("div",_a,u(w.description),1)):D("",!0)]),e("button",{class:"p-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",onClick:j=>S(w),disabled:f.value,title:"分配角色"},[V(l(Be),{class:"w-4 h-4"})],8,wa)]))),128))])):U.value?(n(),v("div",xa,b[5]||(b[5]=[e("div",{class:"text-4xl mb-3"},"🔍",-1),e("div",{class:"text-gray-500"},"没有找到匹配的角色",-1)]))):(n(),v("div",ka,b[6]||(b[6]=[e("div",{class:"text-4xl mb-3"},"✅",-1),e("div",{class:"text-gray-500"},"所有角色都已分配",-1)])))])])]),e("div",{class:"flex items-center justify-end p-6 border-t border-gray-200 bg-gray-50"},[e("button",{class:"inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50",onClick:z}," 关闭 ")])])])):D("",!0)}}),Ca={class:"user-management p-6 space-y-6"},Ua={class:"flex items-center justify-between"},Da={class:"flex items-center space-x-3"},Pa={key:0,class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Sa={class:"bg-white rounded-lg border p-4"},Va={class:"flex items-center space-x-3"},Ra={class:"p-2 bg-blue-100 rounded-lg"},Ia={class:"text-2xl font-bold"},Aa={class:"bg-white rounded-lg border p-4"},Ea={class:"flex items-center space-x-3"},za={class:"p-2 bg-green-100 rounded-lg"},ja={class:"text-2xl font-bold"},Ma={class:"bg-white rounded-lg border p-4"},Ta={class:"flex items-center space-x-3"},Oa={class:"p-2 bg-red-100 rounded-lg"},Ba={class:"text-2xl font-bold"},qa={class:"bg-white rounded-lg border p-4"},Fa={class:"flex items-center space-x-3"},La={class:"p-2 bg-purple-100 rounded-lg"},Na={class:"text-2xl font-bold"},Ha={class:"bg-white rounded-lg border p-4"},Ga={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Qa=["value"],Za={class:"flex items-center justify-between mt-4"},Ka={class:"text-sm text-gray-600"},Xa={key:1,class:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg"},Ja={class:"flex items-center space-x-4"},Wa={class:"text-sm text-blue-700"},Ya={class:"flex items-center space-x-2"},eo=["disabled"],so=["disabled"],to=["disabled"],ao={class:"bg-white rounded-lg border"},oo={class:"relative"},lo={key:0,class:"absolute top-0 left-0 right-0 bottom-0 bg-white bg-opacity-50 flex items-center justify-center z-10"},ro={class:"overflow-x-auto"},no={class:"min-w-full divide-y divide-gray-200"},io={class:"bg-gray-50"},uo={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},co=["checked"],vo={class:"bg-white divide-y divide-gray-200"},po={class:"px-6 py-4 whitespace-nowrap"},mo=["checked","onChange"],fo={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},yo={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},go={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},bo={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ho={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},_o={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},wo={class:"px-6 py-4 whitespace-nowrap"},xo={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ko={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},$o={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Co={class:"flex items-center justify-end space-x-2"},Uo=["onClick"],Do=["onClick"],Po=["onClick"],So=["onClick"],Vo={key:0,class:"text-center py-12"},Ro={class:"mt-6"},Io={key:2,class:"flex items-center justify-between"},Ao={class:"text-sm text-gray-600"},Eo={class:"flex items-center space-x-2"},zo=["value"],jo={class:"flex items-center space-x-1"},Mo=["disabled"],To={class:"flex items-center space-x-1"},Oo=["onClick"],Bo=["disabled"],qo=te({__name:"user-management",setup(g){const a=ae(),{toast:c}=Ae(),A=k(null),P=k(!1),f=k(!1),$=k(!1),U=k(!1),o=k(!1),i=k(!1),r=k(null),S=k(""),E=k(null),z=k(null),x=k(null),b=k("create"),w=se({username:"",name:"",department_id:"",status:""}),j=I(()=>{const y=(t,s=0)=>{const d=[];for(const m of t)d.push({...m,level:s}),m.children&&m.children.length>0&&d.push(...y(m.children,s+1));return d};return y(a.organizationTree)}),h=I(()=>a.users.length>0&&a.users.every(y=>a.selectedUserIds.includes(y.user_id))),p=I(()=>{const y=a.currentPage,t=a.totalPages,s=2,d=[],m=[];for(let M=Math.max(2,y-s);M<=Math.min(t-1,y+s);M++)d.push(M);return y-s>2?m.push(1,"..."):m.push(1),m.push(...d),y+s<t-1?m.push("...",t):t>1&&m.push(t),m.filter(M=>M!==1||t===1)}),C=Ie(()=>{L()},500),L=async()=>{const y=Object.fromEntries(Object.entries(w).filter(([t,s])=>s!==""));a.setQueryParams(y),await a.fetchUsers()},Y=async()=>{Object.assign(w,{username:"",name:"",department_id:"",status:""}),a.resetQueryParams(),await a.fetchUsers()},ee=y=>{y.target.checked?a.selectAllUsers():a.clearSelection()},N=async y=>{a.setPage(y),await a.fetchUsers()},R=async y=>{const t=y.target.value;a.setPageSize(parseInt(t)),await a.fetchUsers()},_=y=>{S.value=y.user_id,f.value=!0},H=y=>{r.value=y,b.value="edit",P.value=!0},ie=y=>{z.value=y,U.value=!0},de=y=>{E.value=y,$.value=!0},ue=async()=>{try{await a.batchOperation("activate"),c({title:"操作成功",description:"用户已批量启用"})}catch{c({title:"操作失败",description:"批量启用用户失败",variant:"destructive"})}},ce=async()=>{try{await a.batchOperation("deactivate"),c({title:"操作成功",description:"用户已批量禁用"})}catch{c({title:"操作失败",description:"批量禁用用户失败",variant:"destructive"})}},ve=()=>{o.value=!0},pe=async()=>{if(z.value)try{await a.removeUser(z.value.user_id),U.value=!1,z.value=null,c({title:"删除成功",description:"用户已删除",variant:"success"})}catch{c({title:"删除失败",description:"删除用户失败",variant:"destructive"})}},me=async()=>{try{await a.batchOperation("delete"),o.value=!1,c({title:"删除成功",description:"用户已批量删除",variant:"success"})}catch{c({title:"删除失败",description:"批量删除用户失败",variant:"destructive"})}},fe=async()=>{const y=b.value==="edit";P.value=!1,r.value=null,b.value="create",c({title:"操作成功",description:y?"用户更新成功":"用户创建成功",variant:"success"}),await a.fetchUsers()},ye=()=>{$.value=!1,E.value=null,c({title:"重置成功",description:"密码已重置",variant:"success"})},ge=()=>{i.value=!1,x.value=null,c({title:"操作成功",description:"角色分配已更新",variant:"success"})},be=y=>{f.value=!1,r.value=y,b.value="edit",P.value=!0},he=y=>{f.value=!1,E.value=y,$.value=!0},_e=y=>{const t=a.users.find(s=>s.user_id===y);t&&(f.value=!1,x.value=t,i.value=!0)},oe=y=>new Date(y).toLocaleString("zh-CN");return Z(P,y=>{y||(r.value=null,b.value="create")}),Ve(async()=>{try{await Promise.all([a.fetchUsers(),a.fetchOrganizationTree(),a.fetchAvailableRoles(),a.fetchStatistics().then(y=>A.value=y)])}catch(y){console.error("初始化用户管理页面失败:",y),c({title:"加载失败",description:"初始化页面数据失败",variant:"destructive"})}}),(y,t)=>(n(),v("div",Ca,[e("div",Ua,[t[18]||(t[18]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"用户管理"),e("p",{class:"text-gray-600"},"管理系统用户账户、角色和权限")],-1)),e("div",Da,[l(a).permissions.canCreate?(n(),v("button",{key:0,onClick:t[0]||(t[0]=s=>P.value=!0),class:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},[V(l(Ee),{class:"h-4 w-4"}),t[17]||(t[17]=e("span",null,"新增用户",-1))])):D("",!0)])]),A.value?(n(),v("div",Pa,[e("div",Sa,[e("div",Va,[e("div",Ra,[V(l(Ue),{class:"h-6 w-6 text-blue-600"})]),e("div",null,[t[19]||(t[19]=e("p",{class:"text-sm text-gray-600"},"总用户数",-1)),e("p",Ia,u(A.value.total_users),1)])])]),e("div",Aa,[e("div",Ea,[e("div",za,[V(l(ze),{class:"h-6 w-6 text-green-600"})]),e("div",null,[t[20]||(t[20]=e("p",{class:"text-sm text-gray-600"},"活跃用户",-1)),e("p",ja,u(A.value.active_users),1)])])]),e("div",Ma,[e("div",Ta,[e("div",Oa,[V(l(je),{class:"h-6 w-6 text-red-600"})]),e("div",null,[t[21]||(t[21]=e("p",{class:"text-sm text-gray-600"},"禁用用户",-1)),e("p",Ba,u(A.value.inactive_users),1)])])]),e("div",qa,[e("div",Fa,[e("div",La,[V(l(He),{class:"h-6 w-6 text-purple-600"})]),e("div",null,[t[22]||(t[22]=e("p",{class:"text-sm text-gray-600"},"部门数量",-1)),e("p",Na,u(A.value.users_by_department?.length||0),1)])])])])):D("",!0),e("div",Ha,[e("div",Ga,[e("div",null,[t[23]||(t[23]=e("label",{for:"search-username",class:"block text-sm font-medium text-gray-700"},"用户名",-1)),B(e("input",{id:"search-username","onUpdate:modelValue":t[1]||(t[1]=s=>w.username=s),placeholder:"请输入用户名",onInput:t[2]||(t[2]=(...s)=>l(C)&&l(C)(...s)),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,544),[[G,w.username]])]),e("div",null,[t[24]||(t[24]=e("label",{for:"search-name",class:"block text-sm font-medium text-gray-700"},"姓名",-1)),B(e("input",{id:"search-name","onUpdate:modelValue":t[3]||(t[3]=s=>w.name=s),placeholder:"请输入姓名",onInput:t[4]||(t[4]=(...s)=>l(C)&&l(C)(...s)),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,544),[[G,w.name]])]),e("div",null,[t[26]||(t[26]=e("label",{for:"search-department",class:"block text-sm font-medium text-gray-700"},"部门",-1)),B(e("select",{"onUpdate:modelValue":t[5]||(t[5]=s=>w.department_id=s),onChange:L,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},[t[25]||(t[25]=e("option",{value:""},"全部部门",-1)),(n(!0),v(K,null,X(j.value,s=>(n(),v("option",{key:s.org_id,value:s.org_id},u("　".repeat(s.level))+u(s.org_name),9,Qa))),128))],544),[[re,w.department_id]])]),e("div",null,[t[28]||(t[28]=e("label",{for:"search-status",class:"block text-sm font-medium text-gray-700"},"状态",-1)),B(e("select",{"onUpdate:modelValue":t[6]||(t[6]=s=>w.status=s),onChange:L,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"},t[27]||(t[27]=[e("option",{value:""},"全部状态",-1),e("option",{value:"ACTIVE"},"活跃",-1),e("option",{value:"INACTIVE"},"禁用",-1)]),544),[[re,w.status]])])]),e("div",Za,[e("div",{class:"flex items-center space-x-2"},[e("button",{onClick:Y,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"}," 重置 ")]),e("div",Ka," 共 "+u(l(a).total)+" 条记录 ",1)])]),l(a).hasSelectedUsers?(n(),v("div",Xa,[e("div",Ja,[e("span",Wa," 已选择 "+u(l(a).selectedUserIds.length)+" 个用户 ",1),e("button",{onClick:t[7]||(t[7]=(...s)=>l(a).clearSelection&&l(a).clearSelection(...s)),class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"}," 取消选择 ")]),e("div",Ya,[l(a).permissions.canBatchOperation?(n(),v("button",{key:0,onClick:ue,disabled:!l(a).canBatchActivate,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"}," 批量启用 ",8,eo)):D("",!0),l(a).permissions.canBatchOperation?(n(),v("button",{key:1,onClick:ce,disabled:!l(a).canBatchDeactivate,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"}," 批量禁用 ",8,so)):D("",!0),l(a).permissions.canDelete?(n(),v("button",{key:2,onClick:ve,disabled:!l(a).canBatchDelete,class:"px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"}," 批量删除 ",8,to)):D("",!0)])])):D("",!0),e("div",ao,[e("div",oo,[l(a).loading?(n(),v("div",lo,t[29]||(t[29]=[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),e("span",{class:"text-gray-600"},"加载中...")],-1)]))):D("",!0),e("div",ro,[e("table",no,[e("thead",io,[e("tr",null,[e("th",uo,[e("input",{type:"checkbox",checked:h.value,onChange:ee,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,co)]),t[30]||(t[30]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"用户名",-1)),t[31]||(t[31]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"姓名",-1)),t[32]||(t[32]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"员工编号",-1)),t[33]||(t[33]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"邮箱",-1)),t[34]||(t[34]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"手机",-1)),t[35]||(t[35]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"部门",-1)),t[36]||(t[36]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"状态",-1)),t[37]||(t[37]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"最后登录",-1)),t[38]||(t[38]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"创建时间",-1)),t[39]||(t[39]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"操作",-1))])]),e("tbody",vo,[(n(!0),v(K,null,X(l(a).users,s=>(n(),v("tr",{key:s.user_id,class:"hover:bg-gray-50"},[e("td",po,[e("input",{type:"checkbox",checked:l(a).selectedUserIds.includes(s.user_id),onChange:d=>l(a).toggleUserSelection(s.user_id),class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,mo)]),e("td",fo,u(s.username),1),e("td",yo,u(s.name),1),e("td",go,u(s.employee_id||"-"),1),e("td",bo,u(s.email||"-"),1),e("td",ho,u(s.phone||"-"),1),e("td",_o,u(s.department_name||"-"),1),e("td",wo,[e("span",{class:T(s.status==="ACTIVE"?"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800")},u(s.status==="ACTIVE"?"活跃":"禁用"),3)]),e("td",xo,u(s.last_login_date?oe(s.last_login_date):"-"),1),e("td",ko,u(oe(s.created_date)),1),e("td",$o,[e("div",Co,[l(a).permissions.canViewDetail?(n(),v("button",{key:0,onClick:d=>_(s),class:"text-blue-600 hover:text-blue-900 text-sm"}," 查看 ",8,Uo)):D("",!0),l(a).permissions.canEdit?(n(),v("button",{key:1,onClick:d=>H(s),class:"text-blue-600 hover:text-blue-900 text-sm"}," 编辑 ",8,Do)):D("",!0),l(a).permissions.canResetPassword?(n(),v("button",{key:2,onClick:d=>de(s),class:"text-orange-600 hover:text-orange-900 text-sm"}," 重置密码 ",8,Po)):D("",!0),l(a).permissions.canDelete?(n(),v("button",{key:3,onClick:d=>ie(s),class:"text-red-600 hover:text-red-900 text-sm"}," 删除 ",8,So)):D("",!0)])])]))),128))])]),!l(a).loading&&l(a).users.length===0?(n(),v("div",Vo,[V(l(Ue),{class:"mx-auto h-12 w-12 text-gray-400"}),t[40]||(t[40]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无用户",-1)),t[41]||(t[41]=e("p",{class:"mt-1 text-sm text-gray-500"},"开始创建第一个用户吧",-1)),e("div",Ro,[e("button",{onClick:t[8]||(t[8]=s=>P.value=!0),class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},"新增用户")])])):D("",!0)])])]),l(a).total>0?(n(),v("div",Io,[e("div",Ao," 显示第 "+u((l(a).currentPage-1)*l(a).pageSize+1)+" - "+u(Math.min(l(a).currentPage*l(a).pageSize,l(a).total))+" 条， 共 "+u(l(a).total)+" 条记录 ",1),e("div",Eo,[e("select",{value:l(a).pageSize.toString(),onChange:R,class:"px-3 py-2 border border-gray-300 rounded-md text-sm"},t[42]||(t[42]=[e("option",{value:"10"},"10",-1),e("option",{value:"20"},"20",-1),e("option",{value:"50"},"50",-1),e("option",{value:"100"},"100",-1)]),40,zo),e("div",jo,[e("button",{disabled:l(a).currentPage<=1,onClick:t[9]||(t[9]=s=>N(l(a).currentPage-1)),class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,Mo),e("div",To,[(n(!0),v(K,null,X(p.value,s=>(n(),v("button",{key:s,class:T(s===l(a).currentPage?"px-3 py-2 text-sm bg-blue-600 text-white rounded-md":"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"),onClick:d=>N(Number(s))},u(s),11,Oo))),128))]),e("button",{disabled:l(a).currentPage>=l(a).totalPages,onClick:t[10]||(t[10]=s=>N(l(a).currentPage+1)),class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,Bo)])])])):D("",!0),V(Es,{visible:P.value,"onUpdate:visible":t[11]||(t[11]=s=>P.value=s),user:r.value,organizations:j.value,onSuccess:fe},null,8,["visible","user","organizations"]),V(kt,{visible:f.value,"onUpdate:visible":t[12]||(t[12]=s=>f.value=s),"user-id":S.value,onEditUser:be,onResetPassword:he,onManageRoles:_e},null,8,["visible","user-id"]),V(Wt,{visible:$.value,"onUpdate:visible":t[13]||(t[13]=s=>$.value=s),user:E.value,onSuccess:ye},null,8,["visible","user"]),V($a,{visible:i.value,"onUpdate:visible":t[14]||(t[14]=s=>i.value=s),user:x.value,roles:l(a).availableRoles,onSuccess:ge},null,8,["visible","user","roles"]),V(Ce,{visible:U.value,"onUpdate:visible":t[15]||(t[15]=s=>U.value=s),type:"danger",title:"确认删除用户",message:`确定要删除用户 ${z.value?.name} 吗？`,details:["此操作不可撤销","用户的所有数据将被永久删除"],"confirm-text":"删除",loading:l(a).submitting,onConfirm:pe},null,8,["visible","message","loading"]),V(Ce,{visible:o.value,"onUpdate:visible":t[16]||(t[16]=s=>o.value=s),type:"danger",title:"确认批量删除",message:`确定要删除选中的 ${l(a).selectedUserIds.length} 个用户吗？`,details:["此操作不可撤销","所有选中用户的数据将被永久删除"],"confirm-text":"批量删除",loading:l(a).submitting,onConfirm:me},null,8,["visible","message","loading"])]))}}),Ko=ne(qo,[["__scopeId","data-v-c91c34f6"]]);export{Ko as default};
