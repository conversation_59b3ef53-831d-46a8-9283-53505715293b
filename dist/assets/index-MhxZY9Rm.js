import{F as _e,M as ie,C as je,ar as Le,o as Oe,a5 as ee,s as Fe,bH as Be,at as We,bi as re,aK as $e,aW as Ue}from"./index-DMDdU1SS.js";function ze(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=ze(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Ce(){for(var e,t,r=0,o="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=ze(e))&&(o&&(o+=" "),o+=t);return o}const he=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,xe=Ce,qe=(e,t)=>r=>{var o;if(t?.variants==null)return xe(e,r?.class,r?.className);const{variants:a,defaultVariants:i}=t,c=Object.keys(a).map(f=>{const g=r?.[f],v=i?.[f];if(g===null)return null;const z=he(g)||he(v);return a[f][z]}),p=r&&Object.entries(r).reduce((f,g)=>{let[v,z]=g;return z===void 0||(f[v]=z),f},{}),m=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((f,g)=>{let{class:v,className:z,...I}=g;return Object.entries(I).every(y=>{let[k,C]=y;return Array.isArray(C)?C.includes({...i,...p}[k]):{...i,...p}[k]===C})?[...f,v,z]:f},[]);return xe(e,c,m,r?.class,r?.className)};function Se(e){return e?e.flatMap(t=>t.type===_e?Se(t.children):[t]):[]}const He=ie({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:t,slots:r}){return()=>{if(!r.default)return null;const o=Se(r.default()),a=o.findIndex(m=>m.type!==je);if(a===-1)return o;const i=o[a];delete i.props?.ref;const c=i.props?Le(t,i.props):t,p=Oe({...i,props:{}},c);return o.length===1?p:(o[a]=p,o)}}}),Ke=["area","img","input"],Je=ie({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:r}){const o=e.asChild?"template":e.as;return typeof o=="string"&&Ke.includes(o)?()=>ee(o,t):o!=="template"?()=>ee(e.as,t,{default:r.default}):()=>ee(He,t,{default:r.default})}}),le="-",Xe=e=>{const t=Qe(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:c=>{const p=c.split(le);return p[0]===""&&p.length!==1&&p.shift(),Ae(p,t)||De(c)},getConflictingClassGroupIds:(c,p)=>{const m=r[c]||[];return p&&o[c]?[...m,...o[c]]:m}}},Ae=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),a=o?Ae(e.slice(1),o):void 0;if(a)return a;if(t.validators.length===0)return;const i=e.join(le);return t.validators.find(({validator:c})=>c(i))?.classGroupId},ve=/^\[(.+)\]$/,De=e=>{if(ve.test(e)){const t=ve.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Qe=e=>{const{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(const a in r)se(r[a],o,a,t);return o},se=(e,t,r,o)=>{e.forEach(a=>{if(typeof a=="string"){const i=a===""?t:ye(t,a);i.classGroupId=r;return}if(typeof a=="function"){if(Ye(a)){se(a(o),t,r,o);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([i,c])=>{se(c,ye(t,i),r,o)})})},ye=(e,t)=>{let r=e;return t.split(le).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},Ye=e=>e.isThemeGetter,Ze=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const a=(i,c)=>{r.set(i,c),t++,t>e&&(t=0,o=r,r=new Map)};return{get(i){let c=r.get(i);if(c!==void 0)return c;if((c=o.get(i))!==void 0)return a(i,c),c},set(i,c){r.has(i)?r.set(i,c):a(i,c)}}},ne="!",ae=":",er=ae.length,rr=e=>{const{prefix:t,experimentalParseClassName:r}=e;let o=a=>{const i=[];let c=0,p=0,m=0,f;for(let y=0;y<a.length;y++){let k=a[y];if(c===0&&p===0){if(k===ae){i.push(a.slice(m,y)),m=y+er;continue}if(k==="/"){f=y;continue}}k==="["?c++:k==="]"?c--:k==="("?p++:k===")"&&p--}const g=i.length===0?a:a.substring(m),v=or(g),z=v!==g,I=f&&f>m?f-m:void 0;return{modifiers:i,hasImportantModifier:z,baseClassName:v,maybePostfixModifierPosition:I}};if(t){const a=t+ae,i=o;o=c=>c.startsWith(a)?i(c.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:c,maybePostfixModifierPosition:void 0}}if(r){const a=o;o=i=>r({className:i,parseClassName:a})}return o},or=e=>e.endsWith(ne)?e.substring(0,e.length-1):e.startsWith(ne)?e.substring(1):e,tr=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const a=[];let i=[];return o.forEach(c=>{c[0]==="["||t[c]?(a.push(...i.sort(),c),i=[]):i.push(c)}),a.push(...i.sort()),a}},sr=e=>({cache:Ze(e.cacheSize),parseClassName:rr(e),sortModifiers:tr(e),...Xe(e)}),nr=/\s+/,ar=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:i}=t,c=[],p=e.trim().split(nr);let m="";for(let f=p.length-1;f>=0;f-=1){const g=p[f],{isExternal:v,modifiers:z,hasImportantModifier:I,baseClassName:y,maybePostfixModifierPosition:k}=r(g);if(v){m=g+(m.length>0?" "+m:m);continue}let C=!!k,R=o(C?y.substring(0,k):y);if(!R){if(!C){m=g+(m.length>0?" "+m:m);continue}if(R=o(y),!R){m=g+(m.length>0?" "+m:m);continue}C=!1}const $=i(z).join(":"),F=I?$+ne:$,V=F+R;if(c.includes(V))continue;c.push(V);const E=a(R,C);for(let G=0;G<E.length;++G){const B=E[G];c.push(F+B)}m=g+(m.length>0?" "+m:m)}return m};function ir(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Me(t))&&(o&&(o+=" "),o+=r);return o}const Me=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=Me(e[o]))&&(r&&(r+=" "),r+=t);return r};function lr(e,...t){let r,o,a,i=c;function c(m){const f=t.reduce((g,v)=>v(g),e());return r=sr(f),o=r.cache.get,a=r.cache.set,i=p,p(m)}function p(m){const f=o(m);if(f)return f;const g=ar(m,r);return a(m,g),g}return function(){return i(ir.apply(null,arguments))}}const b=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Pe=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ie=/^\((?:(\w[\w-]*):)?(.+)\)$/i,cr=/^\d+\/\d+$/,dr=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,mr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ur=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,pr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,j=e=>cr.test(e),u=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),oe=e=>e.endsWith("%")&&u(e.slice(0,-1)),M=e=>dr.test(e),gr=()=>!0,br=e=>mr.test(e)&&!ur.test(e),Re=()=>!1,hr=e=>pr.test(e),xr=e=>fr.test(e),vr=e=>!s(e)&&!n(e),yr=e=>L(e,Te,Re),s=e=>Pe.test(e),T=e=>L(e,Ve,br),te=e=>L(e,Sr,u),ke=e=>L(e,Ge,Re),kr=e=>L(e,Ne,xr),X=e=>L(e,Ee,hr),n=e=>Ie.test(e),W=e=>O(e,Ve),wr=e=>O(e,Ar),we=e=>O(e,Ge),zr=e=>O(e,Te),Cr=e=>O(e,Ne),D=e=>O(e,Ee,!0),L=(e,t,r)=>{const o=Pe.exec(e);return o?o[1]?t(o[1]):r(o[2]):!1},O=(e,t,r=!1)=>{const o=Ie.exec(e);return o?o[1]?t(o[1]):r:!1},Ge=e=>e==="position"||e==="percentage",Ne=e=>e==="image"||e==="url",Te=e=>e==="length"||e==="size"||e==="bg-size",Ve=e=>e==="length",Sr=e=>e==="number",Ar=e=>e==="family-name",Ee=e=>e==="shadow",Mr=()=>{const e=b("color"),t=b("font"),r=b("text"),o=b("font-weight"),a=b("tracking"),i=b("leading"),c=b("breakpoint"),p=b("container"),m=b("spacing"),f=b("radius"),g=b("shadow"),v=b("inset-shadow"),z=b("text-shadow"),I=b("drop-shadow"),y=b("blur"),k=b("perspective"),C=b("aspect"),R=b("ease"),$=b("animate"),F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...V(),n,s],G=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],d=()=>[n,s,m],S=()=>[j,"full","auto",...d()],ce=()=>[P,"none","subgrid",n,s],de=()=>["auto",{span:["full",P,n,s]},P,n,s],U=()=>[P,"auto",n,s],me=()=>["auto","min","max","fr",n,s],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...d()],N=()=>[j,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...d()],l=()=>[e,n,s],ue=()=>[...V(),we,ke,{position:[n,s]}],pe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],fe=()=>["auto","cover","contain",zr,yr,{size:[n,s]}],Y=()=>[oe,W,T],x=()=>["","none","full",f,n,s],w=()=>["",u,W,T],q=()=>["solid","dashed","dotted","double"],ge=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],h=()=>[u,oe,we,ke],be=()=>["","none",y,n,s],H=()=>["none",u,n,s],K=()=>["none",u,n,s],Z=()=>[u,n,s],J=()=>[j,"full",...d()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[M],breakpoint:[M],color:[gr],container:[M],"drop-shadow":[M],ease:["in","out","in-out"],font:[vr],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[M],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[M],shadow:[M],spacing:["px",u],text:[M],"text-shadow":[M],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",j,s,n,C]}],container:["container"],columns:[{columns:[u,s,n,p]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:G()}],"overflow-x":[{"overflow-x":G()}],"overflow-y":[{"overflow-y":G()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",n,s]}],basis:[{basis:[j,"full","auto",p,...d()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[u,j,"auto","initial","none",s]}],grow:[{grow:["",u,n,s]}],shrink:[{shrink:["",u,n,s]}],order:[{order:[P,"first","last","none",n,s]}],"grid-cols":[{"grid-cols":ce()}],"col-start-end":[{col:de()}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":ce()}],"row-start-end":[{row:de()}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":me()}],"auto-rows":[{"auto-rows":me()}],gap:[{gap:d()}],"gap-x":[{"gap-x":d()}],"gap-y":[{"gap-y":d()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:d()}],px:[{px:d()}],py:[{py:d()}],ps:[{ps:d()}],pe:[{pe:d()}],pt:[{pt:d()}],pr:[{pr:d()}],pb:[{pb:d()}],pl:[{pl:d()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":d()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":d()}],"space-y-reverse":["space-y-reverse"],size:[{size:N()}],w:[{w:[p,"screen",...N()]}],"min-w":[{"min-w":[p,"screen","none",...N()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[c]},...N()]}],h:[{h:["screen","lh",...N()]}],"min-h":[{"min-h":["screen","lh","none",...N()]}],"max-h":[{"max-h":["screen","lh",...N()]}],"font-size":[{text:["base",r,W,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,n,te]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",oe,s]}],"font-family":[{font:[wr,s,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,n,s]}],"line-clamp":[{"line-clamp":[u,"none",n,te]}],leading:[{leading:[i,...d()]}],"list-image":[{"list-image":["none",n,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",n,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:l()}],"text-color":[{text:l()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[u,"from-font","auto",n,T]}],"text-decoration-color":[{decoration:l()}],"underline-offset":[{"underline-offset":[u,"auto",n,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:d()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",n,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",n,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ue()}],"bg-repeat":[{bg:pe()}],"bg-size":[{bg:fe()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,n,s],radial:["",n,s],conic:[P,n,s]},Cr,kr]}],"bg-color":[{bg:l()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:l()}],"gradient-via":[{via:l()}],"gradient-to":[{to:l()}],rounded:[{rounded:x()}],"rounded-s":[{"rounded-s":x()}],"rounded-e":[{"rounded-e":x()}],"rounded-t":[{"rounded-t":x()}],"rounded-r":[{"rounded-r":x()}],"rounded-b":[{"rounded-b":x()}],"rounded-l":[{"rounded-l":x()}],"rounded-ss":[{"rounded-ss":x()}],"rounded-se":[{"rounded-se":x()}],"rounded-ee":[{"rounded-ee":x()}],"rounded-es":[{"rounded-es":x()}],"rounded-tl":[{"rounded-tl":x()}],"rounded-tr":[{"rounded-tr":x()}],"rounded-br":[{"rounded-br":x()}],"rounded-bl":[{"rounded-bl":x()}],"border-w":[{border:w()}],"border-w-x":[{"border-x":w()}],"border-w-y":[{"border-y":w()}],"border-w-s":[{"border-s":w()}],"border-w-e":[{"border-e":w()}],"border-w-t":[{"border-t":w()}],"border-w-r":[{"border-r":w()}],"border-w-b":[{"border-b":w()}],"border-w-l":[{"border-l":w()}],"divide-x":[{"divide-x":w()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":w()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:l()}],"border-color-x":[{"border-x":l()}],"border-color-y":[{"border-y":l()}],"border-color-s":[{"border-s":l()}],"border-color-e":[{"border-e":l()}],"border-color-t":[{"border-t":l()}],"border-color-r":[{"border-r":l()}],"border-color-b":[{"border-b":l()}],"border-color-l":[{"border-l":l()}],"divide-color":[{divide:l()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[u,n,s]}],"outline-w":[{outline:["",u,W,T]}],"outline-color":[{outline:l()}],shadow:[{shadow:["","none",g,D,X]}],"shadow-color":[{shadow:l()}],"inset-shadow":[{"inset-shadow":["none",v,D,X]}],"inset-shadow-color":[{"inset-shadow":l()}],"ring-w":[{ring:w()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:l()}],"ring-offset-w":[{"ring-offset":[u,T]}],"ring-offset-color":[{"ring-offset":l()}],"inset-ring-w":[{"inset-ring":w()}],"inset-ring-color":[{"inset-ring":l()}],"text-shadow":[{"text-shadow":["none",z,D,X]}],"text-shadow-color":[{"text-shadow":l()}],opacity:[{opacity:[u,n,s]}],"mix-blend":[{"mix-blend":[...ge(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ge()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[u]}],"mask-image-linear-from-pos":[{"mask-linear-from":h()}],"mask-image-linear-to-pos":[{"mask-linear-to":h()}],"mask-image-linear-from-color":[{"mask-linear-from":l()}],"mask-image-linear-to-color":[{"mask-linear-to":l()}],"mask-image-t-from-pos":[{"mask-t-from":h()}],"mask-image-t-to-pos":[{"mask-t-to":h()}],"mask-image-t-from-color":[{"mask-t-from":l()}],"mask-image-t-to-color":[{"mask-t-to":l()}],"mask-image-r-from-pos":[{"mask-r-from":h()}],"mask-image-r-to-pos":[{"mask-r-to":h()}],"mask-image-r-from-color":[{"mask-r-from":l()}],"mask-image-r-to-color":[{"mask-r-to":l()}],"mask-image-b-from-pos":[{"mask-b-from":h()}],"mask-image-b-to-pos":[{"mask-b-to":h()}],"mask-image-b-from-color":[{"mask-b-from":l()}],"mask-image-b-to-color":[{"mask-b-to":l()}],"mask-image-l-from-pos":[{"mask-l-from":h()}],"mask-image-l-to-pos":[{"mask-l-to":h()}],"mask-image-l-from-color":[{"mask-l-from":l()}],"mask-image-l-to-color":[{"mask-l-to":l()}],"mask-image-x-from-pos":[{"mask-x-from":h()}],"mask-image-x-to-pos":[{"mask-x-to":h()}],"mask-image-x-from-color":[{"mask-x-from":l()}],"mask-image-x-to-color":[{"mask-x-to":l()}],"mask-image-y-from-pos":[{"mask-y-from":h()}],"mask-image-y-to-pos":[{"mask-y-to":h()}],"mask-image-y-from-color":[{"mask-y-from":l()}],"mask-image-y-to-color":[{"mask-y-to":l()}],"mask-image-radial":[{"mask-radial":[n,s]}],"mask-image-radial-from-pos":[{"mask-radial-from":h()}],"mask-image-radial-to-pos":[{"mask-radial-to":h()}],"mask-image-radial-from-color":[{"mask-radial-from":l()}],"mask-image-radial-to-color":[{"mask-radial-to":l()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":V()}],"mask-image-conic-pos":[{"mask-conic":[u]}],"mask-image-conic-from-pos":[{"mask-conic-from":h()}],"mask-image-conic-to-pos":[{"mask-conic-to":h()}],"mask-image-conic-from-color":[{"mask-conic-from":l()}],"mask-image-conic-to-color":[{"mask-conic-to":l()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ue()}],"mask-repeat":[{mask:pe()}],"mask-size":[{mask:fe()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",n,s]}],filter:[{filter:["","none",n,s]}],blur:[{blur:be()}],brightness:[{brightness:[u,n,s]}],contrast:[{contrast:[u,n,s]}],"drop-shadow":[{"drop-shadow":["","none",I,D,X]}],"drop-shadow-color":[{"drop-shadow":l()}],grayscale:[{grayscale:["",u,n,s]}],"hue-rotate":[{"hue-rotate":[u,n,s]}],invert:[{invert:["",u,n,s]}],saturate:[{saturate:[u,n,s]}],sepia:[{sepia:["",u,n,s]}],"backdrop-filter":[{"backdrop-filter":["","none",n,s]}],"backdrop-blur":[{"backdrop-blur":be()}],"backdrop-brightness":[{"backdrop-brightness":[u,n,s]}],"backdrop-contrast":[{"backdrop-contrast":[u,n,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",u,n,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u,n,s]}],"backdrop-invert":[{"backdrop-invert":["",u,n,s]}],"backdrop-opacity":[{"backdrop-opacity":[u,n,s]}],"backdrop-saturate":[{"backdrop-saturate":[u,n,s]}],"backdrop-sepia":[{"backdrop-sepia":["",u,n,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":d()}],"border-spacing-x":[{"border-spacing-x":d()}],"border-spacing-y":[{"border-spacing-y":d()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",n,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[u,"initial",n,s]}],ease:[{ease:["linear","initial",R,n,s]}],delay:[{delay:[u,n,s]}],animate:[{animate:["none",$,n,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[k,n,s]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:H()}],"rotate-x":[{"rotate-x":H()}],"rotate-y":[{"rotate-y":H()}],"rotate-z":[{"rotate-z":H()}],scale:[{scale:K()}],"scale-x":[{"scale-x":K()}],"scale-y":[{"scale-y":K()}],"scale-z":[{"scale-z":K()}],"scale-3d":["scale-3d"],skew:[{skew:Z()}],"skew-x":[{"skew-x":Z()}],"skew-y":[{"skew-y":Z()}],transform:[{transform:[n,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:J()}],"translate-x":[{"translate-x":J()}],"translate-y":[{"translate-y":J()}],"translate-z":[{"translate-z":J()}],"translate-none":["translate-none"],accent:[{accent:l()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:l()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",n,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":d()}],"scroll-mx":[{"scroll-mx":d()}],"scroll-my":[{"scroll-my":d()}],"scroll-ms":[{"scroll-ms":d()}],"scroll-me":[{"scroll-me":d()}],"scroll-mt":[{"scroll-mt":d()}],"scroll-mr":[{"scroll-mr":d()}],"scroll-mb":[{"scroll-mb":d()}],"scroll-ml":[{"scroll-ml":d()}],"scroll-p":[{"scroll-p":d()}],"scroll-px":[{"scroll-px":d()}],"scroll-py":[{"scroll-py":d()}],"scroll-ps":[{"scroll-ps":d()}],"scroll-pe":[{"scroll-pe":d()}],"scroll-pt":[{"scroll-pt":d()}],"scroll-pr":[{"scroll-pr":d()}],"scroll-pb":[{"scroll-pb":d()}],"scroll-pl":[{"scroll-pl":d()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",n,s]}],fill:[{fill:["none",...l()]}],"stroke-w":[{stroke:[u,W,T,te]}],stroke:[{stroke:["none",...l()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Pr=lr(Mr);function Ir(...e){return Pr(Ce(e))}const Nr=ie({__name:"Button",props:{variant:{},size:{},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;return(r,o)=>($e(),Fe(re(Je),{"data-slot":"button",as:r.as,"as-child":r.asChild,class:We(re(Ir)(re(Rr)({variant:r.variant,size:r.size}),t.class))},{default:Be(()=>[Ue(r.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Rr=qe("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});export{Je as P,He as S,Nr as _,Ir as c,Se as r};
