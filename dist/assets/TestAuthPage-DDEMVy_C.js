import{M as T,bO as v,aS as A,q as I,aC as S,u,v as t,t as b,H as o,b9 as e,bi as a,F as h,aV as C,aK as i}from"./index-DMDdU1SS.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"test-auth-page"},_={class:"auth-status"},w={class:"local-storage"},x={class:"actions"},L=["disabled"],V={key:0,class:"logs"},B={class:"timestamp"},D={class:"message"},N=T({__name:"TestAuthPage",setup(F){const n=v(),d=A([]),g=I(()=>typeof window>"u"?{accessToken:"无法访问",refreshToken:"无法访问",userInfo:"无法访问"}:{accessToken:localStorage.getItem("access_token")?"存在":"不存在",refreshToken:localStorage.getItem("refresh_token")?"存在":"不存在",userInfo:localStorage.getItem("user_info")?"存在":"不存在"}),l=r=>{d.value.unshift({timestamp:new Date().toLocaleTimeString(),message:r})},p=async()=>{try{l("开始测试API调用..."),l("API调用成功")}catch(r){l(`API调用失败: ${r.message}`)}},f=()=>{n.clearAuthData(),l("认证数据已清除")},k=()=>{l("页面即将刷新..."),setTimeout(()=>{window.location.reload()},1e3)};return S(()=>{l("页面已加载"),l(`认证状态: ${n.isAuthenticated?"已认证":"未认证"}`)}),(r,s)=>(i(),u("div",y,[s[12]||(s[12]=t("h1",null,"认证状态测试页面",-1)),t("div",_,[s[5]||(s[5]=t("h2",null,"认证状态",-1)),t("p",null,[s[0]||(s[0]=t("strong",null,"已初始化:",-1)),o(" "+e(a(n).isInitialized),1)]),t("p",null,[s[1]||(s[1]=t("strong",null,"已认证:",-1)),o(" "+e(a(n).isAuthenticated),1)]),t("p",null,[s[2]||(s[2]=t("strong",null,"有Token:",-1)),o(" "+e(!!a(n).accessToken),1)]),t("p",null,[s[3]||(s[3]=t("strong",null,"有用户信息:",-1)),o(" "+e(!!a(n).user),1)]),t("p",null,[s[4]||(s[4]=t("strong",null,"用户:",-1)),o(" "+e(a(n).user?.username||"无"),1)])]),t("div",w,[s[9]||(s[9]=t("h2",null,"LocalStorage 状态",-1)),t("p",null,[s[6]||(s[6]=t("strong",null,"Access Token:",-1)),o(" "+e(g.value.accessToken),1)]),t("p",null,[s[7]||(s[7]=t("strong",null,"Refresh Token:",-1)),o(" "+e(g.value.refreshToken),1)]),t("p",null,[s[8]||(s[8]=t("strong",null,"用户信息:",-1)),o(" "+e(g.value.userInfo),1)])]),t("div",x,[s[10]||(s[10]=t("h2",null,"操作",-1)),t("button",{onClick:p,disabled:!a(n).isAuthenticated}," 测试API调用 ",8,L),t("button",{onClick:f},"清除认证"),t("button",{onClick:k},"刷新页面")]),d.value.length>0?(i(),u("div",V,[s[11]||(s[11]=t("h2",null,"日志",-1)),(i(!0),u(h,null,C(d.value,(m,c)=>(i(),u("div",{key:c,class:"log-item"},[t("span",B,e(m.timestamp),1),t("span",D,e(m.message),1)]))),128))])):b("",!0)]))}}),q=P(N,[["__scopeId","data-v-e538983b"]]);export{q as default};
