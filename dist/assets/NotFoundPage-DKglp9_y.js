import{M as m,u,v as e,I as s,bH as l,bi as a,aK as f,H as r,bQ as x}from"./index-DMDdU1SS.js";import{_ as n}from"./index-MhxZY9Rm.js";import{c}from"./createLucideIcon-B64IBOqu.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=c("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=c("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),h={class:"min-h-screen flex items-center justify-center bg-gray-50"},b={class:"max-w-md w-full text-center"},w={class:"space-y-4"},B=m({__name:"NotFoundPage",setup(g){const o=x(),i=()=>{o.push("/")},d=()=>{o.back()};return(k,t)=>(f(),u("div",h,[e("div",b,[t[2]||(t[2]=e("div",{class:"mb-8"},[e("h1",{class:"text-9xl font-bold text-gray-300"},"404"),e("h2",{class:"text-2xl font-semibold text-gray-900 mt-4"},"页面未找到"),e("p",{class:"text-gray-600 mt-2"}," 抱歉，您访问的页面不存在或已被移除。 ")],-1)),e("div",w,[s(a(n),{onClick:i,class:"w-full bg-blue-600 hover:bg-blue-700 text-white"},{default:l(()=>[s(a(p),{class:"h-4 w-4 mr-2"}),t[0]||(t[0]=r(" 返回首页 "))]),_:1,__:[0]}),s(a(n),{onClick:d,variant:"outline",class:"w-full"},{default:l(()=>[s(a(_),{class:"h-4 w-4 mr-2"}),t[1]||(t[1]=r(" 返回上一页 "))]),_:1,__:[1]})])])]))}});export{B as default};
