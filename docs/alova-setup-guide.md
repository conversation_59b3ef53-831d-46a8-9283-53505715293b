# Alova API 配置使用指南

本项目使用 Alova 作为 HTTP 请求库，配置了 Fetch 适配器和 Vue Hook，提供了完整的 TypeScript 类型支持。

## 📁 文件结构

```
src/
├── api/
│   ├── client.ts           # Alova 实例配置
│   ├── auth.ts            # 认证相关 API
│   └── index.ts           # API 统一导出
├── types/
│   ├── api.types.ts       # API 相关类型定义
│   └── index.ts           # 类型统一导出
├── constants/
│   ├── api.ts             # API 相关常量
│   └── index.ts           # 常量统一导出
└── examples/
    └── alova-usage-examples.ts  # 使用示例
```

## 🚀 快速开始

### 1. 环境变量配置

复制 `.env.example` 为 `.env.local` 并配置您的 API 地址：

```bash
cp .env.example .env.local
```

```env
# .env.local
VITE_API_BASE_URL=http://your-api-domain.com/api
```

### 2. 基础使用

```typescript
import { useRequest } from 'alova/client'
import { getCurrentUser } from '@/api'

// 在组合式函数中使用
export function useAuth() {
  const {
    data: user,
    loading,
    error,
    send: fetchUser
  } = useRequest(getCurrentUser, {
    immediate: true // 立即执行请求
  })

  return {
    user,
    loading,
    error,
    fetchUser
  }
}
```

### 3. 在 Vue 组件中使用

```vue
<template>
  <div>
    <div v-if="loading">加载中...</div>
    <div v-else-if="error">{{ error.message }}</div>
    <div v-else-if="user">
      <h2>欢迎, {{ user.data.nickname }}!</h2>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'

const { user, loading, error } = useAuth()
</script>
```

## 🔧 核心功能

### 1. 自动认证

- 自动添加 Authorization 头
- Token 过期自动清理
- 认证失败自动处理

### 2. 统一错误处理

- HTTP 状态码错误处理
- 业务错误码处理
- 网络错误处理
- 自定义错误消息

### 3. 请求拦截

- 自动添加认证头
- 设置默认 Content-Type
- 请求日志记录

### 4. 响应处理

- 统一响应格式
- 业务状态码检查
- 自动 JSON 解析

### 5. TypeScript 支持

- 完整的类型定义
- 自动类型推断
- IDE 智能提示

## 📖 API 方法

### 认证相关

```typescript
import { login, logout, getCurrentUser, refreshToken } from '@/api'

// 登录
const loginResponse = await login({
  username: 'admin',
  password: 'password'
})

// 获取当前用户
const userResponse = await getCurrentUser()

// 登出
await logout()
```

### 用户管理

```typescript
import { getUserList, getUser, createUser, updateUser, deleteUser } from '@/api'

// 获取用户列表
const users = await getUserList({
  page: 1,
  pageSize: 10
})

// 获取单个用户
const user = await getUser('user-id')

// 创建用户
const newUser = await createUser({
  username: 'newuser',
  email: '<EMAIL>',
  // ...
})
```

## 🔗 高级用法

### 1. 动态请求 (useFetcher)

```typescript
import { useFetcher } from 'alova/client'
import { getUserList } from '@/api'

const { fetch, loading, error } = useFetcher()

// 动态获取数据
const loadUsers = (params) => {
  return fetch(getUserList(params))
}
```

### 2. 文件上传

```typescript
import { uploadAvatar } from '@/api'

const handleFileUpload = async (file: File) => {
  try {
    const response = await uploadAvatar(file)
    console.log('上传成功:', response.data.url)
  } catch (error) {
    console.error('上传失败:', error)
  }
}
```

### 3. 错误处理

```typescript
import { alovaInstance } from '@/api'

try {
  const response = await alovaInstance.Get('/api/data')
  // 处理成功响应
} catch (error) {
  if (error.message.includes('401')) {
    // 处理认证错误
    router.push('/login')
  } else {
    // 处理其他错误
    console.error('请求失败:', error.message)
  }
}
```

## 🛠️ 自定义配置

### 1. 添加新的 API 模块

在 `src/api/` 目录下创建新文件：

```typescript
// src/api/pm/product.ts
import { alovaInstance } from './client'
import type { ResponseModel, ResponseListModel } from '@/types/api.types'

export interface Product {
  id: string
  name: string
  price: number
  // ...
}

export const getProductList = (params?: any) => {
  return alovaInstance.Get<ResponseListModel<Product>>('/products', { params })
}

export const getProduct = (id: string) => {
  return alovaInstance.Get<ResponseModel<Product>>(`/products/${id}`)
}
```

然后在 `src/api/index.ts` 中导出：

```typescript
// src/api/index.ts
export * from './products'
```

### 2. 自定义错误处理

修改 `src/api/client.ts` 中的错误处理逻辑：

```typescript
// 在 responded.onError 中添加自定义逻辑
onError: (error, method) => {
  // 自定义错误处理
  if (method.url.includes('/critical-api/')) {
    // 对关键 API 的特殊错误处理
  }
  
  handleApiError(error)
}
```

## 🔍 调试和监控

### 请求日志

所有 API 请求都会在控制台输出详细日志，包括：

- 请求方法和 URL
- 请求参数和数据
- 请求头信息
- 响应数据

### 错误追踪

错误信息会自动记录到控制台，包括：

- 错误类型和消息
- 请求上下文
- 堆栈跟踪

## 📝 最佳实践

1. **统一错误处理**: 在组件中使用 try-catch 处理特定错误
2. **类型安全**: 为所有 API 响应定义 TypeScript 类型
3. **缓存策略**: 合理使用 Alova 的缓存功能
4. **加载状态**: 使用 useRequest 的 loading 状态提升用户体验
5. **错误用户反馈**: 根据错误类型给用户友好的提示

## 🔧 故障排除

### 常见问题

1. **401 认证错误**: 检查 Token 是否过期或无效
2. **CORS 错误**: 确认后端 CORS 配置正确
3. **网络错误**: 检查网络连接和 API 地址配置
4. **类型错误**: 确保 API 响应格式与类型定义匹配

如需更多帮助，请查看 [Alova 官方文档](https://alova.js.org/)。
