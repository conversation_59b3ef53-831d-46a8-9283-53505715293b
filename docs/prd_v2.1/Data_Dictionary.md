# 数据字典 (Data Dictionary) V2.1

> **版本**: 2.1  
> **最后更新**: 2025-08-02  
> **适用范围**: 玻璃深加工行业ERP系统V2.1所有模块  
> **维护责任**: 产品团队  
> **优化说明**: 基于V2.0架构优化，建立统一的数据标准和规范

---

## 使用说明

本数据字典为所有PRD文档提供统一的数据定义和标准。所有模块必须严格遵循本字典中定义的数据格式、约束条件和业务规则。V2.1版本重点建立了跨模块的统一数据标准。

---

## 编码规范标准

### 主数据编码规范

| 数据类型 | 编码格式 | 长度 | 示例 | 说明 |
|----------|----------|------|------|------|
| 客户编码 | CUS-XXXXXXXX | 12位 | CUS-00000001 | 客户唯一标识 |
| 供应商编码 | SUP-XXXXXXXX | 12位 | SUP-00000001 | 供应商唯一标识 |
| 物料编码 | MAT-XXXXXXXX | 12位 | MAT-00000001 | 物料唯一标识 |
| 员工编码 | EMP-XXXXXXXX | 12位 | EMP-00000001 | 员工唯一标识 |
| 设备编码 | EQU-XXXXXXXX | 12位 | EQU-00000001 | 设备唯一标识 |

### 业务单据编码规范

| 单据类型 | 编码格式 | 长度 | 示例 | 说明 |
|----------|----------|------|------|------|
| 销售订单 | SO-YYYYMMDD-XXX | 14位 | SO-20250802-001 | 销售订单编号 |
| 生产订单 | MO-YYYYMMDD-XXX | 14位 | MO-20250802-001 | 生产订单编号 |
| 采购订单 | PO-YYYYMMDD-XXX | 14位 | PO-20250802-001 | 采购订单编号 |
| 入库单 | IN-YYYYMMDD-XXX | 14位 | IN-20250802-001 | 入库单编号 |
| 出库单 | OUT-YYYYMMDD-XXX | 15位 | OUT-20250802-001 | 出库单编号 |
| 报价单 | QT-YYYYMMDD-XXX | 14位 | QT-20250802-001 | 报价单编号 |

### 项目管理编码规范

| 项目类型 | 编码格式 | 长度 | 示例 | 说明 |
|----------|----------|------|------|------|
| 项目编码 | PRJ-YYYYMMDD-XXX | 16位 | PRJ-20250802-001 | 项目唯一标识 |
| 任务编码 | TSK-XXXXXXXX | 12位 | TSK-00000001 | 项目任务标识 |
| 里程碑编码 | MLS-XXXXXXXX | 12位 | MLS-00000001 | 项目里程碑标识 |

---

## 核心数据实体定义

### 客户主数据 (Customer Master Data)

| 字段名称 | 字段类型 | 长度 | 必填 | 默认值 | 说明 |
|----------|----------|------|------|--------|------|
| customer_code | VARCHAR | 12 | 是 | - | 客户编码 |
| customer_name | VARCHAR | 100 | 是 | - | 客户名称 |
| customer_type | VARCHAR | 20 | 是 | - | 客户类型：直销/经销商/工程商 |
| contact_person | VARCHAR | 50 | 否 | - | 联系人 |
| contact_phone | VARCHAR | 20 | 否 | - | 联系电话 |
| contact_email | VARCHAR | 100 | 否 | - | 联系邮箱 |
| address | VARCHAR | 200 | 否 | - | 客户地址 |
| credit_limit | DECIMAL | 15,2 | 否 | 0.00 | 信用额度 |
| payment_terms | VARCHAR | 50 | 否 | - | 付款条件 |
| status | VARCHAR | 10 | 是 | ACTIVE | 状态：ACTIVE/INACTIVE |
| created_date | DATETIME | - | 是 | NOW() | 创建时间 |
| updated_date | DATETIME | - | 是 | NOW() | 更新时间 |

### 物料主数据 (Material Master Data)

| 字段名称 | 字段类型 | 长度 | 必填 | 默认值 | 说明 |
|----------|----------|------|------|--------|------|
| material_code | VARCHAR | 12 | 是 | - | 物料编码 |
| material_name | VARCHAR | 100 | 是 | - | 物料名称 |
| material_type | VARCHAR | 20 | 是 | - | 物料类型：原材料/半成品/成品 |
| specification | VARCHAR | 200 | 否 | - | 规格描述 |
| unit_of_measure | VARCHAR | 10 | 是 | - | 计量单位 |
| standard_cost | DECIMAL | 15,4 | 否 | 0.0000 | 标准成本 |
| safety_stock | DECIMAL | 15,3 | 否 | 0.000 | 安全库存 |
| lead_time | INT | - | 否 | 0 | 采购提前期（天） |
| is_variant | BOOLEAN | - | 是 | FALSE | 是否变体物料 |
| parent_material | VARCHAR | 12 | 否 | - | 父物料编码 |
| status | VARCHAR | 10 | 是 | ACTIVE | 状态：ACTIVE/INACTIVE |
| created_date | DATETIME | - | 是 | NOW() | 创建时间 |
| updated_date | DATETIME | - | 是 | NOW() | 更新时间 |

### 销售订单 (Sales Order)

| 字段名称 | 字段类型 | 长度 | 必填 | 默认值 | 说明 |
|----------|----------|------|------|--------|------|
| order_number | VARCHAR | 14 | 是 | - | 订单编号 |
| customer_code | VARCHAR | 12 | 是 | - | 客户编码 |
| order_date | DATE | - | 是 | TODAY | 订单日期 |
| delivery_date | DATE | - | 是 | - | 交货日期 |
| order_status | VARCHAR | 20 | 是 | DRAFT | 订单状态 |
| total_amount | DECIMAL | 15,2 | 是 | 0.00 | 订单总金额 |
| currency | VARCHAR | 3 | 是 | CNY | 币种 |
| payment_terms | VARCHAR | 50 | 否 | - | 付款条件 |
| sales_person | VARCHAR | 12 | 否 | - | 销售员编码 |
| remarks | TEXT | - | 否 | - | 备注 |
| created_by | VARCHAR | 12 | 是 | - | 创建人 |
| created_date | DATETIME | - | 是 | NOW() | 创建时间 |
| updated_date | DATETIME | - | 是 | NOW() | 更新时间 |

### 生产订单 (Manufacturing Order)

| 字段名称 | 字段类型 | 长度 | 必填 | 默认值 | 说明 |
|----------|----------|------|------|--------|------|
| mo_number | VARCHAR | 14 | 是 | - | 生产订单号 |
| so_number | VARCHAR | 14 | 否 | - | 关联销售订单 |
| material_code | VARCHAR | 12 | 是 | - | 生产物料编码 |
| planned_quantity | DECIMAL | 15,3 | 是 | 0.000 | 计划数量 |
| completed_quantity | DECIMAL | 15,3 | 是 | 0.000 | 完成数量 |
| start_date | DATE | - | 是 | - | 计划开始日期 |
| end_date | DATE | - | 是 | - | 计划完成日期 |
| actual_start_date | DATE | - | 否 | - | 实际开始日期 |
| actual_end_date | DATE | - | 否 | - | 实际完成日期 |
| mo_status | VARCHAR | 20 | 是 | PLANNED | 生产状态 |
| priority | VARCHAR | 10 | 是 | NORMAL | 优先级：HIGH/NORMAL/LOW |
| workshop | VARCHAR | 50 | 否 | - | 生产车间 |
| created_by | VARCHAR | 12 | 是 | - | 创建人 |
| created_date | DATETIME | - | 是 | NOW() | 创建时间 |
| updated_date | DATETIME | - | 是 | NOW() | 更新时间 |

---

## 状态枚举定义

### 订单状态 (Order Status)

| 状态代码 | 状态名称 | 说明 | 允许操作 |
|----------|----------|------|----------|
| DRAFT | 草稿 | 订单创建但未确认 | 编辑、删除、确认 |
| CONFIRMED | 已确认 | 订单已确认，等待生产 | 取消、修改、生产 |
| IN_PRODUCTION | 生产中 | 订单正在生产 | 查看、暂停 |
| COMPLETED | 已完成 | 订单生产完成 | 查看、发货 |
| SHIPPED | 已发货 | 订单已发货 | 查看、收款 |
| CANCELLED | 已取消 | 订单已取消 | 查看 |

### 生产状态 (Manufacturing Status)

| 状态代码 | 状态名称 | 说明 | 允许操作 |
|----------|----------|------|----------|
| PLANNED | 已计划 | 生产订单已创建 | 编辑、删除、下达 |
| RELEASED | 已下达 | 生产订单已下达 | 开始、暂停 |
| IN_PROGRESS | 进行中 | 生产正在进行 | 报工、暂停、完成 |
| COMPLETED | 已完成 | 生产已完成 | 查看、入库 |
| CANCELLED | 已取消 | 生产已取消 | 查看 |

### 库存状态 (Inventory Status)

| 状态代码 | 状态名称 | 说明 | 允许操作 |
|----------|----------|------|----------|
| AVAILABLE | 可用 | 库存可正常使用 | 出库、转移 |
| RESERVED | 已预留 | 库存已被预留 | 查看、释放 |
| QUARANTINE | 隔离 | 质量问题隔离 | 检验、报废 |
| DAMAGED | 损坏 | 库存已损坏 | 报废、维修 |
| EXPIRED | 过期 | 库存已过期 | 报废、处理 |

---

## 数据约束规则

### 数值约束

| 字段类型 | 最小值 | 最大值 | 精度 | 说明 |
|----------|--------|--------|------|------|
| 数量字段 | 0 | 999999999.999 | 3位小数 | 不允许负数 |
| 金额字段 | 0 | 999999999999.99 | 2位小数 | 不允许负数 |
| 单价字段 | 0 | 999999.9999 | 4位小数 | 不允许负数 |
| 百分比字段 | 0 | 100 | 2位小数 | 百分比值 |

### 日期约束

| 字段类型 | 最小值 | 最大值 | 格式 | 说明 |
|----------|--------|--------|------|------|
| 日期字段 | 1900-01-01 | 2099-12-31 | YYYY-MM-DD | 标准日期格式 |
| 时间字段 | - | - | YYYY-MM-DD HH:MM:SS | 标准时间格式 |
| 交货日期 | 当前日期 | 当前日期+365天 | YYYY-MM-DD | 不能早于当前日期 |

### 文本约束

| 字段类型 | 最小长度 | 最大长度 | 字符集 | 说明 |
|----------|----------|----------|--------|------|
| 编码字段 | 8 | 16 | 字母数字 | 不允许特殊字符 |
| 名称字段 | 1 | 100 | UTF-8 | 支持中英文 |
| 描述字段 | 0 | 500 | UTF-8 | 可选字段 |
| 备注字段 | 0 | 2000 | UTF-8 | 长文本字段 |

---

## 数据关系定义

### 主外键关系

| 主表 | 主键 | 从表 | 外键 | 关系类型 | 说明 |
|------|------|------|------|----------|------|
| 客户主数据 | customer_code | 销售订单 | customer_code | 1:N | 一个客户多个订单 |
| 物料主数据 | material_code | 订单明细 | material_code | 1:N | 一个物料多个订单行 |
| 销售订单 | order_number | 生产订单 | so_number | 1:N | 一个销售订单多个生产订单 |
| 生产订单 | mo_number | 工序报工 | mo_number | 1:N | 一个生产订单多个工序 |
| 员工主数据 | employee_code | 工序报工 | operator_code | 1:N | 一个员工多次报工 |

### 业务关联关系

| 业务实体1 | 业务实体2 | 关系描述 | 约束条件 |
|-----------|-----------|----------|----------|
| 报价单 | 销售订单 | 报价转订单 | 报价单状态必须为已确认 |
| 销售订单 | BOM | 订单BOM固化 | 订单确认后BOM不可修改 |
| 生产订单 | 物料需求 | 生产物料需求 | 基于BOM自动计算 |
| 采购订单 | 入库单 | 采购入库 | 入库数量不能超过采购数量 |
| 出库单 | 销售发货 | 销售出库 | 出库数量不能超过订单数量 |

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，建立统一数据字典 | 产品团队 |

---

## 维护说明

1. **新增字段**: 必须经过数据架构师评审
2. **修改字段**: 需要评估对现有数据的影响
3. **删除字段**: 必须确保无业务依赖
4. **数据迁移**: 字段变更需要提供数据迁移脚本
5. **版本同步**: 数据字典版本与PRD版本保持同步

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 数据统一 ✅ 标准规范 ✅ 约束明确 ✅
