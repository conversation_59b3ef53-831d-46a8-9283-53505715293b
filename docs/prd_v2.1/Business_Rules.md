# 核心业务规则库 (Core Business Rules) V2.1

> **版本**: 2.1  
> **最后更新**: 2025-08-02  
> **适用范围**: 玻璃深加工行业ERP系统V2.1所有模块  
> **维护责任**: 产品团队  
> **优化说明**: 基于V2.0架构优化，更新业务规则以反映分层架构和模块整合

---

## 使用说明

本业务规则库为所有PRD文档提供统一的业务规则定义。所有模块必须严格遵循本库中定义的业务规则，确保系统的一致性和完整性。V2.1版本重点更新了跨模块协作规则，以反映分层架构的设计思想。

---

## V2.1架构业务规则

### AR-001: 分层架构调用规则
**规则描述**: 系统各层之间的调用必须遵循分层架构原则  
**具体规则**:
- 上层可以调用下层服务，下层不能直接调用上层
- 同层模块之间不能直接调用，必须通过共享服务层
- 跨层调用必须通过标准API接口
- 禁止跨层直接访问数据库

**适用模块**: 所有模块  
**违规处理**: 系统拒绝调用，记录错误日志

### AR-002: 模块边界规则
**规则描述**: 各业务模块必须明确职责边界，避免功能重复  
**具体规则**:
- 产品配置功能统一由PDM服务提供
- 用户认证和权限管理统一由基础服务提供
- 数据字典维护统一由基础服务提供
- 各模块不得重复实现共享服务的功能

**适用模块**: 所有业务模块  
**违规处理**: 设计评审阶段拒绝通过

### AR-003: 服务调用规则
**规则描述**: 业务模块调用共享服务必须遵循标准规范  
**具体规则**:
- 必须使用RESTful API进行服务调用
- 调用超时时间不得超过5秒
- 必须实现重试机制和降级处理
- 关键业务数据必须进行本地缓存

**适用模块**: 所有业务模块  
**违规处理**: 系统自动降级，告警通知

---

## 数据管理业务规则

### DR-001: 主数据管理规则
**规则描述**: 企业主数据必须统一管理，确保数据一致性  
**具体规则**:
- 客户、供应商、物料等主数据由PDM服务统一管理
- 各业务模块不得维护重复的主数据
- 主数据变更必须通过PDM服务进行
- 主数据删除前必须检查引用关系

**适用模块**: 所有业务模块  
**违规处理**: 数据校验失败，操作被拒绝

### DR-002: 数据编码规则
**规则描述**: 所有业务数据必须遵循统一的编码规范  
**具体规则**:
- 订单编号格式：SO-YYYYMMDD-XXX
- 生产订单号格式：MO-YYYYMMDD-XXX
- 物料编码格式：MAT-XXXXXXXX
- 客户编码格式：CUS-XXXXXXXX
- 供应商编码格式：SUP-XXXXXXXX

**适用模块**: 所有业务模块  
**违规处理**: 数据格式校验失败

### DR-003: 数据同步规则
**规则描述**: 跨模块数据同步必须保证一致性和及时性  
**具体规则**:
- 关键业务数据必须实时同步
- 同步失败必须自动重试，最多重试3次
- 同步延迟不得超过30秒
- 同步异常必须记录日志并告警

**适用模块**: 所有业务模块  
**违规处理**: 系统告警，人工干预

---

## 核心业务流程规则

### BR-001: 订单到生产流程规则
**规则描述**: 从销售订单到生产执行的端到端流程规则  
**具体规则**:
- 销售订单确认后自动触发BOM固化流程
- BOM固化完成后自动生成生产订单
- 生产订单下达前必须完成物料可用性检查
- 生产开始前必须完成工艺路线确认

**适用模块**: 销售管理、生产管理、PDM服务  
**违规处理**: 流程中断，人工干预

### BR-002: BOM固化规则
**规则描述**: 产品BOM从参数化到固化的业务规则  
**具体规则**:
- 销售订单确认后，参数化BOM必须固化为生产BOM
- BOM固化后不允许修改，如需变更必须走变更流程
- 固化BOM必须包含完整的物料清单和工艺路线
- BOM版本必须与订单关联，确保可追溯

**适用模块**: 销售管理、生产管理、PDM服务  
**违规处理**: BOM固化失败，订单无法进入生产

### BR-003: 切割优化规则
**规则描述**: 玻璃切割优化的业务规则  
**具体规则**:
- 切割优化必须基于实际库存的原片规格
- 优化目标：最大化原片利用率，最小化废料
- 切割方案确定后自动更新物料需求
- 切割异常必须记录并影响后续优化算法

**适用模块**: 生产管理、库存管理、PDM服务  
**违规处理**: 切割方案无效，重新计算

### BR-004: 质量检验规则
**规则描述**: 质量检验的业务规则  
**具体规则**:
- 所有入库物料必须经过质量检验
- 检验不合格的物料不得入库
- 检验结果必须与批次信息关联
- 质量问题必须可追溯到供应商或生产工序

**适用模块**: 质量管理、库存管理、生产管理  
**违规处理**: 质量检验失败，物料隔离处理

---

## 财务管理业务规则

### FR-001: 成本核算规则
**规则描述**: 产品成本核算的业务规则  
**具体规则**:
- 成本核算必须基于实际消耗的物料和工时
- 成本归集必须按照成本中心进行
- 间接费用按照预设比例分摊
- 成本核算周期为月度，月末自动结算

**适用模块**: 财务管理、生产管理、人事管理  
**违规处理**: 成本核算异常，财务报表不准确

### FR-002: 计件薪酬规则
**规则描述**: 计件工资计算的业务规则  
**具体规则**:
- 计件薪酬基于实际完成的合格产品数量
- 不合格产品不计入计件数量
- 计件单价按照工序和难度系数确定
- 薪酬计算结果必须经过审核确认

**适用模块**: 财务管理、生产管理、人事管理  
**违规处理**: 薪酬计算错误，重新计算

### FR-003: 应收应付管理规则
**规则描述**: 应收应付款项管理的业务规则  
**具体规则**:
- 应收款项必须与销售订单关联
- 应付款项必须与采购订单关联
- 超期应收必须自动提醒和催收
- 付款必须经过审批流程

**适用模块**: 财务管理、销售管理、库存管理  
**违规处理**: 款项管理异常，影响现金流

---

## 库存管理业务规则

### IR-001: 库存控制规则
**规则描述**: 库存数量控制的业务规则  
**具体规则**:
- 库存数量不得为负数
- 出库必须先进行库存可用性检查
- 安全库存低于预警线时自动提醒
- 呆滞库存超过6个月自动标记

**适用模块**: 库存管理、销售管理、生产管理  
**违规处理**: 出库失败，库存异常告警

### IR-002: 批次管理规则
**规则描述**: 物料批次管理的业务规则  
**具体规则**:
- 所有物料必须按批次管理
- 出库遵循先进先出原则
- 批次信息必须完整记录
- 问题批次必须能够快速定位和隔离

**适用模块**: 库存管理、质量管理、生产管理  
**违规处理**: 批次追溯失败，质量风险

### IR-003: 盘点管理规则
**规则描述**: 库存盘点的业务规则  
**具体规则**:
- 定期盘点周期不得超过3个月
- 盘点期间相关库存冻结
- 盘点差异必须查明原因
- 盘点结果必须经过审批确认

**适用模块**: 库存管理、财务管理  
**违规处理**: 盘点异常，库存数据不准确

---

## 权限管理业务规则

### PR-001: 用户权限规则
**规则描述**: 用户权限管理的业务规则  
**具体规则**:
- 用户权限基于角色分配
- 权限变更必须经过审批
- 敏感操作必须记录审计日志
- 用户离职后权限立即回收

**适用模块**: 基础服务、所有业务模块  
**违规处理**: 权限校验失败，操作被拒绝

### PR-002: 数据权限规则
**规则描述**: 数据访问权限的业务规则  
**具体规则**:
- 数据权限基于组织架构控制
- 用户只能访问权限范围内的数据
- 跨部门数据访问需要特殊授权
- 数据导出必须记录和审批

**适用模块**: 基础服务、所有业务模块  
**违规处理**: 数据访问被拒绝，记录违规日志

---

## 系统运行规则

### SR-001: 性能要求规则
**规则描述**: 系统性能的基本要求  
**具体规则**:
- 页面响应时间不得超过3秒
- API接口响应时间不得超过2秒
- 系统可用性不得低于99.9%
- 并发用户数不得低于300

**适用模块**: 所有模块  
**违规处理**: 性能告警，系统优化

### SR-002: 数据备份规则
**规则描述**: 数据备份和恢复的规则  
**具体规则**:
- 关键业务数据必须实时备份
- 完整备份周期不得超过24小时
- 备份数据必须定期验证可用性
- 灾难恢复时间不得超过4小时

**适用模块**: 所有模块  
**违规处理**: 数据丢失风险，系统不可用

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.0 | 2025-07-30 | 初始版本，建立核心业务规则体系 | 产品团队 |
| 2.1 | 2025-08-02 | 架构优化版本，新增分层架构规则，更新模块协作规则 | 产品团队 |

---

## 维护说明

1. **新增规则**: 必须经过产品团队和技术团队联合评审
2. **修改规则**: 需要评估对现有系统的影响
3. **废弃规则**: 必须提供替代方案和迁移计划
4. **定期审查**: 每季度进行一次业务规则审查和更新
5. **版本同步**: 业务规则版本与PRD版本保持同步

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 规则统一 ✅ 架构对齐 ✅ 流程简化 ✅
