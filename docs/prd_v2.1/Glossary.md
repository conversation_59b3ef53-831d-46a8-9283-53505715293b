# 全局术语表 (Global Glossary) V2.1

> **版本**: 2.1  
> **最后更新**: 2025-08-02  
> **适用范围**: 玻璃深加工行业ERP系统V2.1所有PRD文档  
> **维护责任**: 产品团队  
> **优化说明**: 基于V2.0架构优化，更新术语定义以反映分层架构设计

---

## 使用说明

本术语表为所有PRD文档提供统一的术语定义。所有文档必须严格使用本表中定义的术语，避免术语混用。V2.1版本重点更新了架构相关术语，以反映分层架构的设计思想。

---

## V2.1架构术语

### 分层架构 (Layered Architecture)
**中文定义**: 将系统按功能职责分为多个层次的架构模式  
**使用说明**: V2.1采用的核心架构模式，包含用户界面层、业务层、服务层、数据层

### 核心业务层 (Core Business Layer)
**中文定义**: 承载主要业务逻辑的系统层次  
**使用说明**: 包含销售管理、生产管理、库存管理、财务管理四个核心模块

### 共享服务层 (Shared Service Layer)
**中文定义**: 为多个业务模块提供公共服务的系统层次  
**使用说明**: 包含PDM服务和基础服务，为业务层提供支撑

### 支持业务层 (Support Business Layer)
**中文定义**: 提供专业化业务支持的系统层次  
**使用说明**: 包含项目管理、质量管理、数据中心三个专业模块

### 业务模块 (Business Module)
**中文定义**: V2.1架构中的功能单元，替代V2.0的子系统概念  
**使用说明**: 模块比子系统更加内聚，职责更加明确

---

## 系统架构术语

### ERP (Enterprise Resource Planning)
**中文定义**: 企业资源计划系统  
**使用说明**: 指整个企业管理系统的总称，V2.1采用分层架构设计

### MES (Manufacturing Execution System)
**中文定义**: 制造执行系统  
**使用说明**: V2.1中整合到生产管理模块，负责生产计划执行和现场管理

### WMS (Warehouse Management System)
**中文定义**: 仓储管理系统  
**使用说明**: V2.1中整合到库存管理模块，负责库存和出入库管理

### CRM (Customer Relationship Management)
**中文定义**: 客户关系管理系统  
**使用说明**: V2.1中整合到销售管理模块，负责客户档案和销售机会管理

### PDM (Product Data Management)
**中文定义**: 产品数据管理服务  
**使用说明**: V2.1中作为共享服务，为所有业务模块提供产品数据支撑

### BI (Business Intelligence)
**中文定义**: 商业智能/数据中心  
**使用说明**: V2.1中作为支持业务层的数据中心模块

---

## 业务流程术语

### 端到端流程 (End-to-End Process)
**中文定义**: 从客户需求到最终交付的完整业务流程  
**使用说明**: V2.1优化的核心目标，简化跨模块流程路径

### 业务协同 (Business Collaboration)
**中文定义**: 不同业务模块间的协调配合  
**使用说明**: V2.1通过分层架构实现更好的业务协同

### 数据流转 (Data Flow)
**中文定义**: 数据在不同模块间的传递过程  
**使用说明**: V2.1通过服务层统一管理数据流转

### 订单到生产 (Order-to-Production)
**中文定义**: 从销售订单到生产执行的业务流程  
**使用说明**: V2.1重点优化的核心流程

### 采购到库存 (Procurement-to-Inventory)
**中文定义**: 从采购需求到库存管理的业务流程  
**使用说明**: V2.1中整合到库存管理模块

### 生产到成本 (Production-to-Cost)
**中文定义**: 从生产执行到成本核算的业务流程  
**使用说明**: V2.1中通过财务管理模块统一处理

---

## 数据管理术语

### 统一数据标准 (Unified Data Standard)
**中文定义**: 全系统统一的数据格式和编码规范  
**使用说明**: V2.1强化的数据治理要求

### 数据一致性 (Data Consistency)
**中文定义**: 确保相同数据在不同模块中保持一致  
**使用说明**: V2.1通过共享服务层保障数据一致性

### 主数据 (Master Data)
**中文定义**: 企业核心业务实体的权威数据  
**使用说明**: 由PDM服务统一管理和维护

### 参数化BOM (Parametric BOM)
**中文定义**: 基于参数配置的动态BOM生成机制  
**使用说明**: PDM服务的核心功能，支持复杂规格产品的快速配置

### 物料编码 (Material Code)
**中文定义**: 物料的唯一标识编码  
**格式标准**: MAT-XXXXXXXX  
**示例**: MAT-00000001

### 订单编号 (Order Number)
**中文定义**: 销售订单的唯一标识编码  
**格式标准**: SO-YYYYMMDD-XXX  
**示例**: SO-20250802-001

### 生产订单号 (Production Order Number)
**中文定义**: 生产订单的唯一标识编码  
**格式标准**: MO-YYYYMMDD-XXX  
**示例**: MO-20250802-001

### 客户编码 (Customer Code)
**中文定义**: 客户的唯一标识编码  
**格式标准**: CUS-XXXXXXXX  
**示例**: CUS-00000001

### 供应商编码 (Supplier Code)
**中文定义**: 供应商的唯一标识编码  
**格式标准**: SUP-XXXXXXXX  
**示例**: SUP-00000001

---

## 技术架构术语

### 服务接口 (Service Interface)
**中文定义**: 模块间数据交互的标准接口  
**使用说明**: V2.1采用RESTful API标准

### 微服务 (Microservice)
**中文定义**: 小型、独立部署的服务单元  
**使用说明**: V2.1的共享服务层采用微服务架构

### API (Application Programming Interface)
**中文定义**: 应用程序编程接口  
**使用说明**: 模块间数据交互的标准接口

### RBAC (Role-Based Access Control)
**中文定义**: 基于角色的访问控制  
**使用说明**: 基础服务提供的权限管理机制

### 缓存机制 (Caching Mechanism)
**中文定义**: 提高系统性能的数据缓存策略  
**使用说明**: V2.1优化的性能提升手段

---

## 行业特定术语

### 玻璃深加工 (Glass Deep Processing)
**中文定义**: 对原片玻璃进行切割、磨边、钢化、中空等二次加工  
**使用说明**: 本系统的目标行业

### 切割优化 (Cutting Optimization)
**中文定义**: 通过算法优化玻璃原片的切割方案  
**使用说明**: V2.1中集中到生产管理模块

### 复尺 (Field Measurement)
**中文定义**: 现场实地测量确认产品尺寸  
**使用说明**: 项目制管理中的关键环节

### 外协 (Outsourcing)
**中文定义**: 委托外部供应商进行生产或加工  
**使用说明**: 与自制相对的生产模式

### 计件工资 (Piece-rate Wage)
**中文定义**: 按生产数量计算的薪酬模式  
**使用说明**: 玻璃深加工行业的主要薪酬计算方式

### 变体管理 (Variant Management)
**中文定义**: 管理同一基础物料的不同规格变体  
**使用说明**: PDM服务的重要功能

---

## 用户角色术语

### 系统管理员 (System Administrator)
**中文定义**: 负责系统配置和用户管理的角色  
**权限范围**: 全系统管理权限

### 业务管理员 (Business Administrator)
**中文定义**: 负责业务配置和流程管理的角色  
**权限范围**: 特定业务模块的管理权限

### 销售人员 (Sales Representative)
**中文定义**: 负责客户管理和订单处理的角色  
**权限范围**: 销售管理模块权限

### 生产计划员 (Production Planner)
**中文定义**: 负责生产计划和排程的角色  
**权限范围**: 生产管理模块权限

### 仓库管理员 (Warehouse Manager)
**中文定义**: 负责库存管理和出入库操作的角色  
**权限范围**: 库存管理模块权限

### 财务人员 (Finance Staff)
**中文定义**: 负责财务核算和成本管理的角色  
**权限范围**: 财务管理模块权限

### 质量检验员 (Quality Inspector)
**中文定义**: 负责质量检验和质量管理的角色  
**权限范围**: 质量管理模块权限

### 项目经理 (Project Manager)
**中文定义**: 负责项目管理和进度控制的角色  
**权限范围**: 项目管理模块权限

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-30 | 初始版本，建立核心术语体系 | 产品团队 |
| 2.1 | 2025-08-02 | 架构优化版本，新增分层架构术语，更新模块定义 | 产品团队 |

---

## 维护说明

1. **新增术语**: 必须经过产品团队评审
2. **修改术语**: 需要评估对现有文档的影响
3. **废弃术语**: 必须提供替代术语和迁移计划
4. **定期审查**: 每季度进行一次术语表审查和更新
5. **版本同步**: 术语表版本与PRD版本保持同步

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 术语统一 ✅ 架构对齐 ✅ 定义明确 ✅
