# PRD-M4: 财务管理模块 产品需求文档 V2.1

> **版本**: 2.1  
> **状态**: 架构优化版  
> **撰写人**: 产品团队  
> **日期**: 2025-08-02  
> **优化说明**: 整合V2.0的财务管理子系统和人事管理子系统，形成统一的财务管理模块  
> **术语表**: 参考 [全局术语表](./Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](./Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 V2.1优化目标
**整合V2.0的财务管理子系统和人事管理子系统，解决财务核算与人工成本管理分离导致的成本核算不准确问题，实现业财一体化管理。**

### 1.2 价值主张
构建业财一体化财务管理平台，通过财务核算与人力成本管理的深度融合，实现从业务发生、成本归集到财务报表的全流程自动化，提升财务管理效率和决策支持能力。

### 1.3 商业价值量化
- **财务处理效率提升**: 一体化管理使财务处理效率提升85%
- **成本核算准确性**: 人工成本自动归集使成本核算准确率从85%提升至99%
- **薪酬计算效率**: 自动化计件薪酬计算使HR工作效率提升85%
- **决策响应速度**: 实时财务数据使管理决策响应速度提升70%

---

## 2. 模块架构设计

### 2.1 整合架构
**财务管理模块 = 财务管理子系统 + 人事管理子系统**

```mermaid
graph TB
    subgraph "财务管理模块 V2.1"
        subgraph "财务核算管理"
            A[总账管理]
            B[应收应付管理]
            C[成本核算管理]
            D[财务报表管理]
        end
        
        subgraph "人力成本管理"
            E[员工档案管理]
            F[考勤管理]
            G[薪酬计算管理]
            H[人工成本归集]
        end
        
        subgraph "业财一体化"
            I[业务凭证生成]
            J[成本自动归集]
            K[财务分析报告]
            L[预算管理]
        end
        
        subgraph "共享服务调用"
            M[PDM服务]
            N[基础服务]
        end
    end
    
    G --> H
    H --> C
    C --> D
    I --> A
    J --> C
    E --> N
    A --> N
```

### 2.2 功能整合映射

| V2.1功能模块 | 整合的V2.0功能 | 整合优势 |
|-------------|---------------|----------|
| **成本核算一体化** | 财务成本核算 + 人工成本归集 | 人工成本自动归集，成本核算更准确 |
| **薪酬财务一体化** | 人事薪酬计算 + 财务凭证生成 | 薪酬数据自动生成财务凭证 |
| **业财数据同步** | 财务总账 + 人事数据 | 人事变动自动影响财务核算 |
| **综合成本分析** | 财务分析 + 人工成本分析 | 全面的成本分析和控制 |

---

## 3. 核心功能设计

### 3.1 成本核算一体化

#### 3.1.1 功能描述
整合财务成本核算与人工成本管理，实现材料成本、人工成本、制造费用的自动归集和精确核算。

#### 3.1.2 核心功能
- **自动成本归集**: 材料成本、人工成本、制造费用自动归集
- **人工成本分配**: 直接人工和间接人工成本自动分配
- **成本核算引擎**: 支持品种法、分批法、分步法等核算方法
- **成本分析报告**: 成本构成分析、成本趋势分析、成本差异分析

#### 3.1.3 业务规则
- 直接人工成本按生产订单归集
- 间接人工成本按设定规则分摊到制造费用
- 成本核算按月执行，支持实时查询
- 成本数据与总账自动同步

### 3.2 薪酬财务一体化

#### 3.2.1 功能描述
整合薪酬计算与财务核算，实现计件薪酬自动计算、人工成本自动归集、薪酬凭证自动生成。

#### 3.2.2 核心功能
- **计件薪酬计算**: 基于MES工时数据的自动薪酬计算
- **薪酬结构管理**: 基本工资、计件工资、津贴、奖金等结构化管理
- **薪酬凭证生成**: 薪酬数据自动生成财务凭证
- **员工自助查询**: 员工薪酬明细和计件产量查询

#### 3.2.3 业务规则
- 计件单价在PDM中维护，HR系统自动获取
- 薪酬计算基于MES确认的工时数据
- 薪酬发放自动生成应付职工薪酬凭证
- 支持多种薪酬发放方式和税务处理

### 3.3 业财数据同步

#### 3.3.1 功能描述
实现业务系统与财务系统的数据实时同步，业务发生自动生成财务凭证，确保业财数据一致性。

#### 3.3.2 核心功能
- **业务凭证生成**: 销售、采购、生产等业务自动生成凭证
- **凭证模板管理**: 预设凭证模板，自动匹配科目
- **凭证审核流程**: 凭证审核、过账、反过账流程管理
- **数据同步监控**: 业财数据同步状态监控和异常处理

#### 3.3.3 业务规则
- 业务单据审核后自动生成凭证草稿
- 凭证必须借贷平衡才能过账
- 已过账凭证不允许修改，需要红字冲销
- 支持凭证批量审核和过账

### 3.4 综合财务分析

#### 3.4.1 功能描述
整合财务数据和人力数据，提供全面的财务分析和决策支持。

#### 3.4.2 核心功能
- **财务报表生成**: 资产负债表、利润表、现金流量表自动生成
- **成本分析报告**: 产品成本分析、部门成本分析、项目成本分析
- **人工成本分析**: 人工成本构成、人效分析、薪酬水平分析
- **经营分析报告**: 盈利能力分析、运营效率分析、财务风险分析

#### 3.4.3 业务规则
- 财务报表数据来源于总账科目余额
- 成本分析基于成本核算结果
- 人工成本分析基于薪酬和工时数据
- 支持多维度钻取分析

---

## 4. 服务集成设计

### 4.1 PDM服务集成

#### 4.1.1 工序计件单价获取
```javascript
// 获取工序计件单价用于薪酬计算
const pieceRates = await fetch('/api/pdm/operations/piece-rates', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

#### 4.1.2 产品成本结构获取
```javascript
// 获取产品BOM成本结构用于成本核算
const costStructure = await fetch('/api/pdm/boms/' + bomId + '/cost-structure', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

### 4.2 基础服务集成

#### 4.2.1 用户权限控制
- 财务主管：财务管理、报表分析、决策支持
- 成本会计：成本核算、成本分析、成本控制
- 应收会计：应收账款管理、客户对账、回款跟踪
- HR专员：员工档案、薪酬管理、考勤管理

#### 4.2.2 数据字典集成
- 凭证状态：草稿/待审核/已审核/已过账
- 薪酬类型：基本工资/计件工资/津贴/奖金
- 成本类型：直接材料/直接人工/制造费用

---

## 5. 核心业务流程

### 5.1 计件薪酬到成本核算流程

```mermaid
sequenceDiagram
    participant MES as MES系统
    participant HR as 人事管理
    participant COST as 成本核算
    participant GL as 总账管理
    participant RPT as 财务报表
    
    MES->>HR: 传递工时数据
    HR->>HR: 计算计件薪酬
    HR->>COST: 传递人工成本
    COST->>COST: 执行成本核算
    COST->>GL: 生成成本凭证
    GL->>RPT: 更新财务报表
```

### 5.2 业务到财务一体化流程

```mermaid
sequenceDiagram
    participant BIZ as 业务系统
    participant TEMP as 凭证模板
    participant GL as 总账管理
    participant COST as 成本核算
    participant RPT as 财务报表
    
    BIZ->>TEMP: 触发凭证生成
    TEMP->>GL: 生成凭证草稿
    GL->>GL: 凭证审核过账
    GL->>COST: 更新成本数据
    COST->>RPT: 更新财务报表
```

---

## 6. 数据模型设计

### 6.1 薪酬数据模型
```sql
CREATE TABLE payroll_records (
    payroll_id VARCHAR(30) PRIMARY KEY,
    employee_id VARCHAR(20) NOT NULL,
    pay_period VARCHAR(7) NOT NULL,
    basic_salary DECIMAL(10,2),
    piece_wage DECIMAL(10,2),
    allowance DECIMAL(10,2),
    bonus DECIMAL(10,2),
    total_gross DECIMAL(10,2),
    deductions DECIMAL(10,2),
    net_pay DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'DRAFT',
    created_date DATETIME DEFAULT NOW()
);
```

### 6.2 成本核算数据模型
```sql
CREATE TABLE cost_calculations (
    calc_id VARCHAR(30) PRIMARY KEY,
    period VARCHAR(7) NOT NULL,
    product_code VARCHAR(12) NOT NULL,
    order_id VARCHAR(20),
    material_cost DECIMAL(15,2),
    labor_cost DECIMAL(15,2),
    overhead_cost DECIMAL(15,2),
    total_cost DECIMAL(15,2),
    unit_cost DECIMAL(10,4),
    quantity DECIMAL(15,3),
    calc_date DATETIME DEFAULT NOW()
);
```

### 6.3 财务凭证数据模型
```sql
CREATE TABLE vouchers (
    voucher_id VARCHAR(30) PRIMARY KEY,
    voucher_no VARCHAR(20) UNIQUE NOT NULL,
    voucher_date DATE NOT NULL,
    period VARCHAR(7) NOT NULL,
    source_type VARCHAR(20),
    source_id VARCHAR(30),
    total_debit DECIMAL(15,2),
    total_credit DECIMAL(15,2),
    status VARCHAR(20) DEFAULT 'DRAFT',
    created_by VARCHAR(20),
    created_date DATETIME DEFAULT NOW()
);
```

---

## 7. 性能和可靠性要求

### 7.1 性能指标
- **薪酬计算时间**: 1000员工 ≤ 10分钟
- **成本核算时间**: 1000订单 ≤ 10分钟
- **凭证生成时间**: ≤ 5秒
- **财务报表生成**: ≤ 30秒
- **系统可用性**: ≥ 99.5%

### 7.2 业务指标
- **薪酬计算准确率**: 100%
- **成本核算准确率**: ≥ 99%
- **凭证生成准确率**: 100%
- **业财数据一致性**: 100%
- **财务处理效率**: 提升 ≥ 85%

---

## 8. 用户界面设计

### 8.1 财务工作台
- **财务概览**: 关键财务指标、异常提醒、待办事项
- **凭证中心**: 凭证录入、审核、查询、批量操作
- **成本中心**: 成本核算、成本分析、成本控制
- **报表中心**: 财务报表、分析报告、自定义报表

### 8.2 人事工作台
- **员工管理**: 员工档案、组织架构、入离职管理
- **考勤管理**: 考勤记录、异常处理、统计分析
- **薪酬管理**: 薪酬计算、工资条、薪酬分析
- **自助服务**: 员工自助查询、申请审批、信息维护

### 8.3 成本分析中心
- **成本构成**: 材料成本、人工成本、制造费用分析
- **成本趋势**: 成本变动趋势、同比环比分析
- **成本对比**: 实际成本与标准成本对比分析
- **盈利分析**: 产品盈利能力、客户盈利分析

### 8.4 移动端应用
- **移动审批**: 凭证审批、薪酬审批、费用审批
- **移动查询**: 财务数据查询、薪酬查询、成本查询
- **移动报表**: 关键指标、趋势图表、异常提醒
- **员工自助**: 工资条查询、考勤查询、申请提交

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 架构优化版本，整合财务管理和人事管理子系统 | 产品团队 |

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 业财一体化 ✅ 成本核算精确化 ✅ 薪酬管理自动化 ✅
