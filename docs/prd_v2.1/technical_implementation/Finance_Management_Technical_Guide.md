# 财务管理模块技术实施指导

> **版本**: 2.1  
> **状态**: 技术实施指导  
> **撰写人**: 技术团队  
> **日期**: 2025-08-02  
> **目标**: 为财务管理模块提供完整的技术实施指导，包括界面原型设计、MOCK数据设计、技术实施细节和开发指导文档  
> **设计规范**: 参考 [原型设计规范](../../prototype_design_guidelines.md)  
> **业务需求**: 参考 [财务管理模块PRD](../Finance_Management_Module.md)

---

## 1. 界面原型设计指导

### 1.1 页面架构设计

#### 1.1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (60px)                      │
├─────────────────────────────────────────────────────────┤
│ 侧边栏 │                主内容区域                        │
│(240px) │  ┌─────────────────────────────────────────┐   │
│        │  │     财务管理模块导航 (48px)              │   │
│ 财务   │  ├─────────────────────────────────────────┤   │
│ 概览   │  │                                         │   │
│ 应收   │  │            功能页面内容                  │   │
│ 应付   │  │                                         │   │
│ 发票   │  │                                         │   │
│ 报表   │  └─────────────────────────────────────────┘   │
│ 成本   │                                               │
└─────────────────────────────────────────────────────────┘
```

#### 1.1.2 核心页面设计

**财务概览页面 (FinanceOverview.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 财务概览 [刷新] [导出报表] [财务设置]                     │
├─────────────────────────────────────────────────────────┤
│ 财务指标卡片                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │总资产   │总负债   │净资产   │营业收入 │净利润   │     │
│ │2,500万  │ 800万   │1,700万  │ 1,200万 │ 180万   │     │
│ │ +5.2%   │ +2.1%   │ +6.8%   │ +12.5%  │ +15.3%  │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 现金流量图表                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 经营活动现金流 ████████████████████████████████████ │ │
│ │ 投资活动现金流 ████████████████████████████████████ │ │
│ │ 筹资活动现金流 ████████████████████████████████████ │ │
│ │ 净现金流量     ████████████████████████████████████ │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 应收应付概况                                            │
│ ┌──────────────┬──────────────┬──────────────────────┐ │
│ │ 应收账款     │ 应付账款     │ 资金缺口             │ │
│ │ 450万元      │ 320万元      │ 130万元              │ │
│ │ 逾期: 25万   │ 逾期: 15万   │ 预计回款: 200万      │ │
│ └──────────────┴──────────────┴──────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 待办事项                                                │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ • 客户A逾期账款催收 (25万元)                        │ │
│ │ • 供应商B付款到期 (18万元)                          │ │
│ │ • 月度财务报表待审核                                │ │
│ │ • 发票开具待处理 (12张)                             │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

**应收账款管理页面 (ReceivableManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 应收账款管理 [新增应收] [批量催收] [账龄分析]             │
├─────────────────────────────────────────────────────────┤
│ 应收账款统计                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │总应收   │正常     │逾期     │坏账     │回款率   │     │
│ │ 450万   │ 380万   │ 60万    │ 10万    │  85%    │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 账龄分析图表                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 30天内  ████████████████████████████████████████    │ │
│ │ 31-60天 ████████████████████████████████████████    │ │
│ │ 61-90天 ████████████████████████████████████████    │ │
│ │ 90天以上 ███████████████████████████████████████    │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 应收账款列表                                            │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │客户  │订单号│应收  │已收  │余额  │账龄  │操作  │      │
│ │名称  │      │金额  │金额  │      │      │      │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │客户A │SO001 │50万  │25万  │25万  │45天  │催收  │      │
│ │客户B │SO002 │30万  │30万  │ 0万  │ 0天  │已收  │      │
│ │客户C │SO003 │80万  │60万  │20万  │15天  │正常  │      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

**发票管理页面 (InvoiceManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 发票管理 [开具发票] [批量开票] [发票验证] [税务申报]       │
├─────────────────────────────────────────────────────────┤
│ 发票统计                                                │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │本月开票 │本月收票 │待开票   │待认证   │税额     │     │
│ │ 120张   │  85张   │  15张   │  8张    │ 45万    │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 发票开具流程                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 1. 选择订单 → 2. 填写信息 → 3. 税务审核 → 4. 开具   │ │
│ │    [完成]      [完成]       [进行中]     [待处理]   │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 发票列表                                                │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │发票号│类型  │客户  │金额  │税额  │状态  │操作  │      │
│ │      │      │      │      │      │      │      │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │001   │增值税│客户A │50万  │6.5万 │已开  │查看  │      │
│ │002   │普通  │客户B │30万  │ 0万  │已开  │查看  │      │
│ │003   │增值税│客户C │80万  │10.4万│草稿  │编辑  │      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

### 1.2 关键组件设计

#### 1.2.1 财务报表组件 (FinancialReport.vue)
```vue
<template>
  <div class="financial-report">
    <div class="report-header">
      <h3>{{ reportTitle }}</h3>
      <div class="report-controls">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        />
        <el-select v-model="reportType" @change="handleReportTypeChange">
          <el-option label="资产负债表" value="balance_sheet" />
          <el-option label="利润表" value="income_statement" />
          <el-option label="现金流量表" value="cash_flow" />
          <el-option label="成本分析表" value="cost_analysis" />
        </el-select>
        <el-button type="primary" @click="generateReport">生成报表</el-button>
        <el-button @click="exportReport">导出</el-button>
      </div>
    </div>
    
    <div class="report-content">
      <!-- 资产负债表 -->
      <div v-if="reportType === 'balance_sheet'" class="balance-sheet">
        <div class="report-section">
          <h4>资产</h4>
          <el-table :data="balanceSheetData.assets" border>
            <el-table-column prop="item" label="项目" width="200" />
            <el-table-column prop="currentPeriod" label="期末余额" align="right">
              <template #default="{ row }">
                {{ formatCurrency(row.currentPeriod) }}
              </template>
            </el-table-column>
            <el-table-column prop="previousPeriod" label="期初余额" align="right">
              <template #default="{ row }">
                {{ formatCurrency(row.previousPeriod) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="report-section">
          <h4>负债和所有者权益</h4>
          <el-table :data="balanceSheetData.liabilities" border>
            <el-table-column prop="item" label="项目" width="200" />
            <el-table-column prop="currentPeriod" label="期末余额" align="right">
              <template #default="{ row }">
                {{ formatCurrency(row.currentPeriod) }}
              </template>
            </el-table-column>
            <el-table-column prop="previousPeriod" label="期初余额" align="right">
              <template #default="{ row }">
                {{ formatCurrency(row.previousPeriod) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- 利润表 -->
      <div v-else-if="reportType === 'income_statement'" class="income-statement">
        <el-table :data="incomeStatementData" border>
          <el-table-column prop="item" label="项目" width="200" />
          <el-table-column prop="currentPeriod" label="本期金额" align="right">
            <template #default="{ row }">
              {{ formatCurrency(row.currentPeriod) }}
            </template>
          </el-table-column>
          <el-table-column prop="previousPeriod" label="上期金额" align="right">
            <template #default="{ row }">
              {{ formatCurrency(row.previousPeriod) }}
            </template>
          </el-table-column>
          <el-table-column prop="variance" label="变动额" align="right">
            <template #default="{ row }">
              <span :class="getVarianceClass(row.variance)">
                {{ formatCurrency(row.variance) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="varianceRate" label="变动率" align="right">
            <template #default="{ row }">
              <span :class="getVarianceClass(row.variance)">
                {{ formatPercentage(row.varianceRate) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 现金流量表 -->
      <div v-else-if="reportType === 'cash_flow'" class="cash-flow">
        <div class="cash-flow-chart">
          <div ref="cashFlowChartRef" style="height: 400px;"></div>
        </div>
        <el-table :data="cashFlowData" border>
          <el-table-column prop="item" label="项目" width="200" />
          <el-table-column prop="amount" label="金额" align="right">
            <template #default="{ row }">
              {{ formatCurrency(row.amount) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    
    <!-- 报表分析 -->
    <div class="report-analysis">
      <h4>财务分析</h4>
      <div class="analysis-grid">
        <div class="analysis-item">
          <div class="analysis-title">资产负债率</div>
          <div class="analysis-value">{{ formatPercentage(analysisData.debtRatio) }}</div>
          <div class="analysis-trend" :class="getTrendClass(analysisData.debtRatioTrend)">
            {{ analysisData.debtRatioTrend > 0 ? '↗' : '↘' }} 
            {{ Math.abs(analysisData.debtRatioTrend) }}%
          </div>
        </div>
        
        <div class="analysis-item">
          <div class="analysis-title">流动比率</div>
          <div class="analysis-value">{{ analysisData.currentRatio }}</div>
          <div class="analysis-trend" :class="getTrendClass(analysisData.currentRatioTrend)">
            {{ analysisData.currentRatioTrend > 0 ? '↗' : '↘' }} 
            {{ Math.abs(analysisData.currentRatioTrend) }}%
          </div>
        </div>
        
        <div class="analysis-item">
          <div class="analysis-title">净利润率</div>
          <div class="analysis-value">{{ formatPercentage(analysisData.netProfitMargin) }}</div>
          <div class="analysis-trend" :class="getTrendClass(analysisData.netProfitMarginTrend)">
            {{ analysisData.netProfitMarginTrend > 0 ? '↗' : '↘' }} 
            {{ Math.abs(analysisData.netProfitMarginTrend) }}%
          </div>
        </div>
        
        <div class="analysis-item">
          <div class="analysis-title">总资产周转率</div>
          <div class="analysis-value">{{ analysisData.assetTurnover }}</div>
          <div class="analysis-trend" :class="getTrendClass(analysisData.assetTurnoverTrend)">
            {{ analysisData.assetTurnoverTrend > 0 ? '↗' : '↘' }} 
            {{ Math.abs(analysisData.assetTurnoverTrend) }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  companyId: String,
  defaultReportType: { type: String, default: 'balance_sheet' }
});

const emit = defineEmits(['report-generated', 'report-exported']);

const reportType = ref(props.defaultReportType);
const dateRange = ref([]);
const balanceSheetData = ref({ assets: [], liabilities: [] });
const incomeStatementData = ref([]);
const cashFlowData = ref([]);
const analysisData = ref({});
const cashFlowChartRef = ref();
const cashFlowChart = ref(null);

const reportTitle = computed(() => {
  const titles = {
    'balance_sheet': '资产负债表',
    'income_statement': '利润表',
    'cash_flow': '现金流量表',
    'cost_analysis': '成本分析表'
  };
  return titles[reportType.value] || '财务报表';
});

const generateReport = async () => {
  try {
    const reportData = await financeService.generateReport({
      type: reportType.value,
      dateRange: dateRange.value,
      companyId: props.companyId
    });
    
    switch (reportType.value) {
      case 'balance_sheet':
        balanceSheetData.value = reportData;
        break;
      case 'income_statement':
        incomeStatementData.value = reportData;
        break;
      case 'cash_flow':
        cashFlowData.value = reportData;
        await nextTick();
        renderCashFlowChart();
        break;
    }
    
    // 生成财务分析
    analysisData.value = await financeService.generateAnalysis({
      type: reportType.value,
      dateRange: dateRange.value
    });
    
    emit('report-generated', reportData);
  } catch (error) {
    console.error('生成报表失败:', error);
  }
};

const renderCashFlowChart = () => {
  if (!cashFlowChartRef.value) return;
  
  if (cashFlowChart.value) {
    cashFlowChart.value.dispose();
  }
  
  cashFlowChart.value = echarts.init(cashFlowChartRef.value);
  
  const option = {
    title: {
      text: '现金流量趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['经营活动现金流', '投资活动现金流', '筹资活动现金流', '净现金流']
    },
    xAxis: {
      type: 'category',
      data: cashFlowData.value.map(item => item.period)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: value => formatCurrency(value)
      }
    },
    series: [
      {
        name: '经营活动现金流',
        type: 'line',
        data: cashFlowData.value.map(item => item.operating)
      },
      {
        name: '投资活动现金流',
        type: 'line',
        data: cashFlowData.value.map(item => item.investing)
      },
      {
        name: '筹资活动现金流',
        type: 'line',
        data: cashFlowData.value.map(item => item.financing)
      },
      {
        name: '净现金流',
        type: 'line',
        data: cashFlowData.value.map(item => item.net),
        lineStyle: { width: 3 }
      }
    ]
  };
  
  cashFlowChart.value.setOption(option);
};

const formatCurrency = (value) => {
  if (value === null || value === undefined) return '-';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

const formatPercentage = (value) => {
  if (value === null || value === undefined) return '-';
  return `${(value * 100).toFixed(2)}%`;
};

const getVarianceClass = (variance) => {
  if (variance > 0) return 'positive-variance';
  if (variance < 0) return 'negative-variance';
  return '';
};

const getTrendClass = (trend) => {
  if (trend > 0) return 'positive-trend';
  if (trend < 0) return 'negative-trend';
  return '';
};

const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    generateReport();
  }
};

const handleReportTypeChange = () => {
  generateReport();
};

const exportReport = () => {
  // 导出报表逻辑
  emit('report-exported', {
    type: reportType.value,
    dateRange: dateRange.value
  });
};

onMounted(() => {
  // 设置默认日期范围（当前月）
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  dateRange.value = [firstDay, lastDay];
  
  generateReport();
});
</script>
```

---

## 2. MOCK数据设计规范

### 2.1 核心数据模型

#### 2.1.1 应收账款数据模型 (Receivable)
```javascript
// receivable_mock.js
export const receivableMockData = {
  receivables: [
    {
      id: '***********-001',
      receivableNo: '***********00001',
      
      // 客户信息
      customerId: 'CUS-20250802-001',
      customerName: 'ABC玻璃制品有限公司',
      customerCode: 'CUS-ABC-001',
      
      // 订单信息
      salesOrderId: 'SO-20250802-001',
      salesOrderNo: 'SO-*************',
      contractId: 'CON-20250802-001',
      
      // 金额信息
      totalAmount: 500000, // 应收总额
      receivedAmount: 250000, // 已收金额
      remainingAmount: 250000, // 剩余金额
      currency: 'CNY',
      
      // 账期信息
      creditDays: 30, // 信用期天数
      invoiceDate: '2025-07-15T00:00:00Z',
      dueDate: '2025-08-14T00:00:00Z',
      overdueDays: 18, // 逾期天数
      
      // 状态信息
      status: 'overdue', // pending: 待收款, partial: 部分收款, overdue: 逾期, completed: 已完成, bad_debt: 坏账
      riskLevel: 'medium', // low: 低风险, medium: 中风险, high: 高风险
      
      // 收款记录
      payments: [
        {
          id: 'PAY-001',
          paymentDate: '2025-07-20T00:00:00Z',
          amount: 150000,
          paymentMethod: 'bank_transfer', // bank_transfer: 银行转账, cash: 现金, check: 支票
          bankAccount: '工商银行***1234',
          reference: 'TXN-********-001',
          operator: '财务员A'
        },
        {
          id: 'PAY-002',
          paymentDate: '2025-07-28T00:00:00Z',
          amount: 100000,
          paymentMethod: 'bank_transfer',
          bankAccount: '工商银行***1234',
          reference: 'TXN-********-001',
          operator: '财务员A'
        }
      ],
      
      // 催收记录
      collections: [
        {
          id: 'COL-001',
          collectionDate: '2025-08-01T00:00:00Z',
          method: 'phone', // phone: 电话, email: 邮件, visit: 上门, legal: 法律途径
          content: '电话催收，客户承诺本周内付款',
          result: 'promised', // promised: 承诺付款, refused: 拒绝, no_response: 无回应
          nextFollowUp: '2025-08-05T00:00:00Z',
          operator: '销售员B'
        }
      ],
      
      // 财务分析
      analysis: {
        ageGroup: '31-60天', // 账龄分组
        turnoverDays: 45, // 周转天数
        collectionProbability: 0.8, // 回收概率
        badDebtProvision: 12500, // 坏账准备
        interestCharge: 2500 // 逾期利息
      },
      
      createTime: '2025-07-15T08:00:00Z',
      updateTime: '2025-08-02T16:30:00Z'
    }
  ]
};
```

#### 2.1.2 应付账款数据模型 (Payable)
```javascript
// payable_mock.js
export const payableMockData = {
  payables: [
    {
      id: 'AP-20250802-001',
      payableNo: 'AP-*************',

      // 供应商信息
      supplierId: 'SUP-001',
      supplierName: '某某玻璃原料供应商',
      supplierCode: 'SUP-GLASS-001',

      // 采购信息
      purchaseOrderId: 'PO-20250801-001',
      purchaseOrderNo: 'PO-2025080100001',
      contractId: 'CON-SUP-20250801-001',

      // 金额信息
      totalAmount: 320000, // 应付总额
      paidAmount: 200000, // 已付金额
      remainingAmount: 120000, // 剩余金额
      currency: 'CNY',

      // 账期信息
      paymentTerms: 'NET30', // 付款条件
      invoiceDate: '2025-07-20T00:00:00Z',
      dueDate: '2025-08-19T00:00:00Z',
      overdueDays: 0, // 逾期天数

      // 状态信息
      status: 'pending', // pending: 待付款, partial: 部分付款, overdue: 逾期, completed: 已完成
      priority: 'high', // high: 高优先级, medium: 中优先级, low: 低优先级

      // 付款记录
      payments: [
        {
          id: 'PAY-SUP-001',
          paymentDate: '2025-07-25T00:00:00Z',
          amount: 200000,
          paymentMethod: 'bank_transfer',
          bankAccount: '建设银行***5678',
          reference: 'TXN-OUT-********-001',
          approver: '财务经理',
          operator: '财务员B'
        }
      ],

      // 发票信息
      invoices: [
        {
          invoiceId: 'INV-SUP-001',
          invoiceNo: 'SUP-INV-********-001',
          invoiceAmount: 320000,
          taxAmount: 41600,
          invoiceDate: '2025-07-20T00:00:00Z',
          verificationStatus: 'verified' // pending: 待验证, verified: 已验证, rejected: 已拒绝
        }
      ],

      // 审批流程
      approvalFlow: [
        {
          step: 1,
          approver: '部门经理',
          status: 'approved',
          approveTime: '2025-08-01T09:00:00Z',
          comments: '采购合规，同意付款'
        },
        {
          step: 2,
          approver: '财务经理',
          status: 'pending',
          comments: null
        }
      ],

      createTime: '2025-07-20T08:00:00Z',
      updateTime: '2025-08-02T16:30:00Z'
    }
  ]
};
```

#### 2.1.3 发票数据模型 (Invoice)
```javascript
// invoice_mock.js
export const invoiceMockData = {
  invoices: [
    {
      id: 'INV-20250802-001',
      invoiceNo: 'INV-*************',
      type: 'sales', // sales: 销售发票, purchase: 采购发票
      category: 'vat_special', // vat_special: 增值税专用发票, vat_ordinary: 增值税普通发票, ordinary: 普通发票

      // 开票信息
      issueDate: '2025-08-02T00:00:00Z',
      issuer: {
        name: '某某玻璃制造有限公司',
        taxNo: '91310000********9X',
        address: '上海市浦东新区张江高科技园区',
        phone: '021-********',
        bankAccount: '工商银行上海分行 ****************'
      },

      // 收票方信息
      recipient: {
        name: 'ABC玻璃制品有限公司',
        taxNo: '913200009********Y',
        address: '江苏省苏州市工业园区',
        phone: '0512-********',
        bankAccount: '建设银行苏州分行 65432109********'
      },

      // 发票明细
      items: [
        {
          id: 'INV-ITEM-001',
          productName: '12mm钢化玻璃',
          specification: '1200×2000×12mm',
          unit: '平方米',
          quantity: 100,
          unitPrice: 120,
          amount: 12000,
          taxRate: 0.13, // 税率
          taxAmount: 1560
        },
        {
          id: 'INV-ITEM-002',
          productName: '6mm浮法玻璃',
          specification: '1500×2500×6mm',
          unit: '平方米',
          quantity: 200,
          unitPrice: 45,
          amount: 9000,
          taxRate: 0.13,
          taxAmount: 1170
        }
      ],

      // 金额汇总
      summary: {
        subtotal: 21000, // 不含税金额
        totalTax: 2730, // 税额合计
        total: 23730, // 价税合计
        amountInWords: '贰万叁仟柒佰叁拾元整' // 大写金额
      },

      // 状态信息
      status: 'issued', // draft: 草稿, pending: 待开具, issued: 已开具, cancelled: 已作废, red_flushed: 已红冲

      // 关联业务
      relatedOrders: [
        {
          orderType: 'sales_order',
          orderId: 'SO-20250802-001',
          orderNo: 'SO-*************'
        }
      ],

      // 税务信息
      taxInfo: {
        taxPeriod: '2025-08', // 税款所属期
        declarationStatus: 'pending', // pending: 待申报, declared: 已申报, paid: 已缴税
        declarationDate: null,
        paymentDate: null
      },

      // 电子发票信息
      electronicInfo: {
        isElectronic: true,
        qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...', // 二维码
        verificationCode: 'ABC********9',
        downloadUrl: '/api/invoice/download/INV-*************.pdf'
      },

      createTime: '2025-08-02T10:00:00Z',
      updateTime: '2025-08-02T10:30:00Z'
    }
  ]
};
```

### 2.2 财务报表数据模型

#### 2.2.1 资产负债表数据
```javascript
// balance_sheet_mock.js
export const balanceSheetMockData = {
  reportDate: '2025-07-31T00:00:00Z',
  reportPeriod: '2025-07',

  assets: [
    // 流动资产
    {
      category: 'current_assets',
      item: '货币资金',
      code: '1001',
      currentPeriod: 5800000,
      previousPeriod: 5200000,
      variance: 600000,
      varianceRate: 0.115
    },
    {
      category: 'current_assets',
      item: '应收账款',
      code: '1131',
      currentPeriod: 4500000,
      previousPeriod: 4200000,
      variance: 300000,
      varianceRate: 0.071
    },
    {
      category: 'current_assets',
      item: '存货',
      code: '1401',
      currentPeriod: 8200000,
      previousPeriod: 7800000,
      variance: 400000,
      varianceRate: 0.051
    },

    // 非流动资产
    {
      category: 'non_current_assets',
      item: '固定资产',
      code: '1601',
      currentPeriod: 12000000,
      previousPeriod: 12500000,
      variance: -500000,
      varianceRate: -0.04
    },
    {
      category: 'non_current_assets',
      item: '无形资产',
      code: '1701',
      currentPeriod: 1500000,
      previousPeriod: 1600000,
      variance: -100000,
      varianceRate: -0.063
    }
  ],

  liabilities: [
    // 流动负债
    {
      category: 'current_liabilities',
      item: '应付账款',
      code: '2201',
      currentPeriod: 3200000,
      previousPeriod: 2800000,
      variance: 400000,
      varianceRate: 0.143
    },
    {
      category: 'current_liabilities',
      item: '短期借款',
      code: '2001',
      currentPeriod: 2000000,
      previousPeriod: 2500000,
      variance: -500000,
      varianceRate: -0.2
    },

    // 非流动负债
    {
      category: 'non_current_liabilities',
      item: '长期借款',
      code: '2501',
      currentPeriod: 3000000,
      previousPeriod: 3000000,
      variance: 0,
      varianceRate: 0
    },

    // 所有者权益
    {
      category: 'equity',
      item: '实收资本',
      code: '4001',
      currentPeriod: 10000000,
      previousPeriod: 10000000,
      variance: 0,
      varianceRate: 0
    },
    {
      category: 'equity',
      item: '未分配利润',
      code: '4103',
      currentPeriod: 12800000,
      previousPeriod: 11300000,
      variance: 1500000,
      varianceRate: 0.133
    }
  ],

  summary: {
    totalAssets: 32000000,
    totalLiabilities: 8200000,
    totalEquity: 23800000,
    debtRatio: 0.256, // 资产负债率
    currentRatio: 2.34, // 流动比率
    quickRatio: 1.87 // 速动比率
  }
};
```

---

## 3. 技术实施细节

### 3.1 核心业务逻辑实现

#### 3.1.1 应收账款账龄分析算法
```javascript
// composables/useReceivableAnalysis.js
import { ref, computed } from 'vue';

export function useReceivableAnalysis() {
  const receivables = ref([]);
  const analysisSettings = ref({
    ageGroups: [
      { name: '30天内', min: 0, max: 30, riskLevel: 'low' },
      { name: '31-60天', min: 31, max: 60, riskLevel: 'medium' },
      { name: '61-90天', min: 61, max: 90, riskLevel: 'high' },
      { name: '90天以上', min: 91, max: Infinity, riskLevel: 'critical' }
    ],
    badDebtRates: {
      low: 0.01,    // 1%
      medium: 0.05, // 5%
      high: 0.15,   // 15%
      critical: 0.50 // 50%
    }
  });

  // 计算账龄分析
  const calculateAgeAnalysis = () => {
    const today = new Date();
    const ageAnalysis = analysisSettings.value.ageGroups.map(group => ({
      ...group,
      count: 0,
      amount: 0,
      percentage: 0
    }));

    let totalAmount = 0;

    receivables.value.forEach(receivable => {
      const dueDate = new Date(receivable.dueDate);
      const overdueDays = Math.max(0, Math.floor((today - dueDate) / (1000 * 60 * 60 * 24)));

      // 找到对应的账龄组
      const ageGroup = ageAnalysis.find(group =>
        overdueDays >= group.min && overdueDays <= group.max
      );

      if (ageGroup) {
        ageGroup.count++;
        ageGroup.amount += receivable.remainingAmount;
      }

      totalAmount += receivable.remainingAmount;
    });

    // 计算百分比
    ageAnalysis.forEach(group => {
      group.percentage = totalAmount > 0 ? (group.amount / totalAmount) * 100 : 0;
    });

    return {
      ageGroups: ageAnalysis,
      totalAmount,
      totalCount: receivables.value.length
    };
  };

  // 计算坏账准备
  const calculateBadDebtProvision = () => {
    const ageAnalysis = calculateAgeAnalysis();
    let totalProvision = 0;

    ageAnalysis.ageGroups.forEach(group => {
      const rate = analysisSettings.value.badDebtRates[group.riskLevel] || 0;
      const provision = group.amount * rate;
      totalProvision += provision;
      group.badDebtProvision = provision;
    });

    return {
      totalProvision,
      ageGroups: ageAnalysis.ageGroups
    };
  };

  // 生成催收建议
  const generateCollectionSuggestions = () => {
    const suggestions = [];
    const today = new Date();

    receivables.value.forEach(receivable => {
      const dueDate = new Date(receivable.dueDate);
      const overdueDays = Math.floor((today - dueDate) / (1000 * 60 * 60 * 24));

      if (overdueDays > 0) {
        let urgency = 'low';
        let action = '电话催收';

        if (overdueDays > 90) {
          urgency = 'critical';
          action = '法律催收';
        } else if (overdueDays > 60) {
          urgency = 'high';
          action = '上门催收';
        } else if (overdueDays > 30) {
          urgency = 'medium';
          action = '邮件催收';
        }

        suggestions.push({
          receivableId: receivable.id,
          customerName: receivable.customerName,
          amount: receivable.remainingAmount,
          overdueDays,
          urgency,
          suggestedAction: action,
          priority: calculatePriority(receivable.remainingAmount, overdueDays)
        });
      }
    });

    // 按优先级排序
    return suggestions.sort((a, b) => b.priority - a.priority);
  };

  // 计算优先级
  const calculatePriority = (amount, overdueDays) => {
    // 金额权重 70%，逾期天数权重 30%
    const amountScore = Math.min(amount / 1000000, 1) * 70; // 最高70分
    const overdueScore = Math.min(overdueDays / 90, 1) * 30; // 最高30分
    return amountScore + overdueScore;
  };

  // 预测回款
  const predictCollection = (receivableId) => {
    const receivable = receivables.value.find(r => r.id === receivableId);
    if (!receivable) return null;

    const today = new Date();
    const dueDate = new Date(receivable.dueDate);
    const overdueDays = Math.floor((today - dueDate) / (1000 * 60 * 60 * 24));

    // 基于历史数据和客户信用等级预测回款概率
    let probability = 0.9; // 基础概率90%

    // 逾期天数影响
    if (overdueDays > 0) {
      probability *= Math.max(0.1, 1 - (overdueDays / 365)); // 逾期越久概率越低
    }

    // 客户风险等级影响
    const riskMultipliers = {
      low: 1.0,
      medium: 0.8,
      high: 0.6,
      critical: 0.3
    };
    probability *= riskMultipliers[receivable.riskLevel] || 0.5;

    // 历史付款记录影响
    const paymentHistory = receivable.payments || [];
    if (paymentHistory.length > 0) {
      const avgPaymentDelay = paymentHistory.reduce((sum, payment) => {
        const paymentDate = new Date(payment.paymentDate);
        const expectedDate = new Date(receivable.dueDate);
        const delay = Math.max(0, (paymentDate - expectedDate) / (1000 * 60 * 60 * 24));
        return sum + delay;
      }, 0) / paymentHistory.length;

      if (avgPaymentDelay > 30) {
        probability *= 0.7; // 历史延迟付款降低概率
      }
    }

    // 预计回款时间
    let estimatedDays = 0;
    if (overdueDays <= 30) {
      estimatedDays = 15;
    } else if (overdueDays <= 60) {
      estimatedDays = 30;
    } else if (overdueDays <= 90) {
      estimatedDays = 45;
    } else {
      estimatedDays = 90;
    }

    const estimatedDate = new Date(today.getTime() + estimatedDays * 24 * 60 * 60 * 1000);

    return {
      probability: Math.max(0.05, Math.min(0.95, probability)), // 限制在5%-95%之间
      estimatedAmount: receivable.remainingAmount * probability,
      estimatedDate: estimatedDate.toISOString(),
      confidence: probability > 0.7 ? 'high' : probability > 0.4 ? 'medium' : 'low'
    };
  };

  return {
    receivables,
    calculateAgeAnalysis,
    calculateBadDebtProvision,
    generateCollectionSuggestions,
    predictCollection
  };
}
```

#### 3.1.2 发票管理系统
```javascript
// composables/useInvoiceManagement.js
import { ref, computed } from 'vue';

export function useInvoiceManagement() {
  const invoices = ref([]);
  const taxRates = ref({
    'vat_13': 0.13,  // 增值税13%
    'vat_9': 0.09,   // 增值税9%
    'vat_6': 0.06,   // 增值税6%
    'vat_3': 0.03,   // 增值税3%
    'vat_0': 0.00    // 免税
  });

  // 创建发票
  const createInvoice = async (invoiceData) => {
    try {
      // 验证发票数据
      validateInvoiceData(invoiceData);

      // 生成发票号
      const invoiceNo = await generateInvoiceNumber(invoiceData.type);

      // 计算税额
      const calculatedItems = calculateTaxAmounts(invoiceData.items);

      // 生成发票摘要
      const summary = calculateInvoiceSummary(calculatedItems);

      // 转换金额大写
      const amountInWords = convertToChineseAmount(summary.total);

      const newInvoice = {
        id: `INV-${Date.now()}`,
        invoiceNo,
        ...invoiceData,
        items: calculatedItems,
        summary: {
          ...summary,
          amountInWords
        },
        status: 'draft',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      };

      invoices.value.push(newInvoice);
      return newInvoice;
    } catch (error) {
      console.error('创建发票失败:', error);
      throw error;
    }
  };

  // 验证发票数据
  const validateInvoiceData = (data) => {
    if (!data.issuer?.taxNo) {
      throw new Error('开票方税号不能为空');
    }

    if (!data.recipient?.taxNo) {
      throw new Error('收票方税号不能为空');
    }

    if (!data.items || data.items.length === 0) {
      throw new Error('发票明细不能为空');
    }

    // 验证税号格式
    const taxNoPattern = /^[0-9A-Z]{15,20}$/;
    if (!taxNoPattern.test(data.issuer.taxNo)) {
      throw new Error('开票方税号格式不正确');
    }

    if (!taxNoPattern.test(data.recipient.taxNo)) {
      throw new Error('收票方税号格式不正确');
    }

    // 验证明细项
    data.items.forEach((item, index) => {
      if (!item.productName) {
        throw new Error(`第${index + 1}行商品名称不能为空`);
      }
      if (item.quantity <= 0) {
        throw new Error(`第${index + 1}行数量必须大于0`);
      }
      if (item.unitPrice < 0) {
        throw new Error(`第${index + 1}行单价不能为负数`);
      }
    });
  };

  // 生成发票号
  const generateInvoiceNumber = async (type) => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');

    // 获取当日发票序号
    const todayInvoices = invoices.value.filter(inv =>
      inv.issueDate?.startsWith(`${year}-${month}-${day}`)
    );

    const sequence = String(todayInvoices.length + 1).padStart(4, '0');

    const typePrefix = {
      'sales': 'XS',
      'purchase': 'CG'
    };

    return `${typePrefix[type] || 'INV'}-${year}${month}${day}-${sequence}`;
  };

  // 计算税额
  const calculateTaxAmounts = (items) => {
    return items.map(item => {
      const amount = item.quantity * item.unitPrice;
      const taxRate = item.taxRate || 0;
      const taxAmount = amount * taxRate;

      return {
        ...item,
        amount,
        taxAmount,
        totalAmount: amount + taxAmount
      };
    });
  };

  // 计算发票汇总
  const calculateInvoiceSummary = (items) => {
    const subtotal = items.reduce((sum, item) => sum + item.amount, 0);
    const totalTax = items.reduce((sum, item) => sum + item.taxAmount, 0);
    const total = subtotal + totalTax;

    return {
      subtotal: Math.round(subtotal * 100) / 100,
      totalTax: Math.round(totalTax * 100) / 100,
      total: Math.round(total * 100) / 100
    };
  };

  // 转换金额为中文大写
  const convertToChineseAmount = (amount) => {
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];

    if (amount === 0) return '零元整';

    const integerPart = Math.floor(amount);
    const decimalPart = Math.round((amount - integerPart) * 100);

    let result = '';

    // 处理整数部分
    if (integerPart > 0) {
      const integerStr = integerPart.toString();
      for (let i = 0; i < integerStr.length; i++) {
        const digit = parseInt(integerStr[i]);
        const unitIndex = integerStr.length - i - 1;

        if (digit !== 0) {
          result += digits[digit] + units[unitIndex];
        } else if (result && !result.endsWith('零')) {
          result += '零';
        }
      }
      result += '元';
    }

    // 处理小数部分
    if (decimalPart > 0) {
      const jiao = Math.floor(decimalPart / 10);
      const fen = decimalPart % 10;

      if (jiao > 0) {
        result += digits[jiao] + '角';
      }
      if (fen > 0) {
        result += digits[fen] + '分';
      }
    } else {
      result += '整';
    }

    return result;
  };

  // 开具发票
  const issueInvoice = async (invoiceId) => {
    const invoice = invoices.value.find(inv => inv.id === invoiceId);
    if (!invoice) {
      throw new Error('发票不存在');
    }

    if (invoice.status !== 'draft') {
      throw new Error('只能开具草稿状态的发票');
    }

    try {
      // 调用税务系统API开具发票
      const taxSystemResponse = await callTaxSystem({
        action: 'issue_invoice',
        invoiceData: invoice
      });

      // 更新发票状态
      invoice.status = 'issued';
      invoice.issueDate = new Date().toISOString();
      invoice.electronicInfo = {
        ...invoice.electronicInfo,
        verificationCode: taxSystemResponse.verificationCode,
        qrCode: taxSystemResponse.qrCode
      };
      invoice.updateTime = new Date().toISOString();

      return invoice;
    } catch (error) {
      console.error('开具发票失败:', error);
      throw error;
    }
  };

  // 作废发票
  const cancelInvoice = async (invoiceId, reason) => {
    const invoice = invoices.value.find(inv => inv.id === invoiceId);
    if (!invoice) {
      throw new Error('发票不存在');
    }

    if (invoice.status !== 'issued') {
      throw new Error('只能作废已开具的发票');
    }

    try {
      // 调用税务系统API作废发票
      await callTaxSystem({
        action: 'cancel_invoice',
        invoiceNo: invoice.invoiceNo,
        reason
      });

      // 更新发票状态
      invoice.status = 'cancelled';
      invoice.cancelReason = reason;
      invoice.cancelTime = new Date().toISOString();
      invoice.updateTime = new Date().toISOString();

      return invoice;
    } catch (error) {
      console.error('作废发票失败:', error);
      throw error;
    }
  };

  // 红冲发票
  const redFlushInvoice = async (originalInvoiceId, reason) => {
    const originalInvoice = invoices.value.find(inv => inv.id === originalInvoiceId);
    if (!originalInvoice) {
      throw new Error('原发票不存在');
    }

    if (originalInvoice.status !== 'issued') {
      throw new Error('只能红冲已开具的发票');
    }

    try {
      // 创建红冲发票
      const redInvoiceData = {
        ...originalInvoice,
        type: originalInvoice.type,
        category: originalInvoice.category,
        items: originalInvoice.items.map(item => ({
          ...item,
          quantity: -item.quantity,
          amount: -item.amount,
          taxAmount: -item.taxAmount
        })),
        redFlushInfo: {
          originalInvoiceId,
          originalInvoiceNo: originalInvoice.invoiceNo,
          reason
        }
      };

      const redInvoice = await createInvoice(redInvoiceData);
      await issueInvoice(redInvoice.id);

      // 更新原发票状态
      originalInvoice.status = 'red_flushed';
      originalInvoice.redFlushInfo = {
        redInvoiceId: redInvoice.id,
        redInvoiceNo: redInvoice.invoiceNo,
        reason,
        redFlushTime: new Date().toISOString()
      };
      originalInvoice.updateTime = new Date().toISOString();

      return redInvoice;
    } catch (error) {
      console.error('红冲发票失败:', error);
      throw error;
    }
  };

  // 模拟税务系统调用
  const callTaxSystem = async (params) => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    switch (params.action) {
      case 'issue_invoice':
        return {
          success: true,
          verificationCode: generateVerificationCode(),
          qrCode: generateQRCode(params.invoiceData)
        };

      case 'cancel_invoice':
        return { success: true };

      default:
        throw new Error('不支持的操作');
    }
  };

  // 生成验证码
  const generateVerificationCode = () => {
    return Math.random().toString(36).substr(2, 12).toUpperCase();
  };

  // 生成二维码
  const generateQRCode = (invoiceData) => {
    // 实际项目中应该使用真实的二维码生成库
    return `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;
  };

  return {
    invoices,
    createInvoice,
    issueInvoice,
    cancelInvoice,
    redFlushInvoice,
    calculateTaxAmounts,
    convertToChineseAmount
  };
}
```

### 3.2 财务报表生成引擎

#### 3.2.1 报表数据聚合器
```javascript
// composables/useReportGenerator.js
import { ref, computed } from 'vue';

export function useReportGenerator() {
  const reportData = ref({});
  const reportTemplates = ref({});

  // 生成资产负债表
  const generateBalanceSheet = async (params) => {
    const { startDate, endDate, companyId } = params;

    try {
      // 获取会计科目余额
      const accountBalances = await getAccountBalances(startDate, endDate, companyId);

      // 按科目分类汇总
      const assets = aggregateByCategory(accountBalances, 'asset');
      const liabilities = aggregateByCategory(accountBalances, 'liability');
      const equity = aggregateByCategory(accountBalances, 'equity');

      // 计算财务比率
      const ratios = calculateFinancialRatios(assets, liabilities, equity);

      return {
        reportType: 'balance_sheet',
        reportDate: endDate,
        assets,
        liabilities,
        equity,
        ratios,
        summary: {
          totalAssets: sumCategory(assets),
          totalLiabilities: sumCategory(liabilities),
          totalEquity: sumCategory(equity)
        }
      };
    } catch (error) {
      console.error('生成资产负债表失败:', error);
      throw error;
    }
  };

  // 生成利润表
  const generateIncomeStatement = async (params) => {
    const { startDate, endDate, companyId } = params;

    try {
      // 获取损益类科目发生额
      const incomeData = await getIncomeStatementData(startDate, endDate, companyId);

      // 计算各项指标
      const revenue = incomeData.revenue || 0;
      const costOfSales = incomeData.costOfSales || 0;
      const grossProfit = revenue - costOfSales;
      const operatingExpenses = incomeData.operatingExpenses || 0;
      const operatingProfit = grossProfit - operatingExpenses;
      const nonOperatingIncome = incomeData.nonOperatingIncome || 0;
      const nonOperatingExpenses = incomeData.nonOperatingExpenses || 0;
      const profitBeforeTax = operatingProfit + nonOperatingIncome - nonOperatingExpenses;
      const incomeTax = incomeData.incomeTax || 0;
      const netProfit = profitBeforeTax - incomeTax;

      return {
        reportType: 'income_statement',
        reportPeriod: { startDate, endDate },
        items: [
          { item: '营业收入', amount: revenue },
          { item: '营业成本', amount: costOfSales },
          { item: '毛利润', amount: grossProfit },
          { item: '营业费用', amount: operatingExpenses },
          { item: '营业利润', amount: operatingProfit },
          { item: '营业外收入', amount: nonOperatingIncome },
          { item: '营业外支出', amount: nonOperatingExpenses },
          { item: '利润总额', amount: profitBeforeTax },
          { item: '所得税费用', amount: incomeTax },
          { item: '净利润', amount: netProfit }
        ],
        ratios: {
          grossProfitMargin: revenue > 0 ? grossProfit / revenue : 0,
          operatingProfitMargin: revenue > 0 ? operatingProfit / revenue : 0,
          netProfitMargin: revenue > 0 ? netProfit / revenue : 0
        }
      };
    } catch (error) {
      console.error('生成利润表失败:', error);
      throw error;
    }
  };

  // 生成现金流量表
  const generateCashFlowStatement = async (params) => {
    const { startDate, endDate, companyId } = params;

    try {
      const cashFlowData = await getCashFlowData(startDate, endDate, companyId);

      return {
        reportType: 'cash_flow',
        reportPeriod: { startDate, endDate },
        operatingActivities: {
          cashFromOperations: cashFlowData.operatingInflows || 0,
          cashToOperations: cashFlowData.operatingOutflows || 0,
          netOperatingCashFlow: (cashFlowData.operatingInflows || 0) - (cashFlowData.operatingOutflows || 0)
        },
        investingActivities: {
          cashFromInvesting: cashFlowData.investingInflows || 0,
          cashToInvesting: cashFlowData.investingOutflows || 0,
          netInvestingCashFlow: (cashFlowData.investingInflows || 0) - (cashFlowData.investingOutflows || 0)
        },
        financingActivities: {
          cashFromFinancing: cashFlowData.financingInflows || 0,
          cashToFinancing: cashFlowData.financingOutflows || 0,
          netFinancingCashFlow: (cashFlowData.financingInflows || 0) - (cashFlowData.financingOutflows || 0)
        },
        summary: {
          netCashFlow: cashFlowData.netCashFlow || 0,
          beginningCash: cashFlowData.beginningCash || 0,
          endingCash: cashFlowData.endingCash || 0
        }
      };
    } catch (error) {
      console.error('生成现金流量表失败:', error);
      throw error;
    }
  };

  // 按分类汇总
  const aggregateByCategory = (balances, category) => {
    return balances
      .filter(balance => balance.category === category)
      .map(balance => ({
        item: balance.accountName,
        code: balance.accountCode,
        currentPeriod: balance.endingBalance,
        previousPeriod: balance.beginningBalance,
        variance: balance.endingBalance - balance.beginningBalance,
        varianceRate: balance.beginningBalance !== 0 ?
          (balance.endingBalance - balance.beginningBalance) / balance.beginningBalance : 0
      }));
  };

  // 计算分类合计
  const sumCategory = (categoryData) => {
    return categoryData.reduce((sum, item) => sum + item.currentPeriod, 0);
  };

  // 计算财务比率
  const calculateFinancialRatios = (assets, liabilities, equity) => {
    const totalAssets = sumCategory(assets);
    const totalLiabilities = sumCategory(liabilities);
    const currentAssets = assets
      .filter(item => ['货币资金', '应收账款', '存货'].includes(item.item))
      .reduce((sum, item) => sum + item.currentPeriod, 0);
    const currentLiabilities = liabilities
      .filter(item => ['应付账款', '短期借款'].includes(item.item))
      .reduce((sum, item) => sum + item.currentPeriod, 0);

    return {
      debtRatio: totalAssets > 0 ? totalLiabilities / totalAssets : 0,
      currentRatio: currentLiabilities > 0 ? currentAssets / currentLiabilities : 0,
      quickRatio: currentLiabilities > 0 ?
        (currentAssets - assets.find(item => item.item === '存货')?.currentPeriod || 0) / currentLiabilities : 0
    };
  };

  return {
    generateBalanceSheet,
    generateIncomeStatement,
    generateCashFlowStatement
  };
}
```

---

## 4. 开发指导文档

### 4.1 开发规范

#### 4.1.1 财务模块组件规范
```javascript
// 财务管理组件命名规范
const FINANCE_COMPONENT_NAMING = {
  // 页面组件
  pages: [
    'FinanceOverview.vue',
    'ReceivableManagement.vue',
    'PayableManagement.vue',
    'InvoiceManagement.vue',
    'FinancialReports.vue',
    'CostAnalysis.vue'
  ],

  // 业务组件
  business: [
    'FinancialReportGenerator.vue',
    'InvoiceCreator.vue',
    'ReceivableAgeAnalysis.vue',
    'PaymentScheduler.vue',
    'TaxCalculator.vue'
  ],

  // 通用组件
  common: [
    'CurrencyInput.vue',
    'TaxRateSelector.vue',
    'AccountSelector.vue',
    'FinancialChart.vue'
  ]
};
```

#### 4.1.2 状态管理规范
```javascript
// stores/finance.js
import { defineStore } from 'pinia';

export const useFinanceStore = defineStore('finance', {
  state: () => ({
    // 应收应付数据
    receivables: [],
    payables: [],

    // 发票数据
    invoices: [],

    // 财务报表数据
    reports: {},

    // 财务设置
    settings: {
      taxRates: {},
      accountingPeriod: 'monthly',
      currency: 'CNY'
    },

    // 加载状态
    loading: {
      receivables: false,
      payables: false,
      invoices: false,
      reports: false
    }
  }),

  getters: {
    // 应收账款统计
    receivableStatistics: (state) => {
      const total = state.receivables.reduce((sum, r) => sum + r.remainingAmount, 0);
      const overdue = state.receivables
        .filter(r => r.status === 'overdue')
        .reduce((sum, r) => sum + r.remainingAmount, 0);

      return { total, overdue, normal: total - overdue };
    },

    // 发票统计
    invoiceStatistics: (state) => {
      return state.invoices.reduce((stats, invoice) => {
        stats[invoice.status] = (stats[invoice.status] || 0) + 1;
        return stats;
      }, {});
    }
  },

  actions: {
    // 加载应收账款
    async loadReceivables(filters = {}) {
      this.loading.receivables = true;
      try {
        const response = await financeService.getReceivables(filters);
        this.receivables = response.data;
      } finally {
        this.loading.receivables = false;
      }
    },

    // 创建发票
    async createInvoice(invoiceData) {
      const newInvoice = await financeService.createInvoice(invoiceData);
      this.invoices.push(newInvoice);
      return newInvoice;
    }
  }
});
```

### 4.2 测试策略

#### 4.2.1 财务计算测试
```javascript
// tests/unit/composables/useInvoiceManagement.test.js
import { describe, it, expect } from 'vitest';
import { useInvoiceManagement } from '@/composables/useInvoiceManagement';

describe('useInvoiceManagement', () => {
  it('应该正确计算发票税额', () => {
    const { calculateTaxAmounts } = useInvoiceManagement();

    const items = [
      {
        productName: '测试商品',
        quantity: 100,
        unitPrice: 10,
        taxRate: 0.13
      }
    ];

    const result = calculateTaxAmounts(items);

    expect(result[0].amount).toBe(1000);
    expect(result[0].taxAmount).toBe(130);
    expect(result[0].totalAmount).toBe(1130);
  });

  it('应该正确转换金额为中文大写', () => {
    const { convertToChineseAmount } = useInvoiceManagement();

    expect(convertToChineseAmount(1234.56)).toBe('壹仟贰佰叁拾肆元伍角陆分');
    expect(convertToChineseAmount(10000)).toBe('壹万元整');
    expect(convertToChineseAmount(0)).toBe('零元整');
  });
});
```

### 4.3 集成方案

#### 4.3.1 税务系统集成
```javascript
// services/taxSystemService.js
export class TaxSystemService {
  constructor(config) {
    this.baseURL = config.baseURL;
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000;
  }

  // 开具发票
  async issueInvoice(invoiceData) {
    try {
      const response = await this.request('/api/invoice/issue', {
        method: 'POST',
        data: invoiceData
      });

      return response.data;
    } catch (error) {
      console.error('税务系统开票失败:', error);
      throw new Error('开票失败，请检查网络连接或联系系统管理员');
    }
  }

  // 查询发票状态
  async queryInvoiceStatus(invoiceNo) {
    try {
      const response = await this.request(`/api/invoice/status/${invoiceNo}`);
      return response.data;
    } catch (error) {
      console.error('查询发票状态失败:', error);
      throw error;
    }
  }

  // 发票验证
  async verifyInvoice(invoiceNo, verificationCode) {
    try {
      const response = await this.request('/api/invoice/verify', {
        method: 'POST',
        data: { invoiceNo, verificationCode }
      });

      return response.data;
    } catch (error) {
      console.error('发票验证失败:', error);
      throw error;
    }
  }

  // 通用请求方法
  async request(url, options = {}) {
    const config = {
      url: this.baseURL + url,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // 实际项目中使用 axios 或其他 HTTP 客户端
    return await fetch(config.url, config);
  }
}
```

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，财务管理模块技术实施指导 | 技术团队 |

---

**文档状态**: 技术实施指导完成 ✅
**覆盖内容**: 界面原型设计 ✅ MOCK数据设计 ✅ 技术实施细节 ✅ 开发指导文档 ✅
**技术栈**: Vue3 ✅ 财务计算 ✅ 发票管理 ✅ 报表生成 ✅
```
