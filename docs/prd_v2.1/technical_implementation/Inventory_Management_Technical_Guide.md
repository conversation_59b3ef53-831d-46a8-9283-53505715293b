# 库存管理模块技术实施指导

> **版本**: 2.1  
> **状态**: 技术实施指导  
> **撰写人**: 技术团队  
> **日期**: 2025-08-02  
> **目标**: 为库存管理模块提供完整的技术实施指导，包括界面原型设计、MOCK数据设计、技术实施细节和开发指导文档  
> **设计规范**: 参考 [原型设计规范](../../prototype_design_guidelines.md)  
> **业务需求**: 参考 [库存管理模块PRD](../Inventory_Management_Module.md)

---

## 1. 界面原型设计指导

### 1.1 页面架构设计

#### 1.1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (60px)                      │
├─────────────────────────────────────────────────────────┤
│ 侧边栏 │                主内容区域                        │
│(240px) │  ┌─────────────────────────────────────────┐   │
│        │  │     库存管理模块导航 (48px)              │   │
│ 仓库   │  ├─────────────────────────────────────────┤   │
│ 库存   │  │                                         │   │
│ 入库   │  │            功能页面内容                  │   │
│ 出库   │  │                                         │   │
│ 盘点   │  │                                         │   │
│ 采购   │  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

#### 1.1.2 核心页面设计

**库存总览页面 (InventoryOverview.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 库存总览 [刷新] [导出] [库存预警设置]                     │
├─────────────────────────────────────────────────────────┤
│ 库存统计卡片                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │总库存值 │库存品种 │预警物料 │呆滞库存 │周转率   │     │
│ │ 500万元 │ 1,200种 │  15种   │  8种    │  12次   │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 库存预警列表                                            │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │物料  │当前  │安全  │最大  │预警  │建议  │操作  │      │
│ │名称  │库存  │库存  │库存  │类型  │采购  │      │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │原片A │ 100  │ 200  │1000  │低库存│ 500  │采购  │      │
│ │胶片B │1200  │ 100  │ 800  │超库存│  0   │调拨  │      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
├─────────────────────────────────────────────────────────┤
│ 库存趋势图 (Chart)                                      │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 入库量 ████████████████████████████████████████     │ │
│ │ 出库量 ████████████████████████████████████████     │ │
│ │ 库存量 ████████████████████████████████████████     │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

**仓库管理页面 (WarehouseManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 仓库管理 [新增仓库] [仓库配置] [库位管理]                 │
├─────────────────────────────────────────────────────────┤
│ 仓库布局图 (Warehouse Layout)                           │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ A区(原材料)  │ B区(半成品)  │ C区(成品)            │ │
│ │ ┌─┬─┬─┬─┐   │ ┌─┬─┬─┬─┐   │ ┌─┬─┬─┬─┐         │ │
│ │ │1│2│3│4│   │ │1│2│3│4│   │ │1│2│3│4│         │ │
│ │ ├─┼─┼─┼─┤   │ ├─┼─┼─┼─┤   │ ├─┼─┼─┼─┤         │ │
│ │ │5│6│7│8│   │ │5│6│7│8│   │ │5│6│7│8│         │ │
│ │ └─┴─┴─┴─┘   │ └─┴─┴─┴─┘   │ └─┴─┴─┴─┘         │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 库位状态统计                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │总库位   │已占用   │空闲     │预留     │利用率   │     │
│ │  200个  │  150个  │  30个   │  20个   │  75%    │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 库位详情列表                                            │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │库位  │区域  │状态  │存储  │容量  │利用率│操作  │      │
│ │编码  │      │      │物料  │      │      │      │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │A-01-1│A区   │占用  │原片A │100㎡ │ 80%  │查看  │      │
│ │A-01-2│A区   │空闲  │ 无   │100㎡ │  0%  │分配  │      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

**入库管理页面 (InboundManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 入库管理 [新增入库] [批量入库] [入库模板]                 │
├─────────────────────────────────────────────────────────┤
│ 入库单状态统计                                          │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │待入库   │入库中   │已完成   │异常     │今日入库 │     │
│ │  8单    │  3单    │  25单   │  1单    │ 1,200㎡ │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 入库单列表                                              │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │入库单│供应商│物料  │数量  │状态  │入库员│操作  │      │
│ │号    │      │名称  │      │      │      │      │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │IN001 │供应商A│原片 │500㎡ │待入库│张三  │入库  │      │
│ │IN002 │供应商B│胶片 │200㎡ │入库中│李四  │查看  │      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

### 1.2 关键组件设计

#### 1.2.1 库位选择器组件 (LocationSelector.vue)
```vue
<template>
  <div class="location-selector">
    <el-cascader
      v-model="selectedLocation"
      :options="locationOptions"
      :props="cascaderProps"
      placeholder="请选择库位"
      filterable
      @change="handleLocationChange"
    >
      <template #default="{ node, data }">
        <div class="location-option">
          <span class="location-name">{{ data.label }}</span>
          <span class="location-info">
            <el-tag v-if="data.status" :type="getStatusType(data.status)" size="small">
              {{ data.status }}
            </el-tag>
            <span v-if="data.capacity" class="capacity">
              {{ data.utilization || 0 }}% / {{ data.capacity }}
            </span>
          </span>
        </div>
      </template>
    </el-cascader>
    
    <!-- 库位详情弹窗 -->
    <el-dialog v-model="showLocationDetail" title="库位详情" width="600px">
      <div v-if="selectedLocationDetail" class="location-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="库位编码">
            {{ selectedLocationDetail.code }}
          </el-descriptions-item>
          <el-descriptions-item label="库位名称">
            {{ selectedLocationDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="所属区域">
            {{ selectedLocationDetail.area }}
          </el-descriptions-item>
          <el-descriptions-item label="库位状态">
            <el-tag :type="getStatusType(selectedLocationDetail.status)">
              {{ selectedLocationDetail.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="存储容量">
            {{ selectedLocationDetail.capacity }}
          </el-descriptions-item>
          <el-descriptions-item label="利用率">
            <el-progress :percentage="selectedLocationDetail.utilization" />
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedLocationDetail.currentMaterials?.length" class="current-materials">
          <h4>当前存储物料</h4>
          <el-table :data="selectedLocationDetail.currentMaterials" size="small">
            <el-table-column prop="materialName" label="物料名称" />
            <el-table-column prop="quantity" label="数量" />
            <el-table-column prop="unit" label="单位" />
            <el-table-column prop="inboundDate" label="入库日期" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: [String, Array],
  warehouseId: String,
  materialType: String, // 物料类型，用于过滤合适的库位
  showDetail: { type: Boolean, default: false }
});

const emit = defineEmits(['update:modelValue', 'location-selected']);

const selectedLocation = ref(props.modelValue);
const showLocationDetail = ref(false);
const selectedLocationDetail = ref(null);

// 级联选择器配置
const cascaderProps = {
  value: 'code',
  label: 'name',
  children: 'children',
  checkStrictly: true, // 允许选择任意级别
  emitPath: false // 只返回最后一级的值
};

// 库位选项数据
const locationOptions = computed(() => {
  // 根据仓库ID和物料类型过滤库位
  return generateLocationTree(props.warehouseId, props.materialType);
});

const generateLocationTree = (warehouseId, materialType) => {
  // 模拟生成库位树结构
  return [
    {
      code: 'A',
      name: 'A区(原材料)',
      status: 'active',
      children: [
        {
          code: 'A-01',
          name: 'A-01排',
          children: [
            {
              code: 'A-01-1',
              name: 'A-01-1',
              status: 'occupied',
              capacity: '100㎡',
              utilization: 80
            },
            {
              code: 'A-01-2',
              name: 'A-01-2',
              status: 'available',
              capacity: '100㎡',
              utilization: 0
            }
          ]
        }
      ]
    }
  ];
};

const handleLocationChange = (value) => {
  emit('update:modelValue', value);
  emit('location-selected', value);
  
  if (props.showDetail && value) {
    loadLocationDetail(value);
  }
};

const loadLocationDetail = async (locationCode) => {
  // 加载库位详情
  try {
    // 模拟API调用
    selectedLocationDetail.value = {
      code: locationCode,
      name: `库位${locationCode}`,
      area: 'A区',
      status: 'occupied',
      capacity: '100㎡',
      utilization: 80,
      currentMaterials: [
        {
          materialName: '浮法玻璃原片',
          quantity: 80,
          unit: '㎡',
          inboundDate: '2025-08-01'
        }
      ]
    };
    showLocationDetail.value = true;
  } catch (error) {
    console.error('加载库位详情失败:', error);
  }
};

const getStatusType = (status) => {
  const types = {
    'available': 'success',
    'occupied': 'warning',
    'reserved': 'info',
    'maintenance': 'danger'
  };
  return types[status] || 'info';
};
</script>
```

#### 1.2.2 库存预警组件 (InventoryAlert.vue)
```vue
<template>
  <div class="inventory-alert">
    <div class="alert-header">
      <h3>库存预警</h3>
      <el-button size="small" @click="refreshAlerts">刷新</el-button>
    </div>
    
    <div class="alert-filters">
      <el-select v-model="alertType" placeholder="预警类型" size="small" clearable>
        <el-option label="低库存" value="low_stock" />
        <el-option label="超库存" value="over_stock" />
        <el-option label="呆滞库存" value="dead_stock" />
        <el-option label="即将过期" value="expiring" />
      </el-select>
      
      <el-select v-model="urgencyLevel" placeholder="紧急程度" size="small" clearable>
        <el-option label="紧急" value="urgent" />
        <el-option label="重要" value="important" />
        <el-option label="一般" value="normal" />
      </el-select>
    </div>
    
    <div class="alert-list">
      <div 
        v-for="alert in filteredAlerts" 
        :key="alert.id"
        class="alert-item"
        :class="getAlertClass(alert.urgency)"
      >
        <div class="alert-icon">
          <el-icon :color="getAlertColor(alert.urgency)">
            <Warning v-if="alert.urgency === 'urgent'" />
            <InfoFilled v-else-if="alert.urgency === 'important'" />
            <CircleCheck v-else />
          </el-icon>
        </div>
        
        <div class="alert-content">
          <div class="alert-title">{{ alert.materialName }}</div>
          <div class="alert-message">{{ alert.message }}</div>
          <div class="alert-details">
            <span class="current-stock">当前: {{ alert.currentStock }}{{ alert.unit }}</span>
            <span class="safe-stock">安全: {{ alert.safeStock }}{{ alert.unit }}</span>
            <span class="alert-time">{{ formatTime(alert.alertTime) }}</span>
          </div>
        </div>
        
        <div class="alert-actions">
          <el-button 
            size="small" 
            type="primary" 
            @click="handleQuickAction(alert)"
          >
            {{ getActionText(alert.type) }}
          </el-button>
          <el-button size="small" @click="viewDetail(alert)">详情</el-button>
        </div>
      </div>
    </div>
    
    <div v-if="!filteredAlerts.length" class="no-alerts">
      <el-empty description="暂无库存预警" />
    </div>
  </div>
</template>
```

### 1.3 响应式设计方案

#### 1.3.1 移动端库存看板
```vue
<!-- 移动端库存看板组件 -->
<template>
  <div class="inventory-dashboard-mobile">
    <div class="dashboard-header">
      <h2>库存看板</h2>
      <el-button size="small" @click="refreshData">刷新</el-button>
    </div>
    
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalValue }}</div>
          <div class="stat-label">总库存值</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.alertCount }}</div>
          <div class="stat-label">预警物料</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">📈</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.turnoverRate }}</div>
          <div class="stat-label">周转率</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">🏭</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.utilization }}%</div>
          <div class="stat-label">仓库利用率</div>
        </div>
      </div>
    </div>
    
    <div class="quick-actions">
      <el-button type="primary" @click="quickInbound">快速入库</el-button>
      <el-button @click="quickOutbound">快速出库</el-button>
      <el-button @click="viewAlerts">查看预警</el-button>
    </div>
    
    <div class="recent-activities">
      <h3>最近活动</h3>
      <div 
        v-for="activity in recentActivities" 
        :key="activity.id"
        class="activity-item"
      >
        <div class="activity-icon">
          <el-icon :color="getActivityColor(activity.type)">
            <ArrowUp v-if="activity.type === 'inbound'" />
            <ArrowDown v-else-if="activity.type === 'outbound'" />
            <RefreshRight v-else />
          </el-icon>
        </div>
        <div class="activity-content">
          <div class="activity-title">{{ activity.title }}</div>
          <div class="activity-time">{{ formatTime(activity.time) }}</div>
        </div>
        <div class="activity-quantity">{{ activity.quantity }}</div>
      </div>
    </div>
  </div>
</template>
```

---

## 2. MOCK数据设计规范

### 2.1 核心数据模型

#### 2.1.1 库存数据模型 (Inventory)
```javascript
// inventory_mock.js
export const inventoryMockData = {
  inventories: [
    {
      id: 'INV-20250802-001',
      materialId: 'MAT-001',
      materialCode: 'FLG-6MM',
      materialName: '6mm浮法玻璃原片',
      materialType: 'raw_material', // raw_material: 原材料, semi_finished: 半成品, finished_goods: 成品
      category: 'glass_sheet',
      
      // 库存数量
      currentStock: 800, // 当前库存
      availableStock: 750, // 可用库存(扣除预留)
      reservedStock: 50, // 预留库存
      unit: '平方米',
      
      // 库存控制参数
      safeStock: 200, // 安全库存
      minStock: 100, // 最小库存
      maxStock: 1000, // 最大库存
      reorderPoint: 150, // 再订货点
      reorderQuantity: 500, // 再订货量
      
      // 成本信息
      unitCost: 45, // 单位成本
      totalValue: 36000, // 总价值
      avgCost: 45, // 平均成本
      lastCost: 46, // 最新成本
      
      // 库位信息
      locations: [
        {
          locationId: 'LOC-A-01-1',
          locationCode: 'A-01-1',
          locationName: 'A区01排1号',
          quantity: 500,
          inboundDate: '2025-08-01T08:00:00Z',
          batchNo: 'BATCH-20250801-001'
        },
        {
          locationId: 'LOC-A-01-2',
          locationCode: 'A-01-2',
          locationName: 'A区01排2号',
          quantity: 300,
          inboundDate: '2025-07-28T14:00:00Z',
          batchNo: 'BATCH-20250728-001'
        }
      ],
      
      // 质量信息
      qualityGrades: [
        {
          grade: 'A', // A: 优等品, B: 一等品, C: 合格品
          quantity: 600,
          percentage: 75
        },
        {
          grade: 'B',
          quantity: 150,
          percentage: 18.75
        },
        {
          grade: 'C',
          quantity: 50,
          percentage: 6.25
        }
      ],
      
      // 库存状态
      status: 'normal', // normal: 正常, alert: 预警, shortage: 短缺, excess: 超储
      alertType: null, // low_stock: 低库存, over_stock: 超库存, dead_stock: 呆滞, expiring: 即将过期
      
      // 统计信息
      statistics: {
        lastInboundDate: '2025-08-01T08:00:00Z',
        lastOutboundDate: '2025-08-02T10:00:00Z',
        turnoverRate: 12, // 年周转率
        daysInStock: 30, // 平均库存天数
        inboundCount: 15, // 入库次数
        outboundCount: 18, // 出库次数
        totalInbound: 2000, // 累计入库
        totalOutbound: 1200 // 累计出库
      },
      
      // 供应商信息
      suppliers: [
        {
          supplierId: 'SUP-001',
          supplierName: '某某玻璃供应商',
          leadTime: 7, // 供货周期(天)
          minOrderQuantity: 100, // 最小起订量
          unitPrice: 45
        }
      ],
      
      createTime: '2024-01-15T08:00:00Z',
      updateTime: '2025-08-02T16:30:00Z'
    }
  ]
};
```

#### 2.1.2 入库数据模型 (Inbound)
```javascript
// inbound_mock.js
export const inboundMockData = {
  inbounds: [
    {
      id: 'IB-20250802-001',
      inboundNo: 'IB-2025080200001',
      type: 'purchase', // purchase: 采购入库, production: 生产入库, transfer: 调拨入库, return: 退货入库
      status: 'completed', // pending: 待入库, in_progress: 入库中, completed: 已完成, cancelled: 已取消

      // 来源信息
      sourceType: 'supplier', // supplier: 供应商, production: 生产, warehouse: 仓库
      sourceId: 'SUP-001',
      sourceName: '某某玻璃供应商',

      // 目标仓库
      warehouseId: 'WH-001',
      warehouseName: '主仓库',

      // 单据信息
      purchaseOrderId: 'PO-20250801-001', // 关联采购订单
      deliveryNoteNo: 'DN-20250802-001', // 送货单号

      // 入库明细
      items: [
        {
          id: 'IBI-001',
          materialId: 'MAT-001',
          materialCode: 'FLG-6MM',
          materialName: '6mm浮法玻璃原片',
          specifications: {
            thickness: 6,
            type: 'float_glass',
            color: 'clear'
          },

          // 数量信息
          plannedQuantity: 500, // 计划入库数量
          actualQuantity: 480, // 实际入库数量
          qualifiedQuantity: 460, // 合格数量
          defectiveQuantity: 20, // 不合格数量
          unit: '平方米',

          // 成本信息
          unitPrice: 45,
          totalAmount: 21600,

          // 质检信息
          qualityResult: 'qualified', // qualified: 合格, unqualified: 不合格, pending: 待检
          qualityGrade: 'A',
          inspector: '质检员A',
          inspectionTime: '2025-08-02T10:00:00Z',
          inspectionRemarks: '外观良好，尺寸符合要求',

          // 库位分配
          locationAllocations: [
            {
              locationId: 'LOC-A-01-1',
              locationCode: 'A-01-1',
              quantity: 300,
              batchNo: 'BATCH-20250802-001'
            },
            {
              locationId: 'LOC-A-01-2',
              locationCode: 'A-01-2',
              quantity: 160,
              batchNo: 'BATCH-20250802-001'
            }
          ]
        }
      ],

      // 时间信息
      plannedInboundDate: '2025-08-02T08:00:00Z',
      actualInboundDate: '2025-08-02T09:30:00Z',
      completedTime: '2025-08-02T11:00:00Z',

      // 人员信息
      operator: {
        id: 'EMP-006',
        name: '仓管员A',
        operateTime: '2025-08-02T09:30:00Z'
      },

      // 备注信息
      remarks: '供应商按时交货，质量良好',
      attachments: [
        {
          fileName: '送货单.pdf',
          fileUrl: '/files/delivery_note_20250802.pdf',
          uploadTime: '2025-08-02T09:00:00Z'
        }
      ],

      createTime: '2025-08-02T08:00:00Z',
      updateTime: '2025-08-02T11:00:00Z'
    }
  ]
};
```

#### 2.1.3 出库数据模型 (Outbound)
```javascript
// outbound_mock.js
export const outboundMockData = {
  outbounds: [
    {
      id: 'OB-20250802-001',
      outboundNo: 'OB-2025080200001',
      type: 'sales', // sales: 销售出库, production: 生产领料, transfer: 调拨出库, scrap: 报废出库
      status: 'completed', // pending: 待出库, picking: 拣货中, completed: 已完成, cancelled: 已取消

      // 目标信息
      targetType: 'customer', // customer: 客户, production: 生产, warehouse: 仓库
      targetId: 'CUS-20250802-001',
      targetName: 'ABC玻璃制品有限公司',

      // 来源仓库
      warehouseId: 'WH-001',
      warehouseName: '主仓库',

      // 单据信息
      salesOrderId: 'SO-20250802-001', // 关联销售订单
      deliveryAddress: '上海市浦东新区张江高科技园区',

      // 出库明细
      items: [
        {
          id: 'OBI-001',
          materialId: 'PRD-001',
          materialCode: 'TG-12MM',
          materialName: '12mm钢化玻璃',
          specifications: {
            width: 1200,
            height: 2000,
            thickness: 12,
            type: 'tempered'
          },

          // 数量信息
          requestedQuantity: 100, // 申请出库数量
          allocatedQuantity: 100, // 分配数量
          actualQuantity: 100, // 实际出库数量
          unit: '平方米',

          // 成本信息
          unitCost: 120,
          totalCost: 12000,

          // 库位分配
          locationAllocations: [
            {
              locationId: 'LOC-C-01-1',
              locationCode: 'C-01-1',
              quantity: 60,
              batchNo: 'BATCH-20250801-001',
              pickingSequence: 1
            },
            {
              locationId: 'LOC-C-01-2',
              locationCode: 'C-01-2',
              quantity: 40,
              batchNo: 'BATCH-20250730-001',
              pickingSequence: 2
            }
          ],

          // 拣货信息
          picker: {
            id: 'EMP-007',
            name: '拣货员B',
            pickingTime: '2025-08-02T14:00:00Z'
          }
        }
      ],

      // 时间信息
      requestedDate: '2025-08-02T12:00:00Z',
      plannedOutboundDate: '2025-08-02T15:00:00Z',
      actualOutboundDate: '2025-08-02T14:30:00Z',

      // 物流信息
      logistics: {
        carrier: '某某物流公司',
        trackingNo: '***********-001',
        vehicleNo: '沪A12345',
        driverName: '司机张',
        driverPhone: '13800138000'
      },

      // 人员信息
      operator: {
        id: 'EMP-006',
        name: '仓管员A',
        operateTime: '2025-08-02T14:30:00Z'
      },

      createTime: '2025-08-02T12:00:00Z',
      updateTime: '2025-08-02T14:30:00Z'
    }
  ]
};
```

### 2.2 模块间关联数据

#### 2.2.1 与生产管理的关联数据
```javascript
// production_integration_mock.js
export const productionIntegrationMock = {
  // 生产领料需求
  materialRequests: [
    {
      requestId: '***********-001',
      workOrderId: '***********-001',
      workOrderNo: '***********00001',
      requestType: 'production', // production: 生产领料, maintenance: 维修领料
      status: 'approved', // pending: 待审批, approved: 已批准, rejected: 已拒绝, completed: 已完成

      materials: [
        {
          materialId: 'MAT-001',
          materialName: '6mm浮法玻璃原片',
          requestedQuantity: 1050, // 含损耗
          approvedQuantity: 1050,
          allocatedQuantity: 1000,
          unit: '平方米',
          urgency: 'high' // high: 紧急, medium: 一般, low: 不紧急
        }
      ],

      requestTime: '2025-08-02T08:00:00Z',
      requiredTime: '2025-08-02T10:00:00Z',
      approver: '生产经理',
      approveTime: '2025-08-02T08:30:00Z'
    }
  ],

  // 成品入库通知
  finishedGoodsNotifications: [
    {
      notificationId: 'FGN-20250802-001',
      workOrderId: '***********-001',
      productId: 'PRD-001',
      productName: '12mm钢化玻璃',
      completedQuantity: 950, // 实际完成数量
      qualifiedQuantity: 920, // 合格数量
      defectiveQuantity: 30, // 不合格数量
      completionTime: '2025-08-02T16:00:00Z',
      qualityInspector: '质检员B'
    }
  ]
};
```

---

## 3. 技术实施细节

### 3.1 核心业务逻辑实现

#### 3.1.1 智能库位分配算法
```javascript
// composables/useLocationAllocation.js
import { ref, computed } from 'vue';

export function useLocationAllocation() {
  const warehouses = ref([]);
  const locations = ref([]);
  const allocationRules = ref({});

  // 智能库位分配
  const allocateLocation = async (materialInfo, quantity, warehouseId) => {
    try {
      // 1. 获取可用库位
      const availableLocations = await getAvailableLocations(warehouseId, materialInfo.type);

      // 2. 应用分配策略
      const allocationStrategy = determineAllocationStrategy(materialInfo, quantity);

      // 3. 执行分配算法
      const allocations = await executeAllocation(
        availableLocations,
        quantity,
        allocationStrategy
      );

      return allocations;
    } catch (error) {
      console.error('库位分配失败:', error);
      throw error;
    }
  };

  // 确定分配策略
  const determineAllocationStrategy = (materialInfo, quantity) => {
    // 根据物料特性和数量确定分配策略
    const strategies = {
      // 先进先出策略
      FIFO: 'first_in_first_out',
      // 就近原则
      NEAREST: 'nearest_location',
      // 容量优化
      CAPACITY_OPTIMIZED: 'capacity_optimized',
      // 同类集中
      CATEGORY_GROUPED: 'category_grouped'
    };

    // 根据物料类型选择策略
    if (materialInfo.type === 'raw_material') {
      return strategies.CATEGORY_GROUPED; // 原材料按类别集中存放
    } else if (materialInfo.type === 'finished_goods') {
      return strategies.FIFO; // 成品先进先出
    } else {
      return strategies.CAPACITY_OPTIMIZED; // 半成品容量优化
    }
  };

  // 执行分配算法
  const executeAllocation = async (availableLocations, quantity, strategy) => {
    let allocations = [];
    let remainingQuantity = quantity;

    // 根据策略排序库位
    const sortedLocations = sortLocationsByStrategy(availableLocations, strategy);

    for (const location of sortedLocations) {
      if (remainingQuantity <= 0) break;

      const availableCapacity = location.capacity - location.currentStock;
      const allocationQuantity = Math.min(remainingQuantity, availableCapacity);

      if (allocationQuantity > 0) {
        allocations.push({
          locationId: location.id,
          locationCode: location.code,
          quantity: allocationQuantity,
          utilizationAfter: (location.currentStock + allocationQuantity) / location.capacity
        });

        remainingQuantity -= allocationQuantity;
      }
    }

    if (remainingQuantity > 0) {
      throw new Error(`库位容量不足，还需要 ${remainingQuantity} 的存储空间`);
    }

    return allocations;
  };

  // 根据策略排序库位
  const sortLocationsByStrategy = (locations, strategy) => {
    switch (strategy) {
      case 'first_in_first_out':
        return locations.sort((a, b) => new Date(a.lastInboundDate) - new Date(b.lastInboundDate));

      case 'nearest_location':
        return locations.sort((a, b) => a.distance - b.distance);

      case 'capacity_optimized':
        return locations.sort((a, b) => {
          const utilizationA = a.currentStock / a.capacity;
          const utilizationB = b.currentStock / b.capacity;
          return utilizationA - utilizationB; // 优先使用利用率低的库位
        });

      case 'category_grouped':
        return locations.sort((a, b) => {
          // 优先选择同类物料的库位
          if (a.materialCategory === b.materialCategory) {
            return a.currentStock / a.capacity - b.currentStock / b.capacity;
          }
          return a.materialCategory ? -1 : 1;
        });

      default:
        return locations;
    }
  };

  return {
    allocateLocation,
    determineAllocationStrategy,
    executeAllocation
  };
}
```

#### 3.1.2 库存预警系统
```javascript
// composables/useInventoryAlert.js
import { ref, computed, onMounted, onUnmounted } from 'vue';

export function useInventoryAlert() {
  const alerts = ref([]);
  const alertRules = ref([]);
  const alertSettings = ref({});
  const alertTimer = ref(null);

  // 初始化预警系统
  const initAlertSystem = () => {
    loadAlertRules();
    startAlertMonitoring();
  };

  // 加载预警规则
  const loadAlertRules = async () => {
    try {
      alertRules.value = [
        {
          id: 'RULE-001',
          name: '低库存预警',
          type: 'low_stock',
          condition: 'currentStock <= safeStock',
          urgency: 'important',
          enabled: true
        },
        {
          id: 'RULE-002',
          name: '超库存预警',
          type: 'over_stock',
          condition: 'currentStock >= maxStock',
          urgency: 'normal',
          enabled: true
        },
        {
          id: 'RULE-003',
          name: '呆滞库存预警',
          type: 'dead_stock',
          condition: 'daysInStock >= 90 && turnoverRate < 2',
          urgency: 'important',
          enabled: true
        },
        {
          id: 'RULE-004',
          name: '即将过期预警',
          type: 'expiring',
          condition: 'daysToExpiry <= 30',
          urgency: 'urgent',
          enabled: true
        }
      ];
    } catch (error) {
      console.error('加载预警规则失败:', error);
    }
  };

  // 开始预警监控
  const startAlertMonitoring = () => {
    // 每5分钟检查一次
    alertTimer.value = setInterval(checkInventoryAlerts, 5 * 60 * 1000);

    // 立即执行一次检查
    checkInventoryAlerts();
  };

  // 检查库存预警
  const checkInventoryAlerts = async () => {
    try {
      const inventoryData = await getInventoryData();
      const newAlerts = [];

      for (const inventory of inventoryData) {
        for (const rule of alertRules.value) {
          if (!rule.enabled) continue;

          const alertTriggered = evaluateAlertCondition(inventory, rule);

          if (alertTriggered) {
            const existingAlert = alerts.value.find(
              alert => alert.materialId === inventory.materialId && alert.type === rule.type
            );

            if (!existingAlert) {
              const alert = createAlert(inventory, rule);
              newAlerts.push(alert);
            }
          }
        }
      }

      // 添加新预警
      if (newAlerts.length > 0) {
        alerts.value.unshift(...newAlerts);

        // 发送通知
        newAlerts.forEach(alert => {
          sendAlertNotification(alert);
        });
      }

      // 清理已解决的预警
      cleanupResolvedAlerts(inventoryData);

    } catch (error) {
      console.error('检查库存预警失败:', error);
    }
  };

  // 评估预警条件
  const evaluateAlertCondition = (inventory, rule) => {
    const context = {
      currentStock: inventory.currentStock,
      safeStock: inventory.safeStock,
      maxStock: inventory.maxStock,
      daysInStock: inventory.statistics.daysInStock,
      turnoverRate: inventory.statistics.turnoverRate,
      daysToExpiry: calculateDaysToExpiry(inventory)
    };

    try {
      // 简单的条件评估（实际项目中可能需要更复杂的表达式解析器）
      return evaluateCondition(rule.condition, context);
    } catch (error) {
      console.error('评估预警条件失败:', error);
      return false;
    }
  };

  // 创建预警
  const createAlert = (inventory, rule) => {
    return {
      id: `ALERT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      materialId: inventory.materialId,
      materialName: inventory.materialName,
      type: rule.type,
      urgency: rule.urgency,
      message: generateAlertMessage(inventory, rule),
      currentStock: inventory.currentStock,
      safeStock: inventory.safeStock,
      maxStock: inventory.maxStock,
      unit: inventory.unit,
      alertTime: new Date().toISOString(),
      status: 'active', // active: 活跃, acknowledged: 已确认, resolved: 已解决
      actions: generateSuggestedActions(inventory, rule)
    };
  };

  // 生成预警消息
  const generateAlertMessage = (inventory, rule) => {
    const messages = {
      'low_stock': `${inventory.materialName} 库存不足，当前库存 ${inventory.currentStock}${inventory.unit}，低于安全库存 ${inventory.safeStock}${inventory.unit}`,
      'over_stock': `${inventory.materialName} 库存过多，当前库存 ${inventory.currentStock}${inventory.unit}，超过最大库存 ${inventory.maxStock}${inventory.unit}`,
      'dead_stock': `${inventory.materialName} 存在呆滞风险，已库存 ${inventory.statistics.daysInStock} 天，周转率仅 ${inventory.statistics.turnoverRate} 次/年`,
      'expiring': `${inventory.materialName} 即将过期，剩余 ${calculateDaysToExpiry(inventory)} 天`
    };

    return messages[rule.type] || '库存异常';
  };

  // 生成建议操作
  const generateSuggestedActions = (inventory, rule) => {
    const actions = {
      'low_stock': [
        { type: 'purchase', text: '立即采购', priority: 'high' },
        { type: 'transfer', text: '调拨补充', priority: 'medium' }
      ],
      'over_stock': [
        { type: 'transfer', text: '调拨到其他仓库', priority: 'medium' },
        { type: 'promotion', text: '促销处理', priority: 'low' }
      ],
      'dead_stock': [
        { type: 'promotion', text: '促销清理', priority: 'high' },
        { type: 'scrap', text: '报废处理', priority: 'low' }
      ],
      'expiring': [
        { type: 'urgent_sale', text: '紧急销售', priority: 'urgent' },
        { type: 'return', text: '退货处理', priority: 'medium' }
      ]
    };

    return actions[rule.type] || [];
  };

  // 发送预警通知
  const sendAlertNotification = (alert) => {
    // 浏览器通知
    if (Notification.permission === 'granted') {
      new Notification(`库存预警: ${alert.materialName}`, {
        body: alert.message,
        icon: '/icons/inventory-alert.png',
        tag: alert.id
      });
    }

    // 系统内通知
    // 可以集成消息推送服务
    console.log('库存预警:', alert);
  };

  // 清理已解决的预警
  const cleanupResolvedAlerts = (inventoryData) => {
    alerts.value = alerts.value.filter(alert => {
      const inventory = inventoryData.find(inv => inv.materialId === alert.materialId);
      if (!inventory) return false;

      // 检查预警条件是否仍然成立
      const rule = alertRules.value.find(r => r.type === alert.type);
      if (!rule) return false;

      const stillTriggered = evaluateAlertCondition(inventory, rule);

      if (!stillTriggered) {
        // 预警已解决，发送解决通知
        sendResolvedNotification(alert);
        return false;
      }

      return true;
    });
  };

  // 手动确认预警
  const acknowledgeAlert = (alertId) => {
    const alert = alerts.value.find(a => a.id === alertId);
    if (alert) {
      alert.status = 'acknowledged';
      alert.acknowledgeTime = new Date().toISOString();
    }
  };

  // 手动解决预警
  const resolveAlert = (alertId, resolution) => {
    const alert = alerts.value.find(a => a.id === alertId);
    if (alert) {
      alert.status = 'resolved';
      alert.resolveTime = new Date().toISOString();
      alert.resolution = resolution;
    }
  };

  onMounted(() => {
    initAlertSystem();

    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  });

  onUnmounted(() => {
    if (alertTimer.value) {
      clearInterval(alertTimer.value);
    }
  });

  return {
    alerts,
    alertRules,
    checkInventoryAlerts,
    acknowledgeAlert,
    resolveAlert
  };
}
```

### 3.2 性能优化策略

#### 3.2.1 大数据量库存列表优化
```javascript
// composables/useVirtualInventoryList.js
import { ref, computed, onMounted, onUnmounted } from 'vue';

export function useVirtualInventoryList(props) {
  const containerRef = ref();
  const scrollTop = ref(0);
  const containerHeight = ref(0);
  const itemHeight = 60; // 每行高度
  const bufferSize = 10; // 缓冲区大小

  // 可见区域计算
  const visibleRange = computed(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop.value / itemHeight) - bufferSize);
    const endIndex = Math.min(
      props.inventoryList.length,
      Math.ceil((scrollTop.value + containerHeight.value) / itemHeight) + bufferSize
    );

    return { startIndex, endIndex };
  });

  // 可见数据
  const visibleData = computed(() => {
    const { startIndex, endIndex } = visibleRange.value;
    return props.inventoryList.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      virtualIndex: startIndex + index
    }));
  });

  // 总高度
  const totalHeight = computed(() => props.inventoryList.length * itemHeight);

  // 偏移量
  const offsetY = computed(() => visibleRange.value.startIndex * itemHeight);

  // 滚动处理
  const handleScroll = (e) => {
    scrollTop.value = e.target.scrollTop;
  };

  // 更新容器高度
  const updateContainerHeight = () => {
    if (containerRef.value) {
      containerHeight.value = containerRef.value.clientHeight;
    }
  };

  onMounted(() => {
    updateContainerHeight();
    window.addEventListener('resize', updateContainerHeight);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateContainerHeight);
  });

  return {
    containerRef,
    visibleData,
    totalHeight,
    offsetY,
    handleScroll
  };
}
```

---

## 4. 开发指导文档

### 4.1 开发规范

#### 4.1.1 库存模块组件规范
```javascript
// 库存管理组件命名规范
const INVENTORY_COMPONENT_NAMING = {
  // 页面组件
  pages: [
    'InventoryOverview.vue',
    'WarehouseManagement.vue',
    'InboundManagement.vue',
    'OutboundManagement.vue',
    'StockTaking.vue'
  ],

  // 业务组件
  business: [
    'InventoryLocationSelector.vue',
    'InventoryAlertPanel.vue',
    'InventoryBatchTracker.vue',
    'InventoryQualityGrader.vue'
  ],

  // 通用组件
  common: [
    'VirtualInventoryTable.vue',
    'InventoryChart.vue',
    'LocationPicker.vue',
    'BatchNumberGenerator.vue'
  ]
};
```

#### 4.1.2 状态管理规范
```javascript
// stores/inventory.js
import { defineStore } from 'pinia';

export const useInventoryStore = defineStore('inventory', {
  state: () => ({
    // 库存数据
    inventories: [],
    currentInventory: null,

    // 仓库数据
    warehouses: [],
    locations: [],

    // 出入库数据
    inbounds: [],
    outbounds: [],

    // 预警数据
    alerts: [],
    alertRules: [],

    // 加载状态
    loading: {
      inventories: false,
      inbounds: false,
      outbounds: false,
      alerts: false
    }
  }),

  getters: {
    // 按状态分组的库存
    inventoriesByStatus: (state) => {
      return state.inventories.reduce((groups, inventory) => {
        const status = inventory.status;
        if (!groups[status]) groups[status] = [];
        groups[status].push(inventory);
        return groups;
      }, {});
    },

    // 预警统计
    alertStatistics: (state) => {
      return state.alerts.reduce((stats, alert) => {
        stats[alert.urgency] = (stats[alert.urgency] || 0) + 1;
        return stats;
      }, {});
    },

    // 库存总价值
    totalInventoryValue: (state) => {
      return state.inventories.reduce((total, inventory) => {
        return total + inventory.totalValue;
      }, 0);
    }
  },

  actions: {
    // 加载库存数据
    async loadInventories(filters = {}) {
      this.loading.inventories = true;
      try {
        const response = await inventoryService.getInventories(filters);
        this.inventories = response.data;
      } catch (error) {
        console.error('加载库存数据失败:', error);
        throw error;
      } finally {
        this.loading.inventories = false;
      }
    },

    // 创建入库单
    async createInbound(inboundData) {
      try {
        const newInbound = await inventoryService.createInbound(inboundData);
        this.inbounds.push(newInbound);
        return newInbound;
      } catch (error) {
        console.error('创建入库单失败:', error);
        throw error;
      }
    },

    // 更新库存预警
    updateInventoryAlert(materialId, alertData) {
      const existingAlert = this.alerts.find(alert => alert.materialId === materialId);
      if (existingAlert) {
        Object.assign(existingAlert, alertData);
      } else {
        this.alerts.push({ materialId, ...alertData });
      }
    }
  }
});
```

### 4.2 测试策略

#### 4.2.1 库位分配算法测试
```javascript
// tests/unit/composables/useLocationAllocation.test.js
import { describe, it, expect, vi } from 'vitest';
import { useLocationAllocation } from '@/composables/useLocationAllocation';

describe('useLocationAllocation', () => {
  it('应该正确分配库位', async () => {
    const { allocateLocation } = useLocationAllocation();

    const materialInfo = {
      type: 'raw_material',
      category: 'glass_sheet'
    };

    const quantity = 500;
    const warehouseId = 'WH-001';

    // Mock可用库位
    vi.mocked(getAvailableLocations).mockResolvedValue([
      {
        id: 'LOC-001',
        code: 'A-01-1',
        capacity: 1000,
        currentStock: 200,
        materialCategory: 'glass_sheet'
      },
      {
        id: 'LOC-002',
        code: 'A-01-2',
        capacity: 1000,
        currentStock: 0,
        materialCategory: null
      }
    ]);

    const allocations = await allocateLocation(materialInfo, quantity, warehouseId);

    expect(allocations).toHaveLength(1);
    expect(allocations[0].locationId).toBe('LOC-001');
    expect(allocations[0].quantity).toBe(500);
  });
});
```

### 4.3 集成方案

#### 4.3.1 库存同步配置
```javascript
// config/inventory-sync.js
export const INVENTORY_SYNC_CONFIG = {
  // 同步间隔
  syncInterval: 30000, // 30秒

  // 同步端点
  endpoints: {
    inventory: '/api/inventory/sync',
    alerts: '/api/inventory/alerts',
    movements: '/api/inventory/movements'
  },

  // 同步策略
  syncStrategy: {
    // 增量同步
    incremental: true,
    // 批量大小
    batchSize: 100,
    // 重试次数
    maxRetries: 3,
    // 重试间隔
    retryInterval: 5000
  },

  // 冲突解决
  conflictResolution: {
    // 以服务端为准
    strategy: 'server_wins',
    // 记录冲突日志
    logConflicts: true
  }
};
```

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，库存管理模块技术实施指导 | 技术团队 |

---

**文档状态**: 技术实施指导完成 ✅
**覆盖内容**: 界面原型设计 ✅ MOCK数据设计 ✅ 技术实施细节 ✅ 开发指导文档 ✅
**技术栈**: Vue3 ✅ 智能分配 ✅ 预警系统 ✅ 虚拟化列表 ✅
```
