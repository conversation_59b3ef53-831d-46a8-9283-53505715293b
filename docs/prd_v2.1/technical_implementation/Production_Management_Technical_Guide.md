# 生产管理模块技术实施指导

> **版本**: 2.1
> **状态**: 技术实施指导
> **撰写人**: 技术团队
> **日期**: 2025-08-02
> **目标**: 为生产管理模块提供完整的技术实施指导，包括界面原型设计、MOCK数据设计、技术实施细节和开发指导文档
> **设计规范**: 参考 [原型设计规范](../../prototype_design_guidelines.md)
> **业务需求**: 参考 [生产管理模块PRD](../Production_Management_Module.md)

---

## 1. 界面原型设计指导

### 1.1 页面架构设计

#### 1.1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (60px)                      │
├─────────────────────────────────────────────────────────┤
│ 侧边栏 │                主内容区域                        │
│(240px) │  ┌─────────────────────────────────────────┐   │
│        │  │     生产管理模块导航 (48px)              │   │
│ 生产   │  ├─────────────────────────────────────────┤   │
│ 计划   │  │                                         │   │
│ 工单   │  │            功能页面内容                  │   │
│ 设备   │  │                                         │   │
│ 质检   │  │                                         │   │
│ 报表   │  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

#### 1.1.2 核心页面设计

**生产计划页面 (ProductionPlan.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 生产计划 [新增计划] [批量排产] [导出计划]                 │
├─────────────────────────────────────────────────────────┤
│ 计划概览 (Dashboard)                                    │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │待排产   │进行中   │已完成   │延期     │设备利用率│     │
│ │  25单   │  15单   │  80单   │  3单    │   85%   │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 甘特图视图 (Gantt Chart)                                │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 设备/工位 │ 今天 │ 明天 │ 后天 │ ...              │ │
│ │ 切割机A   │████  │████  │      │                  │ │
│ │ 钢化炉B   │      │████  │████  │                  │ │
│ │ 夹胶线C   │████  │      │████  │                  │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 计划列表 (Table)                                        │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │计划号│订单号│产品  │数量  │开始  │结束  │状态  │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │PP001 │SO001 │钢化  │100㎡ │08-05 │08-10 │进行中│      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

**生产工单页面 (WorkOrder.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 生产工单 [新增工单] [批量操作] [打印工单]                 │
├─────────────────────────────────────────────────────────┤
│ 工单状态统计                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │待开始   │进行中   │暂停     │已完成   │异常     │     │
│ │  12单   │  8单    │  2单    │  45单   │  1单    │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 工单列表 (Table)                                        │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │工单号│计划号│工序  │操作员│进度  │状态  │操作  │      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │WO001 │PP001 │切割  │张三  │80%   │进行中│查看  │      │
│ │WO002 │PP001 │钢化  │李四  │0%    │待开始│开始  │      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

**设备管理页面 (EquipmentManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 设备管理 [新增设备] [维护计划] [设备档案]                 │
├─────────────────────────────────────────────────────────┤
│ 设备状态总览                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┐     │
│ │运行中   │空闲     │维护中   │故障     │总设备   │     │
│ │  15台   │  5台    │  2台    │  1台    │  23台   │     │
│ └─────────┴─────────┴─────────┴─────────┴─────────┘     │
├─────────────────────────────────────────────────────────┤
│ 设备实时监控 (Real-time Monitor)                        │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 设备名称 │ 状态 │ 利用率 │ 温度 │ 压力 │ 报警 │     │ │
│ │ 切割机A  │ 运行 │  85%  │ 25°C │ 正常 │  无  │     │ │
│ │ 钢化炉B  │ 运行 │  92%  │680°C │ 正常 │  无  │     │ │
│ │ 夹胶线C  │ 空闲 │   0%  │ 23°C │ 正常 │  无  │     │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.2 关键组件设计

#### 1.2.1 甘特图组件 (GanttChart.vue)
```vue
<template>
  <div class="gantt-chart">
    <div class="gantt-header">
      <div class="resource-column">设备/工位</div>
      <div class="timeline-header">
        <div
          v-for="date in timelineData"
          :key="date.date"
          class="date-column"
          :style="{ width: `${columnWidth}px` }"
        >
          {{ formatDate(date.date) }}
        </div>
      </div>
    </div>

    <div class="gantt-body">
      <div
        v-for="resource in resources"
        :key="resource.id"
        class="gantt-row"
      >
        <div class="resource-cell">{{ resource.name }}</div>
        <div class="timeline-row">
          <div
            v-for="task in getResourceTasks(resource.id)"
            :key="task.id"
            class="gantt-task"
            :style="getTaskStyle(task)"
            @click="selectTask(task)"
          >
            <div class="task-content">
              <span class="task-title">{{ task.title }}</span>
              <span class="task-progress">{{ task.progress }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  resources: Array,
  tasks: Array,
  startDate: String,
  endDate: String,
  columnWidth: { type: Number, default: 100 }
});

const emit = defineEmits(['task-select', 'task-update']);

// 生成时间轴数据
const timelineData = computed(() => {
  const dates = [];
  const start = new Date(props.startDate);
  const end = new Date(props.endDate);

  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    dates.push({ date: new Date(d) });
  }

  return dates;
});

// 获取资源对应的任务
const getResourceTasks = (resourceId) => {
  return props.tasks.filter(task => task.resourceId === resourceId);
};

// 计算任务样式
const getTaskStyle = (task) => {
  const startDate = new Date(props.startDate);
  const taskStart = new Date(task.startDate);
  const taskEnd = new Date(task.endDate);

  const startOffset = Math.floor((taskStart - startDate) / (1000 * 60 * 60 * 24));
  const duration = Math.ceil((taskEnd - taskStart) / (1000 * 60 * 60 * 24));

  return {
    left: `${startOffset * props.columnWidth}px`,
    width: `${duration * props.columnWidth}px`,
    backgroundColor: getTaskColor(task.status)
  };
};

const getTaskColor = (status) => {
  const colors = {
    'pending': '#faad14',
    'in_progress': '#1890ff',
    'completed': '#52c41a',
    'delayed': '#f5222d'
  };
  return colors[status] || '#d9d9d9';
};
</script>
```

#### 1.2.2 设备状态监控组件 (EquipmentMonitor.vue)
```vue
<template>
  <div class="equipment-monitor">
    <div class="monitor-grid">
      <div
        v-for="equipment in equipmentList"
        :key="equipment.id"
        class="equipment-card"
        :class="getStatusClass(equipment.status)"
      >
        <div class="equipment-header">
          <h4>{{ equipment.name }}</h4>
          <el-tag :type="getStatusType(equipment.status)">
            {{ getStatusText(equipment.status) }}
          </el-tag>
        </div>

        <div class="equipment-metrics">
          <div class="metric-item">
            <span class="metric-label">利用率</span>
            <el-progress
              :percentage="equipment.utilization"
              :color="getUtilizationColor(equipment.utilization)"
            />
          </div>

          <div class="metric-item" v-if="equipment.temperature">
            <span class="metric-label">温度</span>
            <span class="metric-value">{{ equipment.temperature }}°C</span>
          </div>

          <div class="metric-item" v-if="equipment.pressure">
            <span class="metric-label">压力</span>
            <span class="metric-value">{{ equipment.pressure }}</span>
          </div>
        </div>

        <div class="equipment-alerts" v-if="equipment.alerts?.length">
          <el-alert
            v-for="alert in equipment.alerts"
            :key="alert.id"
            :title="alert.message"
            :type="alert.level"
            size="small"
            show-icon
          />
        </div>

        <div class="equipment-actions">
          <el-button size="small" @click="viewDetails(equipment)">详情</el-button>
          <el-button
            size="small"
            type="primary"
            @click="controlEquipment(equipment)"
            :disabled="equipment.status === 'maintenance'"
          >
            控制
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  equipmentList: Array
});

const emit = defineEmits(['view-details', 'control-equipment']);

const getStatusClass = (status) => {
  return `status-${status}`;
};

const getStatusType = (status) => {
  const types = {
    'running': 'success',
    'idle': 'info',
    'maintenance': 'warning',
    'fault': 'danger'
  };
  return types[status] || 'info';
};

const getStatusText = (status) => {
  const texts = {
    'running': '运行中',
    'idle': '空闲',
    'maintenance': '维护中',
    'fault': '故障'
  };
  return texts[status] || '未知';
};

const getUtilizationColor = (utilization) => {
  if (utilization >= 80) return '#52c41a';
  if (utilization >= 60) return '#faad14';
  return '#f5222d';
};

const viewDetails = (equipment) => {
  emit('view-details', equipment);
};

const controlEquipment = (equipment) => {
  emit('control-equipment', equipment);
};
</script>
```

### 1.3 响应式设计方案

#### 1.3.1 移动端生产看板
```vue
<!-- 移动端生产看板组件 -->
<template>
  <div class="production-dashboard-mobile">
    <div class="dashboard-header">
      <h2>生产看板</h2>
      <el-button size="small" @click="refreshData">刷新</el-button>
    </div>

    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-value">{{ stats.totalOrders }}</div>
        <div class="stat-label">总工单</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.inProgress }}</div>
        <div class="stat-label">进行中</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.completed }}</div>
        <div class="stat-label">已完成</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.utilization }}%</div>
        <div class="stat-label">设备利用率</div>
      </div>
    </div>

    <div class="quick-actions">
      <el-button type="primary" @click="createWorkOrder">新增工单</el-button>
      <el-button @click="viewEquipment">设备状态</el-button>
      <el-button @click="viewQuality">质检记录</el-button>
    </div>

    <div class="work-order-list">
      <h3>进行中的工单</h3>
      <div
        v-for="order in activeOrders"
        :key="order.id"
        class="work-order-card"
        @click="viewOrderDetail(order)"
      >
        <div class="order-header">
          <span class="order-no">{{ order.workOrderNo }}</span>
          <el-tag :type="getStatusType(order.status)">
            {{ order.status }}
          </el-tag>
        </div>
        <div class="order-info">
          <div class="info-row">
            <span class="label">产品:</span>
            <span class="value">{{ order.productName }}</span>
          </div>
          <div class="info-row">
            <span class="label">进度:</span>
            <el-progress :percentage="order.progress" size="small" />
          </div>
          <div class="info-row">
            <span class="label">操作员:</span>
            <span class="value">{{ order.operator }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

---

## 2. MOCK数据设计规范

### 2.1 核心数据模型

#### 2.1.1 生产计划数据模型 (ProductionPlan)
```javascript
// production_plan_mock.js
export const productionPlanMockData = {
  plans: [
    {
      id: 'PP-20250802-001',
      planNo: 'PP-2025080200001',
      orderId: 'SO-20250802-001',
      orderNo: 'SO-2025080200001',
      customerName: 'ABC玻璃制品有限公司',
      productId: 'PRD-001',
      productName: '钢化玻璃',
      productCode: 'TG-12MM',
      specifications: {
        width: 1200,
        height: 2000,
        thickness: 12,
        type: 'tempered',
        color: 'clear'
      },
      quantity: 1000,
      unit: '平方米',
      priority: 'high', // high: 高, medium: 中, low: 低
      status: 'in_progress', // pending: 待开始, in_progress: 进行中, completed: 已完成, cancelled: 已取消
      progress: 65, // 完成进度 (%)

      // 时间计划
      plannedStartDate: '2025-08-05T08:00:00Z',
      plannedEndDate: '2025-08-10T18:00:00Z',
      actualStartDate: '2025-08-05T08:30:00Z',
      actualEndDate: null,
      estimatedEndDate: '2025-08-11T16:00:00Z',

      // 资源分配
      resourceAllocations: [
        {
          resourceId: 'EQ-001',
          resourceName: '切割机A',
          resourceType: 'equipment',
          allocatedTime: 8, // 分配时间(小时)
          startTime: '2025-08-05T08:00:00Z',
          endTime: '2025-08-05T16:00:00Z'
        },
        {
          resourceId: 'EQ-002',
          resourceName: '钢化炉B',
          resourceType: 'equipment',
          allocatedTime: 12,
          startTime: '2025-08-06T08:00:00Z',
          endTime: '2025-08-06T20:00:00Z'
        }
      ],

      // 工艺路线
      processRoute: [
        {
          sequenceNo: 1,
          processId: 'PROC-001',
          processName: '切割',
          workstationId: 'WS-001',
          workstationName: '切割工位',
          standardTime: 8, // 标准工时
          actualTime: 8.5, // 实际工时
          status: 'completed'
        },
        {
          sequenceNo: 2,
          processId: 'PROC-002',
          processName: '磨边',
          workstationId: 'WS-002',
          workstationName: '磨边工位',
          standardTime: 4,
          actualTime: null,
          status: 'in_progress'
        },
        {
          sequenceNo: 3,
          processId: 'PROC-003',
          processName: '钢化',
          workstationId: 'WS-003',
          workstationName: '钢化工位',
          standardTime: 12,
          actualTime: null,
          status: 'pending'
        }
      ],

      planner: {
        id: 'EMP-003',
        name: '王计划',
        planTime: '2025-08-04T16:00:00Z'
      },

      createTime: '2025-08-04T14:00:00Z',
      updateTime: '2025-08-06T10:30:00Z'
    }
  ]
};
```

#### 2.1.2 生产工单数据模型 (WorkOrder)
```javascript
// work_order_mock.js
export const workOrderMockData = {
  workOrders: [
    {
      id: 'WO-20250802-001',
      workOrderNo: 'WO-2025080200001',
      planId: 'PP-20250802-001',
      planNo: 'PP-2025080200001',
      orderId: 'SO-20250802-001',
      processId: 'PROC-001',
      processName: '切割',
      workstationId: 'WS-001',
      workstationName: '切割工位',

      // 产品信息
      productId: 'PRD-001',
      productName: '钢化玻璃',
      productCode: 'TG-12MM',
      specifications: {
        width: 1200,
        height: 2000,
        thickness: 12
      },
      quantity: 1000,
      unit: '平方米',

      // 工单状态
      status: 'in_progress', // pending: 待开始, in_progress: 进行中, paused: 暂停, completed: 已完成, cancelled: 已取消
      priority: 'high',
      progress: 80, // 完成进度 (%)

      // 时间信息
      plannedStartTime: '2025-08-05T08:00:00Z',
      plannedEndTime: '2025-08-05T16:00:00Z',
      actualStartTime: '2025-08-05T08:30:00Z',
      actualEndTime: null,

      // 人员分配
      operators: [
        {
          id: 'EMP-004',
          name: '张操作',
          role: '主操作员',
          startTime: '2025-08-05T08:30:00Z'
        },
        {
          id: 'EMP-005',
          name: '李助手',
          role: '助理操作员',
          startTime: '2025-08-05T08:30:00Z'
        }
      ],

      // 设备信息
      equipment: {
        id: 'EQ-001',
        name: '切割机A',
        status: 'running'
      },

      // 物料消耗
      materialConsumption: [
        {
          materialId: 'MAT-001',
          materialName: '浮法玻璃原片',
          plannedQuantity: 1050, // 计划用量(含损耗)
          actualQuantity: 1080, // 实际用量
          unit: '平方米',
          unitCost: 45
        }
      ],

      // 质检信息
      qualityChecks: [
        {
          checkId: 'QC-001',
          checkPoint: '切割尺寸检查',
          inspector: '质检员A',
          checkTime: '2025-08-05T12:00:00Z',
          result: 'pass', // pass: 合格, fail: 不合格, pending: 待检
          remarks: '尺寸符合要求'
        }
      ],

      // 异常记录
      exceptions: [
        {
          id: 'EXC-001',
          type: 'equipment_fault', // equipment_fault: 设备故障, material_shortage: 物料短缺, quality_issue: 质量问题
          description: '切割机刀片磨损，影响切割精度',
          occurTime: '2025-08-05T14:30:00Z',
          resolveTime: '2025-08-05T15:00:00Z',
          handler: '维修员B',
          status: 'resolved' // pending: 待处理, resolving: 处理中, resolved: 已解决
        }
      ],

      createTime: '2025-08-05T08:00:00Z',
      updateTime: '2025-08-05T15:30:00Z'
    }
  ]
};
```

#### 2.1.3 设备管理数据模型 (Equipment)
```javascript
// equipment_mock.js
export const equipmentMockData = {
  equipment: [
    {
      id: 'EQ-001',
      code: 'CUT-A001',
      name: '切割机A',
      type: 'cutting_machine',
      category: 'production_equipment',
      manufacturer: '某某机械公司',
      model: 'CUT-2000',
      serialNumber: '**********',

      // 设备状态
      status: 'running', // running: 运行中, idle: 空闲, maintenance: 维护中, fault: 故障, offline: 离线
      operationMode: 'automatic', // automatic: 自动, manual: 手动, semi_automatic: 半自动

      // 位置信息
      location: {
        workshop: '生产车间A',
        area: '切割区域',
        position: 'A-01'
      },

      // 技术参数
      specifications: {
        maxWidth: 3000, // 最大加工宽度(mm)
        maxHeight: 6000, // 最大加工高度(mm)
        maxThickness: 25, // 最大加工厚度(mm)
        cuttingSpeed: 50, // 切割速度(mm/min)
        precision: 0.1 // 精度(mm)
      },

      // 实时监控数据
      realTimeData: {
        utilization: 85, // 利用率 (%)
        temperature: 25, // 温度 (°C)
        vibration: 0.5, // 振动值
        power: 15.5, // 功率 (kW)
        speed: 45, // 当前速度
        lastUpdateTime: '2025-08-06T10:30:00Z'
      },

      // 报警信息
      alerts: [
        {
          id: 'ALT-001',
          level: 'warning', // info: 信息, warning: 警告, error: 错误, critical: 严重
          message: '刀片磨损度达到80%，建议更换',
          occurTime: '2025-08-06T09:00:00Z',
          status: 'active' // active: 活跃, acknowledged: 已确认, resolved: 已解决
        }
      ],

      // 维护信息
      maintenance: {
        lastMaintenanceDate: '2025-07-15',
        nextMaintenanceDate: '2025-08-15',
        maintenanceInterval: 30, // 维护间隔(天)
        maintenanceType: 'preventive', // preventive: 预防性, corrective: 纠正性
        maintenanceRecords: [
          {
            id: 'MR-001',
            date: '2025-07-15',
            type: 'preventive',
            description: '定期保养，更换润滑油',
            technician: '维修员A',
            duration: 4, // 维护时长(小时)
            cost: 500
          }
        ]
      },

      // 生产统计
      productionStats: {
        totalRunTime: 1200, // 总运行时间(小时)
        totalOutput: 50000, // 总产量(平方米)
        avgUtilization: 78, // 平均利用率 (%)
        mtbf: 720, // 平均故障间隔时间(小时)
        mttr: 2 // 平均修复时间(小时)
      },

      createTime: '2024-01-15T08:00:00Z',
      updateTime: '2025-08-06T10:30:00Z'
    }
  ]
};
```

### 2.2 模块间关联数据

#### 2.2.1 与销售管理的关联数据
```javascript
// sales_integration_mock.js
export const salesIntegrationMock = {
  // 订单生产关联数据
  orderProductionMapping: [
    {
      orderId: 'SO-20250802-001',
      orderNo: 'SO-2025080200001',
      customerId: 'CUS-20250802-001',
      customerName: 'ABC玻璃制品有限公司',
      salesRep: '李销售',
      productionPlans: [
        {
          planId: 'PP-20250802-001',
          planNo: 'PP-2025080200001',
          status: 'in_progress',
          progress: 65,
          estimatedDelivery: '2025-08-11'
        }
      ],
      deliveryRequirement: {
        deliveryDate: '2025-08-12',
        deliveryAddress: '上海市浦东新区张江高科技园区',
        specialRequirements: '需要木箱包装，小心轻放'
      }
    }
  ]
};
```

#### 2.2.2 与库存管理的关联数据
```javascript
// inventory_integration_mock.js
export const inventoryIntegrationMock = {
  // 物料需求计划
  materialRequirements: [
    {
      planId: 'PP-20250802-001',
      requirements: [
        {
          materialId: 'MAT-001',
          materialName: '浮法玻璃原片',
          requiredQuantity: 1050,
          availableQuantity: 800,
          shortageQuantity: 250,
          expectedArrival: '2025-08-07',
          supplier: '某某玻璃供应商'
        }
      ]
    }
  ],

  // 成品入库数据
  finishedGoodsInbound: [
    {
      workOrderId: 'WO-20250802-001',
      productId: 'PRD-001',
      quantity: 800, // 实际完成数量
      qualityGrade: 'A', // A: 优等品, B: 一等品, C: 合格品
      warehouseLocation: 'WH-A-001',
      inboundTime: '2025-08-05T16:30:00Z'
    }
  ]
};
```

---

## 3. 技术实施细节

### 3.1 核心业务逻辑实现

#### 3.1.1 智能排产算法
```javascript
// composables/useProductionScheduling.js
import { ref, computed } from 'vue';

export function useProductionScheduling() {
  const orders = ref([]);
  const resources = ref([]);
  const constraints = ref({});

  // 智能排产算法
  const generateSchedule = async (orderList, resourceList, constraintRules) => {
    try {
      // 1. 订单优先级排序
      const sortedOrders = sortOrdersByPriority(orderList);

      // 2. 资源可用性分析
      const resourceAvailability = analyzeResourceAvailability(resourceList);

      // 3. 约束条件检查
      const validatedOrders = validateConstraints(sortedOrders, constraintRules);

      // 4. 生成排产计划
      const schedule = await createOptimalSchedule(validatedOrders, resourceAvailability);

      return schedule;
    } catch (error) {
      console.error('排产算法执行失败:', error);
      throw error;
    }
  };

  // 订单优先级排序
  const sortOrdersByPriority = (orderList) => {
    return orderList.sort((a, b) => {
      // 优先级权重
      const priorityWeight = { high: 3, medium: 2, low: 1 };

      // 交期紧急度
      const urgencyA = calculateUrgency(a.deliveryDate);
      const urgencyB = calculateUrgency(b.deliveryDate);

      // 综合评分
      const scoreA = priorityWeight[a.priority] * 0.6 + urgencyA * 0.4;
      const scoreB = priorityWeight[b.priority] * 0.6 + urgencyB * 0.4;

      return scoreB - scoreA;
    });
  };

  // 计算交期紧急度
  const calculateUrgency = (deliveryDate) => {
    const now = new Date();
    const delivery = new Date(deliveryDate);
    const daysToDelivery = Math.ceil((delivery - now) / (1000 * 60 * 60 * 24));

    if (daysToDelivery <= 3) return 5; // 极紧急
    if (daysToDelivery <= 7) return 4; // 紧急
    if (daysToDelivery <= 14) return 3; // 一般
    if (daysToDelivery <= 30) return 2; // 宽松
    return 1; // 很宽松
  };

  // 资源可用性分析
  const analyzeResourceAvailability = (resourceList) => {
    return resourceList.map(resource => {
      const availability = calculateResourceAvailability(resource);
      return {
        ...resource,
        availability,
        utilizationRate: calculateUtilizationRate(resource),
        nextAvailableTime: getNextAvailableTime(resource)
      };
    });
  };

  // 创建最优排产计划
  const createOptimalSchedule = async (orders, resources) => {
    const schedule = [];

    for (const order of orders) {
      // 获取工艺路线
      const processRoute = await getProcessRoute(order.productId);

      // 为每个工序分配资源和时间
      let currentStartTime = new Date();

      for (const process of processRoute) {
        // 查找可用资源
        const availableResource = findAvailableResource(
          resources,
          process.requiredResourceType,
          currentStartTime
        );

        if (availableResource) {
          const scheduledProcess = {
            orderId: order.id,
            processId: process.id,
            resourceId: availableResource.id,
            startTime: currentStartTime,
            endTime: new Date(currentStartTime.getTime() + process.standardTime * 60 * 60 * 1000),
            estimatedDuration: process.standardTime
          };

          schedule.push(scheduledProcess);

          // 更新下一工序开始时间
          currentStartTime = scheduledProcess.endTime;

          // 更新资源占用状态
          updateResourceOccupancy(availableResource, scheduledProcess);
        }
      }
    }

    return schedule;
  };

  return {
    generateSchedule,
    sortOrdersByPriority,
    analyzeResourceAvailability
  };
}
```

#### 3.1.2 实时设备监控系统
```javascript
// composables/useEquipmentMonitoring.js
import { ref, onMounted, onUnmounted } from 'vue';

export function useEquipmentMonitoring() {
  const equipmentList = ref([]);
  const monitoringData = ref({});
  const alerts = ref([]);
  const wsConnection = ref(null);

  // 初始化WebSocket连接
  const initWebSocketConnection = () => {
    const wsUrl = `ws://${window.location.host}/api/equipment/monitor`;
    wsConnection.value = new WebSocket(wsUrl);

    wsConnection.value.onopen = () => {
      console.log('设备监控WebSocket连接已建立');
    };

    wsConnection.value.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleRealtimeData(data);
    };

    wsConnection.value.onclose = () => {
      console.log('设备监控WebSocket连接已断开');
      // 自动重连
      setTimeout(initWebSocketConnection, 5000);
    };

    wsConnection.value.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
    };
  };

  // 处理实时数据
  const handleRealtimeData = (data) => {
    switch (data.type) {
      case 'equipment_status':
        updateEquipmentStatus(data.payload);
        break;
      case 'monitoring_data':
        updateMonitoringData(data.payload);
        break;
      case 'alert':
        handleAlert(data.payload);
        break;
      default:
        console.warn('未知的数据类型:', data.type);
    }
  };

  // 更新设备状态
  const updateEquipmentStatus = (statusData) => {
    const equipment = equipmentList.value.find(eq => eq.id === statusData.equipmentId);
    if (equipment) {
      equipment.status = statusData.status;
      equipment.realTimeData = {
        ...equipment.realTimeData,
        ...statusData.data,
        lastUpdateTime: new Date().toISOString()
      };
    }
  };

  // 更新监控数据
  const updateMonitoringData = (data) => {
    monitoringData.value[data.equipmentId] = {
      ...monitoringData.value[data.equipmentId],
      ...data.metrics,
      timestamp: data.timestamp
    };
  };

  // 处理报警
  const handleAlert = (alertData) => {
    alerts.value.unshift({
      ...alertData,
      id: `alert_${Date.now()}`,
      timestamp: new Date().toISOString()
    });

    // 限制报警数量
    if (alerts.value.length > 100) {
      alerts.value = alerts.value.slice(0, 100);
    }

    // 触发通知
    showAlertNotification(alertData);
  };

  // 显示报警通知
  const showAlertNotification = (alert) => {
    if (Notification.permission === 'granted') {
      new Notification(`设备报警: ${alert.equipmentName}`, {
        body: alert.message,
        icon: '/icons/alert.png'
      });
    }
  };

  // 设备控制命令
  const sendControlCommand = async (equipmentId, command, parameters = {}) => {
    try {
      const commandData = {
        type: 'control_command',
        payload: {
          equipmentId,
          command,
          parameters,
          timestamp: new Date().toISOString()
        }
      };

      if (wsConnection.value?.readyState === WebSocket.OPEN) {
        wsConnection.value.send(JSON.stringify(commandData));
      } else {
        throw new Error('WebSocket连接未建立');
      }
    } catch (error) {
      console.error('发送控制命令失败:', error);
      throw error;
    }
  };

  // 获取设备历史数据
  const getEquipmentHistory = async (equipmentId, startTime, endTime, metrics) => {
    try {
      const response = await fetch('/api/equipment/history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          equipmentId,
          startTime,
          endTime,
          metrics
        })
      });

      if (!response.ok) {
        throw new Error('获取历史数据失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取设备历史数据失败:', error);
      throw error;
    }
  };

  onMounted(() => {
    initWebSocketConnection();
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  });

  onUnmounted(() => {
    if (wsConnection.value) {
      wsConnection.value.close();
    }
  });

  return {
    equipmentList,
    monitoringData,
    alerts,
    sendControlCommand,
    getEquipmentHistory
  };
}
```

### 3.2 性能优化策略

#### 3.2.1 大数据量甘特图优化
```javascript
// components/OptimizedGanttChart.vue
import { ref, computed, onMounted, onUnmounted } from 'vue';

export function useVirtualGantt(props) {
  const containerRef = ref();
  const scrollLeft = ref(0);
  const scrollTop = ref(0);
  const containerWidth = ref(0);
  const containerHeight = ref(0);

  // 虚拟化参数
  const rowHeight = 40;
  const columnWidth = 100;
  const bufferSize = 5;

  // 可见区域计算
  const visibleRows = computed(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop.value / rowHeight) - bufferSize);
    const endIndex = Math.min(
      props.resources.length,
      Math.ceil((scrollTop.value + containerHeight.value) / rowHeight) + bufferSize
    );

    return {
      startIndex,
      endIndex,
      items: props.resources.slice(startIndex, endIndex)
    };
  });

  const visibleColumns = computed(() => {
    const startIndex = Math.max(0, Math.floor(scrollLeft.value / columnWidth) - bufferSize);
    const endIndex = Math.min(
      props.timelineData.length,
      Math.ceil((scrollLeft.value + containerWidth.value) / columnWidth) + bufferSize
    );

    return {
      startIndex,
      endIndex,
      items: props.timelineData.slice(startIndex, endIndex)
    };
  });

  // 滚动处理
  const handleScroll = (e) => {
    scrollLeft.value = e.target.scrollLeft;
    scrollTop.value = e.target.scrollTop;
  };

  // 容器尺寸监听
  const updateContainerSize = () => {
    if (containerRef.value) {
      containerWidth.value = containerRef.value.clientWidth;
      containerHeight.value = containerRef.value.clientHeight;
    }
  };

  onMounted(() => {
    updateContainerSize();
    window.addEventListener('resize', updateContainerSize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateContainerSize);
  });

  return {
    containerRef,
    visibleRows,
    visibleColumns,
    handleScroll,
    rowHeight,
    columnWidth
  };
}
```

---

## 4. 开发指导文档

### 4.1 开发规范

#### 4.1.1 生产模块组件规范
```javascript
// 生产管理组件命名规范
const COMPONENT_NAMING = {
  // 页面组件 (PascalCase + 功能描述)
  pages: [
    'ProductionPlanManagement.vue',
    'WorkOrderManagement.vue',
    'EquipmentMonitoring.vue',
    'QualityInspection.vue'
  ],

  // 业务组件 (PascalCase + 业务前缀)
  business: [
    'ProductionGanttChart.vue',
    'ProductionWorkOrderCard.vue',
    'ProductionEquipmentMonitor.vue',
    'ProductionQualityForm.vue'
  ],

  // 通用组件 (PascalCase + 功能描述)
  common: [
    'VirtualTable.vue',
    'RealtimeChart.vue',
    'StatusIndicator.vue',
    'ProgressTracker.vue'
  ]
};
```

#### 4.1.2 状态管理规范
```javascript
// stores/production.js
import { defineStore } from 'pinia';

export const useProductionStore = defineStore('production', {
  state: () => ({
    // 生产计划
    plans: [],
    currentPlan: null,

    // 工单管理
    workOrders: [],
    activeWorkOrders: [],

    // 设备监控
    equipment: [],
    monitoringData: {},
    alerts: [],

    // 加载状态
    loading: {
      plans: false,
      workOrders: false,
      equipment: false
    }
  }),

  getters: {
    // 按状态分组的工单
    workOrdersByStatus: (state) => {
      return state.workOrders.reduce((groups, order) => {
        const status = order.status;
        if (!groups[status]) groups[status] = [];
        groups[status].push(order);
        return groups;
      }, {});
    },

    // 设备利用率统计
    equipmentUtilization: (state) => {
      const totalEquipment = state.equipment.length;
      const runningEquipment = state.equipment.filter(eq => eq.status === 'running').length;
      return totalEquipment > 0 ? (runningEquipment / totalEquipment * 100).toFixed(1) : 0;
    },

    // 活跃报警数量
    activeAlertsCount: (state) => {
      return state.alerts.filter(alert => alert.status === 'active').length;
    }
  },

  actions: {
    // 加载生产计划
    async loadProductionPlans(filters = {}) {
      this.loading.plans = true;
      try {
        const response = await productionService.getPlans(filters);
        this.plans = response.data;
      } catch (error) {
        console.error('加载生产计划失败:', error);
        throw error;
      } finally {
        this.loading.plans = false;
      }
    },

    // 创建工单
    async createWorkOrder(workOrderData) {
      try {
        const newWorkOrder = await productionService.createWorkOrder(workOrderData);
        this.workOrders.push(newWorkOrder);
        return newWorkOrder;
      } catch (error) {
        console.error('创建工单失败:', error);
        throw error;
      }
    },

    // 更新设备状态
    updateEquipmentStatus(equipmentId, statusData) {
      const equipment = this.equipment.find(eq => eq.id === equipmentId);
      if (equipment) {
        Object.assign(equipment, statusData);
      }
    }
  }
});
```

### 4.2 测试策略

#### 4.2.1 排产算法测试
```javascript
// tests/unit/composables/useProductionScheduling.test.js
import { describe, it, expect, vi } from 'vitest';
import { useProductionScheduling } from '@/composables/useProductionScheduling';

describe('useProductionScheduling', () => {
  it('应该正确按优先级排序订单', () => {
    const { sortOrdersByPriority } = useProductionScheduling();

    const orders = [
      { id: '1', priority: 'low', deliveryDate: '2025-08-15' },
      { id: '2', priority: 'high', deliveryDate: '2025-08-10' },
      { id: '3', priority: 'medium', deliveryDate: '2025-08-08' }
    ];

    const sorted = sortOrdersByPriority(orders);

    // 高优先级且交期紧急的订单应该排在前面
    expect(sorted[0].id).toBe('2');
  });

  it('应该正确分析资源可用性', () => {
    const { analyzeResourceAvailability } = useProductionScheduling();

    const resources = [
      {
        id: 'EQ-001',
        name: '切割机A',
        status: 'running',
        currentWorkload: 0.8
      }
    ];

    const analyzed = analyzeResourceAvailability(resources);

    expect(analyzed[0]).toHaveProperty('availability');
    expect(analyzed[0]).toHaveProperty('utilizationRate');
  });
});
```

### 4.3 集成方案

#### 4.3.1 WebSocket集成配置
```javascript
// config/websocket.js
export const WEBSOCKET_CONFIG = {
  // 连接配置
  connection: {
    url: process.env.VUE_APP_WS_URL || 'ws://localhost:8080/ws',
    protocols: ['production-monitor'],
    options: {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000
    }
  },

  // 消息类型
  messageTypes: {
    EQUIPMENT_STATUS: 'equipment_status',
    MONITORING_DATA: 'monitoring_data',
    ALERT: 'alert',
    CONTROL_COMMAND: 'control_command',
    HEARTBEAT: 'heartbeat'
  },

  // 订阅主题
  subscriptions: {
    equipment: '/topic/equipment/{equipmentId}',
    alerts: '/topic/alerts',
    production: '/topic/production/{planId}'
  }
};
```

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，生产管理模块技术实施指导 | 技术团队 |

---

**文档状态**: 技术实施指导完成 ✅
**覆盖内容**: 界面原型设计 ✅ MOCK数据设计 ✅ 技术实施细节 ✅ 开发指导文档 ✅
**技术栈**: Vue3 ✅ WebSocket ✅ 甘特图 ✅ 实时监控 ✅
```
```
```