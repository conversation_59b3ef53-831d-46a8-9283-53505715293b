# 销售管理模块技术实施指导

> **版本**: 2.1  
> **状态**: 技术实施指导  
> **撰写人**: 技术团队  
> **日期**: 2025-08-02  
> **目标**: 为销售管理模块提供完整的技术实施指导，包括界面原型设计、MOCK数据设计、技术实现方案和开发指导  
> **设计规范**: 参考 [原型设计规范](../../prototype_design_guidelines.md)  
> **业务需求**: 参考 [销售管理模块PRD](../Sales_Management_Module.md)

---

## 1. 界面原型设计指导

### 1.1 页面架构设计

#### 1.1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (60px)                      │
├─────────────────────────────────────────────────────────┤
│ 侧边栏 │                主内容区域                        │
│(240px) │  ┌─────────────────────────────────────────┐   │
│        │  │     销售管理模块导航 (48px)              │   │
│ 销售   │  ├─────────────────────────────────────────┤   │
│ 机会   │  │                                         │   │
│ 报价   │  │            功能页面内容                  │   │
│ 订单   │  │                                         │   │
│ 客户   │  │                                         │   │
│ 服务   │  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

#### 1.1.2 核心页面设计

**客户管理页面 (CustomerManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 客户管理 [新增客户] [导入] [导出]                         │
├─────────────────────────────────────────────────────────┤
│ 搜索栏: [客户名称] [客户类型] [地区] [销售员] [搜索]      │
├─────────────────────────────────────────────────────────┤
│ 客户列表 (Table)                                        │
│ ┌─────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │选择 │客户名│类型  │地区  │销售员│状态  │操作  │      │
│ ├─────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │ □   │ABC公司│企业  │上海  │张三  │活跃  │查看  │      │
│ └─────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
├─────────────────────────────────────────────────────────┤
│ 分页器: 共100条 [上一页] 1 2 3 [下一页]                 │
└─────────────────────────────────────────────────────────┘
```

**客户详情页面 (CustomerDetail.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 客户详情 - ABC玻璃公司 [编辑] [删除]                     │
├─────────────────────────────────────────────────────────┤
│ Tab导航: [基本信息] [联系人] [订单历史] [服务记录]       │
├─────────────────────────────────────────────────────────┤
│ 基本信息卡片                                            │
│ ┌─────────────────┬─────────────────────────────────┐   │
│ │ 客户信息        │ 业务统计                        │   │
│ │ 客户编码: CUS001│ 累计订单: 50单                  │   │
│ │ 客户名称: ABC   │ 累计金额: ¥500万                │   │
│ │ 客户类型: 企业  │ 信用等级: A级                   │   │
│ └─────────────────┴─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

**销售机会页面 (OpportunityManagement.vue)**
```
┌─────────────────────────────────────────────────────────┐
│ 销售机会管理 [新增机会] [批量操作]                       │
├─────────────────────────────────────────────────────────┤
│ 机会漏斗图 (Chart)                                      │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 线索(50) → 机会(30) → 报价(20) → 成交(10)          │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 机会列表 (Table)                                        │
│ ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┐      │
│ │机会名│客户  │阶段  │金额  │概率  │预计成交│操作│      │
│ ├──────┼──────┼──────┼──────┼──────┼──────┼──────┤      │
│ │项目A │ABC   │报价  │100万 │70%   │2025-09│查看│      │
│ └──────┴──────┴──────┴──────┴──────┴──────┴──────┘      │
└─────────────────────────────────────────────────────────┘
```

### 1.2 关键组件设计

#### 1.2.1 客户选择器组件 (CustomerSelector.vue)
```vue
<template>
  <el-select
    v-model="selectedCustomer"
    filterable
    remote
    :remote-method="searchCustomers"
    placeholder="请选择客户"
    class="customer-selector"
  >
    <el-option
      v-for="customer in customerOptions"
      :key="customer.id"
      :label="customer.name"
      :value="customer.id"
    >
      <div class="customer-option">
        <span class="customer-name">{{ customer.name }}</span>
        <span class="customer-code">{{ customer.code }}</span>
      </div>
    </el-option>
  </el-select>
</template>
```

#### 1.2.2 产品配置器组件 (ProductConfigurator.vue)
```vue
<template>
  <div class="product-configurator">
    <el-form :model="productConfig" label-width="120px">
      <el-form-item label="产品类型">
        <el-select v-model="productConfig.type" @change="onTypeChange">
          <el-option label="钢化玻璃" value="tempered" />
          <el-option label="夹胶玻璃" value="laminated" />
        </el-select>
      </el-form-item>
      <el-form-item label="规格尺寸">
        <el-input-number v-model="productConfig.width" placeholder="宽度" />
        <span class="dimension-separator">×</span>
        <el-input-number v-model="productConfig.height" placeholder="高度" />
        <span class="dimension-separator">×</span>
        <el-input-number v-model="productConfig.thickness" placeholder="厚度" />
      </el-form-item>
    </el-form>
  </div>
</template>
```

### 1.3 响应式设计方案

#### 1.3.1 断点设计
```scss
// 响应式断点
$breakpoints: (
  'mobile': 768px,
  'tablet': 1024px,
  'desktop': 1440px,
  'large': 1920px
);

// 移动端适配
@media (max-width: 768px) {
  .sales-management {
    .sidebar {
      transform: translateX(-100%);
      &.active {
        transform: translateX(0);
      }
    }
    
    .main-content {
      margin-left: 0;
      padding: 16px;
    }
    
    .customer-table {
      .el-table__header {
        display: none;
      }
      .el-table__row {
        display: block;
        border: 1px solid #eee;
        margin-bottom: 16px;
        padding: 16px;
      }
    }
  }
}
```

#### 1.3.2 移动端组件适配
```vue
<!-- 移动端客户卡片 -->
<template>
  <div class="customer-card-mobile">
    <div class="customer-header">
      <h3>{{ customer.name }}</h3>
      <el-tag :type="getStatusType(customer.status)">
        {{ customer.status }}
      </el-tag>
    </div>
    <div class="customer-info">
      <div class="info-item">
        <span class="label">客户编码:</span>
        <span class="value">{{ customer.code }}</span>
      </div>
      <div class="info-item">
        <span class="label">联系人:</span>
        <span class="value">{{ customer.contact }}</span>
      </div>
    </div>
    <div class="customer-actions">
      <el-button size="small" @click="viewDetail">查看</el-button>
      <el-button size="small" type="primary" @click="createOrder">下单</el-button>
    </div>
  </div>
</template>
```

---

## 2. MOCK数据设计规范

### 2.1 核心数据模型

#### 2.1.1 客户数据模型 (Customer)
```javascript
// customer_mock.js
export const customerMockData = {
  // 客户基本信息
  customers: [
    {
      id: 'CUS-************',
      code: 'CUS001',
      name: 'ABC玻璃制品有限公司',
      shortName: 'ABC玻璃',
      type: 'enterprise', // enterprise: 企业, individual: 个人
      industry: 'construction', // 所属行业
      scale: 'large', // large: 大型, medium: 中型, small: 小型
      region: 'shanghai',
      address: {
        province: '上海市',
        city: '上海市',
        district: '浦东新区',
        street: '张江高科技园区',
        detail: '科苑路123号'
      },
      contacts: [
        {
          id: 'CON-001',
          name: '张经理',
          position: '采购经理',
          phone: '***********',
          email: '<EMAIL>',
          isPrimary: true
        }
      ],
      salesRep: {
        id: 'EMP-001',
        name: '李销售',
        phone: '***********'
      },
      creditLevel: 'A', // A: 优秀, B: 良好, C: 一般, D: 较差
      creditLimit: 1000000, // 信用额度
      status: 'active', // active: 活跃, inactive: 非活跃, blacklist: 黑名单
      tags: ['重点客户', '长期合作'],
      businessStats: {
        totalOrders: 50,
        totalAmount: 5000000,
        lastOrderDate: '2025-07-30',
        avgOrderAmount: 100000
      },
      createTime: '2024-01-15T08:00:00Z',
      updateTime: '2025-08-01T16:30:00Z'
    }
  ]
};
```

#### 2.1.2 销售机会数据模型 (Opportunity)
```javascript
// opportunity_mock.js
export const opportunityMockData = {
  opportunities: [
    {
      id: 'OPP-************',
      name: '某大厦幕墙玻璃项目',
      customerId: 'CUS-************',
      customerName: 'ABC玻璃制品有限公司',
      stage: 'quotation', // lead: 线索, opportunity: 机会, quotation: 报价, negotiation: 谈判, closed_won: 成交, closed_lost: 失败
      amount: 1000000, // 预期金额
      probability: 70, // 成交概率 (%)
      expectedCloseDate: '2025-09-30',
      source: 'website', // website: 官网, referral: 推荐, exhibition: 展会, cold_call: 电话营销
      description: '某商业大厦外立面幕墙玻璃采购项目，预计需要钢化玻璃1000平方米',
      products: [
        {
          productId: 'PRD-001',
          productName: '钢化玻璃',
          quantity: 1000,
          unit: '平方米',
          estimatedPrice: 800
        }
      ],
      competitors: ['竞争对手A', '竞争对手B'],
      salesRep: {
        id: 'EMP-001',
        name: '李销售'
      },
      activities: [
        {
          id: 'ACT-001',
          type: 'call', // call: 电话, meeting: 会议, email: 邮件, visit: 拜访
          subject: '初次沟通项目需求',
          content: '与客户采购经理张经理电话沟通，了解项目基本需求',
          activityDate: '2025-08-01T14:00:00Z',
          nextAction: '安排现场勘测',
          nextActionDate: '2025-08-05T09:00:00Z'
        }
      ],
      createTime: '2025-08-01T10:00:00Z',
      updateTime: '2025-08-02T09:30:00Z'
    }
  ]
};
```

#### 2.1.3 报价数据模型 (Quotation)
```javascript
// quotation_mock.js
export const quotationMockData = {
  quotations: [
    {
      id: 'QUO-************',
      quoteNo: 'QUO-2025080200001',
      customerId: 'CUS-************',
      customerName: 'ABC玻璃制品有限公司',
      opportunityId: 'OPP-************',
      title: '某大厦幕墙玻璃报价单',
      status: 'pending', // draft: 草稿, pending: 待确认, confirmed: 已确认, expired: 已过期, rejected: 已拒绝
      validUntil: '2025-09-01',
      currency: 'CNY',
      items: [
        {
          id: 'QI-001',
          productId: 'PRD-001',
          productName: '钢化玻璃',
          productCode: 'TG-12MM',
          specifications: {
            width: 1200,
            height: 2000,
            thickness: 12,
            type: 'tempered',
            color: 'clear'
          },
          quantity: 1000,
          unit: '平方米',
          unitPrice: 850,
          discount: 0.05, // 5%折扣
          discountAmount: 42500,
          subtotal: 807500,
          deliveryDays: 15,
          remark: '包含钢化处理费用'
        }
      ],
      subtotal: 807500,
      taxRate: 0.13,
      taxAmount: 104975,
      totalAmount: 912475,
      terms: {
        payment: '30%预付，70%发货前付清',
        delivery: '合同签订后15个工作日内交货',
        warranty: '质量保证期12个月',
        packaging: '木箱包装，运费买方承担'
      },
      salesRep: {
        id: 'EMP-001',
        name: '李销售',
        phone: '***********',
        email: '<EMAIL>'
      },
      approver: {
        id: 'EMP-002',
        name: '王经理',
        approveTime: '2025-08-02T11:00:00Z'
      },
      createTime: '2025-08-02T09:00:00Z',
      updateTime: '2025-08-02T11:00:00Z'
    }
  ]
};
```

### 2.2 模块间关联数据

#### 2.2.1 与PDM服务的关联数据
```javascript
// pdm_integration_mock.js
export const pdmIntegrationMock = {
  // 产品配置数据 (来自PDM服务)
  productConfigs: [
    {
      productId: 'PRD-001',
      productName: '钢化玻璃',
      productCode: 'TG',
      category: 'tempered_glass',
      configRules: {
        thickness: [6, 8, 10, 12, 15, 19], // 可选厚度
        maxWidth: 3000, // 最大宽度
        maxHeight: 6000, // 最大高度
        minArea: 0.1, // 最小面积
        maxArea: 18 // 最大面积
      },
      priceRules: {
        basePrice: 600, // 基础价格/平方米
        thicknessPremium: { // 厚度加价
          6: 0,
          8: 50,
          10: 100,
          12: 150,
          15: 250,
          19: 400
        },
        processingFee: 200, // 钢化处理费
        minOrderQuantity: 10 // 最小起订量
      }
    }
  ],
  
  // BOM数据 (来自PDM服务)
  bomData: [
    {
      productId: 'PRD-001',
      bomId: 'BOM-001',
      materials: [
        {
          materialId: 'MAT-001',
          materialName: '浮法玻璃原片',
          quantity: 1.05, // 损耗系数
          unit: '平方米',
          unitCost: 45
        },
        {
          materialId: 'MAT-002',
          materialName: '钢化处理',
          quantity: 1,
          unit: '平方米',
          unitCost: 80
        }
      ]
    }
  ]
};
```

#### 2.2.2 与生产管理的关联数据
```javascript
// production_integration_mock.js
export const productionIntegrationMock = {
  // 订单生产状态数据
  orderProductionStatus: [
    {
      orderId: 'SO-************',
      productionOrderId: 'MO-************',
      status: 'in_production', // pending: 待生产, in_production: 生产中, completed: 已完成
      progress: 65, // 完成进度 (%)
      startDate: '2025-08-05',
      estimatedCompleteDate: '2025-08-20',
      actualCompleteDate: null,
      workstations: [
        {
          workstationId: 'WS-001',
          workstationName: '切割工位',
          status: 'completed',
          startTime: '2025-08-05T08:00:00Z',
          endTime: '2025-08-05T16:00:00Z'
        },
        {
          workstationId: 'WS-002',
          workstationName: '钢化工位',
          status: 'in_progress',
          startTime: '2025-08-06T08:00:00Z',
          endTime: null,
          progress: 80
        }
      ]
    }
  ]
};
```

---

## 3. 技术实施细节

### 3.1 核心业务逻辑实现

#### 3.1.1 智能产品配置算法
```javascript
// composables/useProductConfiguration.js
import { ref, computed } from 'vue';
import { pdmService } from '@/services/pdmService';

export function useProductConfiguration() {
  const selectedProduct = ref(null);
  const configuration = ref({
    width: null,
    height: null,
    thickness: null,
    quantity: 1
  });
  
  // 智能推荐配置
  const getRecommendedConfig = async (customerId, productId) => {
    try {
      // 获取客户历史订单偏好
      const customerPreferences = await getCustomerPreferences(customerId);
      
      // 获取产品配置规则
      const configRules = await pdmService.getProductConfigRules(productId);
      
      // 基于历史数据推荐配置
      const recommended = {
        thickness: customerPreferences.preferredThickness || configRules.defaultThickness,
        width: customerPreferences.commonWidth || 1200,
        height: customerPreferences.commonHeight || 2000
      };
      
      return recommended;
    } catch (error) {
      console.error('获取推荐配置失败:', error);
      return null;
    }
  };
  
  // 配置验证
  const validateConfiguration = computed(() => {
    if (!selectedProduct.value || !configuration.value) return { valid: false };
    
    const rules = selectedProduct.value.configRules;
    const config = configuration.value;
    
    const errors = [];
    
    // 尺寸验证
    if (config.width > rules.maxWidth) {
      errors.push(`宽度不能超过${rules.maxWidth}mm`);
    }
    if (config.height > rules.maxHeight) {
      errors.push(`高度不能超过${rules.maxHeight}mm`);
    }
    
    // 面积验证
    const area = (config.width * config.height) / 1000000; // 转换为平方米
    if (area < rules.minArea) {
      errors.push(`面积不能小于${rules.minArea}平方米`);
    }
    if (area > rules.maxArea) {
      errors.push(`面积不能超过${rules.maxArea}平方米`);
    }
    
    // 厚度验证
    if (!rules.thickness.includes(config.thickness)) {
      errors.push(`厚度必须为${rules.thickness.join('、')}mm中的一种`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  });
  
  // 价格计算
  const calculatePrice = computed(() => {
    if (!selectedProduct.value || !configuration.value || !validateConfiguration.value.valid) {
      return { unitPrice: 0, totalPrice: 0 };
    }
    
    const config = configuration.value;
    const priceRules = selectedProduct.value.priceRules;
    
    // 基础价格
    let unitPrice = priceRules.basePrice;
    
    // 厚度加价
    unitPrice += priceRules.thicknessPremium[config.thickness] || 0;
    
    // 处理费
    unitPrice += priceRules.processingFee;
    
    // 面积
    const area = (config.width * config.height) / 1000000;
    
    // 总价
    const totalPrice = unitPrice * area * config.quantity;
    
    return {
      unitPrice: Math.round(unitPrice * 100) / 100,
      totalPrice: Math.round(totalPrice * 100) / 100,
      area: Math.round(area * 100) / 100
    };
  });
  
  return {
    selectedProduct,
    configuration,
    getRecommendedConfig,
    validateConfiguration,
    calculatePrice
  };
}
```

#### 3.1.2 订单状态管理
```javascript
// composables/useOrderStatus.js
import { ref, computed } from 'vue';
import { orderService } from '@/services/orderService';
import { productionService } from '@/services/productionService';

export function useOrderStatus() {
  const orderStatuses = ref([]);
  
  // 订单状态枚举
  const ORDER_STATUS = {
    DRAFT: 'draft',
    CONFIRMED: 'confirmed',
    IN_PRODUCTION: 'in_production',
    COMPLETED: 'completed',
    SHIPPED: 'shipped',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled'
  };
  
  // 状态流转规则
  const statusTransitions = {
    [ORDER_STATUS.DRAFT]: [ORDER_STATUS.CONFIRMED, ORDER_STATUS.CANCELLED],
    [ORDER_STATUS.CONFIRMED]: [ORDER_STATUS.IN_PRODUCTION, ORDER_STATUS.CANCELLED],
    [ORDER_STATUS.IN_PRODUCTION]: [ORDER_STATUS.COMPLETED, ORDER_STATUS.CANCELLED],
    [ORDER_STATUS.COMPLETED]: [ORDER_STATUS.SHIPPED],
    [ORDER_STATUS.SHIPPED]: [ORDER_STATUS.DELIVERED],
    [ORDER_STATUS.DELIVERED]: [],
    [ORDER_STATUS.CANCELLED]: []
  };
  
  // 获取订单详细状态
  const getOrderDetailStatus = async (orderId) => {
    try {
      const [orderInfo, productionStatus] = await Promise.all([
        orderService.getOrderById(orderId),
        productionService.getProductionStatus(orderId)
      ]);
      
      return {
        ...orderInfo,
        production: productionStatus,
        timeline: generateStatusTimeline(orderInfo, productionStatus)
      };
    } catch (error) {
      console.error('获取订单状态失败:', error);
      throw error;
    }
  };
  
  // 生成状态时间线
  const generateStatusTimeline = (orderInfo, productionStatus) => {
    const timeline = [];
    
    // 订单确认
    if (orderInfo.confirmTime) {
      timeline.push({
        status: '订单确认',
        time: orderInfo.confirmTime,
        description: '订单已确认，等待生产安排',
        type: 'success'
      });
    }
    
    // 生产开始
    if (productionStatus?.startTime) {
      timeline.push({
        status: '开始生产',
        time: productionStatus.startTime,
        description: '订单已开始生产',
        type: 'primary'
      });
    }
    
    // 生产完成
    if (productionStatus?.completeTime) {
      timeline.push({
        status: '生产完成',
        time: productionStatus.completeTime,
        description: '产品生产完成，准备发货',
        type: 'success'
      });
    }
    
    return timeline.sort((a, b) => new Date(a.time) - new Date(b.time));
  };
  
  // 检查状态是否可以流转
  const canTransitionTo = (currentStatus, targetStatus) => {
    return statusTransitions[currentStatus]?.includes(targetStatus) || false;
  };
  
  return {
    ORDER_STATUS,
    orderStatuses,
    getOrderDetailStatus,
    canTransitionTo,
    generateStatusTimeline
  };
}
```

### 3.2 性能优化策略

#### 3.2.1 大数据量表格优化
```vue
<!-- components/VirtualTable.vue -->
<template>
  <div class="virtual-table-container" ref="containerRef">
    <div class="virtual-table-header">
      <table>
        <thead>
          <tr>
            <th v-for="column in columns" :key="column.key" :width="column.width">
              {{ column.title }}
            </th>
          </tr>
        </thead>
      </table>
    </div>
    
    <div 
      class="virtual-table-body" 
      ref="bodyRef"
      @scroll="handleScroll"
      :style="{ height: `${containerHeight}px` }"
    >
      <div :style="{ height: `${totalHeight}px`, position: 'relative' }">
        <table :style="{ transform: `translateY(${offsetY}px)` }">
          <tbody>
            <tr 
              v-for="(item, index) in visibleData" 
              :key="item.id || index"
              :style="{ height: `${itemHeight}px` }"
            >
              <td v-for="column in columns" :key="column.key">
                <slot :name="column.key" :record="item" :index="startIndex + index">
                  {{ item[column.key] }}
                </slot>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  data: Array,
  columns: Array,
  itemHeight: { type: Number, default: 48 },
  containerHeight: { type: Number, default: 400 }
});

const containerRef = ref();
const bodyRef = ref();
const scrollTop = ref(0);

// 计算可见数据
const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight) + 2);
const startIndex = computed(() => Math.floor(scrollTop.value / props.itemHeight));
const endIndex = computed(() => Math.min(startIndex.value + visibleCount.value, props.data.length));
const visibleData = computed(() => props.data.slice(startIndex.value, endIndex.value));
const totalHeight = computed(() => props.data.length * props.itemHeight);
const offsetY = computed(() => startIndex.value * props.itemHeight);

const handleScroll = (e) => {
  scrollTop.value = e.target.scrollTop;
};

onMounted(() => {
  // 初始化滚动监听
});

onUnmounted(() => {
  // 清理监听器
});
</script>
```

#### 3.2.2 数据缓存策略
```javascript
// utils/cache.js
class DataCache {
  constructor(maxSize = 100, ttl = 5 * 60 * 1000) { // 默认5分钟过期
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }
  
  set(key, value) {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
  
  clear() {
    this.cache.clear();
  }
}

// 创建全局缓存实例
export const customerCache = new DataCache(50);
export const productCache = new DataCache(100);
export const orderCache = new DataCache(200);
```

### 3.3 安全策略实现

#### 3.3.1 权限控制组件
```vue
<!-- components/PermissionGuard.vue -->
<template>
  <div v-if="hasPermission">
    <slot />
  </div>
  <div v-else-if="showFallback" class="permission-denied">
    <el-empty description="您没有权限访问此功能" />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useUserStore } from '@/stores/user';

const props = defineProps({
  permission: String, // 权限码
  role: String, // 角色
  showFallback: { type: Boolean, default: true }
});

const userStore = useUserStore();

const hasPermission = computed(() => {
  if (props.permission) {
    return userStore.hasPermission(props.permission);
  }
  if (props.role) {
    return userStore.hasRole(props.role);
  }
  return true;
});
</script>
```

#### 3.3.2 数据脱敏处理
```javascript
// utils/dataMask.js
export const dataMask = {
  // 手机号脱敏
  phone(phone) {
    if (!phone || phone.length < 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },
  
  // 身份证脱敏
  idCard(idCard) {
    if (!idCard || idCard.length < 15) return idCard;
    return idCard.replace(/(\d{6})\d*(\d{4})/, '$1******$2');
  },
  
  // 银行卡脱敏
  bankCard(cardNo) {
    if (!cardNo || cardNo.length < 16) return cardNo;
    return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1 **** **** $2');
  },
  
  // 邮箱脱敏
  email(email) {
    if (!email || !email.includes('@')) return email;
    const [username, domain] = email.split('@');
    const maskedUsername = username.length > 2 
      ? username.substring(0, 2) + '***' 
      : username;
    return `${maskedUsername}@${domain}`;
  }
};
```

---

## 4. 开发指导文档

### 4.1 开发规范

#### 4.1.1 Vue3 Composition API规范
```javascript
// 推荐的组件结构
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 2. 定义props和emits
const props = defineProps({
  customerId: String
});

const emit = defineEmits(['update', 'delete']);

// 3. 响应式数据
const loading = ref(false);
const customerData = ref(null);

// 4. 计算属性
const displayName = computed(() => {
  return customerData.value?.name || '未知客户';
});

// 5. 方法定义
const loadCustomerData = async () => {
  loading.value = true;
  try {
    // 加载数据逻辑
  } finally {
    loading.value = false;
  }
};

// 6. 生命周期
onMounted(() => {
  loadCustomerData();
});
</script>

<style scoped>
/* 组件样式 */
</style>
```

#### 4.1.2 文件组织规范
```
src/
├── views/
│   └── sales/
│       ├── CustomerManagement.vue
│       ├── OpportunityManagement.vue
│       └── OrderManagement.vue
├── components/
│   └── sales/
│       ├── CustomerSelector.vue
│       ├── ProductConfigurator.vue
│       └── OrderStatusTracker.vue
├── composables/
│   └── sales/
│       ├── useCustomer.js
│       ├── useOpportunity.js
│       └── useOrder.js
├── services/
│   ├── customerService.js
│   ├── opportunityService.js
│   └── orderService.js
└── mock/
    ├── customer_mock.js
    ├── opportunity_mock.js
    └── order_mock.js
```

### 4.2 测试策略

#### 4.2.1 单元测试示例
```javascript
// tests/unit/composables/useCustomer.test.js
import { describe, it, expect, vi } from 'vitest';
import { useCustomer } from '@/composables/sales/useCustomer';
import { customerService } from '@/services/customerService';

// Mock服务
vi.mock('@/services/customerService');

describe('useCustomer', () => {
  it('应该正确加载客户数据', async () => {
    // 准备测试数据
    const mockCustomer = {
      id: 'CUS-001',
      name: '测试客户',
      status: 'active'
    };
    
    customerService.getCustomerById.mockResolvedValue(mockCustomer);
    
    // 执行测试
    const { customer, loadCustomer } = useCustomer();
    await loadCustomer('CUS-001');
    
    // 验证结果
    expect(customer.value).toEqual(mockCustomer);
    expect(customerService.getCustomerById).toHaveBeenCalledWith('CUS-001');
  });
  
  it('应该正确处理加载错误', async () => {
    // 模拟错误
    customerService.getCustomerById.mockRejectedValue(new Error('网络错误'));
    
    const { customer, loadCustomer, error } = useCustomer();
    await loadCustomer('CUS-001');
    
    // 验证错误处理
    expect(customer.value).toBeNull();
    expect(error.value).toBeTruthy();
  });
});
```

#### 4.2.2 组件测试示例
```javascript
// tests/unit/components/CustomerSelector.test.js
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import CustomerSelector from '@/components/sales/CustomerSelector.vue';

describe('CustomerSelector', () => {
  it('应该正确渲染客户选择器', () => {
    const wrapper = mount(CustomerSelector, {
      props: {
        modelValue: null,
        customers: [
          { id: 'CUS-001', name: '客户A' },
          { id: 'CUS-002', name: '客户B' }
        ]
      }
    });
    
    expect(wrapper.find('.customer-selector').exists()).toBe(true);
    expect(wrapper.findAll('.el-option')).toHaveLength(2);
  });
  
  it('应该正确触发选择事件', async () => {
    const wrapper = mount(CustomerSelector);
    
    await wrapper.find('.el-select').trigger('click');
    await wrapper.findAll('.el-option')[0].trigger('click');
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
  });
});
```

### 4.3 集成方案

#### 4.3.1 API集成配置
```javascript
// config/api.js
export const API_CONFIG = {
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 10000,
  
  // 模块API端点
  endpoints: {
    sales: {
      customers: '/sales/customers',
      opportunities: '/sales/opportunities',
      quotations: '/sales/quotations',
      orders: '/sales/orders'
    },
    pdm: {
      products: '/pdm/products',
      configurations: '/pdm/configurations',
      boms: '/pdm/boms'
    },
    production: {
      orders: '/production/orders',
      status: '/production/status'
    }
  }
};
```

#### 4.3.2 状态管理集成
```javascript
// stores/sales.js
import { defineStore } from 'pinia';
import { customerService } from '@/services/customerService';

export const useSalesStore = defineStore('sales', {
  state: () => ({
    customers: [],
    opportunities: [],
    orders: [],
    currentCustomer: null
  }),
  
  getters: {
    activeCustomers: (state) => state.customers.filter(c => c.status === 'active'),
    totalOpportunityValue: (state) => state.opportunities.reduce((sum, opp) => sum + opp.amount, 0)
  },
  
  actions: {
    async loadCustomers() {
      try {
        this.customers = await customerService.getCustomers();
      } catch (error) {
        console.error('加载客户数据失败:', error);
        throw error;
      }
    },
    
    async createCustomer(customerData) {
      try {
        const newCustomer = await customerService.createCustomer(customerData);
        this.customers.push(newCustomer);
        return newCustomer;
      } catch (error) {
        console.error('创建客户失败:', error);
        throw error;
      }
    }
  }
});
```

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，销售管理模块技术实施指导 | 技术团队 |

---

**文档状态**: 技术实施指导完成 ✅  
**覆盖内容**: 界面原型设计 ✅ MOCK数据设计 ✅ 技术实施细节 ✅ 开发指导文档 ✅  
**技术栈**: Vue3 ✅ Composition API ✅ 响应式设计 ✅ 组件化开发 ✅
