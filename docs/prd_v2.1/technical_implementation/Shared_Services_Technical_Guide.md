# 共享服务技术实施指导

> **版本**: 2.1  
> **状态**: 技术实施指导  
> **撰写人**: 技术团队  
> **日期**: 2025-08-02  
> **目标**: 为PDM服务和基础服务提供完整的技术实施指导，包括API设计、数据模型和集成方案  
> **设计规范**: 参考 [原型设计规范](../../prototype_design_guidelines.md)  
> **业务需求**: 参考 [PDM服务PRD](../PDM_Service.md) 和 [基础服务PRD](../Basic_Service.md)

---

## 1. 共享服务架构设计

### 1.1 服务架构概览

```
┌─────────────────────────────────────────────────────────┐
│                    API网关层                             │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ 认证授权    │ 路由转发    │ 限流熔断                │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                  共享服务层                              │
│  ┌─────────────────────────┬─────────────────────────┐   │
│  │      PDM服务            │      基础服务           │   │
│  │ ┌─────────────────────┐ │ ┌─────────────────────┐ │   │
│  │ │ 产品数据管理        │ │ │ 用户权限管理        │ │   │
│  │ │ BOM管理             │ │ │ 组织架构管理        │ │   │
│  │ │ 工艺路线管理        │ │ │ 系统配置管理        │ │   │
│  │ │ 技术文档管理        │ │ │ 消息通知服务        │ │   │
│  │ └─────────────────────┘ │ │ 文件存储服务        │ │   │
│  └─────────────────────────┘ │ 日志审计服务        │ │   │
│                              │ 数据字典服务        │ │   │
│                              └─────────────────────┘ │   │
├─────────────────────────────────────────────────────────┤
│                    数据访问层                            │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ 数据库连接池│ 缓存管理    │ 数据同步                │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    基础设施层                            │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ MySQL数据库 │ Redis缓存   │ 文件存储                │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.2 服务通信模式

#### 1.2.1 同步通信 (RESTful API)
```javascript
// API调用示例
const pdmService = {
  // 获取产品信息
  async getProduct(productId) {
    return await apiClient.get(`/api/pdm/products/${productId}`);
  },
  
  // 获取BOM结构
  async getBOM(productId, version = 'latest') {
    return await apiClient.get(`/api/pdm/bom/${productId}`, {
      params: { version }
    });
  },
  
  // 获取工艺路线
  async getProcessRoute(productId) {
    return await apiClient.get(`/api/pdm/process-routes/${productId}`);
  }
};

const basicService = {
  // 用户认证
  async authenticate(credentials) {
    return await apiClient.post('/api/auth/login', credentials);
  },
  
  // 获取用户权限
  async getUserPermissions(userId) {
    return await apiClient.get(`/api/auth/users/${userId}/permissions`);
  },
  
  // 发送通知
  async sendNotification(notification) {
    return await apiClient.post('/api/notifications', notification);
  }
};
```

#### 1.2.2 异步通信 (事件驱动)
```javascript
// 事件发布订阅模式
const eventBus = {
  // 产品数据变更事件
  productUpdated: {
    topic: 'pdm.product.updated',
    schema: {
      productId: 'string',
      version: 'string',
      changes: 'object',
      timestamp: 'datetime',
      operator: 'string'
    }
  },
  
  // BOM变更事件
  bomUpdated: {
    topic: 'pdm.bom.updated',
    schema: {
      productId: 'string',
      bomVersion: 'string',
      changes: 'array',
      timestamp: 'datetime'
    }
  },
  
  // 用户权限变更事件
  permissionChanged: {
    topic: 'auth.permission.changed',
    schema: {
      userId: 'string',
      permissions: 'array',
      action: 'string', // added, removed, modified
      timestamp: 'datetime'
    }
  }
};
```

---

## 2. PDM服务技术实施

### 2.1 产品数据管理API设计

#### 2.1.1 产品信息管理
```javascript
// PDM产品管理API规范
const productAPI = {
  // 创建产品
  'POST /api/pdm/products': {
    requestBody: {
      productCode: 'string', // 产品编码
      productName: 'string', // 产品名称
      category: 'string',    // 产品类别
      specifications: {      // 产品规格
        width: 'number',
        height: 'number',
        thickness: 'number',
        material: 'string',
        color: 'string'
      },
      description: 'string', // 产品描述
      status: 'string',      // 状态: draft, active, obsolete
      version: 'string'      // 版本号
    },
    response: {
      id: 'string',
      productCode: 'string',
      productName: 'string',
      version: 'string',
      createTime: 'datetime',
      updateTime: 'datetime'
    }
  },
  
  // 获取产品列表
  'GET /api/pdm/products': {
    queryParams: {
      category: 'string',    // 产品类别过滤
      status: 'string',      // 状态过滤
      keyword: 'string',     // 关键词搜索
      page: 'number',        // 页码
      pageSize: 'number'     // 页大小
    },
    response: {
      data: 'array',         // 产品列表
      total: 'number',       // 总数
      page: 'number',        // 当前页
      pageSize: 'number'     // 页大小
    }
  },
  
  // 获取产品详情
  'GET /api/pdm/products/{id}': {
    pathParams: {
      id: 'string'           // 产品ID
    },
    queryParams: {
      version: 'string'      // 版本号，默认latest
    },
    response: {
      id: 'string',
      productCode: 'string',
      productName: 'string',
      category: 'string',
      specifications: 'object',
      description: 'string',
      status: 'string',
      version: 'string',
      versions: 'array',     // 所有版本列表
      createTime: 'datetime',
      updateTime: 'datetime'
    }
  },
  
  // 更新产品
  'PUT /api/pdm/products/{id}': {
    pathParams: {
      id: 'string'
    },
    requestBody: {
      // 同创建产品的字段
    },
    response: {
      // 同获取产品详情的响应
    }
  },
  
  // 产品版本管理
  'POST /api/pdm/products/{id}/versions': {
    pathParams: {
      id: 'string'
    },
    requestBody: {
      version: 'string',     // 新版本号
      changes: 'string',     // 变更说明
      baseVersion: 'string'  // 基于的版本
    },
    response: {
      version: 'string',
      createTime: 'datetime'
    }
  }
};
```

#### 2.1.2 BOM管理API
```javascript
// BOM管理API规范
const bomAPI = {
  // 创建BOM
  'POST /api/pdm/bom': {
    requestBody: {
      productId: 'string',   // 产品ID
      version: 'string',     // BOM版本
      bomType: 'string',     // BOM类型: engineering, manufacturing, sales
      items: [
        {
          materialId: 'string',      // 物料ID
          materialCode: 'string',    // 物料编码
          materialName: 'string',    // 物料名称
          quantity: 'number',        // 用量
          unit: 'string',           // 单位
          level: 'number',          // 层级
          parentId: 'string',       // 父级物料ID
          substitute: 'array',      // 替代料
          remarks: 'string'         // 备注
        }
      ],
      effectiveDate: 'date', // 生效日期
      status: 'string'       // 状态
    },
    response: {
      id: 'string',
      productId: 'string',
      version: 'string',
      createTime: 'datetime'
    }
  },
  
  // 获取BOM结构
  'GET /api/pdm/bom/{productId}': {
    pathParams: {
      productId: 'string'
    },
    queryParams: {
      version: 'string',     // BOM版本
      bomType: 'string',     // BOM类型
      level: 'number'        // 展开层级，默认全部
    },
    response: {
      productId: 'string',
      version: 'string',
      bomType: 'string',
      structure: {
        id: 'string',
        materialId: 'string',
        materialCode: 'string',
        materialName: 'string',
        quantity: 'number',
        unit: 'string',
        level: 'number',
        children: 'array'    // 子级BOM结构
      },
      totalMaterials: 'number',
      createTime: 'datetime',
      updateTime: 'datetime'
    }
  },
  
  // BOM对比
  'POST /api/pdm/bom/compare': {
    requestBody: {
      productId: 'string',
      version1: 'string',    // 对比版本1
      version2: 'string'     // 对比版本2
    },
    response: {
      differences: [
        {
          type: 'string',    // added, removed, modified
          materialId: 'string',
          materialCode: 'string',
          oldQuantity: 'number',
          newQuantity: 'number',
          level: 'number'
        }
      ],
      summary: {
        added: 'number',
        removed: 'number',
        modified: 'number'
      }
    }
  },
  
  // BOM成本计算
  'GET /api/pdm/bom/{productId}/cost': {
    pathParams: {
      productId: 'string'
    },
    queryParams: {
      version: 'string',
      costType: 'string'     // material, labor, overhead, total
    },
    response: {
      productId: 'string',
      version: 'string',
      costBreakdown: [
        {
          materialId: 'string',
          materialCode: 'string',
          materialName: 'string',
          quantity: 'number',
          unitCost: 'number',
          totalCost: 'number',
          costType: 'string'
        }
      ],
      summary: {
        materialCost: 'number',
        laborCost: 'number',
        overheadCost: 'number',
        totalCost: 'number'
      }
    }
  }
};
```

### 2.2 工艺路线管理

#### 2.2.1 工艺路线API
```javascript
// 工艺路线管理API
const processRouteAPI = {
  // 创建工艺路线
  'POST /api/pdm/process-routes': {
    requestBody: {
      productId: 'string',   // 产品ID
      routeName: 'string',   // 路线名称
      version: 'string',     // 版本号
      operations: [
        {
          operationId: 'string',     // 工序ID
          operationCode: 'string',   // 工序编码
          operationName: 'string',   // 工序名称
          sequence: 'number',        // 工序顺序
          workCenter: 'string',      // 工作中心
          setupTime: 'number',       // 准备时间(分钟)
          processTime: 'number',     // 加工时间(分钟)
          queueTime: 'number',       // 排队时间(分钟)
          moveTime: 'number',        // 移动时间(分钟)
          description: 'string',     // 工序描述
          qualityRequirements: [     // 质量要求
            {
              parameter: 'string',   // 质量参数
              standard: 'string',    // 标准值
              tolerance: 'string',   // 公差
              method: 'string'       // 检测方法
            }
          ],
          resources: [               // 所需资源
            {
              resourceType: 'string', // 资源类型: equipment, tool, material
              resourceId: 'string',   // 资源ID
              resourceName: 'string', // 资源名称
              quantity: 'number',     // 数量
              unit: 'string'         // 单位
            }
          ]
        }
      ],
      status: 'string'       // 状态
    },
    response: {
      id: 'string',
      productId: 'string',
      routeName: 'string',
      version: 'string',
      createTime: 'datetime'
    }
  },
  
  // 获取工艺路线
  'GET /api/pdm/process-routes/{productId}': {
    pathParams: {
      productId: 'string'
    },
    queryParams: {
      version: 'string'
    },
    response: {
      id: 'string',
      productId: 'string',
      routeName: 'string',
      version: 'string',
      operations: 'array',   // 工序列表
      totalTime: 'number',   // 总时间
      criticalPath: 'array', // 关键路径
      createTime: 'datetime',
      updateTime: 'datetime'
    }
  },
  
  // 工艺路线仿真
  'POST /api/pdm/process-routes/{id}/simulate': {
    pathParams: {
      id: 'string'
    },
    requestBody: {
      quantity: 'number',    // 生产数量
      startDate: 'datetime', // 开始时间
      constraints: {         // 约束条件
        workingHours: 'number',
        availableResources: 'array'
      }
    },
    response: {
      simulationId: 'string',
      estimatedDuration: 'number', // 预计耗时(小时)
      estimatedEndDate: 'datetime',
      resourceUtilization: [
        {
          resourceId: 'string',
          resourceName: 'string',
          utilizationRate: 'number'
        }
      ],
      bottlenecks: [         // 瓶颈工序
        {
          operationId: 'string',
          operationName: 'string',
          waitTime: 'number'
        }
      ]
    }
  }
};
```

---

## 3. 基础服务技术实施

### 3.1 用户权限管理

#### 3.1.1 认证授权API
```javascript
// 认证授权API规范
const authAPI = {
  // 用户登录
  'POST /api/auth/login': {
    requestBody: {
      username: 'string',    // 用户名
      password: 'string',    // 密码
      captcha: 'string',     // 验证码
      rememberMe: 'boolean'  // 记住我
    },
    response: {
      token: 'string',       // JWT令牌
      refreshToken: 'string', // 刷新令牌
      user: {
        id: 'string',
        username: 'string',
        name: 'string',
        email: 'string',
        avatar: 'string',
        roles: 'array',      // 角色列表
        permissions: 'array', // 权限列表
        lastLoginTime: 'datetime'
      },
      expiresIn: 'number'    // 过期时间(秒)
    }
  },
  
  // 刷新令牌
  'POST /api/auth/refresh': {
    requestBody: {
      refreshToken: 'string'
    },
    response: {
      token: 'string',
      expiresIn: 'number'
    }
  },
  
  // 用户登出
  'POST /api/auth/logout': {
    headers: {
      'Authorization': 'Bearer {token}'
    },
    response: {
      message: 'string'
    }
  },
  
  // 获取用户信息
  'GET /api/auth/user': {
    headers: {
      'Authorization': 'Bearer {token}'
    },
    response: {
      // 同登录响应中的user对象
    }
  },
  
  // 修改密码
  'PUT /api/auth/password': {
    headers: {
      'Authorization': 'Bearer {token}'
    },
    requestBody: {
      oldPassword: 'string',
      newPassword: 'string'
    },
    response: {
      message: 'string'
    }
  }
};
```

#### 3.1.2 权限管理API
```javascript
// 权限管理API
const permissionAPI = {
  // 获取权限列表
  'GET /api/auth/permissions': {
    queryParams: {
      module: 'string',      // 模块过滤
      type: 'string',        // 权限类型
      page: 'number',
      pageSize: 'number'
    },
    response: {
      data: [
        {
          id: 'string',
          code: 'string',      // 权限编码
          name: 'string',      // 权限名称
          module: 'string',    // 所属模块
          type: 'string',      // 权限类型: menu, button, api
          resource: 'string',  // 资源路径
          description: 'string'
        }
      ],
      total: 'number'
    }
  },
  
  // 获取角色列表
  'GET /api/auth/roles': {
    response: {
      data: [
        {
          id: 'string',
          code: 'string',      // 角色编码
          name: 'string',      // 角色名称
          description: 'string',
          permissions: 'array', // 权限列表
          userCount: 'number',  // 用户数量
          createTime: 'datetime'
        }
      ]
    }
  },
  
  // 创建角色
  'POST /api/auth/roles': {
    requestBody: {
      code: 'string',
      name: 'string',
      description: 'string',
      permissions: 'array'   // 权限ID列表
    },
    response: {
      id: 'string',
      code: 'string',
      name: 'string',
      createTime: 'datetime'
    }
  },
  
  // 分配用户角色
  'POST /api/auth/users/{userId}/roles': {
    pathParams: {
      userId: 'string'
    },
    requestBody: {
      roleIds: 'array'       // 角色ID列表
    },
    response: {
      message: 'string'
    }
  },
  
  // 检查权限
  'POST /api/auth/check-permission': {
    requestBody: {
      userId: 'string',
      resource: 'string',    // 资源路径
      action: 'string'       // 操作类型
    },
    response: {
      hasPermission: 'boolean',
      reason: 'string'       // 无权限时的原因
    }
  }
};
```

### 3.2 消息通知服务

#### 3.2.1 通知管理API
```javascript
// 消息通知API规范
const notificationAPI = {
  // 发送通知
  'POST /api/notifications': {
    requestBody: {
      type: 'string',        // 通知类型: system, business, alert
      title: 'string',       // 通知标题
      content: 'string',     // 通知内容
      priority: 'string',    // 优先级: low, medium, high, urgent
      channels: 'array',     // 发送渠道: web, email, sms, wechat
      recipients: [          // 接收者
        {
          type: 'string',    // user, role, department
          id: 'string',      // 接收者ID
          name: 'string'     // 接收者名称
        }
      ],
      data: 'object',        // 附加数据
      scheduledTime: 'datetime', // 定时发送时间
      expireTime: 'datetime'     // 过期时间
    },
    response: {
      id: 'string',
      status: 'string',      // sent, scheduled, failed
      sentCount: 'number',   // 发送数量
      createTime: 'datetime'
    }
  },

  // 获取用户通知
  'GET /api/notifications/user/{userId}': {
    pathParams: {
      userId: 'string'
    },
    queryParams: {
      status: 'string',      // unread, read, all
      type: 'string',        // 通知类型过滤
      page: 'number',
      pageSize: 'number'
    },
    response: {
      data: [
        {
          id: 'string',
          type: 'string',
          title: 'string',
          content: 'string',
          priority: 'string',
          status: 'string',  // unread, read
          data: 'object',
          createTime: 'datetime',
          readTime: 'datetime'
        }
      ],
      unreadCount: 'number',
      total: 'number'
    }
  },

  // 标记通知已读
  'PUT /api/notifications/{id}/read': {
    pathParams: {
      id: 'string'
    },
    response: {
      message: 'string'
    }
  },

  // 批量标记已读
  'PUT /api/notifications/batch-read': {
    requestBody: {
      notificationIds: 'array', // 通知ID列表
      userId: 'string'
    },
    response: {
      updatedCount: 'number'
    }
  }
};
```

#### 3.2.2 通知模板管理
```javascript
// 通知模板API
const notificationTemplateAPI = {
  // 创建通知模板
  'POST /api/notification-templates': {
    requestBody: {
      code: 'string',        // 模板编码
      name: 'string',        // 模板名称
      type: 'string',        // 模板类型
      channels: 'array',     // 支持的渠道
      templates: {           // 各渠道模板
        web: {
          title: 'string',   // 支持变量: {{variable}}
          content: 'string'
        },
        email: {
          subject: 'string',
          body: 'string',    // 支持HTML
          attachments: 'array'
        },
        sms: {
          content: 'string'  // 短信内容
        }
      },
      variables: [           // 模板变量
        {
          name: 'string',    // 变量名
          type: 'string',    // 变量类型
          required: 'boolean',
          description: 'string'
        }
      ],
      status: 'string'       // active, inactive
    },
    response: {
      id: 'string',
      code: 'string',
      createTime: 'datetime'
    }
  },

  // 使用模板发送通知
  'POST /api/notifications/template/{templateCode}': {
    pathParams: {
      templateCode: 'string'
    },
    requestBody: {
      recipients: 'array',   // 接收者列表
      variables: 'object',   // 模板变量值
      channels: 'array',     // 发送渠道
      priority: 'string'
    },
    response: {
      id: 'string',
      status: 'string',
      sentCount: 'number'
    }
  }
};
```

### 3.3 文件存储服务

#### 3.3.1 文件管理API
```javascript
// 文件存储API规范
const fileStorageAPI = {
  // 文件上传
  'POST /api/files/upload': {
    requestBody: {
      file: 'file',          // 文件内容
      category: 'string',    // 文件分类: document, image, video, other
      module: 'string',      // 所属模块
      businessId: 'string',  // 业务ID
      description: 'string'  // 文件描述
    },
    response: {
      id: 'string',
      filename: 'string',    // 原始文件名
      storedName: 'string',  // 存储文件名
      url: 'string',         // 访问URL
      size: 'number',        // 文件大小(字节)
      mimeType: 'string',    // MIME类型
      category: 'string',
      uploadTime: 'datetime'
    }
  },

  // 批量上传
  'POST /api/files/batch-upload': {
    requestBody: {
      files: 'array',        // 文件列表
      category: 'string',
      module: 'string',
      businessId: 'string'
    },
    response: {
      success: 'array',      // 成功上传的文件
      failed: 'array',       // 上传失败的文件
      totalCount: 'number',
      successCount: 'number'
    }
  },

  // 文件下载
  'GET /api/files/{id}/download': {
    pathParams: {
      id: 'string'
    },
    queryParams: {
      inline: 'boolean'      // 是否内联显示
    },
    response: 'file'         // 文件流
  },

  // 获取文件信息
  'GET /api/files/{id}': {
    pathParams: {
      id: 'string'
    },
    response: {
      id: 'string',
      filename: 'string',
      storedName: 'string',
      url: 'string',
      size: 'number',
      mimeType: 'string',
      category: 'string',
      module: 'string',
      businessId: 'string',
      description: 'string',
      uploadTime: 'datetime',
      uploader: {
        id: 'string',
        name: 'string'
      }
    }
  },

  // 删除文件
  'DELETE /api/files/{id}': {
    pathParams: {
      id: 'string'
    },
    response: {
      message: 'string'
    }
  },

  // 获取业务相关文件
  'GET /api/files/business/{module}/{businessId}': {
    pathParams: {
      module: 'string',
      businessId: 'string'
    },
    queryParams: {
      category: 'string'
    },
    response: {
      data: 'array'          // 文件列表
    }
  }
};
```

### 3.4 数据字典服务

#### 3.4.1 字典管理API
```javascript
// 数据字典API规范
const dictionaryAPI = {
  // 获取字典分类
  'GET /api/dictionaries/categories': {
    response: {
      data: [
        {
          code: 'string',      // 分类编码
          name: 'string',      // 分类名称
          description: 'string',
          itemCount: 'number'  // 字典项数量
        }
      ]
    }
  },

  // 获取字典项
  'GET /api/dictionaries/{category}': {
    pathParams: {
      category: 'string'     // 字典分类
    },
    queryParams: {
      status: 'string',      // active, inactive, all
      parentCode: 'string'   // 父级编码(用于树形结构)
    },
    response: {
      data: [
        {
          code: 'string',      // 字典编码
          name: 'string',      // 字典名称
          value: 'string',     // 字典值
          parentCode: 'string', // 父级编码
          sort: 'number',      // 排序
          status: 'string',    // 状态
          description: 'string',
          extData: 'object'    // 扩展数据
        }
      ]
    }
  },

  // 创建字典项
  'POST /api/dictionaries/{category}': {
    pathParams: {
      category: 'string'
    },
    requestBody: {
      code: 'string',
      name: 'string',
      value: 'string',
      parentCode: 'string',
      sort: 'number',
      description: 'string',
      extData: 'object'
    },
    response: {
      id: 'string',
      code: 'string',
      createTime: 'datetime'
    }
  },

  // 更新字典项
  'PUT /api/dictionaries/{category}/{code}': {
    pathParams: {
      category: 'string',
      code: 'string'
    },
    requestBody: {
      name: 'string',
      value: 'string',
      sort: 'number',
      status: 'string',
      description: 'string',
      extData: 'object'
    },
    response: {
      message: 'string'
    }
  },

  // 删除字典项
  'DELETE /api/dictionaries/{category}/{code}': {
    pathParams: {
      category: 'string',
      code: 'string'
    },
    response: {
      message: 'string'
    }
  }
};
```

---

## 4. 数据模型设计

### 4.1 PDM服务数据模型

#### 4.1.1 产品数据模型
```sql
-- 产品主表
CREATE TABLE pdm_products (
    id VARCHAR(32) PRIMARY KEY COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    category VARCHAR(50) NOT NULL COMMENT '产品类别',
    specifications JSON COMMENT '产品规格',
    description TEXT COMMENT '产品描述',
    status ENUM('draft', 'active', 'obsolete') DEFAULT 'draft' COMMENT '状态',
    current_version VARCHAR(20) DEFAULT '1.0' COMMENT '当前版本',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(32) COMMENT '创建人',
    updater VARCHAR(32) COMMENT '更新人',

    INDEX idx_product_code (product_code),
    INDEX idx_category (category),
    INDEX idx_status (status)
) COMMENT '产品主表';

-- 产品版本表
CREATE TABLE pdm_product_versions (
    id VARCHAR(32) PRIMARY KEY COMMENT '版本ID',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    changes TEXT COMMENT '变更说明',
    base_version VARCHAR(20) COMMENT '基于版本',
    status ENUM('draft', 'released', 'obsolete') DEFAULT 'draft' COMMENT '版本状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    creator VARCHAR(32) COMMENT '创建人',

    UNIQUE KEY uk_product_version (product_id, version),
    FOREIGN KEY (product_id) REFERENCES pdm_products(id) ON DELETE CASCADE
) COMMENT '产品版本表';

-- BOM主表
CREATE TABLE pdm_bom (
    id VARCHAR(32) PRIMARY KEY COMMENT 'BOM ID',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    version VARCHAR(20) NOT NULL COMMENT 'BOM版本',
    bom_type ENUM('engineering', 'manufacturing', 'sales') DEFAULT 'manufacturing' COMMENT 'BOM类型',
    effective_date DATE COMMENT '生效日期',
    status ENUM('draft', 'active', 'obsolete') DEFAULT 'draft' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(32) COMMENT '创建人',

    UNIQUE KEY uk_product_bom (product_id, version, bom_type),
    FOREIGN KEY (product_id) REFERENCES pdm_products(id) ON DELETE CASCADE
) COMMENT 'BOM主表';

-- BOM明细表
CREATE TABLE pdm_bom_items (
    id VARCHAR(32) PRIMARY KEY COMMENT '明细ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    material_id VARCHAR(32) NOT NULL COMMENT '物料ID',
    material_code VARCHAR(50) NOT NULL COMMENT '物料编码',
    material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
    quantity DECIMAL(15,6) NOT NULL COMMENT '用量',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    level INT NOT NULL DEFAULT 1 COMMENT '层级',
    parent_id VARCHAR(32) COMMENT '父级物料ID',
    substitute JSON COMMENT '替代料',
    remarks TEXT COMMENT '备注',
    sort_order INT DEFAULT 0 COMMENT '排序',

    FOREIGN KEY (bom_id) REFERENCES pdm_bom(id) ON DELETE CASCADE,
    INDEX idx_material (material_id),
    INDEX idx_level (level),
    INDEX idx_parent (parent_id)
) COMMENT 'BOM明细表';
```

#### 4.1.2 工艺路线数据模型
```sql
-- 工艺路线主表
CREATE TABLE pdm_process_routes (
    id VARCHAR(32) PRIMARY KEY COMMENT '路线ID',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    route_name VARCHAR(200) NOT NULL COMMENT '路线名称',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    status ENUM('draft', 'active', 'obsolete') DEFAULT 'draft' COMMENT '状态',
    total_time INT DEFAULT 0 COMMENT '总时间(分钟)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(32) COMMENT '创建人',

    UNIQUE KEY uk_product_route (product_id, version),
    FOREIGN KEY (product_id) REFERENCES pdm_products(id) ON DELETE CASCADE
) COMMENT '工艺路线主表';

-- 工艺工序表
CREATE TABLE pdm_process_operations (
    id VARCHAR(32) PRIMARY KEY COMMENT '工序ID',
    route_id VARCHAR(32) NOT NULL COMMENT '路线ID',
    operation_code VARCHAR(50) NOT NULL COMMENT '工序编码',
    operation_name VARCHAR(200) NOT NULL COMMENT '工序名称',
    sequence INT NOT NULL COMMENT '工序顺序',
    work_center VARCHAR(50) COMMENT '工作中心',
    setup_time INT DEFAULT 0 COMMENT '准备时间(分钟)',
    process_time INT DEFAULT 0 COMMENT '加工时间(分钟)',
    queue_time INT DEFAULT 0 COMMENT '排队时间(分钟)',
    move_time INT DEFAULT 0 COMMENT '移动时间(分钟)',
    description TEXT COMMENT '工序描述',

    FOREIGN KEY (route_id) REFERENCES pdm_process_routes(id) ON DELETE CASCADE,
    INDEX idx_sequence (sequence),
    INDEX idx_work_center (work_center)
) COMMENT '工艺工序表';

-- 工序质量要求表
CREATE TABLE pdm_operation_quality (
    id VARCHAR(32) PRIMARY KEY COMMENT '质量要求ID',
    operation_id VARCHAR(32) NOT NULL COMMENT '工序ID',
    parameter VARCHAR(100) NOT NULL COMMENT '质量参数',
    standard VARCHAR(100) COMMENT '标准值',
    tolerance VARCHAR(50) COMMENT '公差',
    method VARCHAR(200) COMMENT '检测方法',

    FOREIGN KEY (operation_id) REFERENCES pdm_process_operations(id) ON DELETE CASCADE
) COMMENT '工序质量要求表';

-- 工序资源表
CREATE TABLE pdm_operation_resources (
    id VARCHAR(32) PRIMARY KEY COMMENT '资源ID',
    operation_id VARCHAR(32) NOT NULL COMMENT '工序ID',
    resource_type ENUM('equipment', 'tool', 'material') NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(32) NOT NULL COMMENT '资源ID',
    resource_name VARCHAR(200) NOT NULL COMMENT '资源名称',
    quantity DECIMAL(15,6) DEFAULT 1 COMMENT '数量',
    unit VARCHAR(20) COMMENT '单位',

    FOREIGN KEY (operation_id) REFERENCES pdm_process_operations(id) ON DELETE CASCADE,
    INDEX idx_resource_type (resource_type)
) COMMENT '工序资源表';
```

### 4.2 基础服务数据模型

#### 4.2.1 用户权限数据模型
```sql
-- 用户表
CREATE TABLE sys_users (
    id VARCHAR(32) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像URL',
    department_id VARCHAR(32) COMMENT '部门ID',
    status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '状态',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_department (department_id)
) COMMENT '用户表';

-- 角色表
CREATE TABLE sys_roles (
    id VARCHAR(32) PRIMARY KEY COMMENT '角色ID',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_code (code)
) COMMENT '角色表';

-- 权限表
CREATE TABLE sys_permissions (
    id VARCHAR(32) PRIMARY KEY COMMENT '权限ID',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    module VARCHAR(50) NOT NULL COMMENT '所属模块',
    type ENUM('menu', 'button', 'api') NOT NULL COMMENT '权限类型',
    resource VARCHAR(500) COMMENT '资源路径',
    parent_id VARCHAR(32) COMMENT '父级权限ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    description TEXT COMMENT '权限描述',

    INDEX idx_code (code),
    INDEX idx_module (module),
    INDEX idx_type (type),
    INDEX idx_parent (parent_id)
) COMMENT '权限表';

-- 用户角色关联表
CREATE TABLE sys_user_roles (
    id VARCHAR(32) PRIMARY KEY COMMENT '关联ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    role_id VARCHAR(32) NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES sys_users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_roles(id) ON DELETE CASCADE
) COMMENT '用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permissions (
    id VARCHAR(32) PRIMARY KEY COMMENT '关联ID',
    role_id VARCHAR(32) NOT NULL COMMENT '角色ID',
    permission_id VARCHAR(32) NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES sys_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES sys_permissions(id) ON DELETE CASCADE
) COMMENT '角色权限关联表';
```

#### 4.2.2 通知服务数据模型
```sql
-- 通知主表
CREATE TABLE sys_notifications (
    id VARCHAR(32) PRIMARY KEY COMMENT '通知ID',
    type ENUM('system', 'business', 'alert') NOT NULL COMMENT '通知类型',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    data JSON COMMENT '附加数据',
    status ENUM('draft', 'sent', 'failed') DEFAULT 'draft' COMMENT '状态',
    scheduled_time DATETIME COMMENT '定时发送时间',
    expire_time DATETIME COMMENT '过期时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    creator VARCHAR(32) COMMENT '创建人',

    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_status (status),
    INDEX idx_scheduled_time (scheduled_time)
) COMMENT '通知主表';

-- 通知接收者表
CREATE TABLE sys_notification_recipients (
    id VARCHAR(32) PRIMARY KEY COMMENT '接收者ID',
    notification_id VARCHAR(32) NOT NULL COMMENT '通知ID',
    recipient_type ENUM('user', 'role', 'department') NOT NULL COMMENT '接收者类型',
    recipient_id VARCHAR(32) NOT NULL COMMENT '接收者ID',
    recipient_name VARCHAR(100) NOT NULL COMMENT '接收者名称',
    channel ENUM('web', 'email', 'sms', 'wechat') NOT NULL COMMENT '发送渠道',
    status ENUM('pending', 'sent', 'read', 'failed') DEFAULT 'pending' COMMENT '状态',
    sent_time DATETIME COMMENT '发送时间',
    read_time DATETIME COMMENT '阅读时间',

    FOREIGN KEY (notification_id) REFERENCES sys_notifications(id) ON DELETE CASCADE,
    INDEX idx_recipient (recipient_type, recipient_id),
    INDEX idx_channel (channel),
    INDEX idx_status (status)
) COMMENT '通知接收者表';

-- 文件存储表
CREATE TABLE sys_files (
    id VARCHAR(32) PRIMARY KEY COMMENT '文件ID',
    filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    category ENUM('document', 'image', 'video', 'other') DEFAULT 'other' COMMENT '文件分类',
    module VARCHAR(50) COMMENT '所属模块',
    business_id VARCHAR(32) COMMENT '业务ID',
    description TEXT COMMENT '文件描述',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    uploader VARCHAR(32) COMMENT '上传人',

    INDEX idx_category (category),
    INDEX idx_module (module),
    INDEX idx_business (business_id),
    INDEX idx_uploader (uploader)
) COMMENT '文件存储表';

-- 数据字典表
CREATE TABLE sys_dictionaries (
    id VARCHAR(32) PRIMARY KEY COMMENT '字典ID',
    category VARCHAR(50) NOT NULL COMMENT '字典分类',
    code VARCHAR(100) NOT NULL COMMENT '字典编码',
    name VARCHAR(100) NOT NULL COMMENT '字典名称',
    value VARCHAR(500) COMMENT '字典值',
    parent_code VARCHAR(100) COMMENT '父级编码',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    description TEXT COMMENT '描述',
    ext_data JSON COMMENT '扩展数据',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_category_code (category, code),
    INDEX idx_category (category),
    INDEX idx_parent_code (parent_code),
    INDEX idx_status (status)
) COMMENT '数据字典表';
```

---

## 5. 集成方案

### 5.1 服务间通信

#### 5.1.1 API网关配置
```javascript
// API网关路由配置
const gatewayConfig = {
  routes: [
    // PDM服务路由
    {
      path: '/api/pdm/**',
      service: 'pdm-service',
      url: 'http://pdm-service:8080',
      stripPath: true,
      preserveHost: false,
      retries: 3,
      connectTimeout: 5000,
      readTimeout: 30000
    },

    // 基础服务路由
    {
      path: '/api/auth/**',
      service: 'auth-service',
      url: 'http://basic-service:8081',
      stripPath: true,
      preserveHost: false,
      retries: 3,
      connectTimeout: 5000,
      readTimeout: 10000
    },

    {
      path: '/api/notifications/**',
      service: 'notification-service',
      url: 'http://basic-service:8081',
      stripPath: true,
      preserveHost: false,
      retries: 3,
      connectTimeout: 5000,
      readTimeout: 15000
    },

    {
      path: '/api/files/**',
      service: 'file-service',
      url: 'http://basic-service:8081',
      stripPath: true,
      preserveHost: false,
      retries: 3,
      connectTimeout: 5000,
      readTimeout: 60000
    }
  ],

  // 中间件配置
  middleware: [
    'cors',           // 跨域处理
    'auth',           // 身份认证
    'rateLimit',      // 限流
    'logging',        // 日志记录
    'circuitBreaker'  // 熔断器
  ],

  // 认证配置
  auth: {
    exclude: [
      '/api/auth/login',
      '/api/auth/refresh',
      '/api/files/*/download'
    ],
    tokenHeader: 'Authorization',
    tokenPrefix: 'Bearer '
  },

  // 限流配置
  rateLimit: {
    windowMs: 60000,  // 1分钟
    max: 1000,        // 最大请求数
    message: '请求过于频繁，请稍后再试'
  },

  // 熔断器配置
  circuitBreaker: {
    timeout: 30000,           // 超时时间
    errorThresholdPercentage: 50, // 错误率阈值
    resetTimeout: 30000       // 重置时间
  }
};
```

#### 5.1.2 服务发现配置
```javascript
// 服务注册与发现
const serviceRegistry = {
  // 服务注册
  register: async (serviceName, serviceInfo) => {
    const registration = {
      name: serviceName,
      id: `${serviceName}-${Date.now()}`,
      address: serviceInfo.host,
      port: serviceInfo.port,
      tags: serviceInfo.tags || [],
      check: {
        http: `http://${serviceInfo.host}:${serviceInfo.port}/health`,
        interval: '10s',
        timeout: '3s'
      },
      meta: serviceInfo.meta || {}
    };

    // 注册到服务注册中心
    await consulClient.agent.service.register(registration);
  },

  // 服务发现
  discover: async (serviceName) => {
    const services = await consulClient.health.service({
      service: serviceName,
      passing: true
    });

    return services.map(service => ({
      id: service.Service.ID,
      address: service.Service.Address,
      port: service.Service.Port,
      tags: service.Service.Tags,
      meta: service.Service.Meta
    }));
  },

  // 负载均衡
  loadBalance: (services, strategy = 'round-robin') => {
    switch (strategy) {
      case 'round-robin':
        return services[Math.floor(Math.random() * services.length)];
      case 'random':
        return services[Math.floor(Math.random() * services.length)];
      case 'least-connections':
        // 实现最少连接数算法
        return services.reduce((min, service) =>
          service.connections < min.connections ? service : min
        );
      default:
        return services[0];
    }
  }
};
```

### 5.2 数据一致性保证

#### 5.2.1 分布式事务
```javascript
// 分布式事务管理
class DistributedTransaction {
  constructor() {
    this.participants = [];
    this.status = 'INIT';
  }

  // 添加参与者
  addParticipant(service, operation, compensateOperation) {
    this.participants.push({
      service,
      operation,
      compensateOperation,
      status: 'PENDING'
    });
  }

  // 执行事务
  async execute() {
    this.status = 'EXECUTING';
    const results = [];

    try {
      // 第一阶段：执行所有操作
      for (const participant of this.participants) {
        const result = await participant.operation();
        participant.status = 'PREPARED';
        results.push(result);
      }

      // 第二阶段：提交所有操作
      for (const participant of this.participants) {
        await participant.service.commit();
        participant.status = 'COMMITTED';
      }

      this.status = 'COMMITTED';
      return results;
    } catch (error) {
      // 回滚操作
      await this.rollback();
      throw error;
    }
  }

  // 回滚事务
  async rollback() {
    this.status = 'ROLLING_BACK';

    for (const participant of this.participants.reverse()) {
      if (participant.status === 'PREPARED' || participant.status === 'COMMITTED') {
        try {
          await participant.compensateOperation();
          participant.status = 'COMPENSATED';
        } catch (error) {
          console.error(`回滚失败: ${participant.service.name}`, error);
        }
      }
    }

    this.status = 'ROLLED_BACK';
  }
}

// 使用示例
const createProductWithBOM = async (productData, bomData) => {
  const transaction = new DistributedTransaction();

  // 添加创建产品操作
  transaction.addParticipant(
    pdmService,
    () => pdmService.createProduct(productData),
    (productId) => pdmService.deleteProduct(productId)
  );

  // 添加创建BOM操作
  transaction.addParticipant(
    pdmService,
    () => pdmService.createBOM(bomData),
    (bomId) => pdmService.deleteBOM(bomId)
  );

  // 添加发送通知操作
  transaction.addParticipant(
    notificationService,
    () => notificationService.sendNotification({
      type: 'business',
      title: '产品创建成功',
      content: `产品 ${productData.productName} 已创建`
    }),
    () => {} // 通知无需回滚
  );

  return await transaction.execute();
};
```

---

## 6. 开发指导

### 6.1 开发环境搭建

#### 6.1.1 Docker容器化部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  # API网关
  api-gateway:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - pdm-service
      - basic-service
    networks:
      - glass-erp-network

  # PDM服务
  pdm-service:
    build:
      context: ./services/pdm
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=glass_erp_pdm
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - glass-erp-network

  # 基础服务
  basic-service:
    build:
      context: ./services/basic
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=glass_erp_basic
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - glass-erp-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=glass_erp
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - glass-erp-network

  # Redis缓存
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - glass-erp-network

volumes:
  mysql_data:
  redis_data:

networks:
  glass-erp-network:
    driver: bridge
```

### 6.2 测试策略

#### 6.2.1 单元测试
```javascript
// tests/unit/pdm/productService.test.js
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ProductService } from '@/services/ProductService';
import { mockDatabase } from '@/tests/mocks/database';

describe('ProductService', () => {
  let productService;

  beforeEach(() => {
    productService = new ProductService(mockDatabase);
  });

  afterEach(() => {
    mockDatabase.reset();
  });

  describe('createProduct', () => {
    it('应该成功创建产品', async () => {
      const productData = {
        productCode: 'TEST-001',
        productName: '测试产品',
        category: 'glass',
        specifications: {
          width: 1000,
          height: 2000,
          thickness: 12
        }
      };

      const result = await productService.createProduct(productData);

      expect(result).toBeDefined();
      expect(result.productCode).toBe(productData.productCode);
      expect(result.productName).toBe(productData.productName);
    });

    it('应该验证必填字段', async () => {
      const invalidData = {
        productName: '测试产品'
        // 缺少productCode
      };

      await expect(productService.createProduct(invalidData))
        .rejects.toThrow('产品编码不能为空');
    });
  });

  describe('getBOM', () => {
    it('应该返回正确的BOM结构', async () => {
      const productId = 'PROD-001';
      const version = '1.0';

      const bom = await productService.getBOM(productId, version);

      expect(bom).toBeDefined();
      expect(bom.productId).toBe(productId);
      expect(bom.version).toBe(version);
      expect(bom.structure).toBeDefined();
    });
  });
});
```

#### 6.2.2 集成测试
```javascript
// tests/integration/api.test.js
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { app } from '@/app';
import { setupTestDatabase, cleanupTestDatabase } from '@/tests/helpers/database';

describe('API集成测试', () => {
  let authToken;

  beforeAll(async () => {
    await setupTestDatabase();

    // 登录获取token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'test_user',
        password: 'test_password'
      });

    authToken = loginResponse.body.token;
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  describe('产品管理API', () => {
    it('应该能够创建产品', async () => {
      const productData = {
        productCode: 'API-TEST-001',
        productName: 'API测试产品',
        category: 'glass'
      };

      const response = await request(app)
        .post('/api/pdm/products')
        .set('Authorization', `Bearer ${authToken}`)
        .send(productData)
        .expect(201);

      expect(response.body.productCode).toBe(productData.productCode);
    });

    it('应该能够获取产品列表', async () => {
      const response = await request(app)
        .get('/api/pdm/products')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.total).toBeGreaterThan(0);
    });
  });
});
```

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，共享服务技术实施指导 | 技术团队 |

---

**文档状态**: 技术实施指导完成 ✅
**覆盖内容**: API设计 ✅ 数据模型 ✅ 集成方案 ✅ 开发指导 ✅
**技术栈**: Node.js ✅ MySQL ✅ Redis ✅ Docker ✅
