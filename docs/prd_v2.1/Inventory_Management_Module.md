# PRD-M3: 库存管理模块 产品需求文档 V2.1

> **版本**: 2.1  
> **状态**: 架构优化版  
> **撰写人**: 产品团队  
> **日期**: 2025-08-02  
> **优化说明**: 整合V2.0的仓储管理子系统(WMS)和采购管理子系统(PMS)，形成统一的库存管理模块  
> **术语表**: 参考 [全局术语表](./Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](./Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 V2.1优化目标
**整合V2.0的仓储管理子系统(WMS)和采购管理子系统(PMS)，解决库存管理与采购业务分离导致的数据不一致问题，实现采购到库存的一体化管理。**

### 1.2 价值主张
构建智能库存管理平台，通过采购计划与库存控制的深度融合，实现从需求计划、采购执行到库存管理的全流程一体化，提升库存周转率和资金利用效率。

### 1.3 商业价值量化
- **库存成本降低**: 一体化管理使库存资金占用减少35%
- **库存准确率提升**: 从85%提升至99.5%，为生产和财务提供准确数据
- **采购效率提升**: MRP自动计算使采购处理效率提升80%
- **缺料风险降低**: 智能预警使生产停工风险降低90%

---

## 2. 模块架构设计

### 2.1 整合架构
**库存管理模块 = 仓储管理子系统(WMS) + 采购管理子系统(PMS)**

```mermaid
graph TB
    subgraph "库存管理模块 V2.1"
        subgraph "采购计划管理"
            A[供应商管理]
            B[MRP需求计划]
            C[采购订单管理]
            D[采购执行跟踪]
        end
        
        subgraph "仓储作业管理"
            E[仓储结构管理]
            F[收货入库管理]
            G[智能拣货管理]
            H[库存盘点管理]
        end
        
        subgraph "库存控制"
            I[库存查询分析]
            J[变体库存管理]
            K[安全库存控制]
            L[库存预警监控]
        end
        
        subgraph "共享服务调用"
            M[PDM服务]
            N[基础服务]
        end
    end
    
    B --> C
    C --> F
    F --> I
    I --> L
    A --> C
    J --> L
    B --> M
    E --> N
```

### 2.2 功能整合映射

| V2.1功能模块 | 整合的V2.0功能 | 整合优势 |
|-------------|---------------|----------|
| **智能采购计划** | PMS需求计划 + WMS库存查询 | 基于实时库存的精准采购计划 |
| **采购到库存** | PMS采购订单 + WMS收货入库 | 采购订单与入库作业无缝衔接 |
| **变体库存管理** | WMS变体库存 + PMS变体采购 | 变体采购与库存统一管理 |
| **库存成本控制** | WMS库存分析 + PMS成本管理 | 库存成本与采购成本一体化 |

---

## 3. 核心功能设计

### 3.1 智能采购计划

#### 3.1.1 功能描述
整合MRP需求计算与库存数据，实现基于实时库存状态的智能采购计划制定。

#### 3.1.2 核心功能
- **MRP自动计算**: 基于销售订单、生产BOM、实时库存的需求计算
- **采购建议生成**: 考虑供应商能力、价格、交期的采购建议
- **变体需求分析**: 基础物料的变体需求分析和采购优化
- **采购计划优化**: 批量采购、经济订货量、供应商整合优化

#### 3.1.3 业务规则
- MRP计算公式：净需求 = (销售订单需求 + 安全库存) - (现有库存 + 在途采购)
- 变体物料采购时优先考虑通用性和库存周转
- 采购建议必须考虑供应商最小订货量
- 紧急需求支持快速采购流程

### 3.2 采购到库存一体化

#### 3.2.1 功能描述
实现采购订单执行与库存入库的无缝衔接，确保采购数据与库存数据的实时同步。

#### 3.2.2 核心功能
- **采购订单管理**: 订单创建、审批、执行、跟踪全流程管理
- **收货入库集成**: 采购订单与收货入库单据自动关联
- **质量检验集成**: 收货质检与库存状态联动
- **财务集成**: 采购成本与库存成本自动核算

#### 3.2.3 业务规则
- 收货数量不能超过采购订单数量
- 质检不合格物料自动隔离，不计入可用库存
- 采购价格变动自动更新库存成本
- 收货完成自动更新采购订单状态

### 3.3 变体库存管理

#### 3.3.1 功能描述
整合变体采购与变体库存管理，实现变体物料的精细化管理和优化配置。

#### 3.3.2 核心功能
- **变体采购策略**: 基于变体需求分析的采购策略制定
- **变体库存控制**: 按变体规格的独立库存管理和预警
- **变体切割优化**: 原片变体的切割优化和库存配置
- **变体成本分析**: 变体采购成本与库存成本分析

#### 3.3.3 业务规则
- 变体库存独立管理，支持汇总查询
- 变体安全库存按规格独立设置
- 变体切割优化优先使用库存原片
- 变体成本按加权平均法计算

### 3.4 库存控制与预警

#### 3.4.1 功能描述
建立智能库存控制体系，通过多维度预警和分析，实现库存的精细化管理。

#### 3.4.2 核心功能
- **安全库存控制**: 动态安全库存设置和自动补货触发
- **库存预警监控**: 多级预警体系和智能预警推送
- **库存分析报表**: ABC分析、周转率分析、呆滞库存分析
- **库存优化建议**: 基于数据分析的库存优化建议

#### 3.4.3 业务规则
- 安全库存 = 平均日消耗量 × 采购提前期 × 安全系数
- 预警级别：绿色(正常) > 黄色(预警) > 红色(紧急)
- 呆滞库存定义：90天无出库记录的库存
- 库存周转率 = 年出库金额 / 平均库存金额

---

## 4. 服务集成设计

### 4.1 PDM服务集成

#### 4.1.1 BOM数据获取
```javascript
// 获取产品BOM用于MRP计算
const bomData = await fetch('/api/pdm/boms/product/' + productId, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

#### 4.1.2 物料变体信息
```javascript
// 获取物料变体信息用于采购决策
const variantInfo = await fetch('/api/pdm/materials/' + materialId + '/variants', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

### 4.2 基础服务集成

#### 4.2.1 用户权限控制
- 采购员：创建采购订单、供应商管理
- 采购经理：审批采购订单、采购策略制定
- 仓管员：收发货作业、库存盘点
- 仓库主管：库存分析、仓储规划

#### 4.2.2 数据字典集成
- 采购订单状态：草稿/待审核/已审核/执行中/已完成
- 库存状态：可用/冻结/质检中/隔离/报废
- 预警级别：正常/预警/紧急/缺货

---

## 5. 核心业务流程

### 5.1 MRP到采购执行流程

```mermaid
sequenceDiagram
    participant SO as 销售订单
    participant MRP as MRP计算
    participant INV as 库存查询
    participant PO as 采购订单
    participant SUP as 供应商
    participant WMS as 仓储管理
    
    SO->>MRP: 触发需求计算
    MRP->>INV: 查询现有库存
    INV-->>MRP: 返回库存数据
    MRP->>MRP: 计算净需求
    MRP->>PO: 生成采购建议
    PO->>SUP: 发送采购订单
    SUP->>WMS: 送货到仓库
    WMS->>INV: 更新库存数据
    WMS->>PO: 更新订单状态
```

### 5.2 变体库存管理流程

```mermaid
sequenceDiagram
    participant REQ as 需求分析
    participant VAR as 变体管理
    participant PUR as 采购执行
    participant REC as 收货入库
    participant OPT as 切割优化
    
    REQ->>VAR: 分析变体需求
    VAR->>PUR: 制定变体采购策略
    PUR->>REC: 执行变体采购
    REC->>VAR: 更新变体库存
    VAR->>OPT: 触发切割优化
    OPT->>VAR: 更新优化结果
```

---

## 6. 数据模型设计

### 6.1 采购订单数据模型
```sql
CREATE TABLE purchase_orders (
    order_id VARCHAR(20) PRIMARY KEY,
    order_no VARCHAR(30) UNIQUE NOT NULL,
    supplier_id VARCHAR(20) NOT NULL,
    order_date DATE NOT NULL,
    expected_date DATE,
    total_amount DECIMAL(15,2),
    status VARCHAR(20) DEFAULT 'DRAFT',
    created_by VARCHAR(20),
    created_date DATETIME DEFAULT NOW()
);
```

### 6.2 库存数据模型
```sql
CREATE TABLE inventory_stocks (
    stock_id VARCHAR(30) PRIMARY KEY,
    material_code VARCHAR(12) NOT NULL,
    variant_id VARCHAR(20),
    warehouse_code VARCHAR(10) NOT NULL,
    location_code VARCHAR(20),
    batch_no VARCHAR(30),
    quantity DECIMAL(15,3) NOT NULL,
    available_qty DECIMAL(15,3) NOT NULL,
    frozen_qty DECIMAL(15,3) DEFAULT 0,
    unit_cost DECIMAL(10,4),
    last_updated DATETIME DEFAULT NOW()
);
```

### 6.3 库存预警数据模型
```sql
CREATE TABLE inventory_alerts (
    alert_id VARCHAR(30) PRIMARY KEY,
    material_code VARCHAR(12) NOT NULL,
    variant_id VARCHAR(20),
    alert_type VARCHAR(20) NOT NULL,
    alert_level VARCHAR(10) NOT NULL,
    current_qty DECIMAL(15,3),
    safe_stock_qty DECIMAL(15,3),
    alert_message VARCHAR(200),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW()
);
```

---

## 7. 性能和可靠性要求

### 7.1 性能指标
- **MRP计算时间**: 100个订单 ≤ 2分钟
- **库存查询响应时间**: ≤ 2秒
- **收货入库处理时间**: ≤ 30秒
- **预警计算时间**: ≤ 1分钟
- **系统可用性**: ≥ 99.5%

### 7.2 业务指标
- **库存准确率**: ≥ 99.5%
- **采购计划准确率**: ≥ 95%
- **库存周转率**: 提升 ≥ 30%
- **缺料风险**: 降低 ≥ 90%
- **采购效率**: 提升 ≥ 80%

---

## 8. 用户界面设计

### 8.1 采购计划中心
- **MRP运算界面**: 参数设置、运算进度、结果展示
- **采购建议列表**: 需求分析、供应商推荐、批量转单
- **采购订单管理**: 订单创建、审批流程、执行跟踪
- **供应商绩效**: 交期准确率、质量合格率、价格趋势

### 8.2 仓储作业终端
- **收货入库**: 扫码收货、质检确认、库位分配
- **拣货出库**: 任务接收、路径优化、扫码确认
- **库存盘点**: 盘点计划、差异处理、结果确认
- **库位管理**: 库位规划、容量监控、状态维护

### 8.3 库存控制中心
- **库存总览**: 库存结构、周转分析、预警监控
- **变体库存**: 变体汇总、明细查询、切割优化
- **安全库存**: 安全库存设置、补货建议、预警配置
- **库存分析**: ABC分析、呆滞分析、成本分析

### 8.4 移动端应用
- **PDA收发货**: 扫码作业、实时同步、离线支持
- **移动查询**: 库存查询、订单跟踪、预警推送
- **移动审批**: 采购审批、异常处理、状态更新
- **移动盘点**: 盘点作业、差异录入、结果上传

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 架构优化版本，整合仓储管理和采购管理子系统 | 产品团队 |

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 采购库存一体化 ✅ 智能需求计划 ✅ 精细库存控制 ✅
