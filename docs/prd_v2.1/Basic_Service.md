# PRD-S2: 基础服务 产品需求文档 V2.1

> **版本**: 2.1  
> **状态**: 架构优化版  
> **撰写人**: 产品团队  
> **日期**: 2025-08-02  
> **优化说明**: 将V2.0的基础管理子系统重构为共享服务，为所有业务模块提供基础管理能力  
> **术语表**: 参考 [全局术语表](./Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](./Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 V2.1优化目标
**将V2.0的基础管理子系统重构为共享服务，解决各业务模块重复实现用户认证、权限管理、组织架构等基础功能的问题，实现基础服务的统一管理和服务化提供。**

### 1.2 价值主张
构建企业级基础服务中心，通过服务化架构为销售、生产、库存、财务等业务模块提供统一的用户认证、权限管理、组织架构、数据字典等基础服务，实现"一次登录，全系统访问"的用户体验。

### 1.3 商业价值量化
- **安全风险降低**: 统一的权限管理使数据泄露风险降低90%
- **管理效率提升**: 新员工入职配置时间从30分钟缩短至5分钟
- **运维成本降低**: 统一服务管理减少重复配置工作60%
- **开发效率提升**: 服务化设计使各模块开发效率提升40%

---

## 2. 服务架构设计

### 2.1 服务定位
**基础服务作为共享服务层的核心组件，为所有业务模块提供基础管理能力**

```mermaid
graph TB
    subgraph "业务模块层"
        A[销售管理模块]
        B[生产管理模块]
        C[库存管理模块]
        D[财务管理模块]
        E[项目管理模块]
        F[质量管理模块]
    end
    
    subgraph "共享服务层"
        G[基础服务]
        H[PDM服务]
    end
    
    subgraph "基础服务内部"
        I[用户认证服务]
        J[权限管理服务]
        K[组织架构服务]
        L[数据字典服务]
    end
    
    subgraph "数据层"
        M[用户数据库]
        N[权限数据库]
        O[组织数据库]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> I
    G --> J
    G --> K
    G --> L
    
    I --> M
    J --> N
    K --> O
    L --> O
```

### 2.2 服务能力矩阵

| 服务能力 | 服务接口 | 调用模块 | 说明 |
|----------|----------|----------|------|
| 用户认证服务 | /api/auth/* | 所有模块 | 统一的身份认证和会话管理 |
| 权限管理服务 | /api/permissions/* | 所有模块 | RBAC权限控制和数据权限 |
| 组织架构服务 | /api/organizations/* | 所有模块 | 企业组织结构管理 |
| 数据字典服务 | /api/dictionaries/* | 所有模块 | 基础数据字典维护 |
| 用户管理服务 | /api/users/* | 所有模块 | 用户生命周期管理 |
| 角色管理服务 | /api/roles/* | 所有模块 | 角色定义和权限分配 |

---

## 3. 核心服务功能

### 3.1 用户认证服务

#### 3.1.1 服务描述
为所有业务模块提供统一的用户身份认证和会话管理能力，实现"一次登录，全系统访问"。

#### 3.1.2 核心API接口

**用户登录服务**
```
POST /api/auth/login
参数: username, password, captcha
返回: JWT Token, 用户信息, 权限列表
```

**Token验证服务**
```
GET /api/auth/verify
参数: Authorization Header
返回: 用户身份信息和权限
```

**单点登录服务**
```
GET /api/auth/sso
参数: redirect_url
返回: 重定向到目标系统
```

**会话管理服务**
```
POST /api/auth/logout
DELETE /api/auth/sessions/{session_id}
功能: 用户登出和会话管理
```

#### 3.1.3 业务规则
- 支持用户名密码、手机验证码等多种认证方式
- JWT Token有效期可配置，默认8小时
- 支持记住登录状态，最长30天
- 异常登录自动锁定账户

### 3.2 权限管理服务

#### 3.2.1 服务描述
提供基于RBAC的权限管理能力，包括菜单权限、操作权限、数据权限的统一控制。

#### 3.2.2 核心API接口

**权限校验服务**
```
POST /api/permissions/check
参数: user_id, resource, action
返回: 权限校验结果
```

**用户权限查询**
```
GET /api/permissions/user/{user_id}
返回: 用户的完整权限列表
```

**角色权限管理**
```
GET/POST/PUT /api/roles/{role_id}/permissions
功能: 角色权限的配置和管理
```

**数据权限控制**
```
POST /api/permissions/data-scope
参数: user_id, resource_type
返回: 用户的数据访问范围
```

#### 3.2.3 业务规则
- 基于RBAC模型：用户-角色-权限
- 支持菜单权限和操作权限分离
- 数据权限支持：本人/本部门/本部门及下级/全部
- 权限变更实时生效

### 3.3 组织架构服务

#### 3.3.1 服务描述
提供企业组织架构的统一管理能力，支持多级组织结构和灵活的架构调整。

#### 3.3.2 核心API接口

**组织架构查询**
```
GET /api/organizations/tree
返回: 完整的组织架构树
```

**组织节点管理**
```
GET/POST/PUT/DELETE /api/organizations/{org_id}
功能: 组织节点的增删改查
```

**用户组织关系**
```
GET /api/organizations/users/{user_id}
PUT /api/organizations/users/{user_id}/transfer
功能: 用户组织归属管理
```

#### 3.3.3 业务规则
- 支持无限层级的树状组织结构
- 组织编码全局唯一
- 支持拖拽调整组织关系
- 组织变更自动更新用户权限

### 3.4 数据字典服务

#### 3.4.1 服务描述
提供统一的数据字典管理能力，为业务表单提供标准化的数据选项。

#### 3.4.2 核心API接口

**字典数据查询**
```
GET /api/dictionaries/{dict_type}
返回: 指定类型的字典数据
```

**字典管理服务**
```
GET/POST/PUT/DELETE /api/dictionaries
功能: 字典类型和字典值的管理
```

**业务表单集成**
```
GET /api/dictionaries/form-options/{form_field}
返回: 表单字段的选项数据
```

#### 3.4.3 业务规则
- 支持分类管理和层级结构
- 字典值支持排序和启用/禁用
- 被引用的字典值限制删除
- 业务表单自动关联字典

---

## 4. 服务集成规范

### 4.1 API调用规范

#### 4.1.1 认证和授权
- 除登录接口外，所有API调用必须携带JWT Token
- Token格式：Authorization: Bearer {token}
- Token过期自动刷新机制

#### 4.1.2 数据格式
- 统一使用JSON格式
- 日期格式：YYYY-MM-DD HH:MM:SS
- 编码格式：UTF-8

#### 4.1.3 错误处理
- 标准HTTP状态码
- 统一错误响应格式
- 详细错误信息和错误码

### 4.2 服务调用示例

#### 4.2.1 用户登录和权限校验
```javascript
// 用户登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'password123',
    captcha: 'ABCD'
  })
});

// 权限校验
const permissionCheck = await fetch('/api/permissions/check', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    resource: 'sales_order',
    action: 'create'
  })
});
```

#### 4.2.2 组织架构和数据字典调用
```javascript
// 获取组织架构
const orgTree = await fetch('/api/organizations/tree', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

// 获取数据字典
const dictData = await fetch('/api/dictionaries/customer_type', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

---

## 5. 性能和可靠性要求

### 5.1 性能指标
- **登录响应时间**: ≤ 500ms
- **权限校验响应时间**: ≤ 200ms
- **API响应时间**: ≤ 1秒
- **并发处理能力**: ≥ 1000请求/秒
- **服务可用性**: ≥ 99.9%

### 5.2 可靠性保障
- **会话管理**: 分布式会话存储
- **权限缓存**: Redis缓存权限数据
- **故障恢复**: 自动故障检测和恢复
- **降级处理**: 服务异常时的降级策略

### 5.3 安全保障
- **密码安全**: BCrypt加密存储
- **Token安全**: JWT签名验证
- **防暴力破解**: 登录失败锁定机制
- **审计日志**: 完整的操作审计记录

---

## 6. 数据模型设计

### 6.1 核心数据实体

#### 6.1.1 用户数据
```sql
CREATE TABLE users (
    user_id VARCHAR(20) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    employee_id VARCHAR(20) UNIQUE,
    name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    department_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW(),
    updated_date DATETIME DEFAULT NOW()
);
```

#### 6.1.2 角色权限数据
```sql
CREATE TABLE roles (
    role_id VARCHAR(20) PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_code VARCHAR(20) UNIQUE NOT NULL,
    description VARCHAR(200),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW()
);

CREATE TABLE permissions (
    permission_id VARCHAR(20) PRIMARY KEY,
    permission_name VARCHAR(50) NOT NULL,
    permission_code VARCHAR(50) UNIQUE NOT NULL,
    resource_type VARCHAR(20),
    action VARCHAR(20),
    parent_id VARCHAR(20),
    level INT DEFAULT 1
);
```

#### 6.1.3 组织架构数据
```sql
CREATE TABLE organizations (
    org_id VARCHAR(20) PRIMARY KEY,
    org_code VARCHAR(20) UNIQUE NOT NULL,
    org_name VARCHAR(100) NOT NULL,
    parent_id VARCHAR(20),
    level INT DEFAULT 1,
    sort_order INT DEFAULT 0,
    manager_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW()
);
```

---

## 7. 监控和运维

### 7.1 服务监控
- **登录监控**: 登录成功率、失败率、响应时间
- **权限监控**: 权限校验次数、失败次数
- **会话监控**: 在线用户数、会话时长
- **API监控**: 接口调用量、响应时间、错误率

### 7.2 安全监控
- **异常登录**: 异常IP、异常时间登录告警
- **权限异常**: 权限提升、异常访问告警
- **暴力破解**: 密码尝试次数监控
- **数据访问**: 敏感数据访问监控

### 7.3 日志管理
- **访问日志**: 用户登录、API调用日志
- **操作日志**: 权限变更、组织调整日志
- **错误日志**: 系统异常、错误日志
- **审计日志**: 关键操作审计记录

---

## 8. 部署和扩展

### 8.1 部署架构
- **负载均衡**: Nginx负载均衡
- **应用集群**: 多实例部署
- **数据库**: 主从复制，读写分离
- **缓存**: Redis集群

### 8.2 扩展性设计
- **水平扩展**: 支持多实例部署
- **垂直扩展**: 支持资源动态调整
- **微服务**: 支持服务拆分和独立部署
- **容器化**: 支持Docker容器部署

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 架构优化版本，基础管理子系统重构为共享服务 | 产品团队 |

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 服务化架构 ✅ 统一基础管理 ✅ 安全可靠 ✅
