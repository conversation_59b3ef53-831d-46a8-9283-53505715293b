# V2.1功能边界定义文档

> **版本**: 2.1  
> **状态**: 架构优化版  
> **撰写人**: 产品团队  
> **日期**: 2025-08-02  
> **目标**: 解决V2.0功能重复和职责模糊问题，明确V2.1各模块的核心职责和边界  
> **术语表**: 参考 [全局术语表](./Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](./Business_Rules.md)

---

## 1. 边界定义原则

### 1.1 核心原则
- **单一职责原则**: 每个模块只负责一个核心业务领域，避免职责重叠
- **数据归属原则**: 明确数据的主管模块，避免数据重复维护和不一致
- **服务调用原则**: 模块间通过标准API调用，不直接访问其他模块数据
- **边界清晰原则**: 模块边界清晰明确，职责不重叠不遗漏

### 1.2 边界类型
- **数据边界**: 明确数据的归属和访问权限
- **功能边界**: 明确功能的归属和调用关系
- **流程边界**: 明确业务流程的起止点和交接点
- **接口边界**: 明确模块间的接口规范和调用方式

---

## 2. V2.0边界问题分析

### 2.1 主要边界冲突

| 冲突类型 | V2.0问题描述 | 影响程度 | V2.1解决方案 |
|----------|-------------|----------|-------------|
| **产品配置重复** | 销售管理和PDM都有产品配置功能 | 高 | PDM作为唯一产品数据源，销售调用PDM服务 |
| **客户管理割裂** | 销售管理和CRM分离管理客户 | 高 | 整合为销售管理模块，统一客户管理 |
| **库存管理分散** | WMS和PMS分离导致数据不一致 | 高 | 整合为库存管理模块，一体化管理 |
| **成本核算不完整** | 财务和人事分离导致人工成本不准确 | 中 | 整合为财务管理模块，自动成本归集 |
| **生产设备分离** | MES和设备管理分离影响协调 | 中 | 整合为生产管理模块，统一管理 |

### 2.2 边界模糊区域
- **订单配置**: 销售订单中的产品配置与PDM产品配置的边界
- **库存查询**: 各模块对库存数据的查询需求和权限边界
- **成本分摊**: 人工成本在财务和生产模块间的分摊边界
- **质量数据**: 质量数据在生产、库存、销售模块间的共享边界

---

## 3. V2.1模块职责矩阵

### 3.1 核心业务模块职责

| 模块名称 | 核心职责 | 数据归属 | 服务提供 | 服务消费 |
|----------|----------|----------|----------|----------|
| **销售管理模块** | 客户全生命周期管理<br/>销售机会管理<br/>订单管理<br/>客户服务 | 客户主数据<br/>销售机会数据<br/>销售订单数据<br/>客户服务数据 | 客户信息查询<br/>订单状态查询<br/>销售数据分析 | PDM产品配置<br/>库存可用量查询<br/>基础权限服务 |
| **生产管理模块** | 生产计划管理<br/>生产执行控制<br/>设备管理<br/>工艺执行 | 生产订单数据<br/>工时数据<br/>设备数据<br/>生产过程数据 | 生产进度查询<br/>设备状态查询<br/>工时数据提供 | PDM工艺路线<br/>库存物料查询<br/>基础权限服务 |
| **库存管理模块** | 采购计划管理<br/>库存控制<br/>仓储作业<br/>供应商管理 | 库存数据<br/>采购订单数据<br/>供应商数据<br/>仓储数据 | 库存查询服务<br/>采购状态查询<br/>供应商信息 | PDM物料信息<br/>生产需求数据<br/>基础权限服务 |
| **财务管理模块** | 财务核算<br/>成本管理<br/>人力成本<br/>财务分析 | 财务凭证数据<br/>成本数据<br/>薪酬数据<br/>员工数据 | 成本查询服务<br/>财务数据查询<br/>薪酬查询 | 业务单据数据<br/>工时数据<br/>基础权限服务 |

### 3.2 共享服务职责

| 服务名称 | 核心职责 | 数据归属 | 服务提供 | 服务消费 |
|----------|----------|----------|----------|----------|
| **PDM服务** | 产品数据管理<br/>BOM管理<br/>工艺管理<br/>变体管理 | 产品主数据<br/>BOM数据<br/>工艺数据<br/>变体数据 | 产品配置服务<br/>BOM查询服务<br/>工艺路线服务 | 基础权限服务<br/>数据字典服务 |
| **基础服务** | 用户认证<br/>权限管理<br/>组织管理<br/>数据字典 | 用户数据<br/>权限数据<br/>组织数据<br/>字典数据 | 认证服务<br/>权限验证<br/>组织查询<br/>字典查询 | 无 |

### 3.3 支持业务模块职责

| 模块名称 | 核心职责 | 数据归属 | 服务提供 | 服务消费 |
|----------|----------|----------|----------|----------|
| **项目管理模块** | 大型项目管理<br/>项目进度控制<br/>资源协调 | 项目数据<br/>项目进度数据<br/>资源分配数据 | 项目信息查询<br/>进度状态查询 | 销售订单数据<br/>生产进度数据<br/>基础权限服务 |
| **质量管理模块** | 质量标准管理<br/>质量检验<br/>质量分析 | 质量标准数据<br/>检验数据<br/>质量分析数据 | 质量标准查询<br/>检验结果查询 | PDM产品标准<br/>生产过程数据<br/>基础权限服务 |
| **数据中心模块** | 数据分析<br/>报表生成<br/>决策支持 | 分析模型数据<br/>报表配置数据 | 分析报表服务<br/>数据可视化 | 各模块业务数据<br/>基础权限服务 |

---

## 4. 数据归属与访问矩阵

### 4.1 核心数据归属

| 数据类型 | 主管模块 | 读取权限 | 写入权限 | 访问方式 |
|----------|----------|----------|----------|----------|
| **客户主数据** | 销售管理模块 | 销售、财务、项目 | 销售管理模块 | API调用 |
| **产品主数据** | PDM服务 | 所有业务模块 | PDM服务 | API调用 |
| **库存数据** | 库存管理模块 | 销售、生产、财务 | 库存管理模块 | API调用 |
| **生产数据** | 生产管理模块 | 销售、库存、财务、质量 | 生产管理模块 | API调用 |
| **财务数据** | 财务管理模块 | 财务、数据中心 | 财务管理模块 | API调用 |
| **用户权限数据** | 基础服务 | 所有模块 | 基础服务 | API调用 |

### 4.2 数据访问规则
- **主管模块**: 对数据拥有完全的读写权限
- **消费模块**: 通过API获取数据，不直接访问数据库
- **数据同步**: 主管模块负责数据的一致性和完整性
- **缓存策略**: 消费模块可以缓存数据，但需要处理数据更新

---

## 5. 服务调用关系定义

### 5.1 服务调用层次

```mermaid
graph TB
    subgraph "表现层"
        UI[用户界面]
    end
    
    subgraph "业务层"
        SALES[销售管理模块]
        PROD[生产管理模块]
        INV[库存管理模块]
        FIN[财务管理模块]
        PROJ[项目管理模块]
        QM[质量管理模块]
        DC[数据中心模块]
    end
    
    subgraph "服务层"
        PDM[PDM服务]
        BASIC[基础服务]
    end
    
    subgraph "数据层"
        DB[(数据库)]
    end
    
    UI --> SALES
    UI --> PROD
    UI --> INV
    UI --> FIN
    
    SALES --> PDM
    SALES --> BASIC
    PROD --> PDM
    PROD --> BASIC
    INV --> PDM
    INV --> BASIC
    FIN --> BASIC
    
    PDM --> DB
    BASIC --> DB
    SALES --> DB
    PROD --> DB
    INV --> DB
    FIN --> DB
```

### 5.2 关键服务接口

| 服务提供方 | 服务接口 | 服务消费方 | 调用场景 |
|------------|----------|------------|----------|
| **PDM服务** | `/api/pdm/products/{id}/config` | 销售管理模块 | 销售订单产品配置 |
| **PDM服务** | `/api/pdm/boms/{id}/materials` | 库存管理模块 | MRP需求计算 |
| **PDM服务** | `/api/pdm/routes/{id}/operations` | 生产管理模块 | 生产工艺执行 |
| **库存管理模块** | `/api/inventory/stocks/available` | 销售管理模块 | 订单可承诺量查询 |
| **生产管理模块** | `/api/production/orders/{id}/progress` | 销售管理模块 | 订单生产进度查询 |
| **生产管理模块** | `/api/production/workhours/summary` | 财务管理模块 | 人工成本核算 |
| **基础服务** | `/api/auth/verify` | 所有模块 | 用户身份验证 |
| **基础服务** | `/api/permissions/check` | 所有模块 | 权限验证 |

---

## 6. 边界冲突解决方案

### 6.1 产品配置边界解决

**V2.0问题**: 销售管理和PDM都有产品配置功能，导致配置不一致

**V2.1解决方案**:
- **PDM服务**: 负责产品主数据和标准配置规则
- **销售管理模块**: 负责销售订单的个性化配置
- **调用关系**: 销售配置基于PDM标准配置，通过API获取配置规则
- **数据流向**: PDM → 销售配置 → 销售订单

### 6.2 库存数据边界解决

**V2.0问题**: WMS和PMS分离导致库存数据不一致

**V2.1解决方案**:
- **库存管理模块**: 统一管理库存数据，整合采购和仓储
- **其他模块**: 通过API查询库存数据，不维护库存副本
- **数据同步**: 库存变动实时更新，其他模块获取最新数据
- **缓存策略**: 允许短期缓存，但需要处理数据失效

### 6.3 成本数据边界解决

**V2.0问题**: 财务和人事分离导致人工成本核算不准确

**V2.1解决方案**:
- **财务管理模块**: 统一管理所有成本数据，包括人工成本
- **生产管理模块**: 提供工时基础数据，不进行成本核算
- **数据流向**: 生产工时 → 财务成本核算 → 成本分析
- **核算规则**: 财务模块负责成本核算规则和计算逻辑

---

## 7. 边界验证规则

### 7.1 设计时验证
- **职责唯一性**: 每个功能只能归属一个模块
- **数据归属性**: 每类数据只能有一个主管模块
- **接口完整性**: 模块间调用必须通过定义的API接口
- **依赖合理性**: 避免循环依赖和过度依赖

### 7.2 运行时验证
- **数据一致性**: 定期检查数据的一致性和完整性
- **接口可用性**: 监控API接口的可用性和响应时间
- **权限有效性**: 验证模块间调用的权限控制
- **性能指标**: 监控跨模块调用的性能影响

### 7.3 边界违规处理
- **设计违规**: 通过代码审查和架构评审发现和纠正
- **运行违规**: 通过监控告警和日志分析发现问题
- **数据违规**: 通过数据质量检查发现数据不一致
- **性能违规**: 通过性能监控发现边界调用性能问题

---

## 8. 实施指导

### 8.1 开发阶段指导
1. **模块开发**: 严格按照边界定义开发各模块功能
2. **接口开发**: 优先开发共享服务和关键接口
3. **集成测试**: 重点测试模块间的边界调用
4. **边界验证**: 在每个开发阶段验证边界合规性

### 8.2 部署阶段指导
1. **服务部署**: 优先部署基础服务和PDM服务
2. **数据迁移**: 按照数据归属规则迁移V2.0数据
3. **接口配置**: 配置模块间的服务调用关系
4. **权限配置**: 配置基于边界定义的权限控制

### 8.3 运维阶段指导
1. **监控配置**: 配置边界相关的监控指标
2. **告警设置**: 设置边界违规的告警机制
3. **性能优化**: 优化跨模块调用的性能
4. **边界维护**: 定期评估和优化边界定义

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 初始版本，定义V2.1功能边界 | 产品团队 |

---

**文档状态**: 边界定义完成 ✅  
**应用原则**: 单一职责 ✅ 数据归属 ✅ 服务调用 ✅ 边界清晰 ✅  
**解决问题**: 功能重复 ✅ 职责模糊 ✅ 数据不一致 ✅ 边界冲突 ✅
