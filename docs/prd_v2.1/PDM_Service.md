# PRD-S1: PDM服务 产品需求文档 V2.1

> **版本**: 2.1  
> **状态**: 架构优化版  
> **撰写人**: 产品团队  
> **日期**: 2025-08-02  
> **优化说明**: 将V2.0的PDM子系统重构为共享服务，为所有业务模块提供产品数据管理能力  
> **术语表**: 参考 [全局术语表](./Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](./Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 V2.1优化目标
**将V2.0的PDM子系统重构为共享服务，解决各业务模块重复实现产品数据管理功能的问题，实现产品数据的统一管理和服务化提供。**

### 1.2 价值主张
构建企业级产品数据管理服务中心，通过服务化架构为销售、生产、库存、财务等业务模块提供统一的产品数据管理能力，实现数据一致性和业务协同。

### 1.3 商业价值量化
- **开发效率提升**: 服务化设计使各模块开发效率提升50%，避免重复开发
- **数据一致性**: 统一的产品数据管理使数据一致性从85%提升至99%
- **维护成本降低**: 集中式服务管理使维护成本降低60%
- **业务响应速度**: 标准化API使业务响应速度提升40%

---

## 2. 服务架构设计

### 2.1 服务定位
**PDM服务作为共享服务层的核心组件，为所有业务模块提供产品数据管理能力**

```mermaid
graph TB
    subgraph "业务模块层"
        A[销售管理模块]
        B[生产管理模块]
        C[库存管理模块]
        D[财务管理模块]
        E[项目管理模块]
        F[质量管理模块]
    end
    
    subgraph "共享服务层"
        G[PDM服务]
        H[基础服务]
    end
    
    subgraph "数据层"
        I[产品数据库]
        J[文档存储]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> I
    G --> J
```

### 2.2 服务能力矩阵

| 服务能力 | 服务接口 | 调用模块 | 说明 |
|----------|----------|----------|------|
| 物料主数据管理 | /api/pdm/materials | 所有模块 | 统一的物料信息管理 |
| 参数化BOM设计 | /api/pdm/boms | 销售、生产模块 | 动态BOM生成和配置 |
| BOM固化服务 | /api/pdm/boms/solidify | 销售、生产模块 | 订单BOM固化处理 |
| 工艺路线管理 | /api/pdm/routes | 生产、质量模块 | 工艺流程定义和管理 |
| 产品配置服务 | /api/pdm/configure | 销售模块 | 参数化产品配置 |
| 技术文档管理 | /api/pdm/documents | 所有模块 | 技术文档存储和关联 |
| 版本管理服务 | /api/pdm/versions | 所有模块 | 数据版本控制 |

---

## 3. 核心服务功能

### 3.1 物料主数据管理服务

#### 3.1.1 服务描述
为所有业务模块提供统一的物料主数据管理能力，包括物料创建、分类、变体管理等。

#### 3.1.2 核心API接口

**物料查询服务**
```
GET /api/pdm/materials
参数: category, type, status, keyword
返回: 物料列表和详细信息
```

**物料创建服务**
```
POST /api/pdm/materials
参数: 物料基本信息、分类、属性
返回: 创建结果和物料编码
```

**物料变体管理**
```
GET/POST /api/pdm/materials/{id}/variants
功能: 管理物料的不同规格变体
```

#### 3.1.3 业务规则
- 物料编码自动生成，格式：MAT-XXXXXXXX
- 支持玻璃行业特殊属性：长、宽、厚度、颜色、品级
- 变体物料与主物料关联管理
- 物料状态控制：ACTIVE/INACTIVE

### 3.2 参数化BOM设计服务

#### 3.2.1 服务描述
提供参数化BOM设计和动态计算能力，支持复杂产品的快速配置和成本计算。

#### 3.2.2 核心API接口

**BOM模板管理**
```
GET/POST/PUT /api/pdm/bom-templates
功能: BOM模板的创建、修改、查询
```

**参数化计算服务**
```
POST /api/pdm/boms/calculate
参数: 模板ID、客户参数
返回: 计算后的BOM结构和成本
```

**BOM版本管理**
```
GET/POST /api/pdm/boms/{id}/versions
功能: BOM版本创建、对比、审批
```

#### 3.2.3 业务规则
- 支持多层级BOM结构设计
- 参数化公式计算物料用量
- BOM版本状态控制：草稿/激活/归档
- 支持物料BOM和工艺BOM分离

### 3.3 BOM固化服务

#### 3.3.1 服务描述
将参数化BOM转换为固化的生产BOM，确保生产数据的稳定性和可追溯性。

#### 3.3.2 核心API接口

**BOM固化处理**
```
POST /api/pdm/boms/solidify
参数: 订单ID、BOM快照、调整参数
返回: 固化BOM和传递状态
```

**固化BOM查询**
```
GET /api/pdm/boms/solidified/{order_id}
返回: 固化后的完整BOM数据
```

#### 3.3.3 业务规则
- 销售订单确认后自动触发固化流程
- 支持工艺工程师微调：物料替换、用量调整
- 固化后BOM与原模板解除关联
- 固化BOM自动传递给生产和采购系统

### 3.4 工艺路线管理服务

#### 3.4.1 服务描述
提供工艺路线设计、工序管理、外协管理的统一服务能力。

#### 3.4.2 核心API接口

**工艺路线设计**
```
GET/POST/PUT /api/pdm/routes
功能: 工艺路线的设计、修改、查询
```

**工序管理服务**
```
GET/POST/PUT /api/pdm/operations
功能: 标准工序的定义和管理
```

**外协工序管理**
```
GET/POST/PUT /api/pdm/operations/{id}/outsourcing
功能: 外协工序的配置和管理
```

#### 3.4.3 业务规则
- 工艺路线与BOM关联设计
- 支持参数化工艺路线配置
- 内制外协灵活切换
- 工艺参数与工序关联

---

## 4. 服务集成规范

### 4.1 API调用规范

#### 4.1.1 认证和授权
- 所有API调用必须通过基础服务的认证
- 使用JWT Token进行身份验证
- 基于RBAC的权限控制

#### 4.1.2 数据格式
- 统一使用JSON格式
- 日期格式：YYYY-MM-DD HH:MM:SS
- 编码格式：UTF-8

#### 4.1.3 错误处理
- 标准HTTP状态码
- 统一错误响应格式
- 详细错误信息和错误码

### 4.2 服务调用示例

#### 4.2.1 销售模块调用产品配置服务
```javascript
// 销售模块调用PDM服务进行产品配置
const response = await fetch('/api/pdm/configure', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    template_id: 'TEMP-001',
    parameters: {
      length: 2000,
      width: 1500,
      thickness: 6,
      color: '透明'
    }
  })
});
```

#### 4.2.2 生产模块调用BOM固化服务
```javascript
// 生产模块调用PDM服务获取固化BOM
const bomData = await fetch(`/api/pdm/boms/solidified/${orderId}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

---

## 5. 性能和可靠性要求

### 5.1 性能指标
- **API响应时间**: ≤ 2秒
- **并发处理能力**: ≥ 500请求/秒
- **数据查询性能**: 复杂BOM计算 ≤ 5秒
- **服务可用性**: ≥ 99.9%

### 5.2 可靠性保障
- **数据备份**: 实时数据备份机制
- **故障恢复**: 自动故障检测和恢复
- **降级处理**: 服务异常时的降级策略
- **监控告警**: 7×24小时服务监控

### 5.3 扩展性设计
- **水平扩展**: 支持多实例部署
- **缓存机制**: Redis缓存热点数据
- **数据库优化**: 读写分离和分库分表
- **微服务架构**: 支持独立部署和升级

---

## 6. 数据模型设计

### 6.1 核心数据实体

#### 6.1.1 物料主数据
```sql
CREATE TABLE materials (
    material_code VARCHAR(12) PRIMARY KEY,
    material_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(20) NOT NULL,
    specification VARCHAR(200),
    unit_of_measure VARCHAR(10) NOT NULL,
    category_id VARCHAR(20),
    status VARCHAR(10) DEFAULT 'ACTIVE',
    created_date DATETIME DEFAULT NOW(),
    updated_date DATETIME DEFAULT NOW()
);
```

#### 6.1.2 BOM结构数据
```sql
CREATE TABLE bom_structures (
    bom_id VARCHAR(20) PRIMARY KEY,
    parent_material VARCHAR(12),
    child_material VARCHAR(12),
    quantity DECIMAL(15,3) NOT NULL,
    unit VARCHAR(10) NOT NULL,
    level INT NOT NULL,
    sequence INT,
    created_date DATETIME DEFAULT NOW()
);
```

### 6.2 数据关系设计
- 物料主数据与BOM结构的一对多关系
- BOM版本与BOM结构的关联关系
- 工艺路线与BOM的关联关系
- 技术文档与物料/BOM的关联关系

---

## 7. 安全和权限控制

### 7.1 数据安全
- **数据加密**: 敏感数据加密存储
- **访问控制**: 基于角色的数据访问控制
- **审计日志**: 完整的操作审计记录
- **数据脱敏**: 非生产环境数据脱敏

### 7.2 API安全
- **身份认证**: JWT Token认证机制
- **权限校验**: 细粒度权限控制
- **请求限流**: API调用频率限制
- **SQL注入防护**: 参数化查询防护

---

## 8. 监控和运维

### 8.1 服务监控
- **性能监控**: API响应时间、吞吐量监控
- **错误监控**: 错误率、异常监控
- **资源监控**: CPU、内存、磁盘使用率
- **业务监控**: 关键业务指标监控

### 8.2 日志管理
- **访问日志**: API调用日志记录
- **错误日志**: 异常和错误日志
- **业务日志**: 关键业务操作日志
- **审计日志**: 数据变更审计记录

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 架构优化版本，PDM子系统重构为共享服务 | 产品团队 |

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 服务化架构 ✅ 统一数据管理 ✅ 模块解耦 ✅
