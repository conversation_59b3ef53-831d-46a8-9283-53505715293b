# PRD-M2: 生产管理模块 产品需求文档 V2.1

> **版本**: 2.1  
> **状态**: 架构优化版  
> **撰写人**: 产品团队  
> **日期**: 2025-08-02  
> **优化说明**: 整合V2.0的生产管理子系统(MES)和设备管理子系统，形成统一的生产管理模块  
> **术语表**: 参考 [全局术语表](./Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](./Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 V2.1优化目标
**整合V2.0的生产管理子系统(MES)和设备管理子系统，解决生产执行与设备管理分离导致的数据割裂问题，实现生产与设备的一体化管理。**

### 1.2 价值主张
构建智能制造执行平台，通过生产执行与设备管理的深度融合，实现从订单分解、智能排程、设备调度到质量追溯的全流程数字化管理，提升生产效率和设备利用率。

### 1.3 商业价值量化
- **生产效率提升**: 一体化管理使生产效率提升40%，设备利用率提升30%
- **交期准确率提升**: 从70%提升至95%，客户满意度显著改善
- **设备故障率降低**: 预防性维护使设备故障率降低50%
- **质量追溯能力**: 实现单片级别的全流程追溯，满足高端产品认证要求

---

## 2. 模块架构设计

### 2.1 整合架构
**生产管理模块 = 生产管理子系统(MES) + 设备管理子系统**

```mermaid
graph TB
    subgraph "生产管理模块 V2.1"
        subgraph "生产执行管理"
            A[订单分解与任务管理]
            B[APS智能排程]
            C[生产计划下发]
            D[工位任务执行]
            E[扫码报工管理]
        end
        
        subgraph "设备管理"
            F[设备档案管理]
            G[设备状态监控]
            H[设备维护管理]
            I[设备性能分析]
        end
        
        subgraph "质量追溯"
            J[产品追溯管理]
            K[质量数据采集]
            L[生产监控预警]
        end
        
        subgraph "共享服务调用"
            M[PDM服务]
            N[基础服务]
        end
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    F --> B
    G --> L
    H --> F
    E --> J
    A --> M
    F --> N
```

### 2.2 功能整合映射

| V2.1功能模块 | 整合的V2.0功能 | 整合优势 |
|-------------|---------------|----------|
| **智能生产调度** | MES订单分解 + MES排程 + 设备调度 | 生产与设备统一调度，提升资源利用率 |
| **设备生产协同** | MES工位执行 + 设备状态监控 | 生产任务与设备状态实时协同 |
| **一体化维护** | 设备维护 + 生产计划调整 | 维护计划与生产计划统一优化 |
| **全程质量追溯** | MES追溯 + 设备数据采集 | 生产过程与设备参数完整追溯 |

---

## 3. 核心功能设计

### 3.1 智能生产调度

#### 3.1.1 功能描述
整合订单分解、APS排程和设备调度，实现考虑设备状态和能力的智能生产调度。

#### 3.1.2 核心功能
- **订单自动分解**: 销售订单自动分解为工序任务，支持变体物料需求分析
- **APS智能排程**: 考虑设备能力、状态、维护计划的智能排程
- **设备资源调度**: 设备与任务的最优匹配，设备负载均衡
- **计划动态调整**: 基于设备状态变化的计划实时调整

#### 3.1.3 业务规则
- 排程必须考虑设备维护计划，避免冲突
- 设备故障时自动重新排程，任务转移到备用设备
- 优先级高的订单优先分配最优设备资源
- 支持紧急插单和计划调整

### 3.2 设备生产协同

#### 3.2.1 功能描述
实现生产任务执行与设备状态监控的实时协同，确保生产过程的高效执行。

#### 3.2.2 核心功能
- **工位任务执行**: 数字化SOP指导，设备参数自动设置
- **设备状态监控**: 实时监控设备运行状态、参数、报警
- **自动数据采集**: 设备参数自动采集，与生产任务关联
- **异常处理**: Andon报警系统，设备异常快速响应

#### 3.2.3 业务规则
- 任务开始前自动检查设备状态和参数
- 设备异常时自动暂停相关任务
- 关键工序参数超限时自动报警
- 设备维护期间禁止分配新任务

### 3.3 一体化维护管理

#### 3.3.1 功能描述
整合设备维护计划与生产计划，实现预防性维护与生产效率的平衡。

#### 3.3.2 核心功能
- **预防性维护**: 基于设备运行时间、次数的维护计划
- **维护计划优化**: 维护计划与生产计划的协调优化
- **维护执行管理**: 维护任务分派、执行跟踪、完成确认
- **备件库存管理**: 维护备件需求预测、库存管理

#### 3.3.3 业务规则
- 维护计划必须与生产计划协调，避免影响交期
- 设备故障维修优先级高于预防性维护
- 维护完成后必须进行设备状态确认
- 关键设备必须有备用设备或维护窗口

### 3.4 全程质量追溯

#### 3.4.1 功能描述
整合生产过程数据和设备参数数据，实现产品全生命周期的质量追溯。

#### 3.4.2 核心功能
- **产品身份管理**: 唯一身份码(UID)生成和管理
- **过程数据采集**: 工序参数、设备参数、质检结果采集
- **追溯查询**: 基于产品UID的完整追溯查询
- **质量分析**: 质量问题根因分析、趋势分析

#### 3.4.3 业务规则
- 每个产品必须有唯一的身份标识
- 关键工序必须记录完整的过程参数
- 设备参数异常时必须标记相关产品
- 质量问题必须能追溯到具体设备和操作员

---

## 4. 服务集成设计

### 4.1 PDM服务集成

#### 4.1.1 工艺路线获取
```javascript
// 获取产品工艺路线用于任务分解
const processRoute = await fetch('/api/pdm/routes/product/' + productId, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

#### 4.1.2 BOM固化状态同步
```javascript
// 监听BOM固化完成事件
const bomStatus = await fetch('/api/pdm/boms/solidified/' + orderId, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

### 4.2 基础服务集成

#### 4.2.1 用户权限控制
- 生产计划员：制定生产计划、设备调度
- 车间操作员：执行生产任务、设备操作
- 设备维护员：设备维护、故障处理
- 质量工程师：质量检验、追溯分析

#### 4.2.2 数据字典集成
- 设备状态：运行中/空闲/停机/维修中/报废
- 任务状态：待排程/已排程/执行中/已完成/异常
- 维护类型：预防性维护/故障维修/改造升级

---

## 5. 核心业务流程

### 5.1 订单到生产执行流程

```mermaid
sequenceDiagram
    participant ERP as ERP系统
    participant OM as 订单管理
    participant APS as 智能排程
    participant EM as 设备管理
    participant WS as 工位执行
    participant QT as 质量追溯
    
    ERP->>OM: 同步销售订单
    OM->>OM: 订单分解为任务
    OM->>APS: 提交待排程任务
    APS->>EM: 查询设备状态
    EM-->>APS: 返回设备可用性
    APS->>APS: 生成最优排程
    APS->>WS: 下发生产任务
    WS->>EM: 设置设备参数
    WS->>QT: 记录生产数据
    WS->>OM: 报告任务完成
```

### 5.2 设备维护与生产协调流程

```mermaid
sequenceDiagram
    participant EM as 设备管理
    participant MM as 维护管理
    participant APS as 智能排程
    participant WS as 工位执行
    
    EM->>MM: 触发维护计划
    MM->>APS: 申请维护时间窗口
    APS->>APS: 调整生产计划
    APS-->>MM: 确认维护窗口
    MM->>EM: 执行设备维护
    EM->>EM: 更新设备状态
    EM->>APS: 通知维护完成
    APS->>WS: 恢复设备生产任务
```

---

## 6. 数据模型设计

### 6.1 生产任务数据模型
```sql
CREATE TABLE production_tasks (
    task_id VARCHAR(20) PRIMARY KEY,
    order_id VARCHAR(20) NOT NULL,
    operation_code VARCHAR(20) NOT NULL,
    equipment_id VARCHAR(20),
    planned_start_time DATETIME,
    planned_end_time DATETIME,
    actual_start_time DATETIME,
    actual_end_time DATETIME,
    status VARCHAR(20) DEFAULT 'PENDING',
    priority INT DEFAULT 5,
    created_date DATETIME DEFAULT NOW()
);
```

### 6.2 设备状态数据模型
```sql
CREATE TABLE equipment_status (
    equipment_id VARCHAR(20) PRIMARY KEY,
    current_status VARCHAR(20) NOT NULL,
    current_task_id VARCHAR(20),
    utilization_rate DECIMAL(5,2),
    last_maintenance_date DATETIME,
    next_maintenance_date DATETIME,
    total_runtime_hours DECIMAL(10,2),
    status_updated_time DATETIME DEFAULT NOW()
);
```

### 6.3 质量追溯数据模型
```sql
CREATE TABLE product_traceability (
    trace_id VARCHAR(30) PRIMARY KEY,
    product_uid VARCHAR(50) UNIQUE NOT NULL,
    order_id VARCHAR(20) NOT NULL,
    operation_code VARCHAR(20) NOT NULL,
    equipment_id VARCHAR(20),
    operator_id VARCHAR(20),
    process_parameters JSON,
    quality_result VARCHAR(20),
    created_time DATETIME DEFAULT NOW()
);
```

---

## 7. 性能和可靠性要求

### 7.1 性能指标
- **排程计算时间**: 50个订单 ≤ 5分钟
- **设备状态更新**: 实时更新，延迟 ≤ 30秒
- **报工响应时间**: ≤ 30秒
- **追溯查询时间**: ≤ 30秒
- **系统可用性**: ≥ 99.5%

### 7.2 业务指标
- **订单分解准确率**: 100%
- **排程计算成功率**: ≥ 95%
- **设备利用率**: ≥ 85%
- **交期准确率**: ≥ 95%
- **质量追溯完整性**: 100%

---

## 8. 用户界面设计

### 8.1 生产调度中心
- **订单看板**: 显示待分解和已分解订单
- **排程甘特图**: 可视化生产计划和设备调度
- **设备状态监控**: 实时显示设备运行状态
- **异常处理**: 显示异常任务和处理状态

### 8.2 车间执行终端
- **任务队列**: 显示工位待执行任务
- **数字化SOP**: 显示作业指导书
- **设备控制**: 设备参数设置和监控
- **报工界面**: 简单快捷的报工操作

### 8.3 设备管理中心
- **设备档案**: 设备基本信息和技术参数
- **状态监控**: 实时设备状态和报警
- **维护计划**: 维护任务计划和执行
- **性能分析**: 设备利用率和效率分析

### 8.4 质量追溯系统
- **产品档案**: 产品身份信息和生产历史
- **追溯查询**: 基于UID的快速追溯
- **质量分析**: 质量趋势和问题分析
- **报告生成**: 追溯报告和质量报告

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 2.1 | 2025-08-02 | 架构优化版本，整合生产管理和设备管理子系统 | 产品团队 |

---

**文档状态**: 架构优化完成 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅  
**优化目标**: 生产设备一体化 ✅ 智能制造执行 ✅ 全程质量追溯 ✅
