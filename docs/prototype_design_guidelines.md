# 玻璃深加工行业ERP系统 - 原型开发设计规范

> **版本**: 1.0  
> **创建日期**: 2025-07-30  
> **最后更新**: 2025-07-30  
> **适用范围**: 玻璃深加工行业ERP系统所有子系统  
> **维护责任**: UE/UI设计团队

---

## 1. 设计哲学

### 1.1 核心设计理念
**专业、高效、准确、一致**

- **专业性**: 符合制造业用户的操作习惯和心理模型
- **高效性**: 减少操作步骤，提升工作效率，支持快速数据录入
- **准确性**: 通过防错设计确保数据准确性，减少人为错误
- **一致性**: 统一的交互模式和视觉语言，降低学习成本

### 1.2 用户体验原则
1. **任务导向**: 界面设计围绕用户的核心任务展开
2. **信息层次**: 重要信息优先显示，次要信息适当隐藏
3. **即时反馈**: 所有用户操作都要有明确的系统反馈
4. **容错设计**: 预防错误发生，错误发生时提供清晰的解决方案
5. **渐进式披露**: 复杂功能分步骤引导，避免信息过载

---

## 2. 布局与栅格系统

### 2.1 页面布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (60px)                      │
├─────────────────────────────────────────────────────────┤
│ 侧边栏 │                主内容区域                        │
│(240px) │                                                │
│        │  ┌─────────────────────────────────────────┐   │
│        │  │           页面标题区 (80px)              │   │
│        │  ├─────────────────────────────────────────┤   │
│        │  │                                         │   │
│        │  │            内容区域                      │   │
│        │  │                                         │   │
│        │  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### 2.2 栅格系统
- **基础栅格**: 24列栅格系统
- **间距单位**: 8px基础间距单位 (8px, 16px, 24px, 32px)
- **断点设置**:
  - 超大屏: ≥1920px
  - 大屏: ≥1440px  
  - 中屏: ≥1024px
  - 小屏: ≥768px
  - 移动端: <768px

### 2.3 响应式设计
- **桌面端优先**: 主要面向桌面端用户设计
- **移动端适配**: 关键功能支持移动端操作（如车间报工）
- **平板端支持**: 支持平板端的查询和审批操作

---

## 3. 色彩体系

### 3.1 主色调
- **主色**: #1890FF (蓝色) - 用于主要按钮、链接、选中状态
- **辅助色**: #52C41A (绿色) - 用于成功状态、确认操作
- **警告色**: #FAAD14 (橙色) - 用于警告提示、待处理状态
- **错误色**: #F5222D (红色) - 用于错误提示、危险操作
- **信息色**: #1890FF (蓝色) - 用于信息提示、帮助说明

### 3.2 中性色
- **标题色**: #262626 (深灰)
- **正文色**: #595959 (中灰)  
- **辅助文字**: #8C8C8C (浅灰)
- **禁用色**: #BFBFBF (更浅灰)
- **边框色**: #D9D9D9 (边框灰)
- **背景色**: #FAFAFA (背景灰)

### 3.3 业务状态色
- **草稿**: #8C8C8C (灰色)
- **待审核**: #FAAD14 (橙色)
- **已确认**: #1890FF (蓝色)
- **进行中**: #722ED1 (紫色)
- **已完成**: #52C41A (绿色)
- **已取消**: #F5222D (红色)

---

## 4. 字体规范

### 4.1 字体族
- **中文字体**: PingFang SC, Microsoft YaHei, 苹方, 微软雅黑
- **英文字体**: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto
- **数字字体**: Consolas, Monaco, monospace (用于数据展示)

### 4.2 字号规范
- **页面标题**: 24px / Bold
- **区块标题**: 20px / Medium  
- **小标题**: 16px / Medium
- **正文**: 14px / Regular
- **辅助文字**: 12px / Regular
- **说明文字**: 11px / Regular

### 4.3 行高规范
- **标题行高**: 1.2
- **正文行高**: 1.5
- **表格行高**: 1.4
- **按钮文字行高**: 1.0

---

## 5. 通用组件规范

### 5.1 按钮组件
#### 主要按钮 (Primary Button)
- **样式**: 蓝色背景 (#1890FF)，白色文字，圆角4px
- **尺寸**: 大(40px)、中(32px)、小(24px)
- **状态**: 默认、悬停、点击、禁用、加载中

#### 次要按钮 (Secondary Button)  
- **样式**: 白色背景，蓝色边框，蓝色文字
- **用途**: 取消、返回等次要操作

#### 危险按钮 (Danger Button)
- **样式**: 红色背景 (#F5222D)，白色文字
- **用途**: 删除、作废等危险操作

### 5.2 表单组件
#### 输入框 (Input)
- **默认状态**: 白色背景，灰色边框 (#D9D9D9)
- **聚焦状态**: 蓝色边框 (#1890FF)，蓝色阴影
- **错误状态**: 红色边框 (#F5222D)，红色错误提示
- **禁用状态**: 灰色背景 (#F5F5F5)

#### 下拉选择 (Select)
- **样式**: 与输入框保持一致
- **选项**: 悬停高亮，选中状态明确标识
- **搜索**: 支持输入搜索过滤

#### 日期选择 (DatePicker)
- **格式**: YYYY-MM-DD (标准格式)
- **范围选择**: 支持开始-结束日期范围选择
- **快捷选择**: 今天、昨天、本周、本月等快捷选项

### 5.3 数据展示组件
#### 表格 (Table)
- **表头**: 灰色背景 (#FAFAFA)，加粗文字
- **行高**: 48px (标准)，40px (紧凑)
- **斑马纹**: 奇偶行背景色区分
- **排序**: 支持点击表头排序，排序图标明确
- **分页**: 底部分页器，显示总数和当前页

#### 卡片 (Card)
- **样式**: 白色背景，浅灰边框，4px圆角
- **阴影**: 轻微阴影效果 (0 2px 8px rgba(0,0,0,0.1))
- **内边距**: 24px (标准)，16px (紧凑)

---

## 6. 交互动效规范

### 6.1 过渡动画
- **持续时间**: 0.3s (标准)，0.2s (快速)，0.5s (慢速)
- **缓动函数**: ease-out (标准)，ease-in-out (平滑)
- **触发时机**: 页面切换、模态框显示、状态变更

### 6.2 加载状态
- **页面加载**: 骨架屏 + 进度条
- **按钮加载**: 按钮内旋转图标 + 禁用状态
- **数据加载**: 表格/列表区域显示加载动画

### 6.3 反馈动效
- **成功操作**: 绿色对勾动画 + 成功提示
- **错误操作**: 红色感叹号 + 错误提示
- **警告提示**: 橙色感叹号 + 警告信息

---

## 7. 数据可视化规范

### 7.1 图表色彩
- **主色系**: #1890FF, #52C41A, #FAAD14, #F5222D, #722ED1
- **辅助色系**: #13C2C2, #EB2F96, #FA8C16, #A0D911, #2F54EB

### 7.2 图表类型
- **趋势图**: 折线图，用于展示时间序列数据
- **对比图**: 柱状图，用于展示分类数据对比
- **占比图**: 饼图/环形图，用于展示比例关系
- **分布图**: 散点图，用于展示数据分布

### 7.3 仪表盘设计
- **网格布局**: 12列网格系统
- **卡片组织**: 每个指标独立卡片展示
- **层次结构**: 重要指标大卡片，次要指标小卡片

---

## 8. 无障碍设计 (Accessibility)

### 8.1 色彩对比度
- **正文文字**: 对比度 ≥ 4.5:1
- **大字体**: 对比度 ≥ 3:1  
- **图标按钮**: 对比度 ≥ 3:1

### 8.2 键盘导航
- **Tab顺序**: 逻辑清晰的Tab键导航顺序
- **焦点指示**: 明确的焦点指示器
- **快捷键**: 常用功能支持快捷键操作

### 8.3 屏幕阅读器
- **语义化**: 使用语义化HTML标签
- **Alt文本**: 图片和图标提供替代文本
- **标签关联**: 表单控件与标签正确关联

---

## 9. 移动端适配规范

### 9.1 触控设计
- **最小触控区域**: 44px × 44px
- **按钮间距**: 最小8px间距
- **手势支持**: 滑动、长按等手势操作

### 9.2 布局适配
- **单列布局**: 移动端优先使用单列布局
- **折叠导航**: 侧边栏折叠为汉堡菜单
- **底部操作**: 主要操作按钮放置在底部

---

## 10. 设计规范维护

### 10.1 版本管理
- **版本号**: 采用语义化版本号 (Major.Minor.Patch)
- **变更记录**: 详细记录每次变更内容
- **向后兼容**: 确保设计变更的向后兼容性

### 10.2 组件库维护
- **设计组件**: 维护Figma/Sketch组件库
- **代码组件**: 与前端组件库保持同步
- **文档更新**: 及时更新设计文档和使用说明

---

