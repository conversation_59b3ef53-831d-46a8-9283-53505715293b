## 技术栈
- **框架**：Vue 3 (Composition API) + Vue Router
- **语言**：TypeScript (严格模式)
- **样式**：Tailwind CSS (Utility-First)
- **UI 库**：Shadcn Vue UI
- **状态管理**：Pinia
- **构建工具**：Vite
- **API 请求**：Alova
- **包管理器**：pnpm

## 项目架构
基于最佳实践的目录结构，确保代码的可维护性和扩展性：

```
src/
├── assets/                   # 静态资源
│   ├── images/               # 图片资源
│   ├── fonts/                # 字体文件
│   └── styles/               # 全局样式文件
├── docs/                     # 项目文档
├── ├── reference/            # 行业参考文档
│   ├── prd/                  # 产品需求文档
│   ├── dev/                  # 开发文档
│   └── design/               # 设计文档
├── components/               # 可复用组件
│   ├── ui/                   # Shadcn Vue 基础组件
│   ├── shared/               # 项目通用组件
│   ├── layout/               # 布局组件
│   └── feature/              # 功能特定组件
├── composables/              # Vue 组合式函数
│   ├── useAuth.ts            # 认证相关
│   ├── useApi.ts             # API 请求相关
│   └── useStorage.ts         # 存储相关
├── stores/                   # Pinia 状态管理
│   ├── auth.ts               # 用户认证状态
│   ├── user.ts               # 用户信息状态
│   └── app.ts                # 应用全局状态
├── router/                   # 路由配置
│   ├── index.ts              # 路由主配置
│   ├── guards.ts             # 路由守卫
│   └── routes.ts             # 路由定义
├── views/                    # 页面组件
│   ├── auth/                 # 认证相关页面
│   ├── dashboard/            # 仪表板页面
│   └── settings/             # 设置页面
├── api/                      # API 请求层
│   ├── client.ts             # API 客户端配置
│   ├── auth.ts               # 认证 API
│   └── user.ts               # 用户 API
├── lib/                      # 工具函数
│   ├── utils.ts              # 通用工具函数
│   ├── validations.ts        # 验证函数
│   └── cn.ts                 # Tailwind 类名合并
├── types/                    # TypeScript 类型
│   ├── api.types.ts          # API 相关类型
│   ├── user.types.ts         # 用户相关类型
│   └── global.types.ts       # 全局类型
├── constants/                # 常量定义
│   ├── api.ts                # API 常量
│   └── app.ts                # 应用常量
│
public/                       # 不经过构建的静态资源
├── favicon.ico
└── robots.txt
```

## 开发命令
- **启动开发服务器**：`pnpm dev`
- **构建生产版本**：`pnpm build`
- **预览生产版本**：`pnpm preview`
- **运行类型检查**：`pnpm type:check`

## 代码风格规范

### Vue 3 组件最佳实践
- **组合式 API**：优先使用 `<script setup>` 语法
- **组件命名**：使用 PascalCase，如 `UserProfileCard.vue`
- **Props 定义**：使用 `defineProps` 并提供完整的 TypeScript 类型
- **Emits 定义**：使用 `defineEmits` 明确声明事件
- **Composables**：将可复用逻辑抽离到组合式函数中
- **响应式数据**：优先使用 `ref()` 而非 `reactive()`

```vue
<!-- 推荐的组件结构 -->
<script setup lang="ts">
interface Props {
  user: User
  showActions?: boolean
}

interface Emits {
  (e: 'edit', user: User): void
  (e: 'delete', userId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

const emit = defineEmits<Emits>()

// 使用组合式函数
const { isLoading, error } = useApi()
</script>
```

### TypeScript 规范
- **严格模式**：启用所有严格模式选项
- **类型定义**：
  - 接口优先使用 `interface` 而非 `type`
  - 为所有函数参数和返回值提供类型注解
  - 禁止使用 `any` 类型，使用 `unknown` 或具体类型
- **文件命名**：
  - 组件文件：`UserProfile.vue`
  - 页面文件：`UserProfileView.vue`
  - 类型文件：`user.types.ts`
  - 组合式函数：`useUser.ts`

```typescript
// 推荐的类型定义
interface User {
  readonly id: string
  name: string
  email: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

interface UserListProps {
  users: User[]
  loading?: boolean
  onUserSelect?: (user: User) => void
}

// 推荐的组合式函数
export function useUser(userId: string) {
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const fetchUser = async (): Promise<void> => {
    // 实现逻辑...
  }

  return {
    user: readonly(user),
    loading: readonly(loading),
    error: readonly(error),
    fetchUser
  }
}
```

### Tailwind CSS 最佳实践
- **Utility-First**：只使用 Tailwind 工具类，避免自定义 CSS
- **设计系统**：使用 `@theme` 定义设计令牌
- **组件抽象**：将重复的样式组合抽象为 Vue 组件
- **响应式设计**：使用响应式前缀 `sm:` `md:` `lg:` `xl:` `2xl:`
- **类名顺序**：布局 → 间距 → 尺寸 → 颜色 → 状态
- **条件样式**：使用 `cn()` 函数合并类名

```vue
<template>
  <!-- 推荐的类名组织 -->
  <div :class="cn(
    'flex items-center justify-between', // 布局
    'p-4 gap-4', // 间距
    'w-full min-h-16', // 尺寸
    'bg-background border border-border rounded-lg', // 外观
    'hover:shadow-md transition-shadow', // 状态
    {
      'opacity-50': disabled,
      'border-destructive': hasError
    }
  )">
    <!-- 内容 -->
  </div>
</template>
```

### Pinia 状态管理规范
- **Store 组织**：按功能模块划分 Store
- **命名约定**：使用 `use[Name]Store` 格式
- **状态设计**：保持状态结构扁平，避免深层嵌套
- **Actions**：所有状态修改都通过 Actions 进行
- **组合式语法**：优先使用 Setup Store 语法

```typescript
// 推荐的 Store 结构
export const useUserStore = defineStore('user', () => {
  // State
  const user = ref<User | null>(null)
  const users = ref<User[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const activeUsers = computed(() => 
    users.value.filter(u => u.status === 'active')
  )

  // Actions
  async function fetchUser(id: string): Promise<void> {
    loading.value = true
    error.value = null
    
    try {
      const response = await userApi.getUser(id)
      user.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户失败'
    } finally {
      loading.value = false
    }
  }

  function $reset(): void {
    user.value = null
    users.value = []
    loading.value = false
    error.value = null
  }

  return {
    // State
    user: readonly(user),
    users: readonly(users),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    isAuthenticated,
    activeUsers,
    
    // Actions
    fetchUser,
    $reset
  }
})
```

## 测试规范
- **测试框架**：Vitest + Vue Test Utils
- **测试策略**：单元测试 + 组件测试 + E2E 测试
- **覆盖率要求**：核心业务逻辑 ≥ 80%
- **文件命名**：`ComponentName.test.ts` 或 `ComponentName.spec.ts`
- **测试位置**：与源文件同目录或 `__tests__` 文件夹

```typescript
// 组件测试示例
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import UserProfile from './UserProfile.vue'

describe('UserProfile', () => {
  it('应该正确显示用户信息', () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>'
        }
      },
      global: {
        plugins: [createTestingPinia()]
      }
    })

    expect(wrapper.text()).toContain('John Doe')
  })
})
```

## API 设计规范
- **RESTful 设计**：遵循 REST 风格的 API 设计
- **统一响应格式**：标准化所有 API 响应结构
- **错误处理**：统一的错误码和错误信息格式
- **请求拦截**：统一处理认证、重试、超时等

```typescript
/**
 * API 统一响应格式
 */
export interface ResponseModel<T = any> {
  code: number
  msg: string
  data: T
}

/**
 * API 分页响应格式
 */
export interface ResponseListModel<T = any> {
  code: number
  msg: string
  data: {
    items: T[]
    has_more: boolean
    total: number
  }
}

// API 客户端配置
const apiClient = createAlova({
  baseURL: '/api',
  statesHook: VueHook,
  requestAdapter: adapterFetch(),
  beforeRequest(method) {
    // 添加认证头
    const token = getToken()
    if (token) {
      method.config.headers.Authorization = `Bearer ${token}`
    }
  },
  responded: {
    onSuccess: async (response) => {
      const data = await response.json()
      if (!data.success) {
        throw new Error(data.message)
      }
      return data
    },
    onError: (error) => {
      // 统一错误处理
      handleApiError(error)
      throw error
    }
  }
})
```

## Git 工作流
- **分支策略**：GitFlow
  - `main`：生产环境分支
  - `develop`：开发环境分支
  - `feature/*`：功能分支
  - `hotfix/*`：紧急修复分支
  - `release/*`：发布分支
- **提交规范**：Conventional Commits
  - `feat:`：新功能
  - `fix:`：修复 bug
  - `docs:`：文档更新
  - `style:`：代码格式调整
  - `refactor:`：重构
  - `test:`：测试相关
  - `chore:`：构建工具或辅助工具变动

```bash
# 提交示例
git commit -m "feat(auth): 添加用户登录功能"
git commit -m "fix(api): 修复用户信息获取接口错误处理"
git commit -m "docs(readme): 更新项目文档"
```

## 性能优化指南
- **组件懒加载**：使用 `defineAsyncComponent` 和动态导入
- **路由懒加载**：页面级组件按需加载
- **图片优化**：使用现代图片格式，实现懒加载
- **Bundle 分析**：定期分析打包产物，优化包大小
- **缓存策略**：合理配置 HTTP 缓存和 Service Worker

```typescript
// 路由懒加载
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/views/dashboard/DashboardView.vue')
  }
]

// 组件懒加载
const UserProfile = defineAsyncComponent(
  () => import('@/components/user/UserProfile.vue')
)
```

## 安全规范
- **认证授权**：JWT Token + 刷新令牌机制
- **权限控制**：基于角色的访问控制 (RBAC)
- **输入验证**：所有用户输入都需要验证和清理
- **XSS 防护**：使用内容安全策略 (CSP)
- **CSRF 防护**：使用 CSRF Token

## 部署规范
- **环境变量**：使用 `.env` 文件管理环境配置
- **构建优化**：启用代码分割、Tree Shaking、压缩
- **CI/CD**：自动化构建、测试、部署流程
- **监控告警**：配置错误监控和性能监控

## 重要提醒
- ✅ **代码提交前必须通过**：类型检查、单元测试、代码规范检查
- ✅ **使用 TypeScript 严格模式**：确保类型安全
- ✅ **遵循组合式 API 最佳实践**：提高代码复用性和可维护性
- ✅ **利用 Pinia 的响应式特性**：正确使用 Store 状态管理
- ✅ **Tailwind CSS 工具类优先**：保持样式的一致性和可维护性
- ✅ **组件设计原则**：单一职责、高内聚、低耦合
- ⚠️ **性能监控**：定期检查应用性能，及时优化
- ⚠️ **安全意识**：始终考虑安全性，防范常见攻击
