# 质量管理子系统需求文档

## 1. 项目概述

### 1.1 项目背景
质量管理子系统是玻璃深加工ERP系统的核心质量控制模块，专门针对玻璃深加工行业的严格质量要求和复杂检验流程设计。该子系统需要支持从原料检验到成品出厂的全过程质量管理，包括质量标准制定、检验计划执行、质量数据分析、不合格品处理等关键环节。与传统制造业不同，玻璃深加工质量管理具有检验项目多样、质量标准严格、缺陷类型复杂、追溯要求高等特点，需要建立完善的质量管理体系和精确的质量控制机制。

### 1.2 项目目标
- 建立完整的质量管理体系，覆盖从原料到成品的全过程质量控制
- 构建标准化的质量检验流程，确保产品质量稳定可控
- 实现质量数据的实时采集和智能分析，提供质量改进决策支持
- 建立完善的不合格品管理和质量追溯体系，快速定位和解决质量问题
- 支持多种检验方式和设备集成，提高检验效率和准确性
- 实现质量成本控制和质量绩效评估，持续优化质量管理水平

### 1.3 目标用户
- **质量总监**：制定质量策略，监控质量绩效，管理质量体系
- **质量工程师**：制定质量标准，设计检验方案，分析质量数据
- **质检员**：执行质量检验，记录检验数据，处理质量问题
- **车间质检**：现场质量控制，工序质量检验，质量问题反馈
- **供应商质量工程师**：供应商质量管理，来料检验，质量改进
- **客户服务代表**：处理客户质量投诉，跟踪质量问题解决

## 2. 功能需求

### 2.1 质量标准管理模块

#### 2.1.1 质量标准体系
- **国际标准管理**：
  - ISO 9001质量管理体系标准
  - GB/T 11944建筑用安全玻璃标准
  - JGJ 102玻璃幕墙工程技术规范
  - ASTM、EN等国际玻璃标准
- **企业标准制定**：
  - 基于国际标准的企业内控标准
  - 不同产品类型的专项质量标准
  - 客户特殊要求的定制化标准
  - 工艺质量控制标准和参数范围
- **标准版本管理**：
  - 标准文档的版本控制和变更管理
  - 标准更新的影响评估和实施计划
  - 历史版本的归档和追溯查询
  - 标准废止和替换的流程管理

#### 2.1.2 检验标准配置
- **检验项目定义**：
  - 外观质量：气泡、结石、划伤、污点等缺陷
  - 尺寸精度：长度、宽度、厚度、对角线等
  - 物理性能：强度、硬度、透光率、热稳定性
  - 化学成分：成分分析、有害物质检测
- **检验方法规范**：
  - 检验设备和工具要求
  - 检验环境条件设定
  - 检验步骤和操作规程
  - 数据记录和计算方法
- **判定标准设置**：
  - 合格/不合格判定标准
  - 缺陷等级分类标准
  - 抽样方案和接收标准
  - 特殊情况的处理规则

#### 2.1.3 质量等级体系
- **产品等级分类**：
  - A级品：完全符合标准要求的优质产品
  - B级品：轻微缺陷但不影响使用的产品
  - C级品：明显缺陷但可降级使用的产品
  - 废品：严重缺陷无法使用的产品
- **缺陷严重度分级**：
  - 致命缺陷：影响安全使用的严重缺陷
  - 严重缺陷：影响功能使用的重要缺陷
  - 轻微缺陷：不影响使用的外观缺陷
  - 观察缺陷：需要关注但暂不影响的缺陷

### 2.2 检验管理模块

#### 2.2.1 检验计划管理
- **检验计划制定**：
  - 基于生产计划的检验任务安排
  - 检验资源配置和人员分工
  - 检验设备预约和使用计划
  - 检验时间节点和里程碑设置
- **检验类型管理**：
  - 来料检验：原材料和外购件检验
  - 工序检验：生产过程中的质量控制点检验
  - 成品检验：最终产品的全面质量检验
  - 出厂检验：发货前的最终质量确认
- **抽样方案设计**：
  - 基于统计学原理的抽样方案
  - 不同批量和风险等级的抽样策略
  - 特殊产品的全检和重点检验
  - 抽样结果的统计分析和评估

#### 2.2.2 检验执行管理
- **检验任务分配**：
  - 基于检验员技能和工作负荷的任务分配
  - 检验任务的优先级排序和调度
  - 检验进度跟踪和异常预警
  - 检验结果的及时反馈和处理
- **检验数据采集**：
  - 手工录入和自动采集相结合
  - 检验设备数据的自动读取
  - 图像识别和AI辅助检验
  - 移动端检验数据实时上传
- **检验结果处理**：
  - 检验数据的自动计算和判定
  - 不合格品的标识和隔离
  - 检验报告的自动生成和分发
  - 检验异常的升级和处理流程

#### 2.2.3 在线检测集成
- **检测设备集成**：
  - 光学检测设备：表面缺陷检测仪
  - 尺寸测量设备：激光测厚仪、轮廓仪
  - 强度测试设备：冲击试验机、弯曲试验机
  - 成分分析设备：光谱仪、色差仪
- **实时监控系统**：
  - 生产线质量参数实时监控
  - 关键质量指标的趋势分析
  - 质量异常的自动报警和处理
  - 质量数据的实时可视化展示
- **智能检测算法**：
  - 基于机器学习的缺陷识别
  - 图像处理和模式识别技术
  - 质量预测和预警模型
  - 检测精度的持续优化

### 2.3 不合格品管理模块

#### 2.3.1 不合格品识别
- **缺陷分类体系**：
  - 外观缺陷：气泡、结石、划伤、污点、色差
  - 尺寸缺陷：超差、变形、厚薄不均
  - 性能缺陷：强度不足、透光率低、热稳定性差
  - 包装缺陷：包装破损、标识错误、数量不符
- **缺陷原因分析**：
  - 原料问题：原料质量、成分偏差、供应商问题
  - 工艺问题：参数偏差、设备故障、操作失误
  - 环境问题：温湿度异常、清洁度不足、污染源
  - 人员问题：技能不足、操作不当、责任心不强
- **缺陷等级评定**：
  - 基于缺陷严重程度的等级划分
  - 缺陷对产品使用的影响评估
  - 客户接受度和市场影响分析
  - 修复可能性和成本效益评估

#### 2.3.2 不合格品处理
- **处理方式决策**：
  - 返工：可修复缺陷的返工处理
  - 降级：轻微缺陷的降级使用
  - 报废：严重缺陷的报废处理
  - 让步接收：客户同意的特殊处理
- **处理流程管控**：
  - 不合格品的标识和隔离
  - 处理方案的审批和执行
  - 处理过程的跟踪和监控
  - 处理结果的验证和确认
- **成本核算管理**：
  - 不合格品损失成本统计
  - 返工和修复成本核算
  - 质量成本的分类和分析
  - 质量改进的投资回报评估

#### 2.3.3 纠正预防措施
- **根本原因分析**：
  - 5Why分析法深入挖掘根本原因
  - 鱼骨图分析法系统分析影响因素
  - 故障树分析法逻辑分析因果关系
  - 统计分析法量化分析相关性
- **纠正措施制定**：
  - 针对根本原因的纠正措施
  - 措施的可行性和有效性评估
  - 实施计划和责任人分配
  - 措施效果的跟踪和验证
- **预防措施实施**：
  - 预防类似问题再次发生的措施
  - 流程改进和标准完善
  - 人员培训和技能提升
  - 设备改进和技术升级

### 2.4 质量数据分析模块

#### 2.4.1 质量统计分析
- **基础统计分析**：
  - 合格率、不合格率统计分析
  - 缺陷类型和频次统计
  - 质量趋势和变化分析
  - 质量水平的对比分析
- **过程能力分析**：
  - Cp、Cpk过程能力指数计算
  - 过程稳定性和一致性评估
  - 过程改进潜力分析
  - 过程控制限的设定和调整
- **质量成本分析**：
  - 预防成本、评价成本、内部失效成本、外部失效成本
  - 质量成本结构分析和优化
  - 质量投入产出比分析
  - 质量改进的经济效益评估

#### 2.4.2 SPC统计过程控制
- **控制图管理**：
  - X-R控制图：均值和极差控制
  - X-S控制图：均值和标准差控制
  - p控制图：不合格品率控制
  - c控制图：缺陷数控制
- **过程监控预警**：
  - 控制限的自动计算和更新
  - 异常模式的自动识别和报警
  - 过程失控的原因分析和处理
  - 控制图的实时更新和展示
- **过程改进指导**：
  - 基于SPC数据的过程改进建议
  - 关键质量特性的识别和控制
  - 过程变异的减少和控制
  - 质量稳定性的持续提升

#### 2.4.3 质量报表分析
- **日常质量报表**：
  - 日检验报表：当日检验情况汇总
  - 周质量报表：一周质量状况分析
  - 月度质量报表：月度质量绩效评估
  - 年度质量报告：年度质量管理总结
- **专项分析报表**：
  - 供应商质量分析报表
  - 产品质量对比分析报表
  - 客户投诉分析报表
  - 质量成本分析报表
- **管理决策报表**：
  - 质量绩效仪表板
  - 质量趋势预测报表
  - 质量改进建议报表
  - 质量风险评估报表

### 2.5 供应商质量管理模块

#### 2.5.1 供应商质量评估
- **供应商准入评估**：
  - 质量管理体系认证评估
  - 生产能力和技术水平评估
  - 质量控制流程和标准评估
  - 历史质量表现和信誉评估
- **定期质量审核**：
  - 现场质量审核计划和执行
  - 质量管理体系运行评估
  - 质量问题整改跟踪
  - 审核结果评价和改进建议
- **质量绩效评价**：
  - 来料合格率统计和分析
  - 质量问题频次和严重程度评估
  - 质量改进响应速度评价
  - 综合质量绩效评分和排名

#### 2.5.2 来料质量控制
- **来料检验管理**：
  - 来料检验计划和抽样方案
  - 检验标准和方法执行
  - 检验结果记录和判定
  - 不合格来料的处理和退货
- **供应商质量协议**：
  - 质量标准和要求约定
  - 检验方法和判定标准
  - 质量问题处理流程
  - 质量改进目标和措施
- **质量信息反馈**：
  - 质量问题及时反馈供应商
  - 质量数据定期分享和分析
  - 质量改进建议和技术支持
  - 质量培训和技术交流

#### 2.5.3 供应商质量改进
- **质量问题协同解决**：
  - 质量问题的联合分析
  - 改进措施的协同制定
  - 改进效果的跟踪验证
  - 经验总结和推广应用
- **技术支持和培训**：
  - 质量管理技术指导
  - 检验方法培训和交流
  - 质量工具应用推广
  - 最佳实践分享和学习
- **长期合作发展**：
  - 战略供应商培育计划
  - 质量能力提升支持
  - 新产品开发质量协同
  - 质量创新合作项目

### 2.6 客户质量服务模块

#### 2.6.1 客户质量要求管理
- **客户标准对接**：
  - 客户质量标准收集和分析
  - 企业标准与客户标准对比
  - 差异分析和标准调整
  - 客户标准变更跟踪和响应
- **特殊要求管理**：
  - 客户特殊质量要求识别
  - 特殊检验方法和标准制定
  - 特殊质量控制措施实施
  - 特殊要求满足情况评估
- **质量协议签署**：
  - 质量协议条款制定和谈判
  - 质量责任和义务明确
  - 质量争议处理机制约定
  - 质量改进目标和计划

#### 2.6.2 客户投诉处理
- **投诉接收处理**：
  - 多渠道投诉信息收集
  - 投诉信息分类和登记
  - 投诉紧急程度评估
  - 投诉处理责任人分配
- **投诉调查分析**：
  - 现场调查和证据收集
  - 问题原因深入分析
  - 责任归属判定
  - 解决方案制定和评估
- **投诉处理跟踪**：
  - 处理进度实时跟踪
  - 客户满意度调查
  - 处理结果验证确认
  - 投诉处理经验总结

#### 2.6.3 客户满意度管理
- **满意度调查**：
  - 定期客户满意度调查
  - 质量满意度专项调查
  - 调查结果统计分析
  - 满意度趋势跟踪
- **质量服务改进**：
  - 基于满意度的服务改进
  - 质量服务流程优化
  - 服务响应速度提升
  - 服务质量标准化
- **客户关系维护**：
  - 重要客户质量服务专员
  - 定期质量沟通和交流
  - 质量问题预防和预警
  - 长期质量合作关系建立

## 3. 页面与功能映射

### 3.1 页面列表
- 质量标准管理页面
- 检验计划管理页面
- 检验执行管理页面
- 不合格品管理页面
- 质量数据分析页面
- SPC统计控制页面
- 供应商质量管理页面
- 客户质量服务页面
- 质量报表中心页面
- 质量绩效仪表板页面

### 3.2 页面功能明细

#### 3.2.1 质量标准管理页面
- **功能模块**：质量标准制定和维护
- **功能描述**：管理企业质量标准体系，配置检验标准和判定规则
- **主要界面元素**：
  - 标准树形组件：质量标准分类和层级展示
  - 标准编辑组件：标准内容编辑和版本管理
  - 检验项目组件：检验项目配置和参数设置
  - 判定规则组件：合格判定标准和等级设置
  - 标准对比组件：不同版本标准对比分析
- **输入/输出字段**：
  - 字段名：qualityStandard
  - 类型：object
  - 校验规则：标准编号唯一性，版本号递增，生效日期合理性
  - 选项：标准类型（国际标准、行业标准、企业标准），适用范围
  - 依赖关系：产品类型、工艺路线、检验设备能力
- **交互流程**：
  - 创建标准 -> 配置检验项目 -> 设置判定规则 -> 审批发布 -> 执行应用
- **相关API**：
  - 接口名：createQualityStandard, updateStandard, publishStandard
  - 请求参数：{standard: QualityStandard, inspectionItems: InspectionItem[], rules: JudgmentRule[]}
  - 响应结构：{standardId: string, version: string, status: string, effectiveDate: Date}
  - 错误处理：标准冲突时提示修改，审批失败时返回原因
- **权限控制**：质量工程师可编辑，质量总监可审批发布

#### 3.2.2 检验执行管理页面
- **功能模块**：检验任务执行和数据采集
- **功能描述**：执行检验计划，记录检验数据，处理检验结果
- **主要界面元素**：
  - 任务列表组件：待检验任务列表和状态跟踪
  - 检验表单组件：检验数据录入和自动计算
  - 图像采集组件：缺陷图片拍照和标注
  - 设备集成组件：检测设备数据自动读取
  - 结果判定组件：自动判定和人工确认
- **输入/输出字段**：
  - 字段名：inspectionExecution
  - 类型：object
  - 校验规则：检验数据完整性，数值范围合理性，图片清晰度
  - 选项：检验结果（合格、不合格、待定），缺陷类型，严重程度
  - 依赖关系：检验标准、抽样方案、检验设备状态
- **交互流程**：
  - 接收任务 -> 准备检验 -> 执行检验 -> 记录数据 -> 判定结果 -> 提交报告
- **相关API**：
  - 接口名：startInspection, recordInspectionData, submitInspectionResult
  - 请求参数：{taskId: string, inspectionData: InspectionData[], images: File[], result: InspectionResult}
  - 响应结构：{resultId: string, judgment: string, nextAction: string, reportUrl: string}
  - 错误处理：数据异常时重新录入，设备故障时切换备用方案
- **权限控制**：质检员可执行检验，质量工程师可审核结果

#### 3.2.3 不合格品管理页面
- **功能模块**：不合格品识别和处理
- **功能描述**：管理不合格品的识别、分析、处理和跟踪
- **主要界面元素**：
  - 不合格品列表组件：不合格品清单和状态管理
  - 缺陷分析组件：缺陷类型分析和原因识别
  - 处理方案组件：处理方式选择和方案制定
  - 成本核算组件：不合格品损失和处理成本统计
  - 改进措施组件：纠正预防措施制定和跟踪
- **输入/输出字段**：
  - 字段名：nonconformingProduct
  - 类型：object
  - 校验规则：缺陷描述完整性，处理方案可行性，成本核算准确性
  - 选项：处理方式（返工、降级、报废、让步接收），缺陷等级
  - 依赖关系：质量标准、检验结果、客户要求、成本预算
- **交互流程**：
  - 识别不合格 -> 分析原因 -> 制定方案 -> 执行处理 -> 验证效果 -> 总结改进
- **相关API**：
  - 接口名：identifyNonconforming, analyzeDefect, processNonconforming
  - 请求参数：{productId: string, defects: Defect[], processPlan: ProcessPlan, cost: Cost}
  - 响应结构：{processId: string, status: string, cost: number, actions: Action[]}
  - 错误处理：处理方案不可行时重新制定，成本超预算时申请审批
- **权限控制**：质检员可识别，质量工程师可制定方案，质量总监可审批

#### 3.2.4 质量数据分析页面
- **功能模块**：质量数据统计分析和可视化
- **功能描述**：对质量数据进行统计分析，生成质量报表和趋势图
- **主要界面元素**：
  - 数据筛选组件：时间范围、产品类型、质量指标筛选
  - 统计图表组件：合格率趋势、缺陷分布、质量对比图表
  - 分析报告组件：自动生成分析报告和改进建议
  - 预警监控组件：质量指标预警和异常提醒
  - 导出功能组件：报表导出和数据下载
- **输入/输出字段**：
  - 字段名：qualityAnalysis
  - 类型：object
  - 校验规则：时间范围合理性，统计维度有效性，数据完整性
  - 选项：分析维度（时间、产品、工序、供应商），图表类型
  - 依赖关系：检验数据、质量标准、产品信息、时间维度
- **交互流程**：
  - 设置分析条件 -> 数据查询计算 -> 生成图表报告 -> 分析解读 -> 导出分享
- **相关API**：
  - 接口名：analyzeQualityData, generateQualityReport, exportAnalysisResult
  - 请求参数：{timeRange: DateRange, dimensions: string[], metrics: string[], filters: Filter[]}
  - 响应结构：{analysisResult: AnalysisResult, charts: Chart[], report: Report, insights: Insight[]}
  - 错误处理：数据不足时提示扩大范围，计算异常时重新查询
- **权限控制**：质检员可查看基础分析，质量工程师可查看详细分析

#### 3.2.5 SPC统计控制页面
- **功能模块**：统计过程控制和过程能力分析
- **功能描述**：实施SPC统计过程控制，监控过程稳定性和能力
- **主要界面元素**：
  - 控制图组件：X-R图、X-S图、p图、c图等控制图展示
  - 过程能力组件：Cp、Cpk等过程能力指数计算和展示
  - 异常预警组件：控制限超出、异常模式识别和报警
  - 参数设置组件：控制限计算、抽样方案、更新频率设置
  - 改进建议组件：基于SPC分析的过程改进建议
- **输入/输出字段**：
  - 字段名：spcControl
  - 类型：object
  - 校验规则：数据点数量充足，控制限计算正确，异常判定准确
  - 选项：控制图类型，过程能力等级，异常模式类型
  - 依赖关系：检验数据、质量标准、过程参数、统计方法
- **交互流程**：
  - 选择控制对象 -> 设置控制参数 -> 绘制控制图 -> 监控过程 -> 分析改进
- **相关API**：
  - 接口名：createControlChart, calculateProcessCapability, detectAbnormalPattern
  - 请求参数：{processId: string, dataPoints: DataPoint[], controlLimits: ControlLimit, chartType: string}
  - 响应结构：{chartData: ChartData, capability: ProcessCapability, alerts: Alert[], suggestions: Suggestion[]}
  - 错误处理：数据不足时提示增加样本，计算异常时检查参数设置
- **权限控制**：质检员可查看控制图，质量工程师可设置参数和分析

### 3.3 页面交互关系
- **质量标准管理** ↔ **检验执行管理**：标准配置影响检验执行
- **检验执行管理** → **不合格品管理**：检验发现的不合格品流转处理
- **检验执行管理** → **质量数据分析**：检验数据用于统计分析
- **质量数据分析** → **SPC统计控制**：分析结果用于过程控制
- **不合格品管理** → **供应商质量管理**：供应商相关质量问题反馈
- **客户质量服务** ↔ **质量标准管理**：客户要求影响标准制定

## 4. 用户场景与流程

### 4.1 新产品质量标准制定场景
- **用户角色**：质量工程师、技术主管
- **场景描述**：为新产品制定完整的质量标准和检验规范
- **前置条件**：产品设计完成，工艺路线确定，客户要求明确
- **操作流程**：
  1. 收集标准依据 -> 分析国际标准、行业标准、客户要求
  2. 制定企业标准 -> 结合产品特点制定内控标准
  3. 配置检验项目 -> 确定检验项目、方法、设备、环境要求
  4. 设置判定规则 -> 制定合格标准、缺陷分级、抽样方案
  5. 标准审批发布 -> 技术评审、管理审批、正式发布实施
  6. 培训和实施 -> 检验人员培训、标准宣贯、执行监督
- **状态变化与交互说明**：
  - 标准制定过程中需要与产品设计、工艺管理协调
  - 标准审批需要多级审批流程
  - 标准发布后自动推送给相关检验岗位
  - 标准变更需要影响评估和变更控制
- **预期结果**：建立完整可执行的产品质量标准体系
- **异常处理**：标准冲突时协调解决，审批不通过时修改完善

### 4.2 批量产品检验执行场景
- **用户角色**：质检员、车间质检
- **场景描述**：按照检验计划执行批量产品的质量检验
- **前置条件**：检验计划已制定，检验标准已发布，检验设备正常
- **操作流程**：
  1. 接收检验任务 -> 查看检验计划和要求
  2. 准备检验工作 -> 准备检验设备、工具、环境
  3. 执行抽样检验 -> 按抽样方案选取样品
  4. 进行质量检验 -> 按标准执行各项检验项目
  5. 记录检验数据 -> 录入检验结果和缺陷信息
  6. 判定检验结果 -> 自动判定或人工确认检验结论
  7. 处理不合格品 -> 标识隔离不合格品，启动处理流程
  8. 生成检验报告 -> 自动生成检验报告和证书
- **状态变化与交互说明**：
  - 检验过程中实时更新任务状态
  - 发现不合格品立即标识和隔离
  - 检验数据自动同步到质量数据库
  - 异常情况及时上报和处理
- **预期结果**：完成批量产品检验，确保产品质量合格
- **异常处理**：设备故障时切换备用方案，质量异常时升级处理

### 4.3 不合格品处理改进场景
- **用户角色**：质量工程师、生产技术员
- **场景描述**：对发现的不合格品进行分析处理和改进
- **前置条件**：不合格品已识别，相关信息已记录
- **操作流程**：
  1. 不合格品分析 -> 分析缺陷类型、严重程度、影响范围
  2. 原因调查分析 -> 深入分析产生原因和影响因素
  3. 制定处理方案 -> 确定返工、降级、报废等处理方式
  4. 执行处理措施 -> 按方案执行具体处理操作
  5. 验证处理效果 -> 检验处理后的产品质量
  6. 制定改进措施 -> 制定纠正和预防措施
  7. 跟踪改进效果 -> 监控改进措施的实施效果
  8. 总结经验教训 -> 形成改进经验和知识积累
- **状态变化与交互说明**：
  - 处理过程中实时更新不合格品状态
  - 涉及多部门协作时需要流程协调
  - 改进措施需要跟踪验证效果
  - 经验总结需要知识库更新
- **预期结果**：不合格品得到妥善处理，质量问题得到根本改进
- **异常处理**：处理方案不可行时重新制定，改进效果不佳时深入分析

### 4.4 供应商质量管理场景
- **用户角色**：供应商质量工程师、采购员
- **场景描述**：对供应商进行质量管理和改进
- **前置条件**：供应商已准入，质量协议已签署
- **操作流程**：
  1. 制定质量计划 -> 制定供应商质量管理计划
  2. 执行来料检验 -> 按标准检验供应商来料
  3. 质量数据分析 -> 统计分析供应商质量表现
  4. 质量问题反馈 -> 及时反馈质量问题给供应商
  5. 协同改进措施 -> 与供应商共同制定改进措施
  6. 跟踪改进效果 -> 监控供应商质量改进效果
  7. 质量绩效评价 -> 定期评价供应商质量绩效
  8. 优化供应商管理 -> 基于评价结果优化管理策略
- **状态变化与交互说明**：
  - 来料检验结果实时反馈供应商
  - 质量问题需要供应商及时响应
  - 改进措施需要双方协同执行
  - 绩效评价影响供应商合作关系
- **预期结果**：供应商质量持续改进，来料质量稳定可靠
- **异常处理**：质量问题严重时启动应急预案，改进不力时调整合作关系

### 4.5 客户投诉处理场景
- **用户角色**：客户服务代表、质量工程师
- **场景描述**：处理客户质量投诉，维护客户关系
- **前置条件**：收到客户投诉，投诉信息已登记
- **操作流程**：
  1. 投诉信息确认 -> 确认投诉内容和客户要求
  2. 紧急程度评估 -> 评估投诉的紧急程度和影响
  3. 组织调查分析 -> 组织相关人员调查分析问题
  4. 制定解决方案 -> 制定客户满意的解决方案
  5. 实施解决措施 -> 执行解决方案和补救措施
  6. 客户沟通确认 -> 与客户沟通确认解决效果
  7. 内部改进措施 -> 制定内部改进措施防止再发
  8. 投诉处理总结 -> 总结投诉处理经验和教训
- **状态变化与交互说明**：
  - 投诉处理过程中保持与客户密切沟通
  - 紧急投诉需要快速响应和处理
  - 解决方案需要客户确认和满意
  - 内部改进需要相关部门配合
- **预期结果**：客户投诉得到满意解决，客户关系得到维护
- **异常处理**：客户不满意时重新制定方案，争议较大时升级处理

## 5. 数据模型设计

### 5.1 核心实体模型

#### 5.1.1 质量标准实体
```sql
-- 质量标准主表
CREATE TABLE quality_standards (
    id VARCHAR(32) PRIMARY KEY,
    standard_code VARCHAR(50) UNIQUE NOT NULL,
    standard_name VARCHAR(200) NOT NULL,
    standard_type ENUM('INTERNATIONAL', 'INDUSTRY', 'ENTERPRISE', 'CUSTOMER') NOT NULL,
    version VARCHAR(20) NOT NULL,
    status ENUM('DRAFT', 'REVIEWING', 'ACTIVE', 'OBSOLETE') NOT NULL,
    effective_date DATE NOT NULL,
    expiry_date DATE,
    applicable_products TEXT,
    description TEXT,
    created_by VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_standard_code (standard_code),
    INDEX idx_standard_type (standard_type),
    INDEX idx_status (status)
);

-- 检验项目表
CREATE TABLE inspection_items (
    id VARCHAR(32) PRIMARY KEY,
    standard_id VARCHAR(32) NOT NULL,
    item_code VARCHAR(50) NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_category ENUM('APPEARANCE', 'DIMENSION', 'PERFORMANCE', 'COMPOSITION') NOT NULL,
    inspection_method VARCHAR(500),
    equipment_required VARCHAR(200),
    environment_conditions VARCHAR(300),
    measurement_unit VARCHAR(20),
    target_value DECIMAL(15,6),
    upper_limit DECIMAL(15,6),
    lower_limit DECIMAL(15,6),
    tolerance DECIMAL(15,6),
    is_critical BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (standard_id) REFERENCES quality_standards(id),
    INDEX idx_standard_item (standard_id, item_code)
);

-- 判定规则表
CREATE TABLE judgment_rules (
    id VARCHAR(32) PRIMARY KEY,
    standard_id VARCHAR(32) NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    rule_type ENUM('SINGLE_ITEM', 'MULTIPLE_ITEMS', 'STATISTICAL') NOT NULL,
    conditions JSON NOT NULL,
    judgment_result ENUM('PASS', 'FAIL', 'CONDITIONAL') NOT NULL,
    defect_level ENUM('CRITICAL', 'MAJOR', 'MINOR', 'OBSERVATION') NOT NULL,
    action_required VARCHAR(500),
    FOREIGN KEY (standard_id) REFERENCES quality_standards(id),
    INDEX idx_standard_rule (standard_id, rule_type)
);
```

#### 5.1.2 检验管理实体
```sql
-- 检验计划表
CREATE TABLE inspection_plans (
    id VARCHAR(32) PRIMARY KEY,
    plan_code VARCHAR(50) UNIQUE NOT NULL,
    plan_name VARCHAR(200) NOT NULL,
    plan_type ENUM('INCOMING', 'IN_PROCESS', 'FINAL', 'OUTGOING') NOT NULL,
    product_id VARCHAR(32),
    batch_id VARCHAR(32),
    order_id VARCHAR(32),
    standard_id VARCHAR(32) NOT NULL,
    sampling_plan JSON NOT NULL,
    planned_start_date DATE NOT NULL,
    planned_end_date DATE NOT NULL,
    assigned_inspector VARCHAR(32),
    status ENUM('PLANNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED') NOT NULL,
    priority ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') DEFAULT 'NORMAL',
    created_by VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (standard_id) REFERENCES quality_standards(id),
    INDEX idx_plan_code (plan_code),
    INDEX idx_plan_type (plan_type),
    INDEX idx_status (status)
);

-- 检验执行表
CREATE TABLE inspection_executions (
    id VARCHAR(32) PRIMARY KEY,
    plan_id VARCHAR(32) NOT NULL,
    execution_code VARCHAR(50) UNIQUE NOT NULL,
    inspector_id VARCHAR(32) NOT NULL,
    inspection_date DATE NOT NULL,
    inspection_time TIME NOT NULL,
    sample_quantity INT NOT NULL,
    inspection_location VARCHAR(200),
    environment_conditions JSON,
    equipment_used JSON,
    overall_result ENUM('PASS', 'FAIL', 'CONDITIONAL') NOT NULL,
    pass_quantity INT DEFAULT 0,
    fail_quantity INT DEFAULT 0,
    remarks TEXT,
    completed_at TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES inspection_plans(id),
    INDEX idx_execution_code (execution_code),
    INDEX idx_plan_execution (plan_id, inspection_date)
);

-- 检验数据表
CREATE TABLE inspection_data (
    id VARCHAR(32) PRIMARY KEY,
    execution_id VARCHAR(32) NOT NULL,
    item_id VARCHAR(32) NOT NULL,
    sample_no VARCHAR(50) NOT NULL,
    measured_value DECIMAL(15,6),
    text_value VARCHAR(500),
    judgment ENUM('PASS', 'FAIL', 'NA') NOT NULL,
    defect_type VARCHAR(100),
    defect_level ENUM('CRITICAL', 'MAJOR', 'MINOR', 'OBSERVATION'),
    defect_description TEXT,
    image_urls JSON,
    measured_by VARCHAR(32),
    measured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (execution_id) REFERENCES inspection_executions(id),
    FOREIGN KEY (item_id) REFERENCES inspection_items(id),
    INDEX idx_execution_data (execution_id, item_id),
    INDEX idx_judgment (judgment)
);
```

#### 5.1.3 不合格品管理实体
```sql
-- 不合格品表
CREATE TABLE nonconforming_products (
    id VARCHAR(32) PRIMARY KEY,
    ncr_number VARCHAR(50) UNIQUE NOT NULL,
    product_id VARCHAR(32) NOT NULL,
    batch_id VARCHAR(32),
    order_id VARCHAR(32),
    inspection_id VARCHAR(32),
    detected_date DATE NOT NULL,
    detected_by VARCHAR(32) NOT NULL,
    detection_stage ENUM('INCOMING', 'IN_PROCESS', 'FINAL', 'CUSTOMER') NOT NULL,
    quantity INT NOT NULL,
    defect_category VARCHAR(100) NOT NULL,
    defect_description TEXT NOT NULL,
    severity_level ENUM('CRITICAL', 'MAJOR', 'MINOR') NOT NULL,
    root_cause TEXT,
    disposition ENUM('REWORK', 'DOWNGRADE', 'SCRAP', 'USE_AS_IS') NOT NULL,
    disposition_reason TEXT,
    status ENUM('IDENTIFIED', 'ANALYZING', 'PROCESSING', 'COMPLETED') NOT NULL,
    cost_impact DECIMAL(15,2) DEFAULT 0,
    responsible_dept VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ncr_number (ncr_number),
    INDEX idx_product_batch (product_id, batch_id),
    INDEX idx_detected_date (detected_date),
    INDEX idx_status (status)
);

-- 纠正预防措施表
CREATE TABLE corrective_actions (
    id VARCHAR(32) PRIMARY KEY,
    ncr_id VARCHAR(32) NOT NULL,
    action_type ENUM('CORRECTIVE', 'PREVENTIVE') NOT NULL,
    action_description TEXT NOT NULL,
    root_cause_analysis TEXT,
    responsible_person VARCHAR(32) NOT NULL,
    target_date DATE NOT NULL,
    actual_date DATE,
    status ENUM('PLANNED', 'IN_PROGRESS', 'COMPLETED', 'VERIFIED') NOT NULL,
    effectiveness_review TEXT,
    verification_date DATE,
    verified_by VARCHAR(32),
    created_by VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ncr_id) REFERENCES nonconforming_products(id),
    INDEX idx_ncr_action (ncr_id, action_type),
    INDEX idx_responsible (responsible_person, target_date)
);
```

### 5.2 玻璃专业质量模型

#### 5.2.1 玻璃缺陷分类模型
```sql
-- 玻璃缺陷分类表
CREATE TABLE glass_defect_types (
    id VARCHAR(32) PRIMARY KEY,
    defect_code VARCHAR(50) UNIQUE NOT NULL,
    defect_name VARCHAR(200) NOT NULL,
    defect_category ENUM('BUBBLE', 'STONE', 'SCRATCH', 'STAIN', 'DEFORMATION', 'OPTICAL', 'EDGE', 'COATING') NOT NULL,
    severity_mapping JSON NOT NULL, -- 不同尺寸/数量对应的严重程度
    detection_method VARCHAR(200),
    typical_causes TEXT,
    prevention_measures TEXT,
    image_examples JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_defect_code (defect_code),
    INDEX idx_category (defect_category)
);

-- 玻璃规格质量标准表
CREATE TABLE glass_spec_standards (
    id VARCHAR(32) PRIMARY KEY,
    glass_type ENUM('FLOAT', 'TEMPERED', 'LAMINATED', 'INSULATED', 'COATED') NOT NULL,
    thickness_range VARCHAR(50) NOT NULL,
    size_range VARCHAR(100) NOT NULL,
    quality_grade ENUM('GRADE_A', 'GRADE_B', 'GRADE_C') NOT NULL,
    bubble_standards JSON NOT NULL, -- 气泡标准：尺寸、数量限制
    stone_standards JSON NOT NULL, -- 结石标准
    scratch_standards JSON NOT NULL, -- 划伤标准
    optical_standards JSON NOT NULL, -- 光学性能标准
    dimensional_tolerance JSON NOT NULL, -- 尺寸公差
    edge_quality_standards JSON NOT NULL, -- 边部质量标准
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_glass_type (glass_type, thickness_range),
    INDEX idx_quality_grade (quality_grade)
);
```

#### 5.2.2 检验设备集成模型
```sql
-- 检验设备表
CREATE TABLE inspection_equipment (
    id VARCHAR(32) PRIMARY KEY,
    equipment_code VARCHAR(50) UNIQUE NOT NULL,
    equipment_name VARCHAR(200) NOT NULL,
    equipment_type ENUM('OPTICAL', 'DIMENSIONAL', 'STRENGTH', 'COMPOSITION', 'MANUAL') NOT NULL,
    manufacturer VARCHAR(200),
    model VARCHAR(100),
    measurement_range VARCHAR(200),
    accuracy VARCHAR(100),
    calibration_cycle INT DEFAULT 365, -- 校准周期（天）
    last_calibration_date DATE,
    next_calibration_date DATE,
    status ENUM('ACTIVE', 'MAINTENANCE', 'CALIBRATION', 'RETIRED') NOT NULL,
    location VARCHAR(200),
    responsible_person VARCHAR(32),
    api_endpoint VARCHAR(500), -- 设备数据接口
    data_format JSON, -- 数据格式定义
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_equipment_code (equipment_code),
    INDEX idx_equipment_type (equipment_type),
    INDEX idx_status (status)
);

-- 设备检验能力表
CREATE TABLE equipment_capabilities (
    id VARCHAR(32) PRIMARY KEY,
    equipment_id VARCHAR(32) NOT NULL,
    inspection_item_id VARCHAR(32) NOT NULL,
    measurement_range VARCHAR(200),
    accuracy VARCHAR(100),
    repeatability VARCHAR(100),
    measurement_time INT, -- 测量时间（秒）
    sample_requirements TEXT,
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要检验设备
    FOREIGN KEY (equipment_id) REFERENCES inspection_equipment(id),
    FOREIGN KEY (inspection_item_id) REFERENCES inspection_items(id),
    UNIQUE KEY uk_equipment_item (equipment_id, inspection_item_id)
);
```

### 5.3 质量分析模型

#### 5.3.1 质量统计分析模型
```sql
-- 质量统计汇总表
CREATE TABLE quality_statistics (
    id VARCHAR(32) PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_period ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY') NOT NULL,
    product_id VARCHAR(32),
    product_category VARCHAR(100),
    supplier_id VARCHAR(32),
    customer_id VARCHAR(32),
    total_inspected INT NOT NULL DEFAULT 0,
    total_passed INT NOT NULL DEFAULT 0,
    total_failed INT NOT NULL DEFAULT 0,
    pass_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    critical_defects INT NOT NULL DEFAULT 0,
    major_defects INT NOT NULL DEFAULT 0,
    minor_defects INT NOT NULL DEFAULT 0,
    rework_quantity INT NOT NULL DEFAULT 0,
    scrap_quantity INT NOT NULL DEFAULT 0,
    quality_cost DECIMAL(15,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stat_date (stat_date, stat_period),
    INDEX idx_product_stat (product_id, stat_date),
    INDEX idx_supplier_stat (supplier_id, stat_date)
);

-- SPC控制图数据表
CREATE TABLE spc_control_data (
    id VARCHAR(32) PRIMARY KEY,
    control_chart_id VARCHAR(32) NOT NULL,
    sample_group VARCHAR(50) NOT NULL,
    sample_time TIMESTAMP NOT NULL,
    sample_size INT NOT NULL,
    measurement_values JSON NOT NULL, -- 测量值数组
    sample_mean DECIMAL(15,6),
    sample_range DECIMAL(15,6),
    sample_std DECIMAL(15,6),
    is_out_of_control BOOLEAN DEFAULT FALSE,
    violation_rules JSON, -- 违反的控制规则
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_chart_time (control_chart_id, sample_time),
    INDEX idx_out_of_control (is_out_of_control, sample_time)
);

-- 过程能力分析表
CREATE TABLE process_capability (
    id VARCHAR(32) PRIMARY KEY,
    process_id VARCHAR(32) NOT NULL,
    characteristic_id VARCHAR(32) NOT NULL,
    analysis_period_start DATE NOT NULL,
    analysis_period_end DATE NOT NULL,
    sample_size INT NOT NULL,
    process_mean DECIMAL(15,6) NOT NULL,
    process_std DECIMAL(15,6) NOT NULL,
    specification_lower DECIMAL(15,6),
    specification_upper DECIMAL(15,6),
    target_value DECIMAL(15,6),
    cp_value DECIMAL(8,4), -- 过程能力指数
    cpk_value DECIMAL(8,4), -- 过程能力指数
    pp_value DECIMAL(8,4), -- 过程性能指数
    ppk_value DECIMAL(8,4), -- 过程性能指数
    capability_level ENUM('INADEQUATE', 'ADEQUATE', 'GOOD', 'EXCELLENT') NOT NULL,
    improvement_suggestions TEXT,
    analyzed_by VARCHAR(32) NOT NULL,
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_process_period (process_id, analysis_period_start),
    INDEX idx_capability_level (capability_level)
);
```

### 5.4 供应商质量模型

#### 5.4.1 供应商质量评估模型
```sql
-- 供应商质量评估表
CREATE TABLE supplier_quality_assessments (
    id VARCHAR(32) PRIMARY KEY,
    supplier_id VARCHAR(32) NOT NULL,
    assessment_period_start DATE NOT NULL,
    assessment_period_end DATE NOT NULL,
    assessment_type ENUM('INITIAL', 'PERIODIC', 'SPECIAL') NOT NULL,
    incoming_pass_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    delivery_quality_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    response_time_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    improvement_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    overall_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    quality_level ENUM('EXCELLENT', 'GOOD', 'ACCEPTABLE', 'POOR') NOT NULL,
    major_issues TEXT,
    improvement_plans TEXT,
    next_assessment_date DATE,
    assessed_by VARCHAR(32) NOT NULL,
    assessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_supplier_period (supplier_id, assessment_period_start),
    INDEX idx_quality_level (quality_level),
    INDEX idx_overall_score (overall_score)
);

-- 供应商质量问题表
CREATE TABLE supplier_quality_issues (
    id VARCHAR(32) PRIMARY KEY,
    issue_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id VARCHAR(32) NOT NULL,
    material_id VARCHAR(32) NOT NULL,
    batch_number VARCHAR(100),
    issue_date DATE NOT NULL,
    issue_type ENUM('QUALITY_DEFECT', 'DELIVERY_DELAY', 'DOCUMENTATION', 'PACKAGING') NOT NULL,
    severity ENUM('CRITICAL', 'MAJOR', 'MINOR') NOT NULL,
    description TEXT NOT NULL,
    quantity_affected INT NOT NULL,
    cost_impact DECIMAL(15,2) DEFAULT 0,
    supplier_response TEXT,
    corrective_actions TEXT,
    status ENUM('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED') NOT NULL,
    resolution_date DATE,
    satisfaction_rating ENUM('VERY_SATISFIED', 'SATISFIED', 'NEUTRAL', 'DISSATISFIED', 'VERY_DISSATISFIED'),
    created_by VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_issue_number (issue_number),
    INDEX idx_supplier_date (supplier_id, issue_date),
    INDEX idx_status (status)
);
```

### 5.5 客户质量服务模型

#### 5.5.1 客户投诉管理模型
```sql
-- 客户投诉表
CREATE TABLE customer_complaints (
    id VARCHAR(32) PRIMARY KEY,
    complaint_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(32) NOT NULL,
    product_id VARCHAR(32),
    order_id VARCHAR(32),
    batch_id VARCHAR(32),
    complaint_date DATE NOT NULL,
    complaint_channel ENUM('PHONE', 'EMAIL', 'VISIT', 'ONLINE', 'THIRD_PARTY') NOT NULL,
    complaint_type ENUM('QUALITY_DEFECT', 'DELIVERY_DELAY', 'SERVICE_ISSUE', 'DOCUMENTATION') NOT NULL,
    severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW') NOT NULL,
    description TEXT NOT NULL,
    customer_expectation TEXT,
    quantity_affected INT,
    financial_impact DECIMAL(15,2),
    assigned_to VARCHAR(32),
    status ENUM('RECEIVED', 'INVESTIGATING', 'RESOLVING', 'RESOLVED', 'CLOSED') NOT NULL,
    resolution_description TEXT,
    customer_satisfaction ENUM('VERY_SATISFIED', 'SATISFIED', 'NEUTRAL', 'DISSATISFIED', 'VERY_DISSATISFIED'),
    resolution_date DATE,
    closed_date DATE,
    created_by VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_complaint_number (complaint_number),
    INDEX idx_customer_date (customer_id, complaint_date),
    INDEX idx_status (status),
    INDEX idx_severity (severity)
);

-- 客户满意度调查表
CREATE TABLE customer_satisfaction_surveys (
    id VARCHAR(32) PRIMARY KEY,
    survey_code VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(32) NOT NULL,
    survey_type ENUM('REGULAR', 'PROJECT_BASED', 'COMPLAINT_FOLLOWUP') NOT NULL,
    survey_date DATE NOT NULL,
    product_quality_score DECIMAL(3,1) NOT NULL, -- 1-10分
    delivery_quality_score DECIMAL(3,1) NOT NULL,
    service_quality_score DECIMAL(3,1) NOT NULL,
    response_speed_score DECIMAL(3,1) NOT NULL,
    overall_satisfaction DECIMAL(3,1) NOT NULL,
    strengths TEXT,
    improvement_suggestions TEXT,
    would_recommend BOOLEAN,
    survey_method ENUM('ONLINE', 'PHONE', 'VISIT', 'EMAIL') NOT NULL,
    surveyed_by VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_customer_survey (customer_id, survey_date),
    INDEX idx_overall_satisfaction (overall_satisfaction),
    INDEX idx_survey_type (survey_type)
);
```

## 6. API接口规范

### 6.1 质量标准管理接口

#### 6.1.1 质量标准CRUD接口
```javascript
// 创建质量标准
POST /api/quality/standards
{
  "standardCode": "QS-GLASS-001",
  "standardName": "建筑用钢化玻璃质量标准",
  "standardType": "ENTERPRISE",
  "version": "1.0",
  "applicableProducts": ["tempered_glass"],
  "description": "基于GB/T 9963标准制定的企业内控标准",
  "inspectionItems": [
    {
      "itemCode": "APPEARANCE",
      "itemName": "外观质量",
      "category": "APPEARANCE",
      "inspectionMethod": "目视检查",
      "targetValue": null,
      "upperLimit": null,
      "lowerLimit": null,
      "isCritical": true
    }
  ],
  "judgmentRules": [
    {
      "ruleName": "外观合格判定",
      "ruleType": "SINGLE_ITEM",
      "conditions": {"defectLevel": "MINOR", "maxCount": 2},
      "judgmentResult": "PASS",
      "defectLevel": "MINOR"
    }
  ]
}

// 响应
{
  "code": 200,
  "message": "质量标准创建成功",
  "data": {
    "standardId": "std_001",
    "standardCode": "QS-GLASS-001",
    "version": "1.0",
    "status": "DRAFT",
    "effectiveDate": "2024-02-01"
  }
}

// 查询质量标准
GET /api/quality/standards/{id}
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "standardId": "std_001",
    "standardCode": "QS-GLASS-001",
    "standardName": "建筑用钢化玻璃质量标准",
    "standardType": "ENTERPRISE",
    "version": "1.0",
    "status": "ACTIVE",
    "effectiveDate": "2024-02-01",
    "inspectionItems": [...],
    "judgmentRules": [...]
  }
}

// 更新质量标准
PUT /api/quality/standards/{id}
{
  "standardName": "建筑用钢化玻璃质量标准（修订版）",
  "version": "1.1",
  "description": "增加新的检验项目和判定标准"
}

// 删除质量标准
DELETE /api/quality/standards/{id}
{
  "code": 200,
  "message": "质量标准删除成功"
}

// 批量查询质量标准
GET /api/quality/standards?page=1&size=20&type=ENTERPRISE&status=ACTIVE
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 50,
    "page": 1,
    "size": 20,
    "records": [...]
  }
}
```

#### 6.1.2 检验标准配置接口
```javascript
// 配置检验项目
POST /api/quality/standards/{standardId}/inspection-items
{
  "itemCode": "THICKNESS",
  "itemName": "厚度检验",
  "category": "DIMENSION",
  "inspectionMethod": "游标卡尺测量",
  "equipmentRequired": "数显游标卡尺",
  "environmentConditions": "温度20±2℃，湿度≤65%",
  "measurementUnit": "mm",
  "targetValue": 6.0,
  "upperLimit": 6.2,
  "lowerLimit": 5.8,
  "tolerance": 0.2,
  "isCritical": true
}

// 设置判定规则
POST /api/quality/standards/{standardId}/judgment-rules
{
  "ruleName": "尺寸合格判定",
  "ruleType": "SINGLE_ITEM",
  "conditions": {
    "itemCode": "THICKNESS",
    "operator": "BETWEEN",
    "lowerValue": 5.8,
    "upperValue": 6.2
  },
  "judgmentResult": "PASS",
  "defectLevel": "MAJOR",
  "actionRequired": "超差产品需要返工或降级处理"
}

// 获取检验项目配置
GET /api/quality/standards/{standardId}/inspection-items
{
  "code": 200,
  "data": [
    {
      "itemId": "item_001",
      "itemCode": "THICKNESS",
      "itemName": "厚度检验",
      "category": "DIMENSION",
      "targetValue": 6.0,
      "upperLimit": 6.2,
      "lowerLimit": 5.8,
      "isCritical": true
    }
  ]
}
```

### 6.2 检验管理接口

#### 6.2.1 检验计划管理接口
```javascript
// 创建检验计划
POST /api/quality/inspection-plans
{
  "planCode": "IP-2024-001",
  "planName": "钢化玻璃批次检验计划",
  "planType": "FINAL",
  "productId": "prod_001",
  "batchId": "batch_001",
  "orderId": "order_001",
  "standardId": "std_001",
  "samplingPlan": {
    "samplingType": "RANDOM",
    "sampleSize": 10,
    "acceptanceLevel": "AQL_2.5"
  },
  "plannedStartDate": "2024-02-01",
  "plannedEndDate": "2024-02-02",
  "assignedInspector": "inspector_001",
  "priority": "HIGH"
}

// 响应
{
  "code": 200,
  "message": "检验计划创建成功",
  "data": {
    "planId": "plan_001",
    "planCode": "IP-2024-001",
    "status": "PLANNED",
    "estimatedDuration": "4小时"
  }
}

// 查询检验计划
GET /api/quality/inspection-plans?status=PLANNED&inspector=inspector_001&date=2024-02-01
{
  "code": 200,
  "data": {
    "total": 5,
    "records": [
      {
        "planId": "plan_001",
        "planCode": "IP-2024-001",
        "planName": "钢化玻璃批次检验计划",
        "status": "PLANNED",
        "priority": "HIGH",
        "plannedStartDate": "2024-02-01"
      }
    ]
  }
}

// 更新检验计划状态
PUT /api/quality/inspection-plans/{planId}/status
{
  "status": "IN_PROGRESS",
  "actualStartTime": "2024-02-01T09:00:00"
}
```

#### 6.2.2 检验执行管理接口
```javascript
// 开始检验执行
POST /api/quality/inspection-executions
{
  "planId": "plan_001",
  "inspectorId": "inspector_001",
  "inspectionDate": "2024-02-01",
  "inspectionTime": "09:00:00",
  "sampleQuantity": 10,
  "inspectionLocation": "质检室A",
  "environmentConditions": {
    "temperature": 20.5,
    "humidity": 45,
    "lighting": "标准光源D65"
  },
  "equipmentUsed": [
    {
      "equipmentId": "eq_001",
      "equipmentName": "数显游标卡尺"
    }
  ]
}

// 记录检验数据
POST /api/quality/inspection-executions/{executionId}/data
{
  "inspectionData": [
    {
      "itemId": "item_001",
      "sampleNo": "S001",
      "measuredValue": 6.05,
      "judgment": "PASS"
    },
    {
      "itemId": "item_002",
      "sampleNo": "S001",
      "textValue": "表面光洁，无明显缺陷",
      "judgment": "PASS"
    },
    {
      "itemId": "item_001",
      "sampleNo": "S002",
      "measuredValue": 6.25,
      "judgment": "FAIL",
      "defectType": "DIMENSION_OVERSIZE",
      "defectLevel": "MAJOR",
      "defectDescription": "厚度超出上限0.05mm"
    }
  ],
  "images": [
    {
      "sampleNo": "S002",
      "imageType": "DEFECT",
      "imageUrl": "/images/defects/def_001.jpg",
      "description": "厚度超差位置"
    }
  ]
}

// 完成检验执行
PUT /api/quality/inspection-executions/{executionId}/complete
{
  "overallResult": "CONDITIONAL",
  "passQuantity": 8,
  "failQuantity": 2,
  "remarks": "2个样品厚度超差，建议返工处理",
  "completedAt": "2024-02-01T12:30:00"
}

// 查询检验执行结果
GET /api/quality/inspection-executions/{executionId}
{
  "code": 200,
  "data": {
    "executionId": "exec_001",
    "executionCode": "IE-2024-001",
    "planId": "plan_001",
    "inspector": "张三",
    "inspectionDate": "2024-02-01",
    "overallResult": "CONDITIONAL",
    "passQuantity": 8,
    "failQuantity": 2,
    "inspectionData": [...],
    "defectSummary": [
      {
        "defectType": "DIMENSION_OVERSIZE",
        "count": 2,
        "severity": "MAJOR"
      }
    ]
  }
}
```

#### 6.2.3 在线检测集成接口
```javascript
// 获取设备检测数据
GET /api/quality/equipment/{equipmentId}/data?startTime=2024-02-01T09:00:00&endTime=2024-02-01T17:00:00
{
  "code": 200,
  "data": {
    "equipmentId": "eq_optical_001",
    "equipmentName": "光学缺陷检测仪",
    "dataPoints": [
      {
        "timestamp": "2024-02-01T09:15:30",
        "productId": "prod_001",
        "measurements": {
          "bubbleCount": 2,
          "maxBubbleSize": 0.5,
          "scratchLength": 0,
          "stoneCount": 0
        },
        "result": "PASS",
        "confidence": 0.95
      }
    ]
  }
}

// 设备异常报警
POST /api/quality/equipment/{equipmentId}/alerts
{
  "alertType": "QUALITY_ANOMALY",
  "severity": "HIGH",
  "description": "连续5个产品检测到气泡超标",
  "detectedAt": "2024-02-01T10:30:00",
  "affectedProducts": ["prod_001", "prod_002"],
  "recommendedAction": "停机检查生产参数"
}

// 设备校准记录
POST /api/quality/equipment/{equipmentId}/calibrations
{
  "calibrationDate": "2024-02-01",
  "calibratedBy": "calibrator_001",
  "calibrationStandard": "标准样品STD-001",
  "calibrationResults": {
    "accuracy": "±0.01mm",
    "repeatability": "0.005mm",
    "status": "QUALIFIED"
  },
  "nextCalibrationDate": "2024-08-01",
  "certificate": "/documents/cal_cert_001.pdf"
}
```

### 6.3 不合格品管理接口

#### 6.3.1 不合格品识别接口
```javascript
// 创建不合格品记录
POST /api/quality/nonconforming-products
{
  "productId": "prod_001",
  "batchId": "batch_001",
  "orderId": "order_001",
  "inspectionId": "exec_001",
  "detectedDate": "2024-02-01",
  "detectedBy": "inspector_001",
  "detectionStage": "FINAL",
  "quantity": 2,
  "defectCategory": "DIMENSION_DEFECT",
  "defectDescription": "厚度超出规格要求，测量值6.25mm，标准要求6.0±0.2mm",
  "severityLevel": "MAJOR",
  "rootCause": "初步分析：生产参数设置偏差",
  "disposition": "REWORK",
  "dispositionReason": "厚度可通过重新加工调整",
  "responsibleDept": "生产部",
  "images": ["/images/ncr/ncr_001_01.jpg"]
}

// 响应
{
  "code": 200,
  "message": "不合格品记录创建成功",
  "data": {
    "ncrId": "ncr_001",
    "ncrNumber": "NCR-2024-001",
    "status": "IDENTIFIED",
    "estimatedCost": 500.00
  }
}

// 查询不合格品列表
GET /api/quality/nonconforming-products?status=IDENTIFIED&dateFrom=2024-02-01&dateTo=2024-02-07
{
  "code": 200,
  "data": {
    "total": 15,
    "records": [
      {
        "ncrId": "ncr_001",
        "ncrNumber": "NCR-2024-001",
        "productName": "钢化玻璃6mm",
        "defectCategory": "DIMENSION_DEFECT",
        "severityLevel": "MAJOR",
        "quantity": 2,
        "status": "IDENTIFIED",
        "detectedDate": "2024-02-01"
      }
    ]
  }
}
```

#### 6.3.2 不合格品处理接口
```javascript
// 制定处理方案
PUT /api/quality/nonconforming-products/{ncrId}/disposition
{
  "disposition": "REWORK",
  "dispositionReason": "通过重新切割可以达到规格要求",
  "processingPlan": {
    "method": "重新切割加工",
    "estimatedTime": "2小时",
    "estimatedCost": 200.00,
    "responsiblePerson": "technician_001",
    "targetDate": "2024-02-02"
  },
  "qualityRequirements": "重新加工后需要全检验证"
}

// 执行处理过程
POST /api/quality/nonconforming-products/{ncrId}/processing
{
  "processStep": "REWORK_START",
  "processedBy": "technician_001",
  "processedAt": "2024-02-02T09:00:00",
  "processDetails": "开始重新切割加工",
  "actualCost": 180.00,
  "remarks": "加工过程顺利"
}

// 验证处理结果
POST /api/quality/nonconforming-products/{ncrId}/verification
{
  "verificationMethod": "FULL_INSPECTION",
  "verifiedBy": "inspector_002",
  "verificationDate": "2024-02-02",
  "verificationResults": [
    {
      "itemName": "厚度",
      "measuredValue": 6.0,
      "result": "PASS"
    }
  ],
  "overallResult": "QUALIFIED",
  "remarks": "重新加工后产品符合质量要求"
}
```

#### 6.3.3 纠正预防措施接口
```javascript
// 创建纠正措施
POST /api/quality/corrective-actions
{
  "ncrId": "ncr_001",
  "actionType": "CORRECTIVE",
  "actionDescription": "调整切割设备参数，加强操作人员培训",
  "rootCauseAnalysis": "通过5Why分析发现：设备参数设置不当，操作人员对新标准理解不足",
  "responsiblePerson": "engineer_001",
  "targetDate": "2024-02-10",
  "actionSteps": [
    {
      "step": 1,
      "description": "重新校准切割设备",
      "responsible": "maintenance_001",
      "targetDate": "2024-02-05"
    },
    {
      "step": 2,
      "description": "组织操作人员培训",
      "responsible": "trainer_001",
      "targetDate": "2024-02-08"
    }
  ]
}

// 跟踪措施执行
PUT /api/quality/corrective-actions/{actionId}/progress
{
  "status": "IN_PROGRESS",
  "completedSteps": [1],
  "progressDescription": "设备校准已完成，培训计划制定中",
  "actualCost": 1500.00,
  "nextMilestone": "完成人员培训",
  "updatedBy": "engineer_001"
}

// 验证措施有效性
POST /api/quality/corrective-actions/{actionId}/effectiveness
{
  "verificationDate": "2024-02-15",
  "verificationMethod": "统计分析近期生产数据",
  "effectivenessResult": "EFFECTIVE",
  "evidenceDescription": "措施实施后，尺寸超差率从5%降低到0.5%",
  "verifiedBy": "quality_manager_001",
  "followUpActions": "继续监控3个月，确保改进效果持续"
}
```

### 6.4 质量数据分析接口

#### 6.4.1 质量统计分析接口
```javascript
// 获取质量统计数据
GET /api/quality/statistics?period=MONTHLY&year=2024&month=2&productId=prod_001
{
  "code": 200,
  "data": {
    "period": "2024-02",
    "productId": "prod_001",
    "productName": "钢化玻璃6mm",
    "totalInspected": 1000,
    "totalPassed": 985,
    "totalFailed": 15,
    "passRate": 98.5,
    "defectBreakdown": [
      {
        "defectType": "DIMENSION_DEFECT",
        "count": 8,
        "percentage": 53.3
      },
      {
        "defectType": "APPEARANCE_DEFECT",
        "count": 7,
        "percentage": 46.7
      }
    ],
    "qualityCost": {
      "preventionCost": 5000.00,
      "appraisalCost": 8000.00,
      "internalFailureCost": 3000.00,
      "externalFailureCost": 1000.00,
      "totalCost": 17000.00
    }
  }
}

// 质量趋势分析
GET /api/quality/trends?metric=PASS_RATE&period=DAILY&dateFrom=2024-02-01&dateTo=2024-02-29
{
  "code": 200,
  "data": {
    "metric": "PASS_RATE",
    "trendData": [
      {
        "date": "2024-02-01",
        "value": 98.2,
        "target": 99.0
      },
      {
        "date": "2024-02-02",
        "value": 98.8,
        "target": 99.0
      }
    ],
    "trendAnalysis": {
      "direction": "IMPROVING",
      "changeRate": 0.3,
      "significance": "MODERATE",
      "forecast": [
        {
          "date": "2024-03-01",
          "predictedValue": 99.1,
          "confidence": 0.85
        }
      ]
    }
  }
}

// 过程能力分析
POST /api/quality/process-capability
{
  "processId": "process_cutting",
  "characteristicId": "thickness",
  "analysisStartDate": "2024-02-01",
  "analysisEndDate": "2024-02-29",
  "specificationLower": 5.8,
  "specificationUpper": 6.2,
  "targetValue": 6.0
}

// 响应
{
  "code": 200,
  "data": {
    "analysisId": "pca_001",
    "sampleSize": 500,
    "processMean": 6.01,
    "processStd": 0.05,
    "cpValue": 1.33,
    "cpkValue": 1.27,
    "ppValue": 1.30,
    "ppkValue": 1.25,
    "capabilityLevel": "GOOD",
    "improvementSuggestions": [
      "减少过程变异，提高设备精度",
      "加强过程监控，及时调整参数"
    ]
  }
}
```

#### 6.4.2 质量报表生成接口
```javascript
// 生成质量报表
POST /api/quality/reports/generate
{
  "reportType": "MONTHLY_QUALITY_REPORT",
  "reportPeriod": {
    "year": 2024,
    "month": 2
  },
  "scope": {
    "departments": ["production", "quality"],
    "products": ["prod_001", "prod_002"],
    "includeSuppliers": true,
    "includeCustomers": true
  },
  "format": "PDF",
  "language": "zh-CN"
}

// 响应
{
  "code": 200,
  "message": "报表生成中",
  "data": {
    "reportId": "rpt_001",
    "status": "GENERATING",
    "estimatedTime": "5分钟",
    "downloadUrl": null
  }
}

// 查询报表状态
GET /api/quality/reports/{reportId}/status
{
  "code": 200,
  "data": {
    "reportId": "rpt_001",
    "status": "COMPLETED",
    "generatedAt": "2024-02-01T10:30:00",
    "downloadUrl": "/downloads/reports/monthly_quality_2024_02.pdf",
    "fileSize": "2.5MB",
    "validUntil": "2024-02-08T10:30:00"
  }
}

// 获取报表列表
GET /api/quality/reports?type=MONTHLY_QUALITY_REPORT&year=2024
{
  "code": 200,
  "data": {
    "total": 12,
    "records": [
      {
        "reportId": "rpt_001",
        "reportName": "2024年2月质量月报",
        "reportType": "MONTHLY_QUALITY_REPORT",
        "generatedAt": "2024-02-01T10:30:00",
        "status": "COMPLETED",
        "downloadUrl": "/downloads/reports/monthly_quality_2024_02.pdf"
      }
    ]
  }
}
```

### 6.5 SPC统计控制接口

#### 6.5.1 控制图管理接口
```javascript
// 创建控制图
POST /api/quality/spc/control-charts
{
  "chartName": "厚度控制图",
  "chartType": "X_R_CHART",
  "processId": "process_cutting",
  "characteristicId": "thickness",
  "subgroupSize": 5,
  "controlLimits": {
    "calculateMethod": "AUTO",
    "historicalPeriod": 30
  },
  "updateFrequency": "REAL_TIME",
  "alertRules": [
    "POINT_BEYOND_LIMITS",
    "SEVEN_POINTS_TREND",
    "TWO_OF_THREE_BEYOND_2SIGMA"
  ]
}

// 响应
{
  "code": 200,
  "message": "控制图创建成功",
  "data": {
    "chartId": "chart_001",
    "chartName": "厚度控制图",
    "status": "ACTIVE",
    "controlLimits": {
      "xBarUCL": 6.15,
      "xBarCL": 6.0,
      "xBarLCL": 5.85,
      "rUCL": 0.3,
      "rCL": 0.15,
      "rLCL": 0
    }
  }
}

// 添加控制图数据
POST /api/quality/spc/control-charts/{chartId}/data
{
  "sampleGroup": "SG-2024-02-01-001",
  "sampleTime": "2024-02-01T09:00:00",
  "measurementValues": [6.02, 5.98, 6.01, 6.03, 5.99],
  "operatorId": "operator_001",
  "equipmentId": "eq_001"
}

// 响应
{
  "code": 200,
  "data": {
    "dataId": "data_001",
    "sampleMean": 6.006,
    "sampleRange": 0.05,
    "isOutOfControl": false,
    "violationRules": [],
    "alerts": []
  }
}

// 获取控制图数据
GET /api/quality/spc/control-charts/{chartId}/data?dateFrom=2024-02-01&dateTo=2024-02-07
{
  "code": 200,
  "data": {
    "chartId": "chart_001",
    "chartType": "X_R_CHART",
    "controlLimits": {...},
    "dataPoints": [
      {
        "sampleGroup": "SG-2024-02-01-001",
        "sampleTime": "2024-02-01T09:00:00",
        "sampleMean": 6.006,
        "sampleRange": 0.05,
        "isOutOfControl": false
      }
    ],
    "alerts": [
      {
        "alertTime": "2024-02-01T15:30:00",
        "alertType": "POINT_BEYOND_LIMITS",
        "description": "样本均值超出控制上限",
        "severity": "HIGH"
      }
    ]
  }
}
```

#### 6.5.2 异常模式检测接口
```javascript
// 检测异常模式
POST /api/quality/spc/pattern-detection
{
  "chartId": "chart_001",
  "detectionRules": [
    "SEVEN_POINTS_TREND",
    "FOURTEEN_POINTS_ALTERNATING",
    "TWO_OF_THREE_BEYOND_2SIGMA",
    "FOUR_OF_FIVE_BEYOND_1SIGMA"
  ],
  "analysisWindow": 25
}

// 响应
{
  "code": 200,
  "data": {
    "detectionResults": [
      {
        "ruleName": "SEVEN_POINTS_TREND",
        "detected": true,
        "startPoint": 15,
        "endPoint": 21,
        "description": "连续7个点呈上升趋势",
        "severity": "MEDIUM",
        "recommendedAction": "检查过程是否存在系统性变化"
      }
    ],
    "overallStatus": "ABNORMAL",
    "riskLevel": "MEDIUM"
  }
}

// 获取过程改进建议
GET /api/quality/spc/improvement-suggestions/{chartId}
{
  "code": 200,
  "data": {
    "processId": "process_cutting",
    "currentCapability": {
      "cpk": 1.27,
      "level": "GOOD"
    },
    "suggestions": [
      {
        "category": "PROCESS_OPTIMIZATION",
        "priority": "HIGH",
        "description": "优化切割参数设置",
        "expectedImprovement": "Cpk提升至1.5以上",
        "implementationCost": "中等"
      },
      {
        "category": "EQUIPMENT_MAINTENANCE",
        "priority": "MEDIUM",
        "description": "加强设备维护保养",
        "expectedImprovement": "减少过程变异20%",
        "implementationCost": "低"
      }
    ]
  }
}
```

### 6.6 供应商质量管理接口

#### 6.6.1 供应商质量评估接口
```javascript
// 创建供应商质量评估
POST /api/quality/supplier-assessments
{
  "supplierId": "supplier_001",
  "assessmentPeriodStart": "2024-01-01",
  "assessmentPeriodEnd": "2024-01-31",
  "assessmentType": "PERIODIC",
  "assessmentCriteria": {
    "incomingQuality": {
      "weight": 40,
      "metrics": ["pass_rate", "defect_rate", "consistency"]
    },
    "deliveryPerformance": {
      "weight": 30,
      "metrics": ["on_time_delivery", "quantity_accuracy"]
    },
    "responseCapability": {
      "weight": 20,
      "metrics": ["issue_response_time", "improvement_speed"]
    },
    "systemCapability": {
      "weight": 10,
      "metrics": ["quality_system", "documentation"]
    }
  }
}

// 响应
{
  "code": 200,
  "message": "供应商质量评估创建成功",
  "data": {
    "assessmentId": "assess_001",
    "supplierId": "supplier_001",
    "status": "IN_PROGRESS",
    "expectedCompletionDate": "2024-02-05"
  }
}

// 提交评估结果
PUT /api/quality/supplier-assessments/{assessmentId}/results
{
  "incomingPassRate": 98.5,
  "deliveryQualityScore": 95.0,
  "responseTimeScore": 88.0,
  "improvementScore": 92.0,
  "overallScore": 93.4,
  "qualityLevel": "GOOD",
  "majorIssues": [
    "个别批次存在包装不当问题",
    "质量问题响应速度需要提升"
  ],
  "improvementPlans": [
    {
      "issue": "包装质量",
      "plan": "制定包装标准，加强包装检查",
      "targetDate": "2024-03-01",
      "responsible": "supplier_quality_manager"
    }
  ],
  "nextAssessmentDate": "2024-04-01"
}

// 查询供应商质量排名
GET /api/quality/supplier-rankings?period=QUARTERLY&year=2024&quarter=1
{
  "code": 200,
  "data": {
    "period": "2024-Q1",
    "rankings": [
      {
        "rank": 1,
        "supplierId": "supplier_001",
        "supplierName": "优质玻璃原料公司",
        "overallScore": 95.2,
        "qualityLevel": "EXCELLENT",
        "passRate": 99.2,
        "improvementTrend": "STABLE"
      },
      {
        "rank": 2,
        "supplierId": "supplier_002",
        "supplierName": "标准玻璃供应商",
        "overallScore": 88.7,
        "qualityLevel": "GOOD",
        "passRate": 97.8,
        "improvementTrend": "IMPROVING"
      }
    ]
  }
}
```

#### 6.6.2 供应商质量问题管理接口
```javascript
// 创建供应商质量问题
POST /api/quality/supplier-issues
{
  "supplierId": "supplier_001",
  "materialId": "material_001",
  "batchNumber": "BATCH-2024-001",
  "issueDate": "2024-02-01",
  "issueType": "QUALITY_DEFECT",
  "severity": "MAJOR",
  "description": "来料气泡超标，影响产品外观质量",
  "quantityAffected": 100,
  "costImpact": 5000.00,
  "evidenceImages": ["/images/issues/issue_001_01.jpg"],
  "immediateActions": "隔离问题批次，通知供应商停止发货"
}

// 响应
{
  "code": 200,
  "message": "供应商质量问题创建成功",
  "data": {
    "issueId": "issue_001",
    "issueNumber": "SQI-2024-001",
    "status": "OPEN",
    "assignedTo": "supplier_quality_engineer_001"
  }
}

// 供应商响应问题
PUT /api/quality/supplier-issues/{issueId}/supplier-response
{
  "supplierResponse": "已确认问题原因为生产参数调整不当，立即采取纠正措施",
  "rootCauseAnalysis": "熔炉温度控制偏差导致气泡生成增加",
  "correctiveActions": [
    {
      "action": "重新校准熔炉温度控制系统",
      "responsible": "设备工程师",
      "targetDate": "2024-02-03"
    },
    {
      "action": "加强过程监控，增加检验频次",
      "responsible": "质量主管",
      "targetDate": "2024-02-02"
    }
  ],
  "preventiveActions": [
    {
      "action": "建立温度监控预警系统",
      "responsible": "技术部",
      "targetDate": "2024-02-15"
    }
  ],
  "compensationOffer": {
    "type": "REPLACEMENT",
    "description": "免费更换问题批次产品",
    "value": 5000.00
  }
}

// 跟踪问题解决进度
GET /api/quality/supplier-issues/{issueId}/progress
{
  "code": 200,
  "data": {
    "issueId": "issue_001",
    "issueNumber": "SQI-2024-001",
    "status": "IN_PROGRESS",
    "progressTimeline": [
      {
        "date": "2024-02-01",
        "event": "问题识别和报告",
        "responsible": "质检员"
      },
      {
        "date": "2024-02-02",
        "event": "供应商确认问题并制定措施",
        "responsible": "供应商"
      },
      {
        "date": "2024-02-03",
        "event": "纠正措施实施完成",
        "responsible": "供应商",
        "status": "COMPLETED"
      }
    ],
    "nextMilestone": {
      "event": "验证纠正措施有效性",
      "targetDate": "2024-02-10",
      "responsible": "质量工程师"
    }
  }
}
```

### 6.7 客户质量服务接口

#### 6.7.1 客户投诉管理接口
```javascript
// 创建客户投诉
POST /api/quality/customer-complaints
{
  "customerId": "customer_001",
  "productId": "prod_001",
  "orderId": "order_001",
  "batchId": "batch_001",
  "complaintDate": "2024-02-01",
  "complaintChannel": "PHONE",
  "complaintType": "QUALITY_DEFECT",
  "severity": "HIGH",
  "description": "收到的钢化玻璃存在明显划痕，影响使用",
  "customerExpectation": "更换合格产品，并要求质量保证",
  "quantityAffected": 50,
  "financialImpact": 10000.00,
  "customerContact": {
    "contactPerson": "张经理",
    "phone": "13800138000",
    "email": "<EMAIL>"
  },
  "urgencyLevel": "HIGH"
}

// 响应
{
  "code": 200,
  "message": "客户投诉记录创建成功",
  "data": {
    "complaintId": "complaint_001",
    "complaintNumber": "CC-2024-001",
    "status": "RECEIVED",
    "assignedTo": "customer_service_001",
    "responseDeadline": "2024-02-02T18:00:00"
  }
}

// 处理投诉进展
PUT /api/quality/customer-complaints/{complaintId}/progress
{
  "status": "INVESTIGATING",
  "progressDescription": "已派遣质量工程师现场调查，初步确认产品确实存在质量问题",
  "investigationFindings": [
    "产品表面确实存在划痕",
    "划痕可能在运输过程中产生",
    "包装保护不够充分"
  ],
  "proposedSolution": {
    "solutionType": "REPLACEMENT_AND_COMPENSATION",
    "description": "免费更换全部产品，并承担相关损失",
    "timeline": "3个工作日内完成更换",
    "compensation": 2000.00
  },
  "nextActions": [
    "与客户确认解决方案",
    "安排产品更换",
    "改进包装流程"
  ]
}

// 客户满意度反馈
POST /api/quality/customer-complaints/{complaintId}/satisfaction
{
  "customerSatisfaction": "SATISFIED",
  "satisfactionScore": 4,
  "customerFeedback": "处理及时，态度良好，解决方案合理",
  "improvementSuggestions": "希望加强产品包装保护",
  "wouldRecommend": true,
  "followUpRequired": false
}
```

#### 6.7.2 客户满意度管理接口
```javascript
// 创建满意度调查
POST /api/quality/customer-satisfaction-surveys
{
  "customerId": "customer_001",
  "surveyType": "REGULAR",
  "surveyDate": "2024-02-01",
  "surveyPeriod": {
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
  },
  "surveyQuestions": [
    {
      "questionId": "q1",
      "question": "产品质量满意度",
      "type": "RATING",
      "scale": "1-10"
    },
    {
      "questionId": "q2",
      "question": "交付及时性满意度",
      "type": "RATING",
      "scale": "1-10"
    },
    {
      "questionId": "q3",
      "question": "服务质量满意度",
      "type": "RATING",
      "scale": "1-10"
    }
  ],
  "surveyMethod": "ONLINE"
}

// 提交调查结果
PUT /api/quality/customer-satisfaction-surveys/{surveyId}/results
{
  "productQualityScore": 8.5,
  "deliveryQualityScore": 9.0,
  "serviceQualityScore": 8.0,
  "responseSpeedScore": 8.5,
  "overallSatisfaction": 8.5,
  "strengths": [
    "产品质量稳定可靠",
    "交付准时",
    "服务响应及时"
  ],
  "improvementSuggestions": [
    "希望进一步提升产品包装质量",
    "增加产品规格选择"
  ],
  "wouldRecommend": true,
  "additionalComments": "总体合作愉快，希望继续保持良好合作关系"
}

// 获取满意度趋势
GET /api/quality/customer-satisfaction/trends?customerId=customer_001&period=QUARTERLY&year=2024
{
  "code": 200,
  "data": {
    "customerId": "customer_001",
    "customerName": "重要客户A",
    "trendData": [
      {
        "period": "2024-Q1",
        "overallSatisfaction": 8.5,
        "productQuality": 8.5,
        "deliveryQuality": 9.0,
        "serviceQuality": 8.0,
        "responseSpeed": 8.5
      }
    ],
    "trendAnalysis": {
      "direction": "STABLE",
      "keyInsights": [
        "客户对交付质量最为满意",
        "服务质量有提升空间",
        "整体满意度保持稳定"
      ],
      "riskAreas": [],
      "improvementOpportunities": [
        "提升服务响应专业性",
        "增加主动沟通频次"
      ]
    }
  }
}
```

## 7. 系统集成接口

### 7.1 与生产管理子系统的接口依赖

#### 7.1.1 生产质量数据同步
- **接口名称**：`syncProductionQualityData`, `reportQualityStatus`
- **数据流向**：生产管理 -> 质量管理
- **同步内容**：生产批次信息、工序完成状态、在线检测数据、质量控制点数据
- **业务规则**：生产过程中的质量数据实时同步，异常质量状态立即报警
- **同步频率**：实时同步

#### 7.1.2 质量检验计划协调
- **接口名称**：`createInspectionPlan`, `updateProductionSchedule`
- **数据流向**：质量管理 <-> 生产管理
- **同步内容**：检验计划安排、生产进度调整、质量检验结果、生产放行状态
- **业务规则**：检验计划与生产计划协调一致，质量不合格时暂停生产
- **同步频率**：计划变更时实时同步

#### 7.1.3 工艺质量标准同步
- **接口名称**：`syncProcessQualityStandards`, `updateQualityControlPoints`
- **数据流向**：质量管理 -> 生产管理
- **同步内容**：工艺质量标准、质量控制点设置、检验方法、判定标准
- **业务规则**：质量标准变更时自动更新生产系统的质量控制参数
- **同步频率**：标准变更时实时同步

### 7.2 与工艺管理子系统的接口依赖

#### 7.2.1 工艺质量标准制定
- **接口名称**：`getProcessParameters`, `setQualityControlPoints`
- **数据流向**：工艺管理 -> 质量管理
- **同步内容**：工艺参数范围、关键控制点、工艺质量要求、检验频次设置
- **业务规则**：基于工艺参数制定相应的质量检验标准和控制要求
- **同步频率**：工艺变更时同步

#### 7.2.2 质量问题工艺改进
- **接口名称**：`reportQualityIssues`, `optimizeProcessParameters`
- **数据流向**：质量管理 -> 工艺管理
- **反馈内容**：质量问题分析、工艺参数偏差、改进建议、优化方向
- **业务规则**：质量问题追溯到工艺参数，协同制定工艺改进措施
- **同步频率**：问题发现时立即反馈

### 7.3 与仓储管理子系统的接口依赖

#### 7.3.1 入库质量检验
- **接口名称**：`requestIncomingInspection`, `reportInspectionResult`
- **数据流向**：仓储管理 <-> 质量管理
- **同步内容**：入库检验申请、物料信息、检验结果、合格证明、隔离指令
- **业务规则**：所有入库物料必须经过质量检验，不合格物料自动隔离
- **同步频率**：入库时实时检验

#### 7.3.2 库存质量状态管理
- **接口名称**：`updateInventoryQualityStatus`, `queryQualityTraceability`
- **数据流向**：质量管理 -> 仓储管理
- **同步内容**：库存质量状态、批次质量信息、追溯数据、保质期管理
- **业务规则**：质量状态变更时同步更新库存状态，支持质量追溯查询
- **同步频率**：状态变更时实时同步

### 7.4 与销售管理子系统的接口依赖

#### 7.4.1 客户质量要求管理
- **接口名称**：`getCustomerQualityRequirements`, `validateQualityCompliance`
- **数据流向**：销售管理 -> 质量管理
- **同步内容**：客户质量标准、特殊要求、验收标准、质量协议条款
- **业务规则**：根据客户要求制定相应的质量标准和检验方案
- **同步频率**：订单确认时同步

#### 7.4.2 出货质量保证
- **接口名称**：`requestShipmentInspection`, `issueQualityCertificate`
- **数据流向**：质量管理 -> 销售管理
- **同步内容**：出货检验结果、质量证书、合格证明、质量保证书
- **业务规则**：出货前必须完成质量检验，提供相应的质量证明文件
- **同步频率**：出货前实时检验

### 7.5 与采购管理子系统的接口依赖

#### 7.5.1 供应商质量评估
- **接口名称**：`evaluateSupplierQuality`, `updateSupplierRating`
- **数据流向**：质量管理 -> 采购管理
- **同步内容**：供应商质量评估结果、质量等级、改进建议、合作建议
- **业务规则**：质量评估结果影响供应商选择和采购决策
- **同步频率**：评估完成时同步

#### 7.5.2 采购质量要求
- **接口名称**：`setProcurementQualityRequirements`, `validateSupplierCapability`
- **数据流向**：质量管理 -> 采购管理
- **同步内容**：采购质量标准、检验要求、供应商质量能力、质量协议模板
- **业务规则**：采购合同必须包含明确的质量要求和验收标准
- **同步频率**：采购计划制定时同步

### 7.6 与财务管理子系统的接口依赖

#### 7.6.1 质量成本核算
- **接口名称**：`calculateQualityCosts`, `allocateQualityExpenses`
- **数据流向**：质量管理 -> 财务管理
- **同步内容**：质量成本数据、不合格品损失、返工费用、检验费用
- **业务规则**：质量相关费用按照成本中心和产品进行分摊核算
- **同步频率**：每日汇总同步

#### 7.6.2 质量损失统计
- **接口名称**：`reportQualityLosses`, `calculateQualityROI`
- **数据流向**：质量管理 -> 财务管理
- **同步内容**：质量损失统计、客户赔偿、质量改进投资、投资回报分析
- **业务规则**：质量损失和改进投资纳入财务分析和决策支持
- **同步频率**：月度汇总分析

### 7.7 技术规范要求

#### 7.7.1 接口协议标准
- **通信协议**：HTTP/HTTPS、WebSocket（实时数据）
- **数据格式**：JSON、XML（兼容性考虑）
- **认证方式**：OAuth 2.0、JWT Token
- **API版本管理**：RESTful API v1.0，向后兼容

#### 7.7.2 接口性能要求
- **响应时间**：同步接口≤3秒，异步接口≤5秒，查询接口≤2秒
- **并发处理**：支持并发接口调用≥500个，支持并发用户≥200个
- **数据吞吐量**：支持每秒处理质量数据≥1000条
- **可用性**：接口可用率≥99.9%，故障恢复时间≤5分钟

#### 7.7.3 接口安全要求
- **数据加密**：传输数据TLS 1.3加密，敏感数据AES-256加密
- **访问控制**：基于角色的接口访问控制，API调用频率限制
- **审计日志**：完整记录接口调用日志，异常访问实时告警
- **数据完整性**：数据传输校验和纠错，防止数据篡改

#### 7.7.4 接口监控要求
- **性能监控**：接口响应时间、调用成功率、系统资源使用率
- **业务监控**：数据同步状态、业务异常情况、关键指标监控
- **告警机制**：多级告警阈值、多渠道告警通知、告警升级机制
- **日志管理**：结构化日志记录、日志集中存储、日志分析查询

## 8. 非功能性需求

### 8.1 性能需求

#### 8.1.1 响应时间要求
- **用户界面响应**：
  - 页面加载时间≤3秒
  - 表单提交响应≤2秒
  - 查询操作响应≤5秒
  - 报表生成≤30秒（复杂报表）
- **数据处理性能**：
  - 检验数据录入响应≤1秒
  - 质量统计计算≤10秒
  - SPC控制图更新≤5秒
  - 批量数据导入≤60秒/1000条

#### 8.1.2 并发处理能力
- **用户并发**：支持200个用户同时在线操作
- **数据并发**：支持50个并发检验任务执行
- **接口并发**：支持500个并发API调用
- **设备并发**：支持100台检测设备同时接入

#### 8.1.3 数据处理能力
- **数据存储**：支持10年历史质量数据存储
- **数据查询**：支持千万级记录快速查询
- **实时处理**：支持1000个质量数据点实时处理
- **批量处理**：支持10万条检验数据批量分析

### 8.2 可靠性需求

#### 8.2.1 系统可用性
- **可用性指标**：系统可用性≥99.9%
- **故障恢复**：系统故障恢复时间≤15分钟
- **数据备份**：每日自动备份，异地备份保存
- **容灾能力**：支持双机热备，自动故障切换

#### 8.2.2 数据可靠性
- **数据完整性**：质量数据完整性≥99.99%
- **数据一致性**：分布式数据最终一致性保证
- **数据恢复**：支持数据误删除恢复，恢复时间≤1小时
- **数据校验**：关键数据多重校验，异常数据自动标识

#### 8.2.3 业务连续性
- **业务中断**：计划停机时间≤2小时/月
- **关键功能**：核心质量检验功能7×24小时可用
- **应急处理**：质量异常应急响应时间≤5分钟
- **业务恢复**：业务中断后恢复时间≤30分钟

### 8.3 安全性需求

#### 8.3.1 身份认证安全
- **用户认证**：支持多因子认证，密码复杂度要求
- **会话管理**：会话超时控制，异常登录检测
- **权限控制**：基于角色的细粒度权限控制
- **审计追踪**：完整的用户操作审计日志

#### 8.3.2 数据安全保护
- **数据加密**：敏感数据存储加密，传输加密
- **数据脱敏**：非生产环境数据脱敏处理
- **访问控制**：数据访问权限严格控制
- **数据防泄露**：数据下载和导出权限控制

#### 8.3.3 系统安全防护
- **网络安全**：防火墙保护，入侵检测系统
- **应用安全**：SQL注入防护，XSS攻击防护
- **病毒防护**：文件上传病毒扫描
- **安全监控**：安全事件实时监控和告警

### 8.4 易用性需求

#### 8.4.1 用户界面友好性
- **界面设计**：简洁直观的用户界面设计
- **操作便捷**：常用功能快捷访问，操作步骤简化
- **响应式设计**：支持PC、平板、手机多终端访问
- **个性化配置**：支持用户个性化界面配置

#### 8.4.2 系统易学性
- **操作指导**：在线帮助文档，操作向导功能
- **培训支持**：用户培训材料，视频教程
- **错误提示**：友好的错误提示信息
- **快速上手**：新用户30分钟内掌握基本操作

#### 8.4.3 国际化支持
- **多语言**：支持中文、英文界面
- **本地化**：支持本地化日期、时间、数字格式
- **字符编码**：支持UTF-8字符编码
- **文化适应**：界面设计符合本地文化习惯

### 8.5 扩展性需求

#### 8.5.1 功能扩展性
- **模块化设计**：采用模块化架构，支持功能模块独立扩展
- **插件机制**：支持第三方插件集成
- **API开放**：提供开放API接口，支持二次开发
- **配置灵活性**：支持业务规则和流程配置

#### 8.5.2 技术扩展性
- **架构扩展**：支持微服务架构，水平扩展能力
- **数据库扩展**：支持数据库集群，读写分离
- **缓存扩展**：支持分布式缓存，提升性能
- **负载均衡**：支持负载均衡，提高并发处理能力

#### 8.5.3 集成扩展性
- **系统集成**：支持与其他业务系统集成
- **设备集成**：支持新型检测设备接入
- **标准扩展**：支持新的质量标准和检验方法
- **数据格式**：支持多种数据格式导入导出

### 8.6 兼容性需求

#### 8.6.1 浏览器兼容性
- **主流浏览器**：支持Chrome、Firefox、Safari、Edge
- **版本兼容**：支持主流浏览器近3个版本
- **移动浏览器**：支持移动设备浏览器访问
- **兼容性测试**：定期进行兼容性测试

#### 8.6.2 操作系统兼容性
- **服务器系统**：支持Linux、Windows Server
- **客户端系统**：支持Windows、macOS、iOS、Android
- **数据库系统**：支持MySQL、PostgreSQL、Oracle
- **中间件**：支持主流应用服务器和中间件

#### 8.6.3 设备兼容性
- **检测设备**：支持主流质量检测设备接入
- **通信协议**：支持多种设备通信协议
- **数据格式**：支持多种设备数据格式
- **驱动程序**：提供设备驱动程序和接口

## 9. Product Backlog

### 9.1 Epic划分

#### 9.1.1 史诗1：质量标准管理体系
- **史诗描述**：建立完整的质量标准管理体系，支持国际标准、行业标准和企业标准的管理
- **业务价值**：为质量管理提供标准化基础，确保质量要求的一致性和可追溯性
- **验收标准**：
  - 支持多层级质量标准管理
  - 标准版本控制和变更管理
  - 检验项目和判定规则配置
  - 标准符合性验证
- **优先级**：高
- **预估工作量**：40人天

#### 9.1.2 史诗2：智能检验管理系统
- **史诗描述**：构建智能化的检验管理系统，支持检验计划、执行、数据采集和结果处理
- **业务价值**：提高检验效率和准确性，实现检验过程的标准化和自动化
- **验收标准**：
  - 检验计划自动生成和优化
  - 多种检验方式支持
  - 检验数据自动采集和处理
  - 检验结果智能判定
- **优先级**：高
- **预估工作量**：60人天

#### 9.1.3 史诗3：不合格品全生命周期管理
- **史诗描述**：建立不合格品从识别到处理完成的全生命周期管理体系
- **业务价值**：快速响应质量问题，减少质量损失，持续改进质量水平
- **验收标准**：
  - 不合格品快速识别和分类
  - 根本原因分析和纠正措施
  - 处理过程跟踪和效果验证
  - 质量改进经验积累
- **优先级**：高
- **预估工作量**：35人天

#### 9.1.4 史诗4：质量数据分析和SPC控制
- **史诗描述**：实现质量数据的深度分析和统计过程控制，提供质量改进决策支持
- **业务价值**：通过数据驱动的质量管理，实现质量预测和预防性控制
- **验收标准**：
  - 多维度质量统计分析
  - SPC控制图实时监控
  - 过程能力分析和改进建议
  - 质量趋势预测和预警
- **优先级**：中
- **预估工作量**：45人天

#### 9.1.5 史诗5：供应商和客户质量协同
- **史诗描述**：建立与供应商和客户的质量协同管理体系，实现全价值链质量管理
- **业务价值**：提升供应链质量水平，增强客户满意度，建立长期合作关系
- **验收标准**：
  - 供应商质量评估和改进
  - 客户质量要求管理
  - 质量问题协同解决
  - 质量服务持续优化
- **优先级**：中
- **预估工作量**：30人天

### 9.2 用户故事详细描述

#### 9.2.1 用户故事：质量标准制定和管理
- **角色**：质量工程师
- **目标**：能够制定和管理企业质量标准，确保标准的完整性和有效性
- **用户故事**：作为质量工程师，我希望能够制定和管理企业质量标准，以便为质量检验提供统一的标准依据
- **验收标准**：
  - 能够创建、编辑、删除质量标准
  - 支持标准版本控制和变更管理
  - 能够配置检验项目和判定规则
  - 支持标准审批发布流程
- **界面元素**：标准编辑器、版本管理组件、审批流程组件
- **交互动作**：创建标准、编辑内容、版本发布、审批操作
- **输入输出**：标准信息、检验项目、判定规则、审批意见
- **状态变化**：草稿→审核中→已发布→已废止
- **API交互**：createQualityStandard、updateStandard、publishStandard
- **权限控制**：质量工程师可编辑，质量总监可审批
- **优先级**：高
- **预估工作量**：8人天
- **归属史诗**：史诗1-质量标准管理体系

#### 9.2.2 用户故事：检验计划制定和执行
- **角色**：质检员、质量工程师
- **目标**：能够制定检验计划并执行检验任务，以便确保产品质量符合标准要求
- **用户故事**：作为质检员，我希望能够按照检验计划执行检验任务，以便及时发现和处理质量问题
- **验收标准**：
  - 能够创建和管理检验计划
  - 支持检验任务分配和调度
  - 能够记录检验数据和结果
  - 支持检验异常处理流程
- **界面元素**：计划管理组件、任务列表、数据录入表单、异常处理界面
- **交互动作**：创建计划、分配任务、录入数据、处理异常
- **输入输出**：检验计划、检验数据、检验结果、异常信息
- **状态变化**：计划中→执行中→已完成→已归档
- **API交互**：createInspectionPlan、executeInspection、recordData
- **权限控制**：质量工程师可制定计划，质检员可执行检验
- **优先级**：高
- **预估工作量**：12人天
- **归属史诗**：史诗2-智能检验管理系统

#### 9.2.3 用户故事：不合格品识别和处理
- **角色**：质检员、质量工程师、生产主管
- **目标**：能够快速识别不合格品并制定处理方案，以便减少质量损失和影响
- **用户故事**：作为质检员，我希望能够快速识别和记录不合格品，以便及时采取纠正措施
- **验收标准**：
  - 能够快速识别和分类不合格品
  - 支持不合格品隔离和标识
  - 能够制定和执行处理方案
  - 支持处理过程跟踪和验证
- **界面元素**：不合格品登记表单、处理方案制定界面、进度跟踪组件
- **交互动作**：识别登记、制定方案、执行处理、验证结果
- **输入输出**：不合格品信息、处理方案、处理结果、验证报告
- **状态变化**：已识别→处理中→已处理→已验证
- **API交互**：createNCR、createDisposition、trackProgress
- **权限控制**：质检员可识别，质量工程师可制定方案，生产主管可执行
- **优先级**：高
- **预估工作量**：10人天
- **归属史诗**：史诗3-不合格品全生命周期管理

#### 9.2.4 用户故事：质量数据统计分析
- **角色**：质量工程师、质量总监
- **目标**：能够对质量数据进行统计分析，以便发现质量趋势和改进机会
- **用户故事**：作为质量工程师，我希望能够对质量数据进行深度分析，以便为质量改进提供数据支持
- **验收标准**：
  - 能够生成各类质量统计报表
  - 支持质量趋势分析和预测
  - 能够进行过程能力分析
  - 支持质量成本分析
- **界面元素**：统计图表组件、趋势分析界面、能力分析工具、成本分析报表
- **交互动作**：选择分析维度、生成报表、查看趋势、导出数据
- **输入输出**：分析参数、统计结果、趋势图表、分析报告
- **状态变化**：分析中→已完成→已发布
- **API交互**：generateStatistics、analyzeTrends、calculateCapability
- **权限控制**：质量工程师可分析，质量总监可查看所有报表
- **优先级**：中
- **预估工作量**：15人天
- **归属史诗**：史诗4-质量数据分析和SPC控制

#### 9.2.5 用户故事：SPC统计过程控制
- **角色**：质量工程师、车间质检
- **目标**：能够实施SPC统计过程控制，以便实现过程质量的预防性控制
- **用户故事**：作为质量工程师，我希望能够建立SPC控制图，以便实时监控过程质量状态
- **验收标准**：
  - 能够创建和维护控制图
  - 支持实时数据更新和监控
  - 能够检测异常模式和趋势
  - 支持控制限重新计算
- **界面元素**：控制图组件、数据输入界面、异常告警组件、参数设置界面
- **交互动作**：创建控制图、输入数据、查看告警、调整参数
- **输入输出**：过程数据、控制图、异常告警、改进建议
- **状态变化**：受控→失控→调整中→重新受控
- **API交互**：createControlChart、addDataPoint、detectAnomalies
- **权限控制**：质量工程师可创建控制图，车间质检可输入数据
- **优先级**：中
- **预估工作量**：18人天
- **归属史诗**：史诗4-质量数据分析和SPC控制

#### 9.2.6 用户故事：供应商质量管理
- **角色**：供应商质量工程师、采购经理
- **目标**：能够管理供应商质量表现，以便提升供应链整体质量水平
- **用户故事**：作为供应商质量工程师，我希望能够评估和管理供应商质量，以便确保来料质量稳定
- **验收标准**：
  - 能够进行供应商质量评估
  - 支持供应商质量问题管理
  - 能够跟踪供应商改进措施
  - 支持供应商质量排名
- **界面元素**：评估表单、问题管理界面、改进跟踪组件、排名展示
- **交互动作**：创建评估、记录问题、跟踪改进、查看排名
- **输入输出**：评估数据、问题信息、改进计划、排名结果
- **状态变化**：评估中→已完成→改进中→已验证
- **API交互**：createAssessment、reportIssue、trackImprovement
- **权限控制**：供应商质量工程师可评估，采购经理可查看排名
- **优先级**：中
- **预估工作量**：12人天
- **归属史诗**：史诗5-供应商和客户质量协同

#### 9.2.7 用户故事：客户投诉处理
- **角色**：客户服务代表、质量工程师
- **目标**：能够及时处理客户质量投诉，以便维护客户关系和改进产品质量
- **用户故事**：作为客户服务代表，我希望能够快速响应客户投诉，以便及时解决客户质量问题
- **验收标准**：
  - 能够快速记录客户投诉
  - 支持投诉处理流程管理
  - 能够跟踪处理进度
  - 支持客户满意度反馈
- **界面元素**：投诉登记表单、处理流程界面、进度跟踪组件、满意度调查
- **交互动作**：登记投诉、分配处理、跟踪进度、收集反馈
- **输入输出**：投诉信息、处理方案、进度更新、满意度评价
- **状态变化**：已接收→处理中→已解决→已关闭
- **API交互**：createComplaint、assignHandler、trackProgress
- **权限控制**：客户服务代表可登记，质量工程师可处理
- **优先级**：中
- **预估工作量**：10人天
- **归属史诗**：史诗5-供应商和客户质量协同

#### 9.2.8 用户故事：质量证书管理
- **角色**：质量工程师、销售代表
- **目标**：能够生成和管理质量证书，以便为客户提供质量保证文件
- **用户故事**：作为质量工程师，我希望能够自动生成质量证书，以便为出货产品提供质量保证
- **验收标准**：
  - 能够自动生成质量证书
  - 支持证书模板管理
  - 能够进行证书真伪验证
  - 支持证书批量打印
- **界面元素**：证书生成界面、模板编辑器、验证组件、打印预览
- **交互动作**：生成证书、编辑模板、验证真伪、批量打印
- **输入输出**：产品信息、证书模板、质量证书、验证结果
- **状态变化**：生成中→已生成→已发放→已验证
- **API交互**：generateCertificate、validateCertificate、printBatch
- **权限控制**：质量工程师可生成，销售代表可查看
- **优先级**：低
- **预估工作量**：8人天
- **归属史诗**：史诗5-供应商和客户质量协同

#### 9.2.9 用户故事：移动端质量检验
- **角色**：车间质检、现场检验员
- **目标**：能够在移动设备上进行质量检验，以便提高检验效率和便利性
- **用户故事**：作为车间质检，我希望能够在手机上进行质量检验，以便在生产现场及时记录检验数据
- **验收标准**：
  - 能够在移动设备上执行检验任务
  - 支持离线数据录入和同步
  - 能够拍照记录质量问题
  - 支持语音输入和识别
- **界面元素**：移动检验界面、离线同步组件、拍照功能、语音输入
- **交互动作**：选择任务、录入数据、拍照记录、语音输入
- **输入输出**：检验任务、检验数据、照片、语音记录
- **状态变化**：离线录入→待同步→已同步→已确认
- **API交互**：syncOfflineData、uploadImages、processVoice
- **权限控制**：车间质检可使用移动端功能
- **优先级**：低
- **预估工作量**：15人天
- **归属史诗**：史诗2-智能检验管理系统

#### 9.2.10 用户故事：质量知识库管理
- **角色**：质量工程师、质量总监
- **目标**：能够建立和维护质量知识库，以便积累和分享质量管理经验
- **用户故事**：作为质量工程师，我希望能够建立质量知识库，以便积累质量问题解决经验和最佳实践
- **验收标准**：
  - 能够创建和维护知识条目
  - 支持知识分类和标签管理
  - 能够进行知识搜索和推荐
  - 支持知识评价和更新
- **界面元素**：知识编辑器、分类管理、搜索组件、评价系统
- **交互动作**：创建知识、分类管理、搜索查询、评价更新
- **输入输出**：知识内容、分类标签、搜索结果、评价反馈
- **状态变化**：草稿→审核中→已发布→已更新
- **API交互**：createKnowledge、searchKnowledge、rateKnowledge
- **权限控制**：质量工程师可创建，质量总监可审核
- **优先级**：低
- **预估工作量**：12人天
- **归属史诗**：史诗1-质量标准管理体系

### 9.3 Sprint规划建议

#### 9.3.1 Sprint 1（2周）- 质量标准管理基础
**目标**：建立质量标准管理的基础功能
**包含用户故事**：
- 用户故事9.2.1：质量标准制定和管理（8人天）
- 基础数据模型设计和实现（6人天）
**交付成果**：
- 质量标准CRUD功能
- 检验项目配置功能
- 标准版本管理功能
**验收标准**：能够创建、编辑、发布质量标准

#### 9.3.2 Sprint 2（2周）- 检验管理核心功能
**目标**：实现检验计划和执行的核心功能
**包含用户故事**：
- 用户故事9.2.2：检验计划制定和执行（12人天）
- 检验数据录入界面开发（2人天）
**交付成果**：
- 检验计划管理功能
- 检验任务执行功能
- 检验数据录入功能
**验收标准**：能够制定检验计划并执行检验任务

#### 9.3.3 Sprint 3（2周）- 不合格品管理
**目标**：建立完整的不合格品管理流程
**包含用户故事**：
- 用户故事9.2.3：不合格品识别和处理（10人天）
- 纠正预防措施管理（4人天）
**交付成果**：
- 不合格品识别功能
- 处理方案制定功能
- 处理过程跟踪功能
**验收标准**：能够完整处理不合格品从识别到验证的全流程

#### 9.3.4 Sprint 4（2周）- 质量数据分析
**目标**：实现质量数据的统计分析功能
**包含用户故事**：
- 用户故事9.2.4：质量数据统计分析（15人天）
**交付成果**：
- 质量统计报表功能
- 质量趋势分析功能
- 过程能力分析功能
**验收标准**：能够生成各类质量分析报表和图表

#### 9.3.5 Sprint 5（2周）- SPC统计控制
**目标**：实现SPC统计过程控制功能
**包含用户故事**：
- 用户故事9.2.5：SPC统计过程控制（18人天）
**交付成果**：
- 控制图创建和维护功能
- 实时数据监控功能
- 异常模式检测功能
**验收标准**：能够建立控制图并实时监控过程质量

#### 9.3.6 Sprint 6（2周）- 供应商质量管理
**目标**：建立供应商质量管理体系
**包含用户故事**：
- 用户故事9.2.6：供应商质量管理（12人天）
- 供应商评估报表开发（2人天）
**交付成果**：
- 供应商质量评估功能
- 供应商问题管理功能
- 供应商排名功能
**验收标准**：能够完整管理供应商质量表现

#### 9.3.7 Sprint 7（2周）- 客户质量服务
**目标**：建立客户质量服务体系
**包含用户故事**：
- 用户故事9.2.7：客户投诉处理（10人天）
- 用户故事9.2.8：质量证书管理（8人天）
**交付成果**：
- 客户投诉管理功能
- 质量证书生成功能
- 客户满意度管理功能
**验收标准**：能够及时处理客户投诉并生成质量证书

#### 9.3.8 Sprint 8（2周）- 移动端和知识库
**目标**：完善系统功能，提升用户体验
**包含用户故事**：
- 用户故事9.2.9：移动端质量检验（15人天）
- 用户故事9.2.10：质量知识库管理（12人天）
**交付成果**：
- 移动端检验应用
- 质量知识库系统
- 系统集成和优化
**验收标准**：移动端功能完整可用，知识库内容丰富

## 10. 验收标准

### 10.1 功能验收标准

#### 10.1.1 质量标准管理功能验收
- **标准创建功能**：
  - 能够创建各类质量标准，创建成功率≥99%
  - 支持标准模板导入，模板适配准确率≥95%
  - 标准信息完整性检查，必填项验证准确率100%
- **标准版本管理**：
  - 版本控制功能正常，版本号自动递增准确率100%
  - 版本对比功能完整，差异识别准确率≥98%
  - 版本发布流程规范，审批流程执行准确率100%
- **检验项目配置**：
  - 检验项目配置灵活，支持各类检验项目100%
  - 判定规则设置准确，规则执行准确率≥99%
  - 参数范围设置合理，参数验证准确率100%

#### 10.1.2 检验管理功能验收
- **检验计划管理**：
  - 检验计划创建成功率≥99%
  - 计划执行跟踪准确率≥98%
  - 资源分配优化合理性≥90%
- **检验执行管理**：
  - 检验任务分配准确率100%
  - 检验数据录入准确率≥99.5%
  - 检验结果判定准确率≥99%
- **在线检测集成**：
  - 设备数据采集成功率≥98%
  - 数据传输实时性≤3秒
  - 异常检测准确率≥95%

#### 10.1.3 不合格品管理功能验收
- **不合格品识别**：
  - 不合格品识别准确率≥99%
  - 问题分类准确率≥95%
  - 严重度评估准确率≥90%
- **处理流程管理**：
  - 处理方案制定完整率100%
  - 处理过程跟踪准确率≥98%
  - 处理效果验证准确率≥95%
- **纠正预防措施**：
  - 根本原因分析完整率≥90%
  - 纠正措施有效率≥85%
  - 预防措施实施率≥80%

#### 10.1.4 质量数据分析功能验收
- **统计分析功能**：
  - 统计计算准确率≥99.9%
  - 报表生成成功率≥99%
  - 数据可视化准确率≥98%
- **趋势分析功能**：
  - 趋势识别准确率≥90%
  - 预测精度≥80%
  - 异常检测准确率≥95%
- **过程能力分析**：
  - 能力指数计算准确率≥99.9%
  - 分析结果可靠性≥95%
  - 改进建议合理性≥85%

#### 10.1.5 SPC统计控制功能验收
- **控制图管理**：
  - 控制图创建成功率≥99%
  - 控制限计算准确率≥99.9%
  - 数据点绘制准确率≥99.5%
- **异常检测功能**：
  - 异常模式检测准确率≥95%
  - 告警触发及时性≤1分钟
  - 误报率≤5%
- **过程监控功能**：
  - 实时监控数据更新≤5秒
  - 监控状态准确率≥98%
  - 历史数据完整率100%

### 10.2 性能验收标准

#### 10.2.1 响应时间验收标准
- **页面加载性能**：
  - 首页加载时间≤3秒（95%分位数）
  - 列表页面加载时间≤2秒（95%分位数）
  - 详情页面加载时间≤2秒（90%分位数）
- **数据操作性能**：
  - 数据查询响应时间≤5秒（95%分位数）
  - 数据保存响应时间≤2秒（99%分位数）
  - 批量操作响应时间≤30秒/1000条（90%分位数）
- **报表生成性能**：
  - 简单报表生成时间≤10秒（95%分位数）
  - 复杂报表生成时间≤60秒（90%分位数）
  - 大数据量报表生成时间≤300秒（80%分位数）

#### 10.2.2 并发处理验收标准
- **用户并发能力**：
  - 支持200个用户同时在线操作
  - 支持50个用户同时执行检验任务
  - 支持100个用户同时查询数据
- **数据并发处理**：
  - 支持1000个并发数据录入操作
  - 支持500个并发API调用
  - 支持100台设备同时数据上传
- **系统资源利用**：
  - CPU利用率峰值≤80%
  - 内存利用率峰值≤85%
  - 数据库连接池利用率≤90%

#### 10.2.3 数据处理验收标准
- **数据存储能力**：
  - 支持10年历史数据存储
  - 支持千万级检验记录存储
  - 支持TB级质量数据存储
- **数据查询性能**：
  - 单表查询响应时间≤1秒
  - 多表关联查询响应时间≤5秒
  - 全文搜索响应时间≤3秒
- **数据分析性能**：
  - 实时统计计算≤10秒
  - 历史趋势分析≤30秒
  - 复杂分析计算≤120秒

### 10.3 安全验收标准

#### 10.3.1 身份认证安全验收
- **用户认证安全**：
  - 密码复杂度策略执行准确率100%
  - 登录失败锁定机制有效率100%
  - 异常登录检测准确率≥95%
- **会话管理安全**：
  - 会话超时控制准确率100%
  - Token安全性验证通过率100%
  - 并发会话控制有效率100%
- **权限控制安全**：
  - 权限验证准确率≥99.9%
  - 越权访问防护有效率100%
  - 数据权限过滤准确率100%

#### 10.3.2 数据安全验收标准
- **数据加密保护**：
  - 敏感数据加密覆盖率100%
  - 数据传输加密有效率100%
  - 数据存储加密完整率100%
- **数据访问控制**：
  - 数据访问权限控制准确率100%
  - 数据操作审计记录完整率100%
  - 数据脱敏处理准确率100%
- **数据备份恢复**：
  - 数据备份成功率≥99.9%
  - 数据恢复成功率≥99%
  - 备份数据完整性验证100%

#### 10.3.3 系统安全验收标准
- **网络安全防护**：
  - 防火墙规则配置正确率100%
  - 入侵检测系统有效率≥95%
  - 网络攻击防护成功率≥98%
- **应用安全防护**：
  - SQL注入防护有效率100%
  - XSS攻击防护有效率100%
  - CSRF攻击防护有效率100%
- **安全监控告警**：
  - 安全事件检测准确率≥95%
  - 安全告警响应时间≤5分钟
  - 安全日志记录完整率100%

### 10.4 可用性验收标准

#### 10.4.1 系统可用性验收
- **系统稳定性**：
  - 系统可用性≥99.9%
  - 平均故障间隔时间≥720小时
  - 平均故障恢复时间≤15分钟
- **服务连续性**：
  - 计划停机时间≤2小时/月
  - 核心功能7×24小时可用
  - 业务中断恢复时间≤30分钟
- **容错能力**：
  - 单点故障自动切换时间≤5分钟
  - 数据一致性保证100%
  - 服务降级功能有效率100%

#### 10.4.2 用户体验验收标准
- **界面友好性**：
  - 界面操作直观性评分≥4.5/5.0
  - 用户操作满意度≥90%
  - 界面响应流畅度≥95%
- **功能易用性**：
  - 新用户上手时间≤30分钟
  - 常用功能操作步骤≤3步
  - 错误操作恢复时间≤1分钟
- **帮助支持**：
  - 在线帮助覆盖率≥95%
  - 帮助文档准确率≥98%
  - 用户问题解决率≥90%

### 10.5 兼容性验收标准

#### 10.5.1 浏览器兼容性验收
- **主流浏览器支持**：
  - Chrome浏览器兼容性100%
  - Firefox浏览器兼容性100%
  - Safari浏览器兼容性≥95%
  - Edge浏览器兼容性≥95%
- **版本兼容性**：
  - 主流浏览器近3个版本兼容性≥95%
  - 移动浏览器兼容性≥90%
  - 兼容性问题修复率≥98%

#### 10.5.2 设备兼容性验收
- **检测设备兼容**：
  - 主流检测设备接入成功率≥95%
  - 设备数据采集准确率≥98%
  - 设备通信稳定性≥99%
- **移动设备兼容**：
  - iOS设备兼容性≥95%
  - Android设备兼容性≥95%
  - 移动端功能完整性≥90%

## 附录

### 附录A：术语表

| 术语 | 英文 | 定义 | 备注 |
|------|------|------|------|
| 质量管理体系 | Quality Management System (QMS) | 建立质量方针和质量目标并实现这些目标的相互关联或相互作用的一组要素 | ISO 9000标准定义 |
| 统计过程控制 | Statistical Process Control (SPC) | 运用统计技术对过程中的各个阶段进行监控，从而达到改进与保证质量的目的 | 质量控制的重要方法 |
| 不合格品 | Nonconforming Product | 不满足要求的产品 | 包括缺陷品和不良品 |
| 纠正措施 | Corrective Action | 为消除已发现的不合格或其他不期望情况的原因所采取的措施 | 针对已发生问题 |
| 预防措施 | Preventive Action | 为消除潜在不合格或其他不期望情况的原因所采取的措施 | 针对潜在问题 |
| 过程能力 | Process Capability | 过程满足规定要求的能力 | 用Cp、Cpk等指标衡量 |
| 控制图 | Control Chart | 用于区分由于普通原因或特殊原因引起的变差的一种工具 | SPC的核心工具 |
| 质量成本 | Quality Cost | 为确保和保证满意的质量而发生的费用以及没有获得满意质量而导致的损失 | 包括预防、评价、内部失败、外部失败成本 |
| 检验 | Inspection | 通过观察和判断，适当时结合测量、试验或估量所进行的符合性评价 | 质量控制的基本手段 |
| 审核 | Audit | 为获得审核证据并对其进行客观的评价，以确定满足审核准则的程度所进行的系统的、独立的并形成文件的过程 | 质量管理体系评价方法 |
| 追溯性 | Traceability | 追溯所考虑对象的历史、应用情况或所处场所的能力 | 质量问题调查的重要手段 |
| 供应商评价 | Supplier Evaluation | 对供应商的质量保证能力进行评价的活动 | 供应链质量管理的重要环节 |
| 客户满意 | Customer Satisfaction | 顾客对其要求已被满足的程度的感受 | 质量管理的最终目标 |
| 持续改进 | Continual Improvement | 增强满足要求的能力的循环活动 | 质量管理的基本原则 |
| 质量记录 | Quality Record | 阐明所取得的结果或提供所从事活动的证据的文件 | 质量管理的重要证据 |

### 附录B：业务规则详细说明

#### B.1 质量标准管理业务规则

**B.1.1 标准制定规则**
- 规则编号：QS-R001
- 规则名称：质量标准制定规则
- 规则描述：
  - 所有产品必须有对应的质量标准
  - 质量标准必须基于国际标准、行业标准或客户要求制定
  - 企业内控标准应严于国际标准和行业标准
  - 质量标准必须经过技术评审和管理审批
- 执行条件：创建或修改质量标准时
- 违规处理：标准创建失败，需要重新制定

**B.1.2 标准版本管理规则**
- 规则编号：QS-R002
- 规则名称：标准版本控制规则
- 规则描述：
  - 标准版本号采用三位数字格式（如1.0.0）
  - 主版本号变更表示重大修改
  - 次版本号变更表示功能性修改
  - 修订版本号变更表示错误修正
  - 同一标准只能有一个有效版本
- 执行条件：标准版本发布时
- 违规处理：版本发布失败，需要重新设置版本号

**B.1.3 检验项目配置规则**
- 规则编号：QS-R003
- 规则名称：检验项目配置规则
- 规则描述：
  - 每个质量标准至少包含一个检验项目
  - 关键质量特性必须设置为关键检验项目
  - 检验项目必须指定检验方法和判定标准
  - 数值型检验项目必须设置目标值和公差范围
- 执行条件：配置检验项目时
- 违规处理：配置失败，需要补充必要信息

#### B.2 检验管理业务规则

**B.2.1 检验计划制定规则**
- 规则编号：IN-R001
- 规则名称：检验计划制定规则
- 规则描述：
  - 所有生产批次必须制定检验计划
  - 检验计划必须基于适用的质量标准
  - 检验计划必须指定检验类型和抽样方案
  - 关键产品必须增加检验频次
- 执行条件：制定检验计划时
- 违规处理：计划制定失败，需要重新制定

**B.2.2 检验执行规则**
- 规则编号：IN-R002
- 规则名称：检验执行规则
- 规则描述：
  - 检验必须按照检验计划执行
  - 检验员必须具备相应资质
  - 检验设备必须在校准有效期内
  - 检验环境必须符合标准要求
  - 检验数据必须真实准确
- 执行条件：执行检验时
- 违规处理：检验结果无效，需要重新检验

**B.2.3 检验结果判定规则**
- 规则编号：IN-R003
- 规则名称：检验结果判定规则
- 规则描述：
  - 检验结果必须基于判定标准进行判定
  - 关键项目不合格直接判定为不合格品
  - 一般项目不合格根据严重程度判定等级
  - 检验结果必须经过复核确认
- 执行条件：检验结果判定时
- 违规处理：判定结果无效，需要重新判定

#### B.3 不合格品管理业务规则

**B.3.1 不合格品识别规则**
- 规则编号：NC-R001
- 规则名称：不合格品识别规则
- 规则描述：
  - 不合格品必须立即识别和标识
  - 不合格品必须与合格品隔离存放
  - 不合格品信息必须完整记录
  - 严重不合格品必须立即上报
- 执行条件：发现不合格品时
- 违规处理：不合格品处理延误，追究相关责任

**B.3.2 不合格品处理规则**
- 规则编号：NC-R002
- 规则名称：不合格品处理规则
- 规则描述：
  - 不合格品必须制定处理方案
  - 处理方案必须经过授权人员批准
  - 返工产品必须重新检验
  - 让步接收必须客户同意
  - 报废产品必须按规定销毁
- 执行条件：处理不合格品时
- 违规处理：处理方案无效，需要重新制定

#### B.4 质量数据分析业务规则

**B.4.1 数据采集规则**
- 规则编号：DA-R001
- 规则名称：质量数据采集规则
- 规则描述：
  - 质量数据必须及时准确采集
  - 数据来源必须可靠可追溯
  - 异常数据必须标识和说明
  - 数据修改必须有授权和记录
- 执行条件：采集质量数据时
- 违规处理：数据无效，需要重新采集

**B.4.2 统计分析规则**
- 规则编号：DA-R002
- 规则名称：统计分析规则
- 规则描述：
  - 统计分析必须基于足够的数据量
  - 分析方法必须科学合理
  - 分析结果必须经过验证
  - 异常结果必须进行原因分析
- 执行条件：进行统计分析时
- 违规处理：分析结果无效，需要重新分析

### 附录C：数据字典

#### C.1 质量标准相关数据字典

**C.1.1 质量标准表（quality_standards）**

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | 是 | - | 主键ID |
| standard_code | VARCHAR | 50 | 是 | - | 标准编码，唯一 |
| standard_name | VARCHAR | 200 | 是 | - | 标准名称 |
| standard_type | ENUM | - | 是 | - | 标准类型：INTERNATIONAL/INDUSTRY/ENTERPRISE/CUSTOMER |
| version | VARCHAR | 20 | 是 | - | 版本号 |
| status | ENUM | - | 是 | DRAFT | 状态：DRAFT/REVIEWING/ACTIVE/OBSOLETE |
| effective_date | DATE | - | 是 | - | 生效日期 |
| expiry_date | DATE | - | 否 | NULL | 失效日期 |
| applicable_products | TEXT | - | 否 | NULL | 适用产品 |
| description | TEXT | - | 否 | NULL | 标准描述 |
| created_by | VARCHAR | 32 | 是 | - | 创建人ID |
| created_at | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_by | VARCHAR | 32 | 否 | NULL | 更新人ID |
| updated_at | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

**C.1.2 检验项目表（inspection_items）**

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | 是 | - | 主键ID |
| standard_id | VARCHAR | 32 | 是 | - | 质量标准ID |
| item_code | VARCHAR | 50 | 是 | - | 项目编码 |
| item_name | VARCHAR | 200 | 是 | - | 项目名称 |
| item_category | ENUM | - | 是 | - | 项目类别：APPEARANCE/DIMENSION/PERFORMANCE/COMPOSITION |
| inspection_method | VARCHAR | 500 | 否 | NULL | 检验方法 |
| equipment_required | VARCHAR | 200 | 否 | NULL | 所需设备 |
| environment_conditions | VARCHAR | 300 | 否 | NULL | 环境条件 |
| measurement_unit | VARCHAR | 20 | 否 | NULL | 测量单位 |
| target_value | DECIMAL | 15,6 | 否 | NULL | 目标值 |
| upper_limit | DECIMAL | 15,6 | 否 | NULL | 上限值 |
| lower_limit | DECIMAL | 15,6 | 否 | NULL | 下限值 |
| tolerance | DECIMAL | 15,6 | 否 | NULL | 公差 |
| is_critical | BOOLEAN | - | 是 | FALSE | 是否关键项目 |
| sort_order | INT | - | 是 | 0 | 排序序号 |

#### C.2 检验管理相关数据字典

**C.2.1 检验计划表（inspection_plans）**

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | 是 | - | 主键ID |
| plan_code | VARCHAR | 50 | 是 | - | 计划编码，唯一 |
| plan_name | VARCHAR | 200 | 是 | - | 计划名称 |
| plan_type | ENUM | - | 是 | - | 计划类型：INCOMING/IN_PROCESS/FINAL/OUTGOING |
| product_id | VARCHAR | 32 | 否 | NULL | 产品ID |
| batch_id | VARCHAR | 32 | 否 | NULL | 批次ID |
| order_id | VARCHAR | 32 | 否 | NULL | 订单ID |
| standard_id | VARCHAR | 32 | 是 | - | 质量标准ID |
| sampling_plan | JSON | - | 是 | - | 抽样方案 |
| planned_start_date | DATE | - | 是 | - | 计划开始日期 |
| planned_end_date | DATE | - | 是 | - | 计划结束日期 |
| assigned_inspector | VARCHAR | 32 | 否 | NULL | 指定检验员 |
| status | ENUM | - | 是 | PLANNED | 状态：PLANNED/IN_PROGRESS/COMPLETED/CANCELLED |
| priority | ENUM | - | 是 | NORMAL | 优先级：LOW/NORMAL/HIGH/URGENT |
| created_by | VARCHAR | 32 | 是 | - | 创建人ID |
| created_at | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

**C.2.2 检验执行表（inspection_executions）**

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | 是 | - | 主键ID |
| execution_code | VARCHAR | 50 | 是 | - | 执行编码，唯一 |
| plan_id | VARCHAR | 32 | 是 | - | 检验计划ID |
| inspector_id | VARCHAR | 32 | 是 | - | 检验员ID |
| inspection_date | DATE | - | 是 | - | 检验日期 |
| inspection_time | TIME | - | 是 | - | 检验时间 |
| sample_quantity | INT | - | 是 | - | 样品数量 |
| inspection_location | VARCHAR | 100 | 否 | NULL | 检验地点 |
| environment_conditions | JSON | - | 否 | NULL | 环境条件 |
| equipment_used | JSON | - | 否 | NULL | 使用设备 |
| overall_result | ENUM | - | 否 | NULL | 总体结果：PASS/FAIL/CONDITIONAL |
| pass_quantity | INT | - | 否 | 0 | 合格数量 |
| fail_quantity | INT | - | 否 | 0 | 不合格数量 |
| remarks | TEXT | - | 否 | NULL | 备注 |
| status | ENUM | - | 是 | PLANNED | 状态：PLANNED/IN_PROGRESS/COMPLETED |
| started_at | TIMESTAMP | - | 否 | NULL | 开始时间 |
| completed_at | TIMESTAMP | - | 否 | NULL | 完成时间 |

#### C.3 不合格品管理相关数据字典

**C.3.1 不合格品记录表（nonconforming_products）**

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | 是 | - | 主键ID |
| ncr_number | VARCHAR | 50 | 是 | - | 不合格品报告编号，唯一 |
| product_id | VARCHAR | 32 | 是 | - | 产品ID |
| batch_id | VARCHAR | 32 | 否 | NULL | 批次ID |
| order_id | VARCHAR | 32 | 否 | NULL | 订单ID |
| inspection_id | VARCHAR | 32 | 否 | NULL | 检验ID |
| detected_date | DATE | - | 是 | - | 发现日期 |
| detected_by | VARCHAR | 32 | 是 | - | 发现人ID |
| detection_stage | ENUM | - | 是 | - | 发现阶段：INCOMING/IN_PROCESS/FINAL/CUSTOMER |
| quantity | INT | - | 是 | - | 不合格数量 |
| defect_category | VARCHAR | 50 | 是 | - | 缺陷类别 |
| defect_description | TEXT | - | 是 | - | 缺陷描述 |
| severity_level | ENUM | - | 是 | - | 严重程度：CRITICAL/MAJOR/MINOR |
| root_cause | TEXT | - | 否 | NULL | 根本原因 |
| disposition | ENUM | - | 否 | NULL | 处置方式：REWORK/REPAIR/DOWNGRADE/SCRAP/RETURN |
| disposition_reason | TEXT | - | 否 | NULL | 处置原因 |
| responsible_dept | VARCHAR | 100 | 否 | NULL | 责任部门 |
| estimated_cost | DECIMAL | 15,2 | 否 | 0.00 | 预估损失 |
| actual_cost | DECIMAL | 15,2 | 否 | 0.00 | 实际损失 |
| status | ENUM | - | 是 | IDENTIFIED | 状态：IDENTIFIED/PROCESSING/PROCESSED/VERIFIED/CLOSED |
| created_at | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

#### C.4 质量数据分析相关数据字典

**C.4.1 质量统计表（quality_statistics）**

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | 是 | - | 主键ID |
| statistic_date | DATE | - | 是 | - | 统计日期 |
| statistic_period | ENUM | - | 是 | - | 统计周期：DAILY/WEEKLY/MONTHLY/QUARTERLY/YEARLY |
| product_id | VARCHAR | 32 | 否 | NULL | 产品ID |
| department_id | VARCHAR | 32 | 否 | NULL | 部门ID |
| total_inspected | INT | - | 是 | 0 | 总检验数量 |
| total_passed | INT | - | 是 | 0 | 合格数量 |
| total_failed | INT | - | 是 | 0 | 不合格数量 |
| pass_rate | DECIMAL | 5,2 | 是 | 0.00 | 合格率(%) |
| defect_rate | DECIMAL | 5,2 | 是 | 0.00 | 缺陷率(%) |
| first_pass_rate | DECIMAL | 5,2 | 是 | 0.00 | 一次合格率(%) |
| rework_rate | DECIMAL | 5,2 | 是 | 0.00 | 返工率(%) |
| scrap_rate | DECIMAL | 5,2 | 是 | 0.00 | 报废率(%) |
| quality_cost | DECIMAL | 15,2 | 是 | 0.00 | 质量成本 |
| prevention_cost | DECIMAL | 15,2 | 是 | 0.00 | 预防成本 |
| appraisal_cost | DECIMAL | 15,2 | 是 | 0.00 | 评价成本 |
| internal_failure_cost | DECIMAL | 15,2 | 是 | 0.00 | 内部失败成本 |
| external_failure_cost | DECIMAL | 15,2 | 是 | 0.00 | 外部失败成本 |
| created_at | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 附录D：错误码定义

#### D.1 系统级错误码（1000-1999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 1000 | 系统内部错误 | 系统发生未知错误 | 联系系统管理员 |
| 1001 | 数据库连接失败 | 无法连接到数据库 | 检查数据库服务状态 |
| 1002 | 网络连接超时 | 网络请求超时 | 检查网络连接 |
| 1003 | 服务不可用 | 服务暂时不可用 | 稍后重试 |
| 1004 | 参数验证失败 | 请求参数不符合要求 | 检查请求参数 |
| 1005 | 权限验证失败 | 用户权限不足 | 联系管理员分配权限 |
| 1006 | 会话已过期 | 用户会话已过期 | 重新登录 |
| 1007 | 操作频率过高 | 操作过于频繁 | 降低操作频率 |
| 1008 | 文件上传失败 | 文件上传过程中出错 | 检查文件格式和大小 |
| 1009 | 数据格式错误 | 数据格式不正确 | 检查数据格式 |

#### D.2 质量标准管理错误码（2000-2999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 2001 | 质量标准不存在 | 指定的质量标准不存在 | 检查标准ID是否正确 |
| 2002 | 标准编码已存在 | 标准编码重复 | 使用不同的标准编码 |
| 2003 | 标准版本冲突 | 版本号已存在 | 使用新的版本号 |
| 2004 | 标准状态不允许修改 | 当前状态下不允许修改 | 检查标准状态 |
| 2005 | 检验项目不能为空 | 质量标准必须包含检验项目 | 添加检验项目 |
| 2006 | 判定规则配置错误 | 判定规则配置不正确 | 检查规则配置 |
| 2007 | 标准审批失败 | 标准审批未通过 | 根据审批意见修改 |
| 2008 | 标准已被引用 | 标准被其他数据引用，无法删除 | 先删除引用数据 |
| 2009 | 检验项目参数错误 | 检验项目参数设置错误 | 检查参数设置 |
| 2010 | 标准生效日期错误 | 生效日期设置不合理 | 设置合理的生效日期 |

#### D.3 检验管理错误码（3000-3999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 3001 | 检验计划不存在 | 指定的检验计划不存在 | 检查计划ID是否正确 |
| 3002 | 检验员不存在 | 指定的检验员不存在 | 检查检验员ID |
| 3003 | 检验设备不可用 | 检验设备不可用或未校准 | 检查设备状态 |
| 3004 | 抽样方案错误 | 抽样方案配置错误 | 检查抽样参数 |
| 3005 | 检验数据无效 | 检验数据不符合要求 | 检查数据有效性 |
| 3006 | 检验任务已完成 | 检验任务已经完成 | 无需重复操作 |
| 3007 | 检验环境不符合要求 | 检验环境条件不满足 | 调整环境条件 |
| 3008 | 样品数量不足 | 样品数量不够检验要求 | 增加样品数量 |
| 3009 | 检验方法不适用 | 检验方法不适用于当前产品 | 选择合适的检验方法 |
| 3010 | 检验结果异常 | 检验结果超出预期范围 | 重新检验确认 |

#### D.4 不合格品管理错误码（4000-4999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 4001 | 不合格品记录不存在 | 指定的不合格品记录不存在 | 检查记录ID |
| 4002 | 处置方式不合理 | 选择的处置方式不合理 | 选择合适的处置方式 |
| 4003 | 纠正措施未完成 | 纠正措施尚未完成 | 完成纠正措施后再操作 |
| 4004 | 验证结果不合格 | 处理后验证仍不合格 | 重新制定处理方案 |
| 4005 | 成本估算错误 | 成本估算不合理 | 重新评估成本 |
| 4006 | 责任部门未指定 | 未指定责任部门 | 指定责任部门 |
| 4007 | 根本原因分析不充分 | 根本原因分析不够深入 | 深入分析根本原因 |
| 4008 | 预防措施无效 | 预防措施效果不佳 | 制定更有效的预防措施 |
| 4009 | 不合格品未隔离 | 不合格品未进行隔离 | 立即隔离不合格品 |
| 4010 | 处理时限超期 | 处理时间超过规定期限 | 加快处理进度 |

#### D.5 质量数据分析错误码（5000-5999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 5001 | 数据样本不足 | 数据样本量不够分析要求 | 增加数据样本 |
| 5002 | 统计方法不适用 | 选择的统计方法不适用 | 选择合适的统计方法 |
| 5003 | 分析参数错误 | 分析参数设置错误 | 检查参数设置 |
| 5004 | 数据质量问题 | 数据存在质量问题 | 清理数据后重新分析 |
| 5005 | 报表生成失败 | 报表生成过程中出错 | 检查报表模板和数据 |
| 5006 | 趋势分析异常 | 趋势分析结果异常 | 检查数据和分析方法 |
| 5007 | 过程能力计算错误 | 过程能力指数计算错误 | 检查计算参数 |
| 5008 | 控制限计算失败 | 控制限计算失败 | 控制限计算过程中出错 | 检查数据和计算公式 |
| 5009 | 报表模板不存在 | 指定的报表模板不存在 | 检查模板ID或创建模板 |
| 5010 | 数据导出失败 | 数据导出过程中出错 | 检查导出格式和权限 |
| 5011 | 分析任务超时 | 分析任务执行超时 | 减少数据量或优化查询 |
| 5012 | 统计指标配置错误 | 统计指标配置不正确 | 检查指标配置参数 |
| 5013 | 数据权限不足 | 无权访问指定数据 | 申请相应数据权限 |
| 5014 | 分析结果过期 | 分析结果已过期 | 重新执行分析任务 |
| 5015 | 对比基准缺失 | 缺少对比分析的基准数据 | 设置对比基准或补充数据 |

#### D.6 SPC统计控制错误码（6000-6999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 6001 | 控制图不存在 | 指定的控制图不存在 | 检查控制图ID是否正确 |
| 6002 | 控制图类型不支持 | 不支持的控制图类型 | 选择支持的控制图类型 |
| 6003 | 数据点数量不足 | 数据点数量不够建立控制图 | 增加数据点数量 |
| 6004 | 控制限计算失败 | 无法计算控制限 | 检查数据质量和计算参数 |
| 6005 | 异常模式检测失败 | 异常模式检测算法失败 | 检查检测算法配置 |
| 6006 | 控制图参数错误 | 控制图参数设置错误 | 检查参数设置 |
| 6007 | 数据更新频率过高 | 数据更新过于频繁 | 调整数据更新频率 |
| 6008 | 控制图状态异常 | 控制图处于异常状态 | 检查控制图状态 |
| 6009 | 历史数据不完整 | 历史数据缺失或不完整 | 补充完整历史数据 |
| 6010 | 控制规则冲突 | 多个控制规则产生冲突 | 调整控制规则配置 |
| 6011 | 子组大小不一致 | 子组大小不一致 | 统一子组大小 |
| 6012 | 测量系统误差 | 测量系统存在系统误差 | 校准测量系统 |
| 6013 | 过程不稳定 | 过程处于不稳定状态 | 分析并消除特殊原因 |
| 6014 | 控制图重新计算失败 | 控制限重新计算失败 | 检查重新计算条件 |
| 6015 | 告警规则配置错误 | 告警规则配置不正确 | 检查告警规则设置 |

#### D.7 供应商质量管理错误码（7000-7999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 7001 | 供应商不存在 | 指定的供应商不存在 | 检查供应商ID是否正确 |
| 7002 | 供应商评估记录不存在 | 指定的评估记录不存在 | 检查评估记录ID |
| 7003 | 评估周期未到 | 尚未到评估周期 | 等待评估周期到达 |
| 7004 | 评估标准未定义 | 未定义评估标准 | 设置评估标准 |
| 7005 | 评估数据不完整 | 评估所需数据不完整 | 补充完整评估数据 |
| 7006 | 供应商状态不允许操作 | 当前供应商状态不允许此操作 | 检查供应商状态 |
| 7007 | 质量协议未签署 | 质量协议尚未签署 | 签署质量协议 |
| 7008 | 供应商改进计划未制定 | 未制定改进计划 | 制定供应商改进计划 |
| 7009 | 来料检验标准缺失 | 缺少来料检验标准 | 制定来料检验标准 |
| 7010 | 供应商审核未通过 | 供应商审核未通过 | 根据审核意见整改 |
| 7011 | 质量问题未关闭 | 存在未关闭的质量问题 | 关闭所有质量问题 |
| 7012 | 供应商资质过期 | 供应商资质证书已过期 | 更新供应商资质证书 |
| 7013 | 评估权重配置错误 | 评估权重配置不正确 | 检查权重配置 |
| 7014 | 供应商排名计算失败 | 排名计算过程中出错 | 检查排名算法和数据 |
| 7015 | 改进措施验证失败 | 改进措施验证未通过 | 重新制定改进措施 |

#### D.8 客户质量管理错误码（8000-8999）

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 8001 | 客户投诉记录不存在 | 指定的投诉记录不存在 | 检查投诉记录ID |
| 8002 | 投诉处理人员未指定 | 未指定投诉处理人员 | 指定处理人员 |
| 8003 | 投诉响应超时 | 投诉响应时间超过规定时限 | 加快响应速度 |
| 8004 | 客户信息不完整 | 客户信息不完整 | 补充完整客户信息 |
| 8005 | 投诉分类错误 | 投诉分类不正确 | 重新分类投诉 |
| 8006 | 处理方案未制定 | 未制定投诉处理方案 | 制定处理方案 |
| 8007 | 客户满意度调查未完成 | 满意度调查尚未完成 | 完成满意度调查 |
| 8008 | 质量证书生成失败 | 质量证书生成过程中出错 | 检查证书模板和数据 |
| 8009 | 证书模板不存在 | 指定的证书模板不存在 | 检查模板ID或创建模板 |
| 8010 | 证书验证失败 | 证书验证未通过 | 检查证书信息和验证码 |
| 8011 | 客户要求不明确 | 客户质量要求不明确 | 与客户确认具体要求 |
| 8012 | 质量追溯数据缺失 | 质量追溯所需数据缺失 | 补充追溯数据 |
| 8013 | 投诉处理流程异常 | 投诉处理流程出现异常 | 检查流程配置 |
| 8014 | 客户沟通记录缺失 | 缺少客户沟通记录 | 补充沟通记录 |
| 8015 | 投诉关闭条件不满足 | 不满足投诉关闭条件 | 满足关闭条件后再操作 |

### 附录E：接口变更日志

#### E.1 变更日志格式说明

接口变更日志记录了质量管理子系统API接口的所有变更历史，包括新增、修改、废弃等操作。每条变更记录包含以下字段：

- **版本号**：接口版本号，遵循语义化版本规范（如v1.0.0）
- **变更日期**：变更发布日期
- **变更类型**：变更类型（新增/修改/废弃/修复）
- **接口路径**：受影响的接口路径
- **变更描述**：详细的变更内容描述
- **影响范围**：变更对系统的影响范围
- **兼容性**：是否向后兼容
- **迁移指南**：升级迁移指导

#### E.2 接口变更记录

| 版本号 | 变更日期 | 变更类型 | 接口路径 | 变更描述 | 影响范围 | 兼容性 | 迁移指南 |
|--------|----------|----------|----------|----------|----------|--------|----------|
| v1.0.0 | 2024-01-15 | 新增 | /api/quality/standards | 新增质量标准管理接口 | 质量标准模块 | N/A | 初始版本，无需迁移 |
| v1.0.0 | 2024-01-15 | 新增 | /api/quality/inspection-plans | 新增检验计划管理接口 | 检验管理模块 | N/A | 初始版本，无需迁移 |
| v1.0.0 | 2024-01-15 | 新增 | /api/quality/nonconforming-products | 新增不合格品管理接口 | 不合格品模块 | N/A | 初始版本，无需迁移 |
| v1.1.0 | 2024-02-01 | 修改 | /api/quality/standards/{id} | 增加标准版本历史查询功能 | 质量标准模块 | 向后兼容 | 无需修改现有调用 |
| v1.1.0 | 2024-02-01 | 新增 | /api/quality/spc/control-charts | 新增SPC控制图管理接口 | SPC统计控制模块 | N/A | 新功能，按文档集成 |
| v1.1.1 | 2024-02-10 | 修复 | /api/quality/inspection-executions | 修复检验数据批量录入的并发问题 | 检验执行模块 | 向后兼容 | 无需修改，性能优化 |
| v1.2.0 | 2024-02-20 | 修改 | /api/quality/statistics | 增加质量成本分析字段 | 质量数据分析模块 | 向后兼容 | 响应增加新字段，可选使用 |
| v1.2.0 | 2024-02-20 | 新增 | /api/quality/supplier-assessments | 新增供应商质量评估接口 | 供应商质量模块 | N/A | 新功能，按文档集成 |
| v1.2.1 | 2024-03-01 | 修复 | /api/quality/certificates | 修复质量证书生成的模板解析问题 | 客户质量服务模块 | 向后兼容 | 无需修改，错误修复 |
| v1.3.0 | 2024-03-15 | 修改 | /api/quality/inspection-plans | 增加移动端检验任务同步接口 | 检验管理模块 | 向后兼容 | 新增移动端相关字段 |
| v1.3.0 | 2024-03-15 | 新增 | /api/quality/mobile/sync | 新增移动端数据同步接口 | 移动端模块 | N/A | 移动端专用接口 |
| v1.3.1 | 2024-03-25 | 修复 | /api/quality/spc/control-charts/{id}/data | 修复控制图数据点异常检测算法 | SPC统计控制模块 | 向后兼容 | 无需修改，算法优化 |
| v1.4.0 | 2024-04-01 | 修改 | /api/quality/customer-complaints | 增加投诉处理时限监控功能 | 客户质量服务模块 | 向后兼容 | 响应增加时限相关字段 |
| v1.4.0 | 2024-04-01 | 新增 | /api/quality/knowledge-base | 新增质量知识库管理接口 | 知识库模块 | N/A | 新功能，按文档集成 |
| v1.4.1 | 2024-04-10 | 修复 | /api/quality/reports/generate | 修复大数据量报表生成超时问题 | 质量数据分析模块 | 向后兼容 | 无需修改，性能优化 |

#### E.3 废弃接口记录

| 版本号 | 废弃日期 | 接口路径 | 废弃原因 | 替代接口 | 停止支持日期 |
|--------|----------|----------|----------|----------|--------------|
| v1.5.0 | 2024-05-01 | /api/quality/legacy/standards | 接口设计不合理，性能较差 | /api/quality/standards | 2024-08-01 |
| v1.5.0 | 2024-05-01 | /api/quality/old-reports | 报表格式过时，不支持新需求 | /api/quality/reports | 2024-08-01 |

#### E.4 接口变更通知机制

**E.4.1 变更通知流程**
1. **变更计划阶段**：提前30天发布变更计划通知
2. **变更预告阶段**：提前15天发布详细变更内容
3. **变更实施阶段**：发布变更实施通知和迁移指南
4. **变更完成阶段**：确认变更完成并提供技术支持

**E.4.2 通知渠道**
- 系统内消息通知
- 邮件通知相关开发人员
- 技术文档更新
- API文档版本更新

**E.4.3 兼容性保证**
- 向后兼容的变更：保持原有接口功能不变
- 不兼容的变更：提供至少3个月的过渡期
- 废弃接口：提供至少6个月的支持期

#### E.5 变更影响评估

**E.5.1 影响范围分类**
- **核心功能影响**：影响系统核心业务功能
- **扩展功能影响**：影响系统扩展功能
- **性能影响**：影响系统性能表现
- **安全影响**：影响系统安全性
- **数据影响**：影响数据结构或格式

**E.5.2 风险等级评估**
- **高风险**：可能导致系统不可用或数据丢失
- **中风险**：可能影响部分功能或性能
- **低风险**：对系统影响较小，易于处理

**E.5.3 回滚策略**
- 所有接口变更都必须具备回滚能力
- 数据库结构变更必须支持向前和向后兼容
- 关键接口变更需要制定详细的回滚计划

**文档结束**

*本文档为玻璃深加工ERP系统质量管理子系统的完整需求规格说明书，包含了系统的功能需求、非功能需求、技术规范、验收标准等全部内容。文档将作为系统设计、开发、测试和验收的重要依据。*