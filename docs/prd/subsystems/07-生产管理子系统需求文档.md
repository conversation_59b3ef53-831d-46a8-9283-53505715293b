# 生产管理子系统需求文档

## 1. 项目概述

### 1.1 项目背景
生产管理子系统是玻璃深加工ERP系统的核心制造模块，专门针对玻璃深加工行业的智能制造需求设计，符合工业4.0标准。该子系统需要支持从原片切割到成品包装的完整生产流程管理，包括切割优化、生产排程、工艺执行、质量控制、设备监控等关键环节。与传统制造业不同，玻璃深加工生产具有工艺复杂、设备精密、质量要求严格、原料利用率要求高等特点，需要建立智能化的生产管理体系和精确的过程控制机制。

### 1.2 项目目标
- 建立符合工业4.0标准的智能制造管理体系，实现生产过程的数字化和智能化
- 构建先进的切割优化算法系统，最大化玻璃原片利用率，降低生产成本
- 实现多工艺线的协同生产排程，优化资源配置和生产效率
- 建立实时生产监控和数据采集体系，实现生产过程的透明化管理
- 构建完善的质量控制和在线检测系统，确保产品质量稳定可控
- 实现设备智能化管理和预测性维护，提高设备利用率和可靠性
- 提供生产数据分析和决策支持，持续优化生产管理水平

### 1.3 目标用户
- **生产总监**：制定生产策略，监控生产绩效，协调资源配置
- **生产计划员**：制定生产计划，优化排程方案，协调生产资源
- **车间主任**：管理车间生产，监控生产进度，处理现场问题
- **工艺技术员**：执行工艺标准，监控工艺参数，处理技术问题
- **质检员**：执行质量检验，处理质量问题，维护质量记录
- **设备工程师**：管理设备运行，执行维护保养，处理设备故障
- **操作工人**：执行生产操作，反馈生产状况，参与质量控制

## 2. 功能需求

### 2.1 智能切割优化模块

#### 2.1.1 切割方案优化
- **功能描述**：基于订单需求和原片库存，智能生成最优切割方案
- **核心功能**：
  - 多算法支持：降维启发式、模拟退火、遗传算法等优化算法
  - 约束条件管理：设备能力、工艺要求、质量标准等约束
  - 方案评估对比：利用率、成本、时间等多维度评估
  - 实时优化调整：根据生产变化动态调整切割方案
- **业务规则**：
  - 原片利用率目标≥95%
  - 切割方案生成时间≤30秒
  - 支持多种原片规格同时优化
  - 考虑设备切割能力限制

#### 2.1.2 切割计划管理
- **功能描述**：管理切割计划的制定、执行和调整
- **核心功能**：
  - 切割任务分解：将订单需求分解为具体切割任务
  - 设备分配优化：根据设备能力和负荷分配切割任务
  - 计划执行跟踪：实时跟踪切割计划的执行进度
  - 异常处理机制：处理切割异常和计划调整
- **业务规则**：
  - 切割计划与生产计划联动
  - 设备故障时自动重新分配任务
  - 切割余料自动回收利用
  - 切割质量实时监控

#### 2.1.3 原片库存优化
- **功能描述**：优化原片库存结构，提高库存周转率
- **核心功能**：
  - 库存结构分析：分析原片规格的库存结构合理性
  - 采购建议生成：基于切割需求生成原片采购建议
  - 库存预警管理：设置库存上下限和预警机制
  - 呆滞库存处理：识别和处理呆滞原片库存
- **业务规则**：
  - 库存周转率目标≥6次/年
  - 呆滞库存定期清理
  - 原片规格标准化管理
  - 库存成本最小化

### 2.2 生产排程管理模块

#### 2.2.1 智能排程算法
- **功能描述**：基于订单需求和资源约束，智能生成生产排程
- **核心功能**：
  - 多目标优化：交期、成本、资源利用率等多目标平衡
  - 约束条件处理：设备能力、人员配置、物料供应等约束
  - 动态调度算法：支持生产过程中的动态调度调整
  - 排程方案评估：评估不同排程方案的优劣
- **业务规则**：
  - 订单交期达成率≥95%
  - 设备利用率≥85%
  - 支持紧急订单插单
  - 考虑设备维护时间窗口

#### 2.2.2 生产计划管理
- **功能描述**：制定和管理详细的生产计划
- **核心功能**：
  - 主生产计划：制定月度、周度主生产计划
  - 车间作业计划：分解为具体的车间作业计划
  - 计划执行监控：实时监控计划执行情况
  - 计划调整优化：根据实际情况调整生产计划
- **业务规则**：
  - 计划制定考虑工艺路线和BOM
  - 计划变更需要影响分析
  - 计划执行偏差及时预警
  - 计划完成率统计分析

#### 2.2.3 资源调度管理
- **功能描述**：优化配置生产资源，提高资源利用效率
- **核心功能**：
  - 设备资源调度：优化设备的使用安排和负荷平衡
  - 人员资源调度：合理安排人员班次和技能匹配
  - 物料资源调度：协调物料供应和生产需求
  - 工装夹具调度：管理工装夹具的使用和调配
- **业务规则**：
  - 资源利用率最大化
  - 技能匹配优先原则
  - 关键资源重点保障
  - 资源冲突自动预警

### 2.3 生产执行控制模块

#### 2.3.1 工单管理
- **功能描述**：管理生产工单的创建、下达和执行
- **核心功能**：
  - 工单自动生成：基于生产计划自动生成工单
  - 工单下达控制：控制工单的下达时机和条件
  - 工单执行跟踪：实时跟踪工单的执行状态
  - 工单完工确认：处理工单的完工确认和数据收集
- **业务规则**：
  - 工单必须包含完整的工艺信息
  - 工单状态实时更新
  - 工单异常及时处理
  - 工单数据完整记录

#### 2.3.2 工艺执行控制
- **功能描述**：控制生产过程中的工艺执行和参数监控
- **核心功能**：
  - 工艺参数监控：实时监控关键工艺参数
  - 工艺标准执行：确保工艺标准的严格执行
  - 工艺异常处理：及时发现和处理工艺异常
  - 工艺数据采集：自动采集工艺执行数据
- **业务规则**：
  - 关键参数超限自动报警
  - 工艺变更需要授权确认
  - 工艺数据实时记录
  - 工艺质量持续监控

#### 2.3.3 生产现场管理
- **功能描述**：管理生产现场的日常运营和协调
- **核心功能**：
  - 现场作业指导：提供标准化的作业指导
  - 现场问题处理：快速响应和处理现场问题
  - 现场数据采集：采集现场生产和质量数据
  - 现场安全管理：监控现场安全状况和风险
- **业务规则**：
  - 作业指导标准化执行
  - 问题处理及时响应
  - 安全规程严格遵守
  - 现场5S管理标准

### 2.4 质量控制管理模块

#### 2.4.1 在线质量检测
- **功能描述**：实现生产过程中的在线质量检测和控制
- **核心功能**：
  - 自动检测设备：集成自动化质量检测设备
  - 检测数据采集：自动采集质量检测数据
  - 质量异常预警：实时发现和预警质量异常
  - 质量数据分析：分析质量数据趋势和规律
- **业务规则**：
  - 关键质量点必须检测
  - 不合格品自动隔离
  - 质量数据实时记录
  - 质量趋势持续监控

#### 2.4.2 质量控制流程
- **功能描述**：建立完善的质量控制流程和标准
- **核心功能**：
  - 质量检验计划：制定详细的质量检验计划
  - 检验标准管理：维护和更新质量检验标准
  - 不合格品处理：规范不合格品的处理流程
  - 质量改进跟踪：跟踪质量改进措施的实施效果
- **业务规则**：
  - 检验标准严格执行
  - 不合格品必须处理
  - 质量问题根因分析
  - 改进措施闭环管理

#### 2.4.3 质量追溯管理
- **功能描述**：建立完整的产品质量追溯体系
- **核心功能**：
  - 批次追溯管理：建立产品批次的完整追溯链
  - 质量档案管理：维护产品的完整质量档案
  - 追溯查询功能：快速查询产品的质量追溯信息
  - 质量报告生成：自动生成质量追溯报告
- **业务规则**：
  - 追溯信息完整准确
  - 追溯查询快速响应
  - 质量档案长期保存
  - 追溯报告标准化

### 2.5 设备管理模块

#### 2.5.1 设备运行监控
- **功能描述**：实时监控设备运行状态和性能参数
- **核心功能**：
  - 设备状态监控：实时监控设备的运行状态
  - 性能参数采集：自动采集设备性能参数
  - 设备效率分析：分析设备的运行效率和利用率
  - 设备异常预警：及时发现和预警设备异常
- **业务规则**：
  - 设备状态实时更新
  - 异常情况及时报警
  - 设备数据完整记录
  - 效率指标持续监控

#### 2.5.2 预测性维护
- **功能描述**：基于数据分析实现设备的预测性维护
- **核心功能**：
  - 维护计划制定：制定设备的预防性维护计划
  - 故障预测分析：基于历史数据预测设备故障
  - 维护任务管理：管理设备维护任务的执行
  - 维护效果评估：评估维护工作的效果和质量
- **业务规则**：
  - 维护计划严格执行
  - 故障预测准确及时
  - 维护记录完整保存
  - 维护效果持续改进

#### 2.5.3 设备资产管理
- **功能描述**：管理设备资产的全生命周期
- **核心功能**：
  - 设备档案管理：建立完整的设备技术档案
  - 设备价值管理：管理设备的资产价值和折旧
  - 设备配件管理：管理设备配件的库存和使用
  - 设备报废处置：处理设备的报废和处置
- **业务规则**：
  - 设备档案信息完整
  - 资产价值准确核算
  - 配件库存合理控制
  - 报废程序规范执行

### 2.6 生产数据分析模块

#### 2.6.1 生产效率分析
- **功能描述**：分析生产效率和绩效指标
- **核心功能**：
  - 效率指标计算：计算各种生产效率指标
  - 瓶颈分析识别：识别生产过程中的瓶颈环节
  - 效率趋势分析：分析生产效率的变化趋势
  - 改进建议生成：基于分析结果生成改进建议
- **业务规则**：
  - 效率指标标准化计算
  - 瓶颈分析准确识别
  - 趋势分析定期更新
  - 改进建议可操作性

#### 2.6.2 成本核算分析
- **功能描述**：分析生产成本构成和变化趋势
- **核心功能**：
  - 成本要素分析：分析材料、人工、制造费用等成本要素
  - 成本中心核算：按成本中心核算生产成本
  - 成本差异分析：分析实际成本与标准成本的差异
  - 成本优化建议：提供成本优化的改进建议
- **业务规则**：
  - 成本核算准确及时
  - 成本分析全面深入
  - 差异分析原因明确
  - 优化建议切实可行

#### 2.6.3 生产报表管理
- **功能描述**：生成各类生产管理报表
- **核心功能**：
  - 日报表生成：生成日度生产报表
  - 周月报表：生成周度和月度生产报表
  - 专项分析报表：生成专项分析报表
  - 报表自定义：支持用户自定义报表格式
- **业务规则**：
  - 报表数据准确完整
  - 报表格式标准规范
  - 报表生成及时高效
  - 报表权限严格控制

## 3. 页面与功能映射

### 3.1 页面列表
- 生产管理首页/仪表板
- 切割优化管理页面
- 生产排程管理页面
- 生产执行监控页面
- 质量控制管理页面
- 设备管理页面
- 生产数据分析页面

### 3.2 页面功能明细

#### 3.2.1 生产管理首页/仪表板
- **功能模块**：生产管理概览和实时监控
- **功能描述**：展示生产关键指标，提供实时生产监控和快速操作
- **主要界面元素**：
  - 仪表盘组件：生产效率、设备利用率、质量指标等关键KPI
  - 图表组件：生产趋势、设备状态、质量分析等实时图表
  - 地图组件：车间布局和设备分布可视化
  - 预警组件：生产异常、设备故障、质量问题等预警信息
  - 快捷操作：紧急调度、异常处理、报表查询等快捷入口
- **输入/输出字段**：
  - 字段名：productionDashboard
  - 类型：object
  - 校验规则：数据实时性校验，异常数据自动标记
  - 选项：时间范围（实时、小时、班次、日），车间选择，指标类型
  - 依赖关系：依赖生产数据、设备数据、质量数据的实时采集
- **交互流程**：
  - 页面加载 -> 获取实时数据 -> 渲染仪表盘 -> 异常预警 -> 快捷操作响应
- **相关API**：
  - 接口名：getProductionDashboard, getRealTimeMetrics, getAlerts
  - 请求参数：{timeRange: string, workshop: string, userId: string}
  - 响应结构：{metrics: ProductionMetrics, alerts: Alert[], trends: TrendData[]}
  - 错误处理：数据采集异常时显示历史数据，网络中断时本地缓存
- **权限控制**：生产总监查看全局数据，车间主任查看本车间数据

#### 3.2.2 切割优化管理页面
- **功能模块**：智能切割优化和方案管理
- **功能描述**：基于订单需求和原片库存，生成和管理最优切割方案
- **主要界面元素**：
  - 优化配置组件：算法选择、约束条件设置、优化目标配置
  - 方案展示组件：切割方案可视化展示和对比分析
  - 原片管理组件：原片库存状态和规格管理
  - 计算结果组件：利用率计算、成本分析、时间预估
  - 执行控制组件：方案确认、任务下达、执行监控
- **输入/输出字段**：
  - 字段名：cuttingOptimization
  - 类型：object
  - 校验规则：原片尺寸有效性，订单需求合理性，算法参数正确性
  - 选项：优化算法（启发式、模拟退火、遗传算法），目标函数（利用率、成本、时间）
  - 依赖关系：订单需求数据、原片库存数据、设备能力数据
- **交互流程**：
  - 选择订单 -> 配置优化参数 -> 执行优化计算 -> 方案对比选择 -> 确认执行 -> 监控进度
- **相关API**：
  - 接口名：optimizeCutting, comparePlans, executeCuttingPlan
  - 请求参数：{orders: Order[], glassStock: GlassStock, algorithm: string, constraints: Constraints}
  - 响应结构：{plans: CuttingPlan[], utilization: number, cost: number, time: number}
  - 错误处理：优化失败时提供手动方案，数据异常时重新计算
- **权限控制**：生产计划员可执行优化，车间主任可确认方案

#### 3.2.3 生产排程管理页面
- **功能模块**：智能生产排程和计划管理
- **功能描述**：制定和管理生产排程，优化资源配置和生产效率
- **主要界面元素**：
  - 甘特图组件：生产排程甘特图展示和编辑
  - 资源视图组件：设备、人员、物料等资源状态视图
  - 排程配置组件：排程规则、约束条件、优化目标设置
  - 计划对比组件：不同排程方案的对比分析
  - 执行监控组件：排程执行进度和异常监控
- **输入/输出字段**：
  - 字段名：productionSchedule
  - 类型：object
  - 校验规则：时间逻辑合理性，资源约束有效性，优先级设置正确性
  - 选项：排程算法（优先级、关键路径、遗传算法），时间粒度（小时、班次、日）
  - 依赖关系：订单数据、工艺路线、资源能力、库存状态
- **交互流程**：
  - 加载订单 -> 配置排程参数 -> 执行排程算法 -> 方案优化调整 -> 发布执行 -> 监控反馈
- **相关API**：
  - 接口名：generateSchedule, optimizeSchedule, publishSchedule
  - 请求参数：{orders: Order[], resources: Resource[], constraints: Constraints, algorithm: string}
  - 响应结构：{schedule: Schedule, efficiency: number, conflicts: Conflict[]}
  - 错误处理：排程冲突时提供解决方案，资源不足时预警提示
- **权限控制**：生产计划员可制定排程，生产总监可审批发布

#### 3.2.4 生产执行监控页面
- **功能模块**：生产过程实时监控和执行管理
- **功能描述**：实时监控生产执行状态，管理工单和工艺执行
- **主要界面元素**：
  - 工单列表组件：工单状态、进度、异常等信息展示
  - 工艺监控组件：关键工艺参数实时监控和报警
  - 设备状态组件：设备运行状态和性能参数监控
  - 质量监控组件：在线质量检测数据和趋势分析
  - 异常处理组件：生产异常的快速响应和处理
- **输入/输出字段**：
  - 字段名：productionExecution
  - 类型：object
  - 校验规则：工单状态一致性，工艺参数范围检查，设备状态有效性
  - 选项：监控粒度（实时、分钟、小时），异常级别（提醒、警告、严重）
  - 依赖关系：工单数据、工艺参数、设备数据、质量数据
- **交互流程**：
  - 工单下达 -> 工艺执行 -> 参数监控 -> 异常处理 -> 质量检验 -> 完工确认
- **相关API**：
  - 接口名：monitorProduction, handleException, confirmCompletion
  - 请求参数：{workOrder: string, parameters: ProcessParams, quality: QualityData}
  - 响应结构：{status: string, progress: number, alerts: Alert[], quality: QualityResult}
  - 错误处理：参数异常时自动调整，设备故障时切换备用设备
- **权限控制**：车间主任可监控全部，工艺技术员可监控本工序

#### 3.2.5 质量控制管理页面
- **功能模块**：质量控制和检验管理
- **功能描述**：管理质量检验流程，监控产品质量状态
- **主要界面元素**：
  - 检验计划组件：质量检验计划制定和管理
  - 检测数据组件：在线检测数据展示和分析
  - 质量统计组件：质量指标统计和趋势分析
  - 不合格品组件：不合格品处理和跟踪管理
  - 质量报告组件：质量报告生成和分发
- **输入/输出字段**：
  - 字段名：qualityControl
  - 类型：object
  - 校验规则：检验标准符合性，检测数据有效性，处理流程完整性
  - 选项：检验类型（首检、巡检、终检），质量等级（A、B、C级）
  - 依赖关系：工艺标准、检验标准、检测设备数据
- **交互流程**：
  - 制定检验计划 -> 执行质量检验 -> 数据采集分析 -> 异常处理 -> 质量改进
- **相关API**：
  - 接口名：createInspectionPlan, recordQualityData, handleDefects
  - 请求参数：{inspectionPlan: InspectionPlan, qualityData: QualityData, defects: Defect[]}
  - 响应结构：{inspectionResult: InspectionResult, qualityLevel: string, actions: Action[]}
  - 错误处理：检验异常时重新检验，数据异常时人工确认
- **权限控制**：质检员可执行检验，质量工程师可制定标准

#### 3.2.6 设备管理页面
- **功能模块**：设备运行监控和维护管理
- **功能描述**：监控设备运行状态，管理设备维护和保养
- **主要界面元素**：
  - 设备状态组件：设备运行状态实时监控
  - 性能监控组件：设备性能参数和效率分析
  - 维护计划组件：设备维护计划制定和执行
  - 故障管理组件：设备故障记录和处理跟踪
  - 资产管理组件：设备资产信息和价值管理
- **输入/输出字段**：
  - 字段名：equipmentManagement
  - 类型：object
  - 校验规则：设备状态有效性，维护计划合理性，故障记录完整性
  - 选项：设备类型（切割、磨边、钢化等），状态类型（运行、停机、维护）
  - 依赖关系：设备基础数据、运行参数、维护记录
- **交互流程**：
  - 设备监控 -> 异常预警 -> 维护计划 -> 执行维护 -> 效果评估 -> 持续改进
- **相关API**：
  - 接口名：monitorEquipment, scheduleMaintenance, recordMaintenance
  - 请求参数：{equipmentId: string, maintenancePlan: MaintenancePlan, maintenanceRecord: MaintenanceRecord}
  - 响应结构：{equipmentStatus: EquipmentStatus, maintenanceResult: MaintenanceResult, nextMaintenance: Date}
  - 错误处理：监控异常时切换备用监控，维护冲突时重新安排
- **权限控制**：设备工程师可管理设备，操作工人可查看状态

#### 3.2.7 生产数据分析页面
- **功能模块**：生产数据分析和决策支持
- **功能描述**：分析生产数据，提供决策支持和改进建议
- **主要界面元素**：
  - 效率分析组件：生产效率多维度分析图表
  - 成本分析组件：生产成本构成和趋势分析
  - 质量分析组件：质量指标统计和改进分析
  - 对比分析组件：不同时期、车间、产品的对比分析
  - 报表生成组件：各类生产报表自动生成
- **输入/输出字段**：
  - 字段名：productionAnalysis
  - 类型：object
  - 校验规则：分析维度有效性，数据完整性，时间范围合理性
  - 选项：分析类型（效率、成本、质量），时间粒度（日、周、月、年）
  - 依赖关系：生产数据、成本数据、质量数据、设备数据
- **交互流程**：
  - 选择分析维度 -> 设置分析参数 -> 执行数据分析 -> 查看分析结果 -> 生成改进建议
- **相关API**：
  - 接口名：analyzeProduction, generateReport, getImprovementSuggestions
  - 请求参数：{analysisType: string, timeRange: TimeRange, dimensions: string[], filters: Filter[]}
  - 响应结构：{analysisResult: AnalysisResult, report: Report, suggestions: Suggestion[]}
  - 错误处理：数据不足时扩大分析范围，计算异常时重新分析
- **权限控制**：生产总监可查看全部分析，车间主任可查看本车间分析

## 4. 用户场景与流程

### 4.1 智能切割优化场景
- **用户角色**：生产计划员、工艺技术员
- **场景描述**：基于订单需求进行玻璃原片切割方案优化，最大化原片利用率
- **前置条件**：订单需求已确认，原片库存充足，切割设备正常运行
- **操作流程**：
  1. 订单需求分析 -> 进入切割优化管理页面
  2. 选择待优化订单 -> 系统自动分析规格需求
  3. 配置优化参数 -> 选择算法、设置约束条件
  4. 执行优化计算 -> 系统生成多种切割方案
  5. 方案对比评估 -> 对比利用率、成本、时间
  6. 确认最优方案 -> 生成切割任务和工单
  7. 下达执行指令 -> 切割设备开始执行
  8. 监控执行进度 -> 实时跟踪切割进度和质量
- **状态变化与交互说明**：
  - 优化过程中实时显示计算进度
  - 方案生成后自动计算关键指标
  - 方案确认后自动更新库存预占
  - 执行过程中实时反馈切割状态
- **预期结果**：原片利用率≥95%，切割方案30秒内生成，执行效率提升20%
- **异常处理**：优化失败时提供手动方案，设备故障时自动重新分配

### 4.2 生产排程优化场景
- **用户角色**：生产计划员、生产总监
- **场景描述**：制定多工艺线的协同生产排程，优化资源配置和交期达成
- **前置条件**：订单信息完整，工艺路线确定，资源能力明确
- **操作流程**：
  1. 订单优先级设定 -> 根据交期和重要性设定优先级
  2. 资源能力评估 -> 评估设备、人员、物料等资源状况
  3. 约束条件配置 -> 设置设备能力、维护时间等约束
  4. 排程算法执行 -> 选择算法并执行排程计算
  5. 排程方案优化 -> 手工调整和优化排程方案
  6. 冲突解决处理 -> 处理资源冲突和时间冲突
  7. 排程方案审批 -> 提交审批并获得确认
  8. 排程发布执行 -> 正式发布并开始执行
- **状态变化与交互说明**：
  - 排程过程中动态显示资源利用率
  - 冲突检测时自动标记问题点
  - 方案调整时实时计算影响范围
  - 发布后自动同步到各执行系统
- **预期结果**：订单交期达成率≥95%，设备利用率≥85%，排程效率提升30%
- **异常处理**：资源冲突时提供解决建议，紧急订单时支持插单调整

### 4.3 生产过程监控场景
- **用户角色**：车间主任、工艺技术员、操作工人
- **场景描述**：实时监控生产过程，及时发现和处理生产异常
- **前置条件**：生产计划已下达，设备正常运行，监控系统正常
- **操作流程**：
  1. 工单接收确认 -> 接收生产工单并确认工艺要求
  2. 生产准备就绪 -> 确认物料、设备、人员准备就绪
  3. 生产过程启动 -> 启动生产并开始实时监控
  4. 工艺参数监控 -> 实时监控关键工艺参数
  5. 异常情况处理 -> 及时发现和处理生产异常
  6. 质量检验确认 -> 执行质量检验并确认合格
  7. 工单完工报告 -> 完工后及时报告并更新状态
  8. 数据记录归档 -> 完整记录生产数据并归档
- **状态变化与交互说明**：
  - 生产启动时自动开始数据采集
  - 参数异常时立即触发报警
  - 异常处理时记录处理过程
  - 完工确认时自动更新库存
- **预期结果**：生产异常响应时间≤5分钟，工艺参数合格率≥99%，数据记录完整率100%
- **异常处理**：参数超限时自动调整，设备故障时紧急停机

### 4.4 质量控制管理场景
- **用户角色**：质检员、质量工程师、车间主任
- **场景描述**：执行全过程质量控制，确保产品质量稳定可控
- **前置条件**：质量标准明确，检测设备正常，检验人员到位
- **操作流程**：
  1. 检验计划制定 -> 根据生产计划制定检验计划
  2. 首件检验执行 -> 对首件产品进行全面检验
  3. 过程检验监控 -> 执行过程中的巡回检验
  4. 在线检测监控 -> 监控自动检测设备数据
  5. 异常质量处理 -> 及时处理质量异常和不合格品
  6. 质量数据分析 -> 分析质量数据和趋势
  7. 质量改进跟踪 -> 跟踪质量改进措施实施
  8. 质量报告生成 -> 生成质量报告和档案
- **状态变化与交互说明**：
  - 检验过程中实时记录检验数据
  - 不合格品自动隔离和标识
  - 质量异常时自动触发改进流程
  - 质量数据实时更新到质量档案
- **预期结果**：产品合格率≥99.5%，质量异常响应时间≤10分钟，质量追溯完整率100%
- **异常处理**：质量异常时立即停产整改，检测设备故障时人工检验

### 4.5 设备维护管理场景
- **用户角色**：设备工程师、维护技工、车间主任
- **场景描述**：实施预测性维护，确保设备稳定运行和高效利用
- **前置条件**：设备档案完整，维护计划制定，维护资源准备
- **操作流程**：
  1. 设备状态监控 -> 实时监控设备运行状态和参数
  2. 维护需求分析 -> 基于数据分析确定维护需求
  3. 维护计划制定 -> 制定详细的维护计划和安排
  4. 维护资源准备 -> 准备维护所需的人员、工具、配件
  5. 维护作业执行 -> 按计划执行维护作业
  6. 维护质量检查 -> 检查维护质量和效果
  7. 设备试运行 -> 维护后进行设备试运行
  8. 维护记录归档 -> 完整记录维护过程和结果
- **状态变化与交互说明**：
  - 监控过程中异常参数自动预警
  - 维护计划自动考虑生产安排
  - 维护执行时实时更新设备状态
  - 维护完成后自动更新设备档案
- **预期结果**：设备故障率降低50%，维护成本降低20%，设备利用率≥90%
- **异常处理**：紧急故障时启动应急维护，配件缺货时寻找替代方案

### 4.6 生产数据分析场景
- **用户角色**：生产总监、生产计划员、工艺工程师
- **场景描述**：分析生产数据，发现改进机会，制定优化策略
- **前置条件**：生产数据完整，分析工具可用，分析目标明确
- **操作流程**：
  1. 分析目标确定 -> 明确分析的目标和重点
  2. 数据收集整理 -> 收集相关的生产数据
  3. 数据清洗验证 -> 清洗和验证数据的准确性
  4. 分析模型建立 -> 建立适当的分析模型
  5. 数据分析执行 -> 执行数据分析和计算
  6. 结果解读评估 -> 解读分析结果和评估意义
  7. 改进建议制定 -> 基于分析结果制定改进建议
  8. 改进措施跟踪 -> 跟踪改进措施的实施效果
- **状态变化与交互说明**：
  - 数据分析过程中显示计算进度
  - 分析结果自动生成可视化图表
  - 改进建议自动关联到责任部门
  - 改进效果持续跟踪和评估
- **预期结果**：发现改进机会10个以上，生产效率提升15%，成本降低10%
- **异常处理**：数据异常时重新收集，分析失败时调整模型

## 5. 接口依赖关系

### 5.1 与工艺管理子系统的接口依赖
- **工艺数据获取**：
  - 接口名称：getProcessData, getProcessRoute, getProcessParameters
  - 数据流向：工艺管理 -> 生产管理
  - 同步内容：工艺路线、工艺参数、作业指导书、质量标准
  - 业务规则：工艺变更时自动同步到生产系统

- **生产反馈上报**：
  - 接口名称：reportProductionFeedback, reportProcessIssues
  - 数据流向：生产管理 -> 工艺管理
  - 反馈内容：工艺执行情况、参数偏差、质量问题、改进建议
  - 同步频率：实时反馈

### 5.2 与销售管理子系统的接口依赖
- **订单生产需求**：
  - 接口名称：getProductionOrders, getOrderRequirements
  - 数据流向：销售管理 -> 生产管理
  - 同步内容：订单信息、产品规格、交期要求、优先级
  - 业务规则：订单变更时自动更新生产计划

- **生产进度反馈**：
  - 接口名称：updateProductionProgress, reportDeliveryStatus
  - 数据流向：生产管理 -> 销售管理
  - 反馈内容：生产进度、完工情况、交期预警、质量状态
  - 同步频率：每日同步

### 5.3 与项目制管理子系统的接口依赖
- **项目生产计划**：
  - 接口名称：getProjectProductionPlan, syncProjectSchedule
  - 数据流向：项目制管理 <-> 生产管理
  - 同步内容：项目生产计划、里程碑节点、资源需求、质量要求
  - 业务规则：项目计划变更时同步更新生产安排

- **项目生产状态**：
  - 接口名称：reportProjectProgress, updateProjectStatus
  - 数据流向：生产管理 -> 项目制管理
  - 反馈内容：项目生产进度、质量状态、问题风险、资源使用
  - 同步频率：实时同步

### 5.4 与采购管理子系统的接口依赖
- **物料需求计划**：
  - 接口名称：generateMaterialRequirement, updateMRPDemand
  - 数据流向：生产管理 -> 采购管理
  - 同步内容：物料需求计划、需求时间、质量要求、紧急程度
  - 业务规则：生产计划变更时自动更新物料需求

- **物料供应状态**：
  - 接口名称：getMaterialSupplyStatus, getDeliverySchedule
  - 数据流向：采购管理 -> 生产管理
  - 反馈内容：物料供应状态、到货计划、质量状态、异常预警
  - 同步频率：每日同步

### 5.5 与仓储管理子系统的接口依赖
- **原料出库需求**：
  - 接口名称：requestMaterialIssue, confirmMaterialReceipt
  - 数据流向：生产管理 -> 仓储管理
  - 同步内容：物料出库需求、出库时间、出库数量、质量要求
  - 业务规则：生产开始前确保物料及时出库

- **成品入库管理**：
  - 接口名称：reportProductionCompletion, requestProductStorage
  - 数据流向：生产管理 -> 仓储管理
  - 同步内容：成品入库信息、产品规格、数量质量、存储要求
  - 同步频率：完工后立即同步

### 5.6 与质量管理子系统的接口依赖
- **质量标准同步**：
  - 接口名称：getQualityStandards, syncInspectionPlan
  - 数据流向：质量管理 -> 生产管理
  - 同步内容：质量标准、检验计划、检验方法、判定标准
  - 业务规则：质量标准变更时自动同步到生产系统

- **质量数据上报**：
  - 接口名称：reportQualityData, reportQualityIssues
  - 数据流向：生产管理 -> 质量管理
  - 反馈内容：质量检验数据、不合格品信息、质量问题、改进建议
  - 同步频率：实时上报

### 5.7 与基础管理子系统的接口依赖
- **基础数据同步**：
  - 接口名称：syncMasterData, getCodeRules
  - 数据流向：基础管理 -> 生产管理
  - 同步内容：设备编码、工序代码、参数单位、标准规范
  - 业务规则：基础数据变更时自动同步到生产系统

- **权限控制集成**：
  - 接口名称：validateUserPermission, getAccessRights
  - 数据流向：基础管理 -> 生产管理
  - 控制内容：用户权限验证、功能访问控制、数据安全管理
  - 验证频率：每次操作验证

## 6. 非功能性需求

### 6.1 性能需求
- **响应时间要求**：
  - 实时监控数据：1秒内更新
  - 切割优化计算：30秒内完成
  - 生产排程计算：5分钟内完成复杂排程
  - 报表生成：大型报表3分钟内生成完成

- **并发处理能力**：
  - 支持100个用户同时在线监控
  - 支持20个并发切割优化请求
  - 支持50个并发数据采集点
  - 支持10个并发排程计算

- **数据处理能力**：
  - 实时处理1000个数据采集点
  - 存储10年历史生产数据
  - 支持100条生产线同时监控
  - 处理10万个工单并发执行

### 6.2 可靠性需求
- **系统可用性**：99.9%以上（7×24小时运行）
- **数据备份**：实时备份关键数据，每小时增量备份
- **故障恢复**：系统故障后10分钟内恢复服务
- **数据一致性**：确保生产数据与其他子系统的强一致性

### 6.3 安全性需求
- **访问控制**：基于角色的权限控制，支持生产数据的分级管理
- **数据加密**：生产数据传输和存储加密
- **操作审计**：完整记录生产操作日志，支持审计追踪
- **设备安全**：设备控制指令的安全验证和防篡改

### 6.4 易用性需求
- **界面友好性**：生产监控界面直观清晰，支持大屏显示
- **操作便捷性**：紧急操作一键完成，常用操作不超过3次点击
- **移动支持**：支持移动设备访问，现场操作便捷
- **多语言支持**：支持中英文界面切换

### 6.5 扩展性需求
- **设备接入**：支持不同厂商设备的标准化接入
- **算法扩展**：支持新优化算法的插件式扩展
- **数据接口**：提供标准API接口，支持第三方系统集成
- **功能模块**：支持新功能模块的快速集成

## 7. Product Backlog

### 7.1 史诗：智能切割优化系统
- **关联页面**：切割优化管理页面、生产排程管理页面
- **关联功能模块**：切割优化、方案管理、执行监控
- **史诗描述**：构建先进的智能切割优化系统，最大化原片利用率，降低生产成本

#### 用户故事
1. **作为生产计划员，我希望能够自动生成最优切割方案，以提高原片利用率**
   - 验收标准：原片利用率≥95%，方案生成时间≤30秒
   - 优先级：高
   - 故事点：21

2. **作为工艺技术员，我希望能够可视化查看切割方案，以便确认方案可行性**
   - 验收标准：切割方案可视化展示，支持方案对比分析
   - 优先级：高
   - 故事点：13

3. **作为车间主任，我希望能够监控切割执行进度，以确保按时完成**
   - 验收标准：实时显示切割进度，异常情况及时预警
   - 优先级：中
   - 故事点：8

### 7.2 史诗：生产排程管理系统
- **关联页面**：生产排程管理页面、生产执行监控页面
- **关联功能模块**：排程算法、资源调度、计划管理
- **史诗描述**：建立智能化的生产排程管理系统，优化资源配置和生产效率

#### 用户故事
1. **作为生产计划员，我希望能够自动生成生产排程，以提高排程效率**
   - 验收标准：排程自动生成，考虑多种约束条件
   - 优先级：高
   - 故事点：21

2. **作为生产总监，我希望能够可视化查看排程方案，以便决策优化**
   - 验收标准：甘特图展示排程，支持拖拽调整
   - 优先级：高
   - 故事点：13

3. **作为车间主任，我希望能够处理排程冲突，以确保生产顺利进行**
   - 验收标准：自动识别冲突，提供解决方案
   - 优先级：中
   - 故事点：8

### 7.3 史诗：生产过程监控系统
- **关联页面**：生产执行监控页面、生产管理首页
- **关联功能模块**：实时监控、数据采集、异常处理
- **史诗描述**：构建全面的生产过程监控系统，实现生产过程的透明化管理

#### 用户故事
1. **作为车间主任，我希望能够实时监控生产状态，以及时发现问题**
   - 验收标准：生产状态实时更新，异常情况立即预警
   - 优先级：高
   - 故事点：21

2. **作为工艺技术员，我希望能够监控工艺参数，以确保工艺执行标准**
   - 验收标准：关键参数实时监控，超限自动报警
   - 优先级：高
   - 故事点：13

3. **作为操作工人，我希望能够快速报告异常，以获得及时支持**
   - 验收标准：异常报告一键提交，响应时间≤5分钟
   - 优先级：中
   - 故事点：5

### 7.4 史诗：质量控制管理系统
- **关联页面**：质量控制管理页面、生产执行监控页面
- **关联功能模块**：在线检测、质量分析、追溯管理
- **史诗描述**：建立完善的质量控制管理系统，确保产品质量稳定可控

#### 用户故事
1. **作为质检员，我希望能够自动采集质量数据，以提高检验效率**
   - 验收标准：质量数据自动采集，检验结果实时记录
   - 优先级：高
   - 故事点：13

2. **作为质量工程师，我希望能够分析质量趋势，以制定改进措施**
   - 验收标准：质量趋势分析准确，改进建议可行
   - 优先级：中
   - 故事点：8

3. **作为车间主任，我希望能够快速追溯质量问题，以找到根本原因**
   - 验收标准：质量追溯完整准确，5分钟内完成查询
   - 优先级：中
   - 故事点：8

### 7.5 史诗：设备管理系统
- **关联页面**：设备管理页面、生产管理首页
- **关联功能模块**：设备监控、预测维护、资产管理
- **史诗描述**：构建智能化的设备管理系统，实现设备的高效利用和可靠运行

#### 用户故事
1. **作为设备工程师，我希望能够预测设备故障，以实现预防性维护**
   - 验收标准：故障预测准确率≥80%，维护计划自动生成
   - 优先级：中
   - 故事点：21

2. **作为维护技工，我希望能够获得维护指导，以提高维护质量**
   - 验收标准：维护指导详细准确，维护记录完整
   - 优先级：中
   - 故事点：8

3. **作为车间主任，我希望能够监控设备状态，以合理安排生产**
   - 验收标准：设备状态实时更新，利用率统计准确
   - 优先级：低
   - 故事点：5

### 7.6 史诗：生产数据分析系统
- **关联页面**：生产数据分析页面、生产管理首页
- **关联功能模块**：数据分析、报表生成、决策支持
- **史诗描述**：建立全面的生产数据分析系统，提供决策支持和持续改进

#### 用户故事
1. **作为生产总监，我希望能够分析生产效率，以制定改进策略**
   - 验收标准：效率分析全面准确，改进建议可操作
   - 优先级：中
   - 故事点：13

2. **作为成本会计，我希望能够分析生产成本，以控制成本水平**
   - 验收标准：成本分析详细准确，成本构成清晰
   - 优先级：中
   - 故事点：8

3. **作为工艺工程师，我希望能够获得工艺改进建议，以优化工艺水平**
   - 验收标准：基于数据的改进建议，改进效果可量化
   - 优先级：低
   - 故事点：5

### 7.7 功能特性优先级排序

#### 高优先级特性（Sprint 1-3）
1. 智能切割优化功能
2. 生产排程管理功能
3. 生产过程监控功能
4. 质量控制管理功能

#### 中优先级特性（Sprint 4-6）
1. 设备管理功能
2. 生产数据分析功能
3. 移动端支持
4. 高级算法优化

#### 低优先级特性（Sprint 7-8）
1. 预测性维护功能
2. 高级数据分析
3. 第三方系统集成
4. 人工智能应用

### 7.8 发布计划

#### 版本1.0（基础版本）
- 发布时间：开发启动后6个月
- 主要功能：切割优化、生产排程、过程监控、质量控制
- 目标用户：生产计划员、车间主任、质检员

#### 版本1.1（增强版本）
- 发布时间：开发启动后8个月
- 主要功能：设备管理、数据分析、移动支持
- 目标用户：设备工程师、生产总监

#### 版本2.0（智能版本）
- 发布时间：开发启动后12个月
- 主要功能：预测性维护、人工智能、高级分析
- 目标用户：技术管理层、决策层

## 8. 验收标准

### 8.1 功能验收标准
- **切割优化功能**：原片利用率≥95%，方案生成时间≤30秒
- **生产排程功能**：订单交期达成率≥95%，设备利用率≥85%
- **过程监控功能**：数据采集实时性≤1秒，异常响应时间≤5分钟
- **质量控制功能**：产品合格率≥99.5%，质量追溯完整率100%
- **设备管理功能**：设备故障率降低50%，维护成本降低20%

### 8.2 性能验收标准
- **响应时间**：实时监控1秒内响应，切割优化30秒内完成
- **并发处理**：支持100个用户同时监控，20个并发优化请求
- **数据处理**：支持1000个数据采集点，10年历史数据存储
- **系统可用性**：99.9%以上的系统可用性

### 8.3 集成验收标准
- **工艺系统集成**：工艺数据同步准确，生产反馈及时
- **销售系统集成**：订单需求同步完整，生产进度反馈准确
- **采购系统集成**：物料需求计算准确，供应状态同步及时
- **项目系统集成**：项目生产计划同步，项目进度反馈准确
- **仓储系统集成**：物料出入库数据一致，库存状态实时同步
- **质量系统集成**：质量标准同步完整，质量数据上报及时
- **基础系统集成**：基础数据同步准确，权限控制有效

### 8.4 用户体验验收标准
- **界面友好性**：生产监控界面直观清晰，符合用户操作习惯
- **操作便捷性**：常用功能3次点击内完成，紧急操作一键响应
- **响应及时性**：用户操作响应时间≤2秒，数据更新实时显示
- **错误处理**：提供清晰的错误信息和解决建议
- **帮助支持**：提供完整的在线帮助文档和操作指导

### 8.5 业务验收标准
- **生产效率提升**：整体生产效率提升≥20%
- **原片利用率**：玻璃原片利用率≥95%
- **设备利用率**：关键设备利用率≥85%
- **交期达成率**：订单交期达成率≥95%
- **质量合格率**：产品一次合格率≥99.5%
- **成本控制**：生产成本降低≥10%
- **异常响应**：生产异常响应时间≤5分钟

### 8.6 技术验收标准
- **数据准确性**：生产数据准确率≥99.9%
- **系统稳定性**：系统连续运行时间≥720小时
- **数据安全性**：数据传输加密率100%，访问控制有效率100%
- **扩展性**：支持新设备接入，支持新算法集成
- **兼容性**：与现有系统兼容，支持主流浏览器和移动设备

## 9. API接口规范

### 9.1 切割优化接口

#### 9.1.1 启动切割优化
```http
POST /api/production/cutting/optimize
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "orders": [
    {
      "orderId": "ORD001",
      "specifications": [
        {
          "width": 1200,
          "height": 800,
          "thickness": 6,
          "quantity": 100
        }
      ]
    }
  ],
  "glassStock": [
    {
      "stockId": "STK001",
      "width": 3300,
      "height": 2140,
      "thickness": 6,
      "quantity": 50
    }
  ],
  "algorithm": "genetic",
  "constraints": {
    "minUtilization": 0.95,
    "maxCalculationTime": 30
  }
}

Response:
{
  "code": 200,
  "message": "优化成功",
  "data": {
    "optimizationId": "OPT001",
    "plans": [
      {
        "planId": "PLN001",
        "utilization": 0.96,
        "cost": 15000,
        "time": 120,
        "cuttingLayout": {
          "stockId": "STK001",
          "cuts": [
            {
              "x": 0,
              "y": 0,
              "width": 1200,
              "height": 800,
              "orderId": "ORD001"
            }
          ]
        }
      }
    ]
  }
}
```

#### 9.1.2 获取切割方案
```http
GET /api/production/cutting/plans/{optimizationId}
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "plans": [
      {
        "planId": "PLN001",
        "utilization": 0.96,
        "cost": 15000,
        "status": "pending"
      }
    ]
  }
}
```

#### 9.1.3 确认切割方案
```http
POST /api/production/cutting/plans/{planId}/confirm
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "confirmedBy": "user001",
  "notes": "方案确认，开始执行"
}

Response:
{
  "code": 200,
  "message": "方案确认成功",
  "data": {
    "workOrders": [
      {
        "workOrderId": "WO001",
        "equipmentId": "EQ001",
        "status": "created"
      }
    ]
  }
}
```

### 9.2 生产排程接口

#### 9.2.1 生成生产排程
```http
POST /api/production/schedule/generate
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "orders": [
    {
      "orderId": "ORD001",
      "priority": "high",
      "dueDate": "2024-01-15"
    }
  ],
  "resources": [
    {
      "resourceId": "RES001",
      "type": "equipment",
      "capacity": 100,
      "availability": [
        {
          "start": "2024-01-01T08:00:00",
          "end": "2024-01-01T18:00:00"
        }
      ]
    }
  ],
  "algorithm": "priority",
  "constraints": {
    "maxDelay": 24
  }
}

Response:
{
  "code": 200,
  "message": "排程生成成功",
  "data": {
    "scheduleId": "SCH001",
    "schedule": [
      {
        "taskId": "TSK001",
        "orderId": "ORD001",
        "resourceId": "RES001",
        "startTime": "2024-01-01T08:00:00",
        "endTime": "2024-01-01T10:00:00"
      }
    ],
    "metrics": {
      "utilization": 0.85,
      "onTimeDelivery": 0.95
    }
  }
}
```

#### 9.2.2 发布生产排程
```http
POST /api/production/schedule/{scheduleId}/publish
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "publishedBy": "user001",
  "effectiveDate": "2024-01-01T00:00:00"
}

Response:
{
  "code": 200,
  "message": "排程发布成功",
  "data": {
    "status": "published",
    "workOrders": [
      {
        "workOrderId": "WO001",
        "status": "released"
      }
    ]
  }
}
```

### 9.3 生产监控接口

#### 9.3.1 获取生产状态
```http
GET /api/production/monitor/status
Authorization: Bearer {token}
Query Parameters:
- workshop: 车间ID
- timeRange: 时间范围

Response:
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "overview": {
      "totalOrders": 100,
      "completedOrders": 80,
      "inProgressOrders": 15,
      "delayedOrders": 5
    },
    "equipment": [
      {
        "equipmentId": "EQ001",
        "status": "running",
        "utilization": 0.85,
        "currentTask": "TSK001"
      }
    ],
    "quality": {
      "passRate": 0.995,
      "defectRate": 0.005
    }
  }
}
```

#### 9.3.2 上报生产数据
```http
POST /api/production/monitor/data
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "workOrderId": "WO001",
  "equipmentId": "EQ001",
  "timestamp": "2024-01-01T10:00:00",
  "processData": {
    "temperature": 580,
    "pressure": 1.2,
    "speed": 100
  },
  "qualityData": {
    "thickness": 6.0,
    "flatness": 0.1,
    "stress": 15
  },
  "quantity": {
    "completed": 50,
    "defective": 1
  }
}

Response:
{
  "code": 200,
  "message": "数据上报成功",
  "data": {
    "recordId": "REC001",
    "alerts": [
      {
        "type": "warning",
        "message": "温度接近上限"
      }
    ]
  }
}
```

### 9.4 质量控制接口

#### 9.4.1 创建检验计划
```http
POST /api/production/quality/inspection-plan
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "workOrderId": "WO001",
  "inspectionPoints": [
    {
      "processStep": "cutting",
      "inspectionType": "dimensional",
      "frequency": "first_piece",
      "standards": {
        "tolerance": 0.5,
        "method": "caliper"
      }
    }
  ]
}

Response:
{
  "code": 200,
  "message": "检验计划创建成功",
  "data": {
    "planId": "INS001",
    "status": "active"
  }
}
```

#### 9.4.2 记录检验结果
```http
POST /api/production/quality/inspection-result
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "planId": "INS001",
  "workOrderId": "WO001",
  "inspectionData": [
    {
      "parameter": "width",
      "measured": 1200.2,
      "standard": 1200,
      "tolerance": 0.5,
      "result": "pass"
    }
  ],
  "inspector": "user002",
  "timestamp": "2024-01-01T10:30:00"
}

Response:
{
  "code": 200,
  "message": "检验结果记录成功",
  "data": {
    "resultId": "RES001",
    "overallResult": "pass",
    "actions": []
  }
}
```

### 9.5 设备管理接口

#### 9.5.1 获取设备状态
```http
GET /api/production/equipment/status
Authorization: Bearer {token}
Query Parameters:
- equipmentId: 设备ID
- workshop: 车间ID

Response:
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "equipment": [
      {
        "equipmentId": "EQ001",
        "name": "切割机1号",
        "status": "running",
        "parameters": {
          "temperature": 25,
          "vibration": 0.1,
          "power": 15.5
        },
        "utilization": 0.85,
        "nextMaintenance": "2024-01-15"
      }
    ]
  }
}
```

#### 9.5.2 创建维护计划
```http
POST /api/production/equipment/maintenance-plan
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "equipmentId": "EQ001",
  "maintenanceType": "preventive",
  "scheduledDate": "2024-01-15T08:00:00",
  "duration": 4,
  "tasks": [
    {
      "taskName": "更换切割刀片",
      "estimatedTime": 2,
      "parts": ["PART001"]
    }
  ]
}

Response:
{
  "code": 200,
  "message": "维护计划创建成功",
  "data": {
    "planId": "MNT001",
    "status": "scheduled"
  }
}
```

### 9.6 数据分析接口

#### 9.6.1 生产效率分析
```http
GET /api/production/analysis/efficiency
Authorization: Bearer {token}
Query Parameters:
- startDate: 开始日期
- endDate: 结束日期
- dimension: 分析维度

Response:
{
  "code": 200,
  "message": "分析成功",
  "data": {
    "overall": {
      "efficiency": 0.85,
      "utilization": 0.82,
      "oee": 0.78
    },
    "trends": [
      {
        "date": "2024-01-01",
        "efficiency": 0.83,
        "utilization": 0.80
      }
    ],
    "bottlenecks": [
      {
        "process": "cutting",
        "impact": 0.15
      }
    ]
  }
}
```

#### 9.6.2 成本分析
```http
GET /api/production/analysis/cost
Authorization: Bearer {token}
Query Parameters:
- startDate: 开始日期
- endDate: 结束日期
- costType: 成本类型

Response:
{
  "code": 200,
  "message": "分析成功",
  "data": {
    "totalCost": 150000,
    "breakdown": {
      "material": 90000,
      "labor": 40000,
      "overhead": 20000
    },
    "trends": [
      {
        "date": "2024-01-01",
        "cost": 5000
      }
    ],
    "variance": {
      "actual": 150000,
      "budget": 140000,
      "variance": 10000
    }
  }
}
```

## 10. 数据模型

### 10.1 核心实体模型

#### 10.1.1 生产订单（ProductionOrder）
```json
{
  "orderId": "string",
  "salesOrderId": "string",
  "projectId": "string",
  "productSpecs": [
    {
      "specId": "string",
      "width": "number",
      "height": "number",
      "thickness": "number",
      "quantity": "number",
      "processRoute": "string"
    }
  ],
  "priority": "string",
  "dueDate": "datetime",
  "status": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

#### 10.1.2 切割方案（CuttingPlan）
```json
{
  "planId": "string",
  "optimizationId": "string",
  "glassStock": [
    {
      "stockId": "string",
      "width": "number",
      "height": "number",
      "thickness": "number",
      "quantity": "number"
    }
  ],
  "cuttingLayout": [
    {
      "stockId": "string",
      "cuts": [
        {
          "x": "number",
          "y": "number",
          "width": "number",
          "height": "number",
          "orderId": "string",
          "specId": "string"
        }
      ]
    }
  ],
  "utilization": "number",
  "cost": "number",
  "status": "string",
  "createdAt": "datetime"
}
```

#### 10.1.3 生产排程（ProductionSchedule）
```json
{
  "scheduleId": "string",
  "scheduleName": "string",
  "tasks": [
    {
      "taskId": "string",
      "orderId": "string",
      "processStep": "string",
      "resourceId": "string",
      "startTime": "datetime",
      "endTime": "datetime",
      "duration": "number",
      "status": "string"
    }
  ],
  "constraints": {
    "maxDelay": "number",
    "resourceLimits": "object"
  },
  "metrics": {
    "utilization": "number",
    "onTimeDelivery": "number"
  },
  "status": "string",
  "createdAt": "datetime",
  "publishedAt": "datetime"
}
```

#### 10.1.4 工单（WorkOrder）
```json
{
  "workOrderId": "string",
  "orderId": "string",
  "taskId": "string",
  "processStep": "string",
  "equipmentId": "string",
  "operatorId": "string",
  "processParams": {
    "temperature": "number",
    "pressure": "number",
    "speed": "number"
  },
  "qualityStandards": {
    "tolerance": "number",
    "inspectionPoints": "array"
  },
  "quantity": {
    "planned": "number",
    "completed": "number",
    "defective": "number"
  },
  "status": "string",
  "startTime": "datetime",
  "endTime": "datetime",
  "createdAt": "datetime"
}
```

#### 10.1.5 设备信息（Equipment）
```json
{
  "equipmentId": "string",
  "equipmentName": "string",
  "equipmentType": "string",
  "workshop": "string",
  "specifications": {
    "maxWidth": "number",
    "maxHeight": "number",
    "maxThickness": "number",
    "capacity": "number"
  },
  "status": "string",
  "parameters": {
    "temperature": "number",
    "vibration": "number",
    "power": "number"
  },
  "utilization": "number",
  "lastMaintenance": "datetime",
  "nextMaintenance": "datetime",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

#### 10.1.6 质量记录（QualityRecord）
```json
{
  "recordId": "string",
  "workOrderId": "string",
  "inspectionPlanId": "string",
  "inspectionType": "string",
  "inspectionData": [
    {
      "parameter": "string",
      "measured": "number",
      "standard": "number",
      "tolerance": "number",
      "result": "string"
    }
  ],
  "overallResult": "string",
  "inspector": "string",
  "inspectionTime": "datetime",
  "actions": [
    {
      "actionType": "string",
      "description": "string",
      "responsible": "string"
    }
  ],
  "createdAt": "datetime"
}
```

### 10.2 关系模型

#### 10.2.1 实体关系图
```
ProductionOrder ||--o{ WorkOrder : generates
ProductionOrder ||--o{ CuttingPlan : requires
ProductionSchedule ||--o{ WorkOrder : schedules
WorkOrder ||--o{ QualityRecord : inspects
Equipment ||--o{ WorkOrder : processes
Equipment ||--o{ MaintenanceRecord : maintains
```

#### 10.2.2 数据流关系
- **订单到工单**：生产订单分解为多个工单
- **排程到工单**：生产排程确定工单的执行时间
- **工单到质量**：工单执行过程中产生质量记录
- **设备到工单**：设备执行工单任务
- **设备到维护**：设备定期维护产生维护记录

### 10.3 状态模型

#### 10.3.1 生产订单状态流转
```
created -> planned -> scheduled -> in_progress -> completed -> closed
         -> cancelled
```

#### 10.3.2 工单状态流转
```
created -> released -> started -> in_progress -> completed -> closed
        -> suspended -> cancelled
```

#### 10.3.3 设备状态流转
```
idle -> running -> maintenance -> fault -> repair -> idle
```

#### 10.3.4 质量状态流转
```
pending -> inspecting -> pass -> approved
                      -> fail -> rework -> inspecting
                              -> scrap
```

---

**文档版本**：v1.0  
**创建日期**：2024年1月  
**最后更新**：2024年1月  
**文档状态**：已完成  
**审核状态**：待审核
