# 项目制管理子系统需求文档

## 1. 项目概述

### 1.1 项目背景
项目制管理子系统是玻璃深加工ERP系统的核心业务模块，专门针对防火窗、酒店隔断、幕墙工程等项目制业务设计。该子系统需要支持复杂的项目层级结构管理、多阶段交付模式、内外协生产协调、设计变更管理等玻璃深加工行业的特殊需求。与传统制造业的项目管理不同，玻璃深加工项目具有规格复杂、定制化程度高、现场安装要求严格等特点，需要从项目立项到售后服务的全生命周期精细化管理。

### 1.2 项目目标
- 建立完整的项目制管理体系，支持从立项到售后的全生命周期管理
- 构建灵活的项目层级结构，适应不同类型项目的管理需求
- 实现设计确认、现场复尺、分阶段交付等关键业务流程的数字化管理
- 支持内外协生产的协调管理，提高生产效率和交付质量
- 提供项目进度监控、成本控制、质量管理的一体化解决方案
- 实现与客户、设计方、施工方的协同工作，提升项目管理效率

### 1.3 目标用户
- **项目经理**：负责项目整体规划、进度控制、资源协调、风险管理
- **设计师**：负责项目设计方案制定、设计变更处理、技术方案确认
- **生产计划员**：负责生产任务分解、生产计划制定、内外协协调
- **施工人员**：负责现场安装、进度反馈、质量问题上报
- **质检员**：负责项目质量检验、质量问题处理、验收确认
- **客户代表**：查看项目进度、确认设计方案、参与验收评价

## 2. 功能需求

### 2.1 项目基础管理模块

#### 2.1.1 项目信息管理
- **项目档案管理**：
  - 项目基本信息：项目名称、项目编号、项目类型、合同金额、计划工期
  - 客户信息：业主信息、设计方信息、施工方信息、监理方信息
  - 合同信息：合同编号、签约时间、付款条件、质保期限
  - 项目地址：详细地址、地理坐标、现场联系人、交通路线
- **项目分类管理**：
  - 防火窗项目：住宅防火窗、商业防火窗、工业防火窗
  - 酒店隔断项目：淋浴房隔断、卫生间隔断、办公隔断
  - 幕墙工程项目：玻璃幕墙、石材幕墙、金属幕墙、组合幕墙
  - 装饰玻璃项目：艺术玻璃、装饰镜面、彩色玻璃
- **项目状态管理**：
  - 项目阶段：立项、设计、生产、安装、验收、售后
  - 项目状态：正常、延期、暂停、变更、完成、取消
  - 里程碑管理：关键节点定义、完成标准、责任人分配

#### 2.1.2 项目层级结构管理
- **防火窗项目层级**：
  - 项目 → 楼栋 → 楼层 → 房间 → 防火窗产品
  - 支持批量创建同类型房间和产品
  - 层级编码规则：项目编号-楼栋号-楼层号-房间号-产品序号
- **酒店隔断项目层级**：
  - 酒店 → 楼栋 → 楼层 → 房间 → 户型 → 淋浴房产品
  - 支持户型标准化管理和批量应用
  - 现场复尺确认机制：标准尺寸 → 现场复尺 → 尺寸确认 → 生产下单
- **幕墙工程项目层级**：
  - 工程 → 建筑 → 立面 → 分区 → 构件 → 玻璃单元
  - 支持BIM模型导入和三维可视化管理
  - 构件编码与设计图纸关联
- **层级权限控制**：
  - 上级节点状态影响下级节点操作权限
  - 支持层级数据的批量操作和权限继承
  - 层级变更的影响评估和审批流程

#### 2.1.3 项目团队管理
- **项目角色定义**：
  - 项目经理：项目整体负责人，拥有项目全部权限
  - 设计负责人：负责设计方案和技术确认
  - 生产负责人：负责生产计划和质量控制
  - 安装负责人：负责现场安装和进度反馈
  - 客户代表：参与设计确认和验收评价
- **团队成员管理**：
  - 成员信息：姓名、职位、联系方式、专业技能、工作经验
  - 角色分配：支持一人多角色、角色权限继承
  - 工作负荷：成员当前项目负荷、可用时间分析
  - 绩效评价：项目贡献度、工作质量评价、客户满意度

### 2.2 设计管理模块

#### 2.2.1 设计方案管理
- **设计文档管理**：
  - 设计图纸：CAD图纸、效果图、施工图、节点详图
  - 技术规范：产品规格、工艺要求、质量标准、安装要求
  - 材料清单：玻璃规格、五金配件、密封材料、安装材料
  - 版本控制：设计版本管理、变更记录、审批流程
- **设计确认流程**：
  - 初步设计 → 客户确认 → 深化设计 → 技术确认 → 生产图纸
  - 多方确认：业主确认、设计院确认、施工方确认
  - 确认记录：确认时间、确认人员、确认意见、修改要求
- **设计标准化管理**：
  - 标准户型库：常用户型的标准化设计方案
  - 标准构件库：标准化构件的设计模板
  - 设计规则库：设计约束条件、工艺可行性规则

#### 2.2.2 设计变更管理
- **变更申请管理**：
  - 变更来源：客户要求、设计优化、施工条件、法规变化
  - 变更类型：规格变更、数量变更、工艺变更、材料变更
  - 变更申请：变更原因、变更内容、影响分析、处理建议
  - 变更审批：技术审批、商务审批、客户确认、实施授权
- **变更影响评估**：
  - 技术影响：工艺可行性、质量影响、安全影响、标准符合性
  - 成本影响：材料成本、加工成本、人工成本、管理费用
  - 进度影响：设计周期、生产周期、安装周期、整体工期
  - 质量影响：产品性能、外观质量、使用寿命、维护成本
- **变更实施管理**：
  - 变更通知：相关部门通知、供应商通知、客户通知
  - 变更执行：设计修改、生产调整、采购变更、安装调整
  - 变更跟踪：实施进度、质量监控、成本控制、风险管理
  - 变更验收：技术验收、质量验收、客户验收、项目确认

#### 2.2.3 技术文档管理
- **图纸管理系统**：
  - 图纸分类：总图、详图、节点图、安装图、维护图
  - 图纸版本：版本号管理、修改记录、审批状态、发布控制
  - 图纸权限：查看权限、下载权限、打印权限、修改权限
  - 图纸关联：与项目层级关联、与产品关联、与工序关联
- **技术规范管理**：
  - 产品规范：技术参数、性能指标、质量标准、检验方法
  - 工艺规范：加工工艺、安装工艺、质量控制、安全要求
  - 材料规范：材料标准、供应商要求、检验标准、存储要求
  - 验收规范：验收标准、验收程序、验收记录、整改要求

### 2.3 生产计划管理模块

#### 2.3.1 项目生产计划
- **生产任务分解**：
  - 基于项目层级的任务分解：按楼栋、楼层、房间分解生产任务
  - 基于产品类型的任务分解：按产品规格、工艺要求分解任务
  - 基于交期要求的任务分解：按交付阶段、关键节点分解任务
  - 任务依赖关系：前置任务、并行任务、后续任务、关键路径
- **生产排程管理**：
  - 资源约束排程：设备能力、人员能力、原料供应、场地限制
  - 交期约束排程：客户要求交期、合同约定交期、关键里程碑
  - 优先级排程：项目优先级、客户重要性、合同条款、紧急程度
  - 动态调整机制：计划变更、资源调整、异常处理、优化改进
- **内外协协调**：
  - 内部生产计划：自有设备生产计划、人员安排、质量控制
  - 外协生产计划：外协厂商选择、外协任务分配、质量要求
  - 协调机制：生产进度同步、质量标准统一、交期协调
  - 风险管控：外协质量风险、交期风险、成本风险、技术风险

#### 2.3.2 物料需求计划
- **项目BOM管理**：
  - 项目级BOM：整个项目的物料需求汇总
  - 阶段级BOM：按交付阶段的物料需求分解
  - 产品级BOM：单个产品的物料需求明细
  - 动态BOM：根据设计变更动态调整物料需求
- **物料需求计算**：
  - 基于项目进度的物料需求时间计划
  - 基于生产计划的物料消耗计划
  - 基于库存状况的物料采购计划
  - 基于供应周期的物料到货计划
- **采购协调管理**：
  - 采购需求提报：物料规格、数量、质量要求、到货时间
  - 采购进度跟踪：订单状态、生产进度、物流状态、到货确认
  - 质量协调：供应商质量要求、检验标准、质量问题处理
  - 成本控制：采购成本、运输成本、质量成本、库存成本

#### 2.3.3 生产进度监控
- **实时进度跟踪**：
  - 任务级进度：每个生产任务的完成情况、质量状况、资源消耗
  - 工序级进度：关键工序的进度状态、瓶颈识别、效率分析
  - 项目级进度：整体项目进度、关键路径、里程碑达成情况
  - 异常预警：进度延误预警、质量异常预警、资源短缺预警
- **进度分析报告**：
  - 进度偏差分析：计划与实际的偏差、偏差原因、影响评估
  - 趋势分析：进度趋势、效率趋势、质量趋势、成本趋势
  - 关键路径分析：关键任务识别、瓶颈分析、优化建议
  - 资源利用分析：设备利用率、人员效率、材料消耗、成本控制

### 2.4 现场管理模块

#### 2.4.1 现场复尺管理
- **复尺计划制定**：
  - 复尺时间安排：基于项目进度、现场条件、人员安排
  - 复尺人员分配：技术人员、测量人员、记录人员、安全员
  - 复尺工具准备：测量工具、记录设备、安全设备、通讯设备
  - 复尺标准制定：测量精度、记录格式、确认流程、异常处理
- **复尺作业执行**：
  - 现场测量：尺寸测量、角度测量、平整度检查、障碍物识别
  - 数据记录：测量数据、现场照片、异常情况、特殊要求
  - 数据确认：现场确认、客户确认、技术确认、质量确认
  - 数据传输：数据上传、图纸更新、生产通知、采购调整
- **复尺数据管理**：
  - 数据存储：测量数据、图片资料、确认文档、变更记录
  - 数据分析：尺寸偏差分析、标准化程度、特殊要求统计
  - 数据应用：生产指导、质量控制、安装指导、维护参考
  - 数据安全：数据备份、访问控制、版本管理、审计跟踪

#### 2.4.2 现场安装管理
- **安装计划管理**：
  - 安装队伍组织：安装人员、技术人员、质检人员、安全员
  - 安装工具准备：安装工具、检测设备、安全设备、运输工具
  - 安装顺序规划：安装顺序、作业时间、质量检查点、安全措施
  - 现场协调：与其他施工方协调、与业主协调、与监理协调
- **安装作业执行**：
  - 安装前检查：产品质量检查、现场条件检查、工具设备检查
  - 安装过程控制：安装工艺执行、质量实时监控、安全措施落实
  - 安装质量检验：尺寸检查、外观检查、功能检查、安全检查
  - 安装完成确认：客户验收、质量确认、资料移交、保修启动
- **安装质量管理**：
  - 质量标准：安装精度、外观质量、功能性能、安全要求
  - 质量检验：自检、互检、专检、客户验收
  - 质量记录：检验记录、测试报告、照片资料、客户签字
  - 质量问题处理：问题识别、原因分析、整改措施、预防措施

#### 2.4.3 现场服务管理
- **技术服务管理**：
  - 技术指导：安装技术指导、使用培训、维护指导、故障排除
  - 技术支持：远程技术支持、现场技术支持、紧急技术支持
  - 技术文档：安装手册、使用说明、维护手册、故障处理指南
  - 技术培训：客户培训、安装人员培训、维护人员培训
- **售后服务管理**：
  - 保修服务：保修期限、保修范围、保修条件、保修流程
  - 维护服务：定期维护、预防性维护、故障维修、零件更换
  - 客户服务：客户回访、满意度调查、投诉处理、改进建议
  - 服务记录：服务内容、服务时间、服务人员、客户评价

### 2.5 项目监控模块

#### 2.5.1 进度监控管理
- **项目进度跟踪**：
  - 里程碑监控：关键里程碑的完成情况、延误风险、影响分析
  - 任务进度监控：各项任务的进度状态、完成质量、资源消耗
  - 关键路径监控：关键路径的进度状态、瓶颈识别、优化措施
  - 整体进度评估：项目整体进度、预计完成时间、风险评估
- **进度预警机制**：
  - 延误预警：进度延误的早期识别、影响评估、应对措施
  - 资源预警：资源短缺预警、资源冲突预警、资源优化建议
  - 质量预警：质量问题预警、质量风险评估、质量改进措施
  - 成本预警：成本超支预警、成本风险评估、成本控制措施
- **进度报告管理**：
  - 日报：每日进度、完成任务、存在问题、明日计划
  - 周报：周进度总结、关键成果、问题处理、下周计划
  - 月报：月度进度、里程碑达成、成本分析、风险评估
  - 专项报告：重大问题报告、变更影响报告、风险评估报告

#### 2.5.2 成本监控管理
- **项目成本跟踪**：
  - 实际成本统计：材料成本、人工成本、设备成本、管理费用
  - 预算对比分析：实际成本与预算的对比、偏差分析、原因分析
  - 成本趋势分析：成本变化趋势、成本控制效果、优化空间
  - 成本预测：基于当前进度的成本预测、完工成本估算
- **成本控制措施**：
  - 成本限额管理：各项成本的限额设定、超限预警、审批控制
  - 成本优化：材料优化、工艺优化、管理优化、效率提升
  - 变更成本控制：设计变更的成本影响、变更成本审批
  - 风险成本管理：风险成本预留、风险成本控制、风险转移
- **成本分析报告**：
  - 成本构成分析：各项成本的构成比例、成本结构优化
  - 成本效益分析：投入产出分析、效益评估、改进建议
  - 成本对标分析：与同类项目对比、行业标杆对比、改进方向
  - 成本预测报告：未来成本趋势、成本风险、控制措施

#### 2.5.3 质量监控管理
- **质量过程监控**：
  - 设计质量监控：设计方案质量、图纸质量、技术文档质量
  - 生产质量监控：原料质量、加工质量、装配质量、检验质量
  - 安装质量监控：安装工艺质量、安装精度、功能性能、外观质量
  - 服务质量监控：服务及时性、服务专业性、客户满意度
- **质量问题管理**：
  - 问题识别：质量问题的及时发现、问题分类、严重程度评估
  - 问题分析：问题原因分析、责任分析、影响评估、改进建议
  - 问题处理：纠正措施、预防措施、责任追究、效果验证
  - 问题跟踪：处理进度跟踪、效果评估、经验总结、知识积累
- **质量改进管理**：
  - 质量数据分析：质量统计、趋势分析、问题识别、改进机会
  - 质量改进计划：改进目标、改进措施、实施计划、效果评估
  - 质量培训：质量意识培训、技能培训、标准培训、经验分享
  - 质量激励：质量奖励、质量考核、质量文化、持续改进

## 3. 页面与功能映射

### 3.1 页面列表
- 项目概览页面
- 项目详情页面
- 项目层级管理页面
- 设计管理页面
- 生产计划页面
- 现场管理页面
- 项目监控页面
- 项目报表页面

### 3.2 页面功能明细

#### 3.2.1 项目概览页面
- **功能模块**：项目总体状况展示
- **主要界面元素**：
  - 仪表盘组件：项目关键指标（进度、成本、质量、风险）
  - 卡片组件：项目状态卡片（按项目类型、阶段、状态分组）
  - 图表组件：项目统计图表（进度分布、成本分析、质量趋势）
  - 地图组件：项目地理分布（项目位置、状态标识、进度显示）
  - 日历组件：项目时间轴（关键里程碑、重要事件、计划安排）
- **输入/输出字段**：
  - 字段名：projectOverview
  - 类型：object
  - 校验规则：数据权限校验，状态一致性校验，时间逻辑校验
  - 依赖关系：依赖项目基础数据、进度数据、成本数据、质量数据

#### 3.2.2 项目详情页面
- **功能模块**：项目全生命周期管理
- **主要界面元素**：
  - 标签页组件：项目信息标签页（基本信息、层级结构、任务管理、进度跟踪、设计文档、质量管理）
  - 树形组件：项目层级结构树（支持拖拽排序、批量操作、节点搜索）
  - 甘特图组件：项目进度甘特图（支持关键路径显示、任务依赖关系、资源分配）
  - 表单组件：项目信息编辑表单（分步骤表单、字段验证、自动保存）
  - 地图组件：项目地理位置显示（支持地址搜索、路径规划、周边设施）
  - 团队组件：项目团队管理（成员头像、角色标识、联系方式、工作负荷）
  - 文件组件：项目文档管理（文档分类、版本控制、在线预览、权限控制）
- **输入/输出字段**：
  - 字段名：projectDetail
  - 类型：object
  - 校验规则：项目信息完整性校验，层级结构逻辑校验，任务依赖关系校验，地址格式校验
  - 选项：项目阶段（立项、设计、生产、安装、验收、售后），任务状态（未开始、进行中、已完成、已取消、已阻塞），资源类型（人员、设备、材料）
  - 依赖关系：项目状态影响可编辑字段，层级结构影响任务分解，团队角色影响操作权限

#### 3.2.3 项目层级管理页面
- **功能模块**：项目层级结构设计和管理
- **主要界面元素**：
  - 树形组件：层级结构树（支持多选、拖拽、批量操作、层级搜索）
  - 表格组件：层级明细表格（支持内联编辑、批量导入、数据验证）
  - 模板组件：层级模板选择器（标准模板、自定义模板、模板预览）
  - 向导组件：层级创建向导（分步骤创建、智能推荐、批量生成）
  - 编码组件：层级编码生成器（编码规则、自动生成、唯一性校验）
- **输入/输出字段**：
  - 字段名：projectHierarchy
  - 类型：object
  - 校验规则：层级结构完整性校验，编码唯一性校验，层级深度限制校验
  - 依赖关系：项目类型影响层级模板，层级结构影响任务分解和权限控制

#### 3.2.4 设计管理页面
- **功能模块**：设计方案和变更管理
- **主要界面元素**：
  - 文档组件：设计文档管理（图纸上传、版本控制、在线预览、批注功能）
  - 审批组件：设计审批流程（审批状态、审批意见、审批历史、流程图）
  - 对比组件：设计版本对比（版本差异、变更标识、影响分析）
  - 3D组件：三维模型展示（BIM模型、构件展示、装配动画）
  - 计算组件：设计计算器（材料用量、成本估算、性能计算）
- **输入/输出字段**：
  - 字段名：designManagement
  - 类型：object
  - 校验规则：设计文档格式校验，版本号规则校验，审批权限校验
  - 依赖关系：设计变更影响生产计划，审批状态影响后续流程

#### 3.2.5 生产计划页面
- **功能模块**：项目生产计划制定和执行
- **主要界面元素**：
  - 甘特图组件：生产计划甘特图（任务排程、资源分配、关键路径、进度跟踪）
  - 看板组件：生产任务看板（任务状态、负责人、优先级、进度）
  - 资源组件：资源分配图（设备利用率、人员负荷、材料需求）
  - 优化组件：计划优化器（自动排程、冲突解决、效率优化）
  - 协调组件：内外协协调（外协任务、进度同步、质量要求）
- **输入/输出字段**：
  - 字段名：productionPlan
  - 类型：object
  - 校验规则：计划可行性校验，资源约束校验，交期逻辑校验
  - 依赖关系：依赖项目需求、资源状况、生产能力

#### 3.2.6 现场管理页面
- **功能模块**：现场复尺、安装、服务管理
- **主要界面元素**：
  - 地图组件：现场位置地图（项目位置、人员位置、路径规划）
  - 表单组件：现场作业表单（复尺数据、安装记录、质量检查）
  - 相机组件：现场拍照功能（照片上传、位置标记、时间戳）
  - 签名组件：电子签名（客户签字、确认签名、数字证书）
  - 通讯组件：现场通讯（语音通话、视频通话、即时消息）
- **输入/输出字段**：
  - 字段名：siteManagement
  - 类型：object
  - 校验规则：现场数据完整性校验，GPS位置校验，签名有效性校验
  - 依赖关系：现场数据影响生产调整，安装进度影响项目状态

#### 3.2.7 项目监控页面
- **功能模块**：项目进度、成本、质量监控
- **主要界面元素**：
  - 仪表盘组件：监控仪表盘（关键指标、预警信息、趋势图表）
  - 报警组件：异常报警面板（进度延误、成本超支、质量问题）
  - 分析组件：数据分析图表（进度分析、成本分析、质量分析）
  - 预测组件：趋势预测（完工预测、成本预测、风险预测）
  - 对比组件：计划实际对比（进度对比、成本对比、质量对比）
- **输入/输出字段**：
  - 字段名：projectMonitoring
  - 类型：object
  - 校验规则：监控数据实时性校验，预警阈值校验，权限访问校验
  - 依赖关系：依赖项目执行数据、基准计划数据、预警规则配置

#### 3.2.8 项目报表页面
- **功能模块**：项目分析报表和决策支持
- **主要界面元素**：
  - 报表组件：标准报表模板（进度报表、成本报表、质量报表、综合报表）
  - 图表组件：可视化图表（柱状图、折线图、饼图、散点图、热力图）
  - 筛选组件：报表筛选器（时间范围、项目类型、状态筛选、自定义条件）
  - 导出组件：报表导出（PDF导出、Excel导出、图片导出、打印功能）
  - 订阅组件：报表订阅（定时发送、邮件推送、微信推送、短信提醒）
- **输入/输出字段**：
  - 字段名：projectReports
  - 类型：object
  - 校验规则：报表参数校验，数据权限校验，导出格式校验
  - 依赖关系：依赖项目数据、用户权限、报表模板配置

## 4. 用户场景与流程

### 4.1 防火窗项目全流程管理场景

#### 4.1.1 项目立项阶段
- **用户角色**：项目经理、销售经理
- **场景描述**：某住宅小区防火窗项目从合同签订到项目启动的完整流程
- **前置条件**：合同已签订，客户信息已录入CRM系统
- **操作流程**：
  1. 销售经理在CRM系统中标记合同状态为"已签订"
  2. 项目经理接收到项目创建通知，登录项目管理系统
  3. 创建新项目，录入基本信息：
     - 项目名称：XX住宅小区防火窗工程
     - 项目类型：防火窗
     - 合同金额：500万元
     - 计划工期：90天
     - 项目地址：北京市朝阳区XX路XX号
  4. 分配项目团队：
     - 项目经理：张三
     - 设计负责人：李四
     - 生产负责人：王五
     - 安装负责人：赵六
  5. 设置项目里程碑：
     - 设计确认：项目启动后15天
     - 生产完成：项目启动后60天
     - 安装完成：项目启动后85天
     - 项目验收：项目启动后90天
  6. 系统自动生成项目编号：PJ20240115001
  7. 发送项目启动通知给相关团队成员
- **预期结果**：项目成功创建，团队成员收到通知，项目状态为"规划中"
- **异常处理**：如果客户信息不完整，系统提示补充必要信息

#### 4.1.2 项目层级结构创建
- **用户角色**：项目经理、设计师
- **场景描述**：为防火窗项目建立详细的层级结构
- **操作流程**：
  1. 项目经理选择防火窗项目模板
  2. 根据小区规划创建层级结构：
     - 项目：XX住宅小区防火窗工程
     - 楼栋：1号楼、2号楼、3号楼
     - 楼层：每栋楼1-33层
     - 房间：每层4户，共396户
     - 产品：每户2-3个防火窗
  3. 批量创建层级节点：
     - 使用Excel模板批量导入房间信息
     - 系统自动生成层级编码：PJ001-B01-F01-R01-W01
  4. 设置层级属性：
     - 房间类型：一居室、两居室、三居室
     - 防火窗规格：900×1200、1200×1500、1500×1800
     - 特殊要求：开启方式、玻璃类型、五金配置
  5. 确认层级结构，系统自动统计：
     - 总计防火窗数量：1200个
     - 按规格分类统计
     - 按楼栋分布统计
- **预期结果**：完整的项目层级结构，为后续设计和生产提供基础
- **异常处理**：层级编码冲突时自动调整，缺失信息提示补充

#### 4.1.3 设计确认阶段
- **用户角色**：设计师、客户代表、项目经理
- **场景描述**：防火窗产品设计方案的确认流程
- **操作流程**：
  1. 设计师根据项目需求制定设计方案：
     - 产品技术规格：耐火极限1.0小时，隔声性能35dB
     - 材料选择：钢质窗框、防火玻璃、防火密封条
     - 外观设计：颜色、表面处理、五金配置
  2. 上传设计文档：
     - CAD图纸：总图、详图、节点图
     - 技术规范：产品标准、检验标准、安装要求
     - 材料清单：主材、辅材、五金件
  3. 内部技术评审：
     - 技术可行性评估
     - 成本估算确认
     - 生产工艺确认
     - 质量标准确认
  4. 客户设计确认：
     - 发送设计方案给客户
     - 客户提出修改意见
     - 设计师调整方案
     - 客户最终确认签字
  5. 设计方案锁定：
     - 设计版本锁定为V1.0
     - 生成生产用图纸
     - 通知生产部门准备生产
- **预期结果**：设计方案获得客户确认，可以开始生产准备
- **异常处理**：客户要求重大变更时，重新评估成本和工期

### 4.2 酒店隔断项目复尺管理场景

#### 4.2.1 现场复尺准备
- **用户角色**：项目经理、技术人员、现场工程师
- **场景描述**：某五星级酒店淋浴房隔断项目的现场复尺准备工作
- **前置条件**：设计方案已初步确认，酒店装修进度允许复尺
- **操作流程**：
  1. 项目经理制定复尺计划：
     - 复尺时间：酒店主体装修完成后
     - 复尺范围：300间客房淋浴房
     - 复尺人员：2名技术人员，1名记录员
     - 复尺工期：5天
  2. 准备复尺工具和设备：
     - 激光测距仪、钢卷尺、角度尺
     - 平板电脑、复尺APP、相机
     - 安全帽、安全鞋、工作服
  3. 制定复尺标准：
     - 测量精度：±2mm
     - 记录格式：标准化表格
     - 照片要求：关键部位拍照
     - 确认流程：现场确认、客户签字
  4. 与酒店方协调：
     - 确认复尺时间安排
     - 申请现场通行证
     - 确认安全要求
     - 指定酒店联系人
- **预期结果**：复尺准备工作完成，可以开始现场复尺
- **异常处理**：现场条件不满足时，调整复尺计划

#### 4.2.2 现场复尺执行
- **用户角色**：现场工程师、技术人员、酒店代表
- **场景描述**：在酒店现场进行淋浴房尺寸复核测量
- **操作流程**：
  1. 现场复尺作业：
     - 按房间号顺序进行复尺
     - 测量淋浴房开口尺寸、墙体厚度、地面高差
     - 检查墙体垂直度、地面平整度
     - 识别管道、开关等障碍物位置
  2. 数据记录和确认：
     - 使用平板电脑录入测量数据
     - 拍摄现场照片，标注关键尺寸
     - 记录特殊情况和客户要求
     - 酒店代表现场确认签字
  3. 数据实时上传：
     - 复尺数据实时上传到项目系统
     - 系统自动与设计尺寸对比
     - 标识超出公差范围的尺寸
     - 生成复尺异常报告
  4. 复尺结果汇总：
     - 每日复尺进度汇总
     - 异常情况统计分析
     - 设计调整建议
     - 生产影响评估
- **预期结果**：获得准确的现场尺寸数据，为生产提供依据
- **异常处理**：发现重大尺寸偏差时，及时通知设计和生产部门

#### 4.2.3 复尺数据处理
- **用户角色**：设计师、生产计划员、项目经理
- **场景描述**：处理现场复尺数据，调整设计和生产计划
- **操作流程**：
  1. 复尺数据分析：
     - 系统自动分析复尺数据
     - 识别标准尺寸和非标尺寸
     - 统计尺寸分布和偏差情况
     - 生成尺寸调整建议
  2. 设计方案调整：
     - 设计师根据复尺数据调整设计
     - 更新产品规格和技术图纸
     - 重新计算材料用量
     - 客户确认设计变更
  3. 生产计划调整：
     - 生产计划员根据最终尺寸制定生产计划
     - 按标准尺寸和非标尺寸分类排产
     - 调整原料采购计划
     - 安排专门的非标生产线
  4. 项目计划更新：
     - 更新项目层级结构中的产品信息
     - 调整项目进度计划
     - 重新评估项目成本
     - 通知相关部门和客户
- **预期结果**：基于准确尺寸的设计和生产计划，确保产品完美安装
- **异常处理**：重大变更影响工期时，与客户协商调整交期

### 4.3 幕墙工程项目协调管理场景

#### 4.3.1 多方协调管理
- **用户角色**：项目经理、技术总监、客户代表、总包方代表
- **场景描述**：某商业综合体玻璃幕墙工程的多方协调管理
- **前置条件**：幕墙工程合同已签订，总包方已确定施工计划
- **操作流程**：
  1. 建立协调机制：
     - 成立项目协调小组
     - 制定定期协调会议制度
     - 建立信息沟通渠道
     - 明确各方责任和权限
  2. 技术方案协调：
     - 与建筑设计院协调幕墙设计方案
     - 与结构设计院确认连接节点设计
     - 与总包方协调施工界面
     - 与监理方确认质量标准
  3. 进度计划协调：
     - 与总包方协调总体施工进度
     - 确定幕墙安装的时间窗口
     - 协调与其他专业的施工顺序
     - 制定应急预案和备选方案
  4. 质量标准协调：
     - 确认幕墙性能指标要求
     - 协调检测试验安排
     - 统一质量验收标准
     - 建立质量问题处理机制
- **预期结果**：建立有效的多方协调机制，确保项目顺利实施
- **异常处理**：协调分歧时，按合同约定的争议解决机制处理

#### 4.3.2 BIM协同管理
- **用户角色**：BIM工程师、设计师、项目经理
- **场景描述**：基于BIM模型的幕墙工程协同设计和管理
- **操作流程**：
  1. BIM模型建立：
     - 基于建筑设计创建幕墙BIM模型
     - 建立幕墙构件库和标准节点
     - 设置构件编码和属性信息
     - 与建筑、结构模型进行整合
  2. 协同设计管理：
     - 多专业在统一平台上协同设计
     - 实时检查设计冲突和干涉
     - 自动生成材料统计和工程量
     - 版本控制和变更管理
  3. 施工模拟和优化：
     - 基于BIM模型进行施工模拟
     - 优化施工顺序和方法
     - 预测施工难点和风险
     - 制定详细的施工方案
  4. 项目信息集成：
     - 将BIM模型与项目管理系统集成
     - 实现设计、采购、生产、安装的信息联动
     - 基于模型进行进度和成本管控
     - 支持移动端现场应用
- **预期结果**：实现基于BIM的协同管理，提高设计和施工效率
- **异常处理**：模型冲突时，组织相关方协调解决

#### 4.3.3 分阶段交付管理
- **用户角色**：项目经理、生产经理、安装队长
- **场景描述**：幕墙工程按楼层分阶段交付的管理流程
- **操作流程**：
  1. 分阶段计划制定：
     - 根据总包进度要求制定分阶段交付计划
     - 第一阶段：1-5层幕墙，工期30天
     - 第二阶段：6-15层幕墙，工期45天
     - 第三阶段：16-30层幕墙，工期60天
  2. 生产计划协调：
     - 按阶段分解生产任务
     - 优先安排第一阶段产品生产
     - 协调内外协生产资源
     - 确保各阶段产品按时完成
  3. 安装进度管控：
     - 第一阶段安装完成后进行阶段验收
     - 总结安装经验，优化后续阶段
     - 根据安装进度调整后续生产计划
     - 确保各阶段无缝衔接
  4. 质量控制管理：
     - 每个阶段独立进行质量验收
     - 建立阶段质量档案
     - 持续改进质量控制措施
     - 确保整体工程质量
- **预期结果**：按计划完成分阶段交付，满足总包进度要求
- **异常处理**：某阶段延误时，调整后续阶段计划，确保总工期

## 5. 数据模型设计

### 5.1 核心实体模型

#### 5.1.1 项目实体（Project）
```sql
CREATE TABLE pm_project (
    id BIGINT PRIMARY KEY,
    project_code VARCHAR(50) UNIQUE NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    project_type VARCHAR(50) NOT NULL, -- FIRE_WINDOW, HOTEL_PARTITION, CURTAIN_WALL, DECORATIVE_GLASS
    contract_id BIGINT, -- 关联销售合同
    customer_id BIGINT NOT NULL,
    project_manager_id BIGINT NOT NULL,
    project_status VARCHAR(20) NOT NULL, -- PLANNING, DESIGNING, PRODUCING, INSTALLING, COMPLETED, CANCELLED
    project_stage VARCHAR(20) NOT NULL, -- INITIATION, DESIGN, PRODUCTION, INSTALLATION, ACCEPTANCE, MAINTENANCE
    contract_amount DECIMAL(20,4), -- 合同金额
    planned_start_date DATE, -- 计划开始日期
    planned_end_date DATE, -- 计划结束日期
    actual_start_date DATE, -- 实际开始日期
    actual_end_date DATE, -- 实际结束日期
    project_address TEXT, -- 项目地址
    project_location POINT, -- 项目地理坐标
    project_description TEXT, -- 项目描述
    technical_requirements TEXT, -- 技术要求
    quality_standards TEXT, -- 质量标准
    special_requirements TEXT, -- 特殊要求
    risk_level VARCHAR(20) DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, CRITICAL
    priority_level VARCHAR(20) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    created_time DATETIME,
    updated_time DATETIME
);
```

#### 5.1.2 项目层级实体（ProjectHierarchy）
```sql
CREATE TABLE pm_project_hierarchy (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    parent_id BIGINT, -- 父节点ID，根节点为NULL
    node_code VARCHAR(100) NOT NULL, -- 节点编码
    node_name VARCHAR(200) NOT NULL, -- 节点名称
    node_type VARCHAR(50) NOT NULL, -- PROJECT, BUILDING, FLOOR, ROOM, UNIT_TYPE, PRODUCT
    node_level INT NOT NULL, -- 层级深度
    sort_order INT DEFAULT 0, -- 排序号
    node_status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, INACTIVE, DELETED
    node_attributes JSON, -- 节点属性（规格、数量、要求等）
    quantity DECIMAL(15,3) DEFAULT 0, -- 数量
    unit VARCHAR(20), -- 单位
    specifications TEXT, -- 规格说明
    technical_requirements TEXT, -- 技术要求
    special_notes TEXT, -- 特殊说明
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_project_parent (project_id, parent_id),
    INDEX idx_node_code (node_code)
);
```

#### 5.1.3 项目团队实体（ProjectTeam）
```sql
CREATE TABLE pm_project_team (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role_code VARCHAR(50) NOT NULL, -- PROJECT_MANAGER, DESIGNER, PRODUCTION_MANAGER, INSTALLER, QC_INSPECTOR
    role_name VARCHAR(100) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE, -- 是否主要负责人
    join_date DATE NOT NULL, -- 加入日期
    leave_date DATE, -- 离开日期
    responsibilities TEXT, -- 职责描述
    contact_info JSON, -- 联系方式
    work_load_percentage DECIMAL(5,2) DEFAULT 100, -- 工作负荷百分比
    performance_rating DECIMAL(3,2), -- 绩效评分
    created_time DATETIME,
    UNIQUE KEY uk_project_user_role (project_id, user_id, role_code)
);
```

#### 5.1.4 设计文档实体（DesignDocument）
```sql
CREATE TABLE pm_design_document (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    hierarchy_id BIGINT, -- 关联层级节点
    document_code VARCHAR(100) NOT NULL,
    document_name VARCHAR(200) NOT NULL,
    document_type VARCHAR(50) NOT NULL, -- CAD_DRAWING, TECHNICAL_SPEC, BOM, MANUAL, CERTIFICATE
    document_category VARCHAR(50), -- GENERAL, DETAIL, NODE, INSTALLATION, MAINTENANCE
    version VARCHAR(20) NOT NULL DEFAULT '1.0',
    document_status VARCHAR(20) NOT NULL, -- DRAFT, REVIEWING, APPROVED, PUBLISHED, ARCHIVED
    file_path VARCHAR(500), -- 文件路径
    file_size BIGINT, -- 文件大小
    file_format VARCHAR(20), -- 文件格式
    created_by BIGINT NOT NULL,
    reviewed_by BIGINT, -- 审核人
    approved_by BIGINT, -- 批准人
    review_comments TEXT, -- 审核意见
    approval_comments TEXT, -- 批准意见
    is_current_version BOOLEAN DEFAULT TRUE, -- 是否当前版本
    parent_document_id BIGINT, -- 父文档ID（版本关系）
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_project_type (project_id, document_type),
    INDEX idx_document_code (document_code)
);
```

#### 5.1.5 设计变更实体（DesignChange）
```sql
CREATE TABLE pm_design_change (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    change_code VARCHAR(100) NOT NULL,
    change_title VARCHAR(200) NOT NULL,
    change_type VARCHAR(50) NOT NULL, -- SPECIFICATION, QUANTITY, PROCESS, MATERIAL, SCHEDULE
    change_source VARCHAR(50) NOT NULL, -- CUSTOMER, DESIGN_OPTIMIZATION, SITE_CONDITION, REGULATION
    change_reason TEXT NOT NULL, -- 变更原因
    change_description TEXT NOT NULL, -- 变更描述
    change_status VARCHAR(20) NOT NULL, -- SUBMITTED, REVIEWING, APPROVED, REJECTED, IMPLEMENTED
    impact_assessment JSON, -- 影响评估（技术、成本、进度、质量）
    cost_impact DECIMAL(15,4) DEFAULT 0, -- 成本影响
    schedule_impact INT DEFAULT 0, -- 进度影响（天数）
    submitted_by BIGINT NOT NULL, -- 提交人
    submitted_time DATETIME NOT NULL,
    reviewed_by BIGINT, -- 审核人
    reviewed_time DATETIME,
    approved_by BIGINT, -- 批准人
    approved_time DATETIME,
    implemented_by BIGINT, -- 实施人
    implemented_time DATETIME,
    review_comments TEXT, -- 审核意见
    approval_comments TEXT, -- 批准意见
    created_time DATETIME,
    updated_time DATETIME
);
```

#### 5.1.6 项目任务实体（ProjectTask）
```sql
CREATE TABLE pm_project_task (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    hierarchy_id BIGINT, -- 关联层级节点
    parent_task_id BIGINT, -- 父任务ID
    task_code VARCHAR(100) NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL, -- DESIGN, PRODUCTION, PROCUREMENT, INSTALLATION, QC, DELIVERY
    task_category VARCHAR(50), -- 任务分类
    task_status VARCHAR(20) NOT NULL, -- NOT_STARTED, IN_PROGRESS, COMPLETED, CANCELLED, BLOCKED
    task_priority VARCHAR(20) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    planned_start_date DATE, -- 计划开始日期
    planned_end_date DATE, -- 计划结束日期
    actual_start_date DATE, -- 实际开始日期
    actual_end_date DATE, -- 实际结束日期
    planned_duration INT, -- 计划工期（天）
    actual_duration INT, -- 实际工期（天）
    progress_percentage DECIMAL(5,2) DEFAULT 0, -- 完成百分比
    assigned_to BIGINT, -- 负责人
    task_description TEXT, -- 任务描述
    deliverables TEXT, -- 交付物
    acceptance_criteria TEXT, -- 验收标准
    dependencies JSON, -- 依赖关系
    resources_required JSON, -- 所需资源
    estimated_effort DECIMAL(8,2), -- 预估工作量（人天）
    actual_effort DECIMAL(8,2), -- 实际工作量（人天）
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_project_status (project_id, task_status),
    INDEX idx_assigned_to (assigned_to)
);
```

#### 5.1.7 现场作业实体（SiteWork）
```sql
CREATE TABLE pm_site_work (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    hierarchy_id BIGINT, -- 关联层级节点
    work_type VARCHAR(50) NOT NULL, -- SURVEY, MEASUREMENT, INSTALLATION, INSPECTION, MAINTENANCE
    work_code VARCHAR(100) NOT NULL,
    work_title VARCHAR(200) NOT NULL,
    work_status VARCHAR(20) NOT NULL, -- PLANNED, IN_PROGRESS, COMPLETED, CANCELLED
    planned_date DATE, -- 计划日期
    actual_date DATE, -- 实际日期
    work_location TEXT, -- 作业位置
    work_location_gps POINT, -- GPS坐标
    assigned_team JSON, -- 作业团队
    work_description TEXT, -- 作业描述
    work_requirements TEXT, -- 作业要求
    safety_requirements TEXT, -- 安全要求
    tools_equipment JSON, -- 工具设备
    work_results JSON, -- 作业结果
    measurement_data JSON, -- 测量数据
    quality_check_results JSON, -- 质量检查结果
    photos JSON, -- 现场照片
    customer_signature TEXT, -- 客户签字
    worker_signature TEXT, -- 作业人员签字
    completion_notes TEXT, -- 完成说明
    issues_found TEXT, -- 发现问题
    follow_up_actions TEXT, -- 后续行动
    created_by BIGINT NOT NULL,
    created_time DATETIME,
    updated_time DATETIME
);
```

#### 5.1.8 项目里程碑实体（ProjectMilestone）
```sql
CREATE TABLE pm_project_milestone (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    milestone_code VARCHAR(100) NOT NULL,
    milestone_name VARCHAR(200) NOT NULL,
    milestone_type VARCHAR(50) NOT NULL, -- DESIGN_APPROVAL, PRODUCTION_START, DELIVERY, INSTALLATION, ACCEPTANCE
    milestone_category VARCHAR(50), -- INTERNAL, CUSTOMER, REGULATORY, CONTRACTUAL
    planned_date DATE NOT NULL, -- 计划日期
    baseline_date DATE, -- 基线日期
    actual_date DATE, -- 实际日期
    milestone_status VARCHAR(20) NOT NULL, -- PENDING, ACHIEVED, MISSED, CANCELLED
    achievement_criteria TEXT, -- 达成标准
    deliverables TEXT, -- 交付物
    responsible_person BIGINT, -- 负责人
    stakeholders JSON, -- 相关方
    dependencies JSON, -- 依赖关系
    risks JSON, -- 风险因素
    achievement_evidence TEXT, -- 达成证据
    delay_reason TEXT, -- 延误原因
    impact_assessment TEXT, -- 影响评估
    corrective_actions TEXT, -- 纠正措施
    created_time DATETIME,
    updated_time DATETIME
);
```

### 5.2 实体关系图
- Project ←→ ProjectHierarchy（一对多关系）
- Project ←→ ProjectTeam（一对多关系）
- Project ←→ DesignDocument（一对多关系）
- Project 