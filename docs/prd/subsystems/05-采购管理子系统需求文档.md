# 采购管理子系统需求文档

## 1. 项目概述

### 1.1 项目背景
采购管理子系统是玻璃深加工ERP系统的核心供应链模块，专门针对玻璃深加工行业的复杂采购需求和多样化供应商管理设计。该子系统需要支持玻璃原片、五金配件、密封材料、包装材料等多种物料类型的采购管理，处理从MRP需求计算、采购计划制定、供应商选择到采购执行的全流程管理。与传统制造业不同，玻璃深加工采购具有规格复杂、质量要求严格、外协依赖度高、供应商专业化程度高等特点，需要精细化的采购控制和智能化的供应商管理。

### 1.2 项目目标
- 建立完整的MRP需求计算体系，实现基于销售订单和库存状况的智能需求预测
- 构建供应商全生命周期管理平台，提升供应商质量和供应稳定性
- 实现采购全流程的数字化管理，提高采购效率和成本控制能力
- 支持玻璃深加工行业的特殊采购需求，确保原料质量和交期保障
- 建立完善的外协管理体系，优化外协资源配置和质量控制
- 提供采购数据分析和决策支持，持续优化采购策略和成本结构

### 1.3 目标用户
- **采购总监**：制定采购策略，监控采购绩效，管理供应商关系
- **采购经理**：管理采购团队，审批采购计划，处理供应商问题
- **采购员**：执行采购任务，维护供应商关系，处理日常采购事务
- **外协专员**：管理外协供应商，跟踪外协订单，控制外协质量
- **质检员**：执行到货检验，处理质量问题，维护供应商质量档案
- **仓库管理员**：处理到货验收，执行入库操作，反馈库存状况

## 2. 功能需求

### 2.1 MRP需求计算模块

#### 2.1.1 需求来源管理
- **销售订单需求**：
  - 确认订单需求：基于已确认销售订单计算物料需求
  - 预测订单需求：基于销售预测和历史数据计算预期需求
  - 紧急订单需求：紧急订单的物料需求优先级处理
  - 订单变更影响：订单变更对物料需求的动态调整
- **项目制需求**：
  - 项目物料需求：基于项目BOM和进度计划计算物料需求
  - 分阶段需求：按项目交付阶段分解物料需求时间计划
  - 项目变更影响：设计变更对物料需求的影响评估和调整
  - 现场复尺调整：基于现场复尺结果调整物料规格和数量
- **库存补充需求**：
  - 安全库存需求：基于安全库存设置计算补充需求
  - 最小订货量：考虑供应商最小订货量的需求调整
  - 季节性需求：基于季节性销售模式的库存准备
  - 呆滞库存处理：呆滞库存的处理和替代方案

#### 2.1.2 需求计算引擎
- **BOM展开计算**：
  - 多层级BOM展开：支持多层级产品结构的物料需求展开
  - 替代料处理：主料缺货时的替代料自动推荐和计算
  - 损耗率计算：考虑生产损耗和运输损耗的需求调整
  - 工艺路线影响：不同工艺路线对物料需求的影响
- **时间需求计算**：
  - 提前期计算：考虑采购提前期、生产提前期、运输时间
  - 关键路径分析：识别影响交期的关键物料和供应商
  - 需求时间分布：按时间轴分布物料需求，支持分批采购
  - 紧急需求处理：紧急需求的快速响应和资源调配
- **库存平衡计算**：
  - 现有库存扣减：基于实时库存数据计算净需求
  - 在途库存考虑：考虑已下单未到货的在途库存
  - 预留库存处理：已分配但未出库的预留库存影响
  - 质量状态影响：不合格库存、待检库存对可用库存的影响

#### 2.1.3 需求优化算法
- **批量优化**：
  - 经济订货批量：基于EOQ模型计算最优订货批量
  - 供应商最小起订量：满足供应商最小起订量要求的批量调整
  - 运输成本优化：考虑运输成本的批量和频次优化
  - 仓储成本平衡：平衡库存成本和缺货成本的最优批量
- **供应商分配**：
  - 多供应商分配：同一物料多个供应商的订单分配策略
  - 供应商产能约束：考虑供应商产能限制的订单分配
  - 风险分散策略：避免单一供应商依赖的风险分散
  - 成本优化分配：基于总成本最低的供应商选择和分配
- **时间优化**：
  - 需求合并：相近时间需求的合并采购优化
  - 提前采购：基于价格波动和供应风险的提前采购决策
  - 分批交付：大批量订单的分批交付时间安排
  - 应急预案：供应中断时的应急采购和替代方案

### 2.2 采购计划管理模块

#### 2.2.1 采购计划制定
- **年度采购计划**：
  - 战略采购规划：基于公司战略和市场预测制定年度采购计划
  - 品类采购策略：不同物料品类的采购策略和供应商策略
  - 预算分配计划：采购预算的分配和控制计划
  - 供应商发展计划：新供应商开发和现有供应商优化计划
- **月度采购计划**：
  - 月度需求汇总：基于MRP计算结果制定月度采购计划
  - 供应商产能协调：与主要供应商协调月度供应能力
  - 资金需求计划：月度采购资金需求和付款计划
  - 风险预警计划：月度供应风险识别和应对措施
- **周度执行计划**：
  - 周度采购任务：具体的采购任务分解和责任分配
  - 紧急需求处理：紧急采购需求的快速响应计划
  - 供应商沟通计划：与供应商的定期沟通和协调安排
  - 质量检验计划：到货检验和质量控制计划

#### 2.2.2 采购预算管理
- **预算编制管理**：
  - 预算分类设置：按物料类别、供应商类别、项目类别设置预算
  - 预算审批流程：预算编制、审核、批准的完整流程
  - 预算调整机制：预算执行过程中的调整申请和审批
  - 预算版本管理：预算的版本控制和变更记录
- **预算执行控制**：
  - 预算额度控制：采购订单金额的预算额度检查和控制
  - 预算执行监控：实时监控预算执行情况和偏差分析
  - 超预算预警：预算超支的预警机制和审批流程
  - 预算调剂管理：不同预算科目间的资金调剂
- **预算分析报告**：
  - 预算执行分析：预算执行率、偏差分析、趋势分析
  - 成本结构分析：采购成本的构成分析和优化建议
  - 供应商成本分析：不同供应商的成本对比和评价
  - 预算绩效评估：预算管理的绩效评估和改进建议

#### 2.2.3 采购策略管理
- **品类采购策略**：
  - 玻璃原片策略：基于规格、品牌、质量等级的采购策略
  - 五金配件策略：标准件集中采购、定制件分散采购策略
  - 外协服务策略：核心工艺自制、非核心工艺外协策略
  - 包装材料策略：环保要求、成本控制、供应稳定性策略
- **供应商策略**：
  - 供应商分级：战略供应商、重要供应商、一般供应商分级管理
  - 供应商发展：新供应商培育、现有供应商能力提升
  - 供应商整合：供应商数量优化、供应商关系深化
  - 供应商风险管理：供应商风险评估、风险分散、应急预案
- **采购模式策略**：
  - 集中采购：大宗原料、标准件的集中采购模式
  - 分散采购：定制件、紧急件的分散采购模式
  - 框架协议：与主要供应商签订框架协议，按需下单
  - 电子采购：标准化物料的电子化采购平台

### 2.3 供应商管理模块

#### 2.3.1 供应商档案管理
- **基础信息管理**：
  - 供应商基本信息：公司名称、注册地址、联系方式、法人代表
  - 资质证照管理：营业执照、生产许可证、质量认证、安全认证
  - 财务信息：注册资本、年营业额、财务状况、信用等级
  - 技术能力：生产设备、技术水平、研发能力、质量控制能力
- **供应商分类**：
  - 按物料分类：玻璃原片供应商、五金配件供应商、外协加工商
  - 按重要性分类：战略供应商、重要供应商、一般供应商、备选供应商
  - 按地域分类：本地供应商、国内供应商、国外供应商
  - 按合作模式：长期合作、短期合作、框架协议、现货采购
- **供应商状态管理**：
  - 供应商生命周期：潜在→认证→合格→优秀→战略→淘汰
  - 状态变更流程：状态变更的申请、审核、批准流程
  - 状态权限控制：不同状态供应商的业务权限控制
  - 状态监控预警：供应商状态异常的监控和预警

#### 2.3.2 供应商认证管理
- **认证标准体系**：
  - 基础认证标准：法律合规、财务状况、基本生产能力
  - 质量认证标准：质量管理体系、产品质量标准、检测能力
  - 技术认证标准：技术水平、工艺能力、设备先进性
  - 服务认证标准：交付能力、响应速度、售后服务
- **认证流程管理**：
  - 认证申请：供应商认证申请和资料提交
  - 资料审核：供应商提交资料的完整性和真实性审核
  - 现场审核：供应商现场考察和能力评估
  - 样品测试：产品样品的质量测试和性能验证
  - 认证决策：基于审核结果的认证决策和等级评定
- **认证结果管理**：
  - 认证证书：供应商认证证书的颁发和管理
  - 认证有效期：认证证书的有效期管理和到期提醒
  - 复审管理：定期复审和认证更新
  - 认证撤销：不符合要求时的认证撤销流程

#### 2.3.3 供应商绩效管理
- **绩效评价体系**：
  - 质量绩效：产品合格率、质量稳定性、质量改进能力
  - 交付绩效：准时交付率、交付数量准确性、应急响应能力
  - 成本绩效：价格竞争力、成本控制能力、降本贡献
  - 服务绩效：沟通配合、技术支持、售后服务质量
- **绩效数据收集**：
  - 自动数据收集：从采购订单、入库检验、财务结算自动收集数据
  - 手工数据录入：服务质量、沟通配合等主观评价数据录入
  - 第三方数据：行业评价、客户反馈、市场信息
  - 供应商自评：供应商自我评价和改进计划
- **绩效分析报告**：
  - 供应商排名：基于综合绩效的供应商排名和分级
  - 绩效趋势分析：供应商绩效的变化趋势和改进效果
  - 对标分析：同类供应商的对比分析和最佳实践
  - 改进建议：基于绩效分析的供应商改进建议

#### 2.3.4 供应商关系管理
- **战略合作管理**：
  - 战略伙伴关系：与核心供应商建立长期战略合作关系
  - 合作协议管理：框架协议、战略合作协议的签订和管理
  - 联合开发：与供应商联合开发新产品、新工艺
  - 信息共享：与战略供应商的信息共享和协同计划
- **日常沟通管理**：
  - 定期会议：与主要供应商的定期沟通会议
  - 问题处理：供应商问题的快速响应和处理机制
  - 培训支持：对供应商的技术培训和管理培训
  - 激励机制：优秀供应商的激励和表彰机制
- **风险管理**：
  - 风险识别：供应商风险的识别和评估
  - 风险监控：供应商风险的持续监控和预警
  - 风险应对：供应商风险的应对措施和应急预案
  - 风险分散：通过多供应商策略分散供应风险

### 2.4 采购执行模块

#### 2.4.1 采购申请管理
- **申请创建流程**：
  - 需求部门申请：基于MRP计算结果或部门实际需求创建采购申请
  - 申请信息完整性：物料规格、数量、质量要求、交期要求
  - 预算检查：采购申请的预算额度检查和可用性确认
  - 供应商推荐：系统基于历史数据推荐合适的供应商
- **申请审批流程**：
  - 多级审批：部门经理→采购经理→财务经理→总经理
  - 审批权限：基于申请金额、物料类型、紧急程度的审批权限
  - 并行审批：技术审批、商务审批、财务审批的并行处理
  - 审批时效：审批时限控制和超时处理机制
- **申请变更管理**：
  - 变更申请：采购申请的规格、数量、交期变更
  - 变更影响评估：变更对成本、交期、质量的影响评估
  - 变更审批：变更申请的审批流程和权限控制
  - 变更通知：变更确认后的相关方通知机制

#### 2.4.2 采购订单管理
- **订单创建管理**：
  - 基于申请创建：从已审批的采购申请创建采购订单
  - 供应商选择：基于价格、质量、交期的供应商选择
  - 合同条款：标准合同条款和特殊条款的设置
  - 订单确认：供应商订单确认和交期承诺
- **订单执行跟踪**：
  - 生产进度跟踪：供应商生产进度的跟踪和监控
  - 质量过程控制：生产过程中的质量控制和检验
  - 交期管理：交期风险识别和应对措施
  - 异常处理：订单执行过程中异常情况的处理
- **订单变更管理**：
  - 变更类型：规格变更、数量变更、交期变更、价格变更
  - 变更协商：与供应商的变更协商和确认
  - 变更成本：变更对成本和交期的影响评估
  - 变更确认：变更协议的签署和执行

#### 2.4.3 到货验收管理
- **到货通知处理**：
  - 供应商发货通知：供应商发货信息和物流跟踪
  - 到货预约：与供应商协调到货时间和卸货安排
  - 验收准备：验收人员、工具、场地的准备
  - 验收标准：验收标准和检验方法的确认
- **验收作业执行**：
  - 数量验收：到货数量与订单数量的核对
  - 质量检验：按照质量标准进行抽样检验
  - 包装检查：包装完整性和标识正确性检查
  - 单据核对：送货单、发票、质量证书的核对
- **验收结果处理**：
  - 合格品处理：合格品的入库和库位分配
  - 不合格品处理：不合格品的隔离、标识、处理
  - 验收记录：验收结果的记录和归档
  - 供应商反馈：验收结果向供应商的反馈

### 2.5 外协管理模块

#### 2.5.1 外协需求管理
- **外协工序识别**：
  - 工艺能力评估：内部工艺能力与需求的匹配分析
  - 外协工序确定：需要外协的工序和工艺环节
  - 外协标准制定：外协工序的技术标准和质量要求
  - 外协成本评估：外协成本与自制成本的对比分析
- **外协计划制定**：
  - 外协需求计划：基于生产计划制定外协需求计划
  - 外协商选择：外协供应商的选择和能力匹配
  - 外协时间安排：外协工序的时间安排和进度协调
  - 外协质量计划：外协质量控制和检验计划

#### 2.5.2 外协供应商管理
- **外协商认证**：
  - 技术能力认证：外协商的技术水平和工艺能力认证
  - 质量体系认证：外协商的质量管理体系认证
  - 产能评估：外协商的生产能力和交付能力评估
  - 现场审核：外协商生产现场的实地审核
- **外协商分类**：
  - 按工艺分类：切割外协、磨边外协、钢化外协、镀膜外协
  - 按能力分类：核心外协商、一般外协商、备选外协商
  - 按地域分类：本地外协商、异地外协商
  - 按合作模式：长期合作、项目合作、临时合作
- **外协商绩效**：
  - 质量绩效：外协产品的质量合格率和稳定性
  - 交期绩效：外协订单的准时完成率
  - 成本绩效：外协成本的竞争力和控制能力
  - 配合绩效：沟通配合、技术支持、应急响应

#### 2.5.3 外协订单管理
- **外协订单创建**：
  - 外协任务分解：生产任务的外协工序分解
  - 技术要求确定：外协工序的技术参数和质量标准
  - 外协商选择：基于能力和成本的外协商选择
  - 订单条款协商：外协订单的商务条款和技术条款
- **外协执行监控**：
  - 进度跟踪：外协订单的执行进度跟踪
  - 质量监控：外协过程中的质量控制和检验
  - 技术支持：向外协商提供技术指导和支持
  - 异常处理：外协执行过程中异常情况的处理
- **外协交付管理**：
  - 交付验收：外协产品的交付验收和质量检验
  - 入库管理：外协产品的入库和库位管理
  - 结算管理：外协费用的结算和付款
  - 质量追溯：外协产品的质量追溯和责任界定

### 2.6 采购分析模块

#### 2.6.1 采购成本分析
- **成本构成分析**：
  - 直接成本：原料成本、加工成本、运输成本
  - 间接成本：质量成本、管理成本、风险成本
  - 成本趋势：采购成本的历史趋势和未来预测
  - 成本对比：不同供应商、不同时期的成本对比
- **价格分析管理**：
  - 价格走势：主要原料的价格走势分析和预测
  - 价格敏感性：价格变动对总成本的影响分析
  - 价格基准：建立价格基准和价格指数
  - 价格谈判：基于价格分析的谈判策略制定
- **成本优化建议**：
  - 供应商优化：基于成本分析的供应商选择优化
  - 采购策略优化：采购批量、采购时机的优化
  - 规格优化：产品规格标准化的成本优化
  - 流程优化：采购流程优化的成本节约

#### 2.6.2 供应商分析
- **供应商绩效分析**：
  - 综合绩效排名：供应商综合绩效的排名和分级
  - 绩效趋势分析：供应商绩效的变化趋势
  - 绩效对比分析：同类供应商的绩效对比
  - 绩效改进跟踪：供应商绩效改进措施的效果跟踪
- **供应商风险分析**：
  - 风险识别：供应商风险因素的识别和评估
  - 风险等级：供应商风险等级的划分和管理
  - 风险监控：供应商风险的持续监控和预警
  - 风险应对：供应商风险的应对策略和措施
- **供应商发展分析**：
  - 发展潜力评估：供应商发展潜力和合作前景
  - 能力提升计划：供应商能力提升的支持计划
  - 合作深化建议：深化供应商合作的建议和措施
  - 新供应商开发：新供应商开发的需求和计划

#### 2.6.3 采购绩效分析
- **采购效率分析**：
  - 采购周期：从需求提出到货物到达的采购周期
  - 采购成本：采购成本的控制和优化效果
  - 采购质量：采购物料的质量合格率和稳定性
  - 采购服务：采购服务的及时性和专业性
- **采购合规分析**：
  - 流程合规：采购流程的合规性和规范性
  - 制度执行：采购制度的执行情况和偏差分析
  - 权限控制：采购权限的控制和监督
  - 审计发现：内外部审计发现问题的整改情况
- **采购改进建议**：
  - 流程优化：采购流程的优化建议和改进措施
  - 制度完善：采购制度的完善和更新建议
  - 能力提升：采购团队能力提升的培训计划
  - 系统优化：采购系统功能的优化和升级建议

## 3. 页面与功能映射

### 3.1 页面列表
- 采购概览页面
- MRP需求计算页面
- 采购计划管理页面
- 供应商管理页面
- 采购申请页面
- 采购订单页面
- 外协管理页面
- 采购分析页面

### 3.2 页面功能明细

#### 3.2.1 采购概览页面
- **功能模块**：采购业务总体状况展示
- **主要界面元素**：
  - 仪表盘组件：采购关键指标（采购金额、订单数量、供应商数量、交付及时率）
  - 卡片组件：待处理任务卡片（待审批申请、待确认订单、逾期订单、质量问题）
  - 图表组件：采购统计图表（月度采购趋势、品类分布、供应商分布、成本分析）
  - 预警组件：采购预警信息（库存预警、交期预警、质量预警、价格异常）
  - 日历组件：采购计划日历（重要交期、供应商会议、质量审核、合同到期）
- **输入/输出字段**：
  - 字段名：procurementOverview
  - 类型：object
  - 校验规则：数据权限校验，时间范围校验，指标计算准确性校验
  - 依赖关系：依赖采购订单数据、供应商数据、库存数据、质量数据

#### 3.2.2 MRP需求计算页面
- **功能模块**：物料需求计算和分析
- **主要界面元素**：
  - 参数组件：MRP计算参数设置（计算周期、提前期、安全库存、损耗率）
  - 表格组件：需求明细表格（物料编码、需求数量、需求时间、来源订单、库存状况）
  - 计算组件：需求计算引擎（BOM展开、库存扣减、批量优化、供应商分配）
  - 分析组件：需求分析图表（需求趋势、品类分布、紧急程度、供应商分布）
  - 优化组件：采购建议（合并建议、提前建议、替代建议、供应商推荐）
- **输入/输出字段**：
  - 字段名：mrpCalculation
  - 类型：object
  - 校验规则：BOM数据完整性校验，库存数据准确性校验，计算参数合理性校验
  - 依赖关系：依赖销售订单、项目需求、BOM数据、库存数据、供应商数据

#### 3.2.3 采购计划管理页面
- **功能模块**：采购计划制定和执行管理
- **主要界面元素**：
  - 甘特图组件：采购计划甘特图（采购任务、时间安排、依赖关系、关键路径）
  - 表格组件：计划明细表格（物料信息、计划数量、计划时间、负责人、执行状态）
  - 预算组件：采购预算管理（预算分配、执行情况、偏差分析、调整申请）
  - 策略组件：采购策略设置（品类策略、供应商策略、采购模式、风险控制）
  - 审批组件：计划审批流程（提交审批、审批状态、审批意见、版本管理）
- **输入/输出字段**：
  - 字段名：procurementPlan
  - 类型：object
  - 校验规则：计划可行性校验，预算额度校验，时间逻辑校验，审批权限校验
  - 依赖关系：依赖MRP需求、预算数据、供应商能力、市场价格

#### 3.2.4 供应商管理页面
- **功能模块**：供应商全生命周期管理
- **主要界面元素**：
  - 档案组件：供应商档案管理（基本信息、资质证照、联系人、合作历史）
  - 认证组件：供应商认证管理（认证标准、认证流程、认证结果、复审计划）
  - 绩效组件：供应商绩效评价（绩效指标、评价结果、排名对比、改进计划）
  - 关系组件：供应商关系管理（合作等级、沟通记录、合同管理、风险评估）

## 4. 数据模型设计

### 4.1 核心实体模型

#### 4.1.1 供应商实体（Supplier）
```sql
CREATE TABLE pm_supplier (
    id BIGINT PRIMARY KEY,
    supplier_code VARCHAR(50) UNIQUE NOT NULL,
    supplier_name VARCHAR(200) NOT NULL,
    supplier_type VARCHAR(50) NOT NULL, -- MATERIAL, COMPONENT, SUBCONTRACTOR, SERVICE
    supplier_category VARCHAR(50), -- GLASS_SHEET, GLASS_FIBER, GLASS_CUTTING, etc.
    business_license VARCHAR(100),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(100),
    company_address TEXT,
    technical_capability JSON, -- 技术能力详情
    financial_info JSON, -- 财务信息
    credit_rating VARCHAR(10), -- 信用等级
    status VARCHAR(20) DEFAULT 'POTENTIAL', -- POTENTIAL, CERTIFIED, QUALIFIED, EXCELLENT, STRATEGIC, ELIMINATED
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_supplier_status (status)
);
```

#### 4.1.2 采购申请实体（ProcurementRequest）
```sql
CREATE TABLE pm_procurement_request (
    id BIGINT PRIMARY KEY,
    request_code VARCHAR(100) UNIQUE NOT NULL,
    request_date DATE NOT NULL,
    requester_id BIGINT NOT NULL, -- 申请人
    department_id BIGINT, -- 部门
    project_id BIGINT, -- 项目
    request_status VARCHAR(20) NOT NULL, -- DRAFT, SUBMITTED, APPROVED, REJECTED, CANCELLED
    total_amount DECIMAL(20,4), -- 总金额
    currency_code VARCHAR(10) DEFAULT 'CNY', -- 币种
    exchange_rate DECIMAL(10,6) DEFAULT 1, -- 汇率
    budget_id BIGINT, -- 预算ID
    approval_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    rejection_reason TEXT, -- 拒绝原因
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_request_date (request_date),
    INDEX idx_request_status (request_status),
    FOREIGN KEY (requester_id) REFERENCES pm_user(id),
    FOREIGN KEY (department_id) REFERENCES pm_department(id),
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    FOREIGN KEY (budget_id) REFERENCES pm_budget(id)
);
```

#### 4.1.3 采购申请明细实体（ProcurementRequestItem）
```sql
CREATE TABLE pm_procurement_request_item (
    id BIGINT PRIMARY KEY,
    request_id BIGINT NOT NULL,
    requirement_id BIGINT, -- 关联需求ID
    line_number INT NOT NULL, -- 行号
    material_id BIGINT, -- 物料ID
    material_code VARCHAR(100), -- 物料编码
    material_name VARCHAR(200), -- 物料名称
    material_specification TEXT, -- 物料规格
    requested_quantity DECIMAL(15,3) NOT NULL, -- 申请数量
    unit VARCHAR(20) NOT NULL, -- 单位
    estimated_price DECIMAL(15,4), -- 预估单价
    line_amount DECIMAL(20,4), -- 行金额
    required_date DATE, -- 需求日期
    quality_requirements TEXT, -- 质量要求
    technical_requirements TEXT, -- 技术要求
    suggested_supplier_id BIGINT, -- 建议供应商
    supplier_quote_price DECIMAL(15,4), -- 供应商报价
    approval_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    rejection_reason TEXT, -- 拒绝原因
    created_time DATETIME,
    INDEX idx_request_id (request_id),
    INDEX idx_material_id (material_id),
    FOREIGN KEY (request_id) REFERENCES pm_procurement_request(id)
);
```

#### 4.1.4 采购订单实体（ProcurementOrder）
```sql
CREATE TABLE pm_procurement_order (
    id BIGINT PRIMARY KEY,
    order_code VARCHAR(100) UNIQUE NOT NULL,
    request_id BIGINT, -- 关联采购申请
    supplier_id BIGINT NOT NULL,
    order_type VARCHAR(50) NOT NULL, -- MATERIAL, COMPONENT, SUBCONTRACT, SERVICE, EMERGENCY
    order_status VARCHAR(20) NOT NULL, -- DRAFT, CONFIRMED, PRODUCING, SHIPPED, RECEIVED, COMPLETED, CANCELLED
    order_date DATE NOT NULL, -- 订单日期
    required_date DATE NOT NULL, -- 要求交期
    promised_date DATE, -- 承诺交期
    actual_delivery_date DATE, -- 实际交期
    currency_code VARCHAR(10) DEFAULT 'CNY', -- 币种
    exchange_rate DECIMAL(10,6) DEFAULT 1, -- 汇率
    subtotal_amount DECIMAL(20,4), -- 小计金额
    tax_rate DECIMAL(5,4) DEFAULT 0, -- 税率
    tax_amount DECIMAL(20,4) DEFAULT 0, -- 税额
    total_amount DECIMAL(20,4), -- 总金额
    payment_terms VARCHAR(200), -- 付款条件
    delivery_terms VARCHAR(200), -- 交货条件
    quality_terms TEXT, -- 质量条款
    contract_terms TEXT, -- 合同条款
    special_requirements TEXT, -- 特殊要求
    delivery_address TEXT, -- 交货地址
    contact_person VARCHAR(100), -- 联系人
    contact_phone VARCHAR(50), -- 联系电话
    buyer_id BIGINT NOT NULL, -- 采购员
    approver_id BIGINT, -- 审批人
    approved_time DATETIME, -- 审批时间
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_supplier_date (supplier_id, order_date),
    INDEX idx_order_status (order_status),
    FOREIGN KEY (supplier_id) REFERENCES pm_supplier(id)
);
```

#### 4.1.5 采购订单明细实体（ProcurementOrderItem）
```sql
CREATE TABLE pm_procurement_order_item (
    id BIGINT PRIMARY KEY,
    order_id BIGINT NOT NULL,
    request_item_id BIGINT, -- 关联申请明细
    line_number INT NOT NULL, -- 行号
    material_id BIGINT NOT NULL,
    material_code VARCHAR(100),
    material_name VARCHAR(200),
    material_specification TEXT,
    ordered_quantity DECIMAL(15,3) NOT NULL, -- 订购数量
    received_quantity DECIMAL(15,3) DEFAULT 0, -- 已收货数量
    qualified_quantity DECIMAL(15,3) DEFAULT 0, -- 合格数量
    rejected_quantity DECIMAL(15,3) DEFAULT 0, -- 拒收数量
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(15,4) NOT NULL, -- 单价
    discount_rate DECIMAL(5,2) DEFAULT 0, -- 折扣率
    line_amount DECIMAL(20,4), -- 行金额
    required_date DATE, -- 要求交期
    promised_date DATE, -- 承诺交期
    actual_delivery_date DATE, -- 实际交期
    quality_requirements TEXT, -- 质量要求
    technical_requirements TEXT, -- 技术要求
    delivery_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PARTIAL, COMPLETED, CANCELLED
    quality_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, QUALIFIED, UNQUALIFIED, PARTIAL
    batch_number VARCHAR(100), -- 批次号
    production_date DATE, -- 生产日期
    expiry_date DATE, -- 过期日期
    created_time DATETIME,
    INDEX idx_order_id (order_id),
    INDEX idx_material_id (material_id),
    FOREIGN KEY (order_id) REFERENCES pm_procurement_order(id)
);
```

#### 4.1.6 外协订单实体（SubcontractOrder）
```sql
CREATE TABLE pm_subcontract_order (
    id BIGINT PRIMARY KEY,
    order_code VARCHAR(100) UNIQUE NOT NULL,
    production_task_id BIGINT, -- 关联生产任务
    subcontractor_id BIGINT NOT NULL, -- 外协商
    order_type VARCHAR(50) NOT NULL, -- CUTTING, TEMPERING, LAMINATING, COATING, ASSEMBLY
    order_status VARCHAR(20) NOT NULL, -- DRAFT, CONFIRMED, PROCESSING, COMPLETED, CANCELLED
    order_date DATE NOT NULL,
    required_date DATE NOT NULL,
    promised_date DATE,
    actual_completion_date DATE,
    material_delivery_date DATE, -- 原料交付日期
    pickup_date DATE, -- 成品提取日期
    total_quantity DECIMAL(15,3), -- 总数量
    completed_quantity DECIMAL(15,3) DEFAULT 0, -- 完成数量
    qualified_quantity DECIMAL(15,3) DEFAULT 0, -- 合格数量
    defective_quantity DECIMAL(15,3) DEFAULT 0, -- 不合格数量
    unit_price DECIMAL(15,4), -- 单价
    total_amount DECIMAL(20,4), -- 总金额
    quality_requirements TEXT, -- 质量要求
    process_requirements TEXT, -- 工艺要求
    technical_drawings TEXT, -- 技术图纸
    special_instructions TEXT, -- 特殊说明
    material_provided BOOLEAN DEFAULT TRUE, -- 是否提供原料
    pickup_address TEXT, -- 提取地址
    delivery_address TEXT, -- 交付地址
    project_manager_id BIGINT, -- 项目负责人
    quality_inspector_id BIGINT, -- 质检员
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_subcontractor_date (subcontractor_id, order_date),
    INDEX idx_order_status (order_status),
    FOREIGN KEY (subcontractor_id) REFERENCES pm_supplier(id)
);
```

#### 4.1.7 供应商绩效实体（SupplierPerformance）
```sql
CREATE TABLE pm_supplier_performance (
    id BIGINT PRIMARY KEY,
    supplier_id BIGINT NOT NULL,
    evaluation_period VARCHAR(20) NOT NULL, -- MONTHLY, QUARTERLY, YEARLY
    evaluation_year INT NOT NULL,
    evaluation_month INT, -- 月度评价时使用
    evaluation_quarter INT, -- 季度评价时使用
    quality_score DECIMAL(5,2), -- 质量得分
    delivery_score DECIMAL(5,2), -- 交付得分
    cost_score DECIMAL(5,2), -- 成本得分
    service_score DECIMAL(5,2), -- 服务得分
    overall_score DECIMAL(5,2), -- 综合得分
    performance_grade VARCHAR(10), -- 绩效等级：A+, A, B+, B, C+, C, D
    quality_metrics JSON, -- 质量指标详情
    delivery_metrics JSON, -- 交付指标详情
    cost_metrics JSON, -- 成本指标详情
    service_metrics JSON, -- 服务指标详情
    total_orders INT DEFAULT 0, -- 总订单数
    total_amount DECIMAL(20,4) DEFAULT 0, -- 总金额
    on_time_delivery_rate DECIMAL(5,4), -- 准时交付率
    quality_pass_rate DECIMAL(5,4), -- 质量合格率
    price_competitiveness DECIMAL(5,4), -- 价格竞争力
    response_time_avg DECIMAL(8,2), -- 平均响应时间（小时）
    improvement_suggestions TEXT, -- 改进建议
    evaluator_id BIGINT, -- 评价人
    evaluation_date DATE, -- 评价日期
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_supplier_period (supplier_id, evaluation_year, evaluation_month),
    INDEX idx_performance_grade (performance_grade),
    FOREIGN KEY (supplier_id) REFERENCES pm_supplier(id)
);
```

#### 4.1.8 到货验收实体（GoodsReceipt）
```sql
CREATE TABLE pm_goods_receipt (
    id BIGINT PRIMARY KEY,
    receipt_code VARCHAR(100) UNIQUE NOT NULL,
    order_id BIGINT NOT NULL, -- 关联采购订单
    supplier_id BIGINT NOT NULL,
    receipt_type VARCHAR(50) NOT NULL, -- MATERIAL, COMPONENT, SUBCONTRACT_RETURN
    receipt_status VARCHAR(20) NOT NULL, -- PENDING, INSPECTING, COMPLETED, REJECTED
    planned_date DATE, -- 计划到货日期
    actual_date DATE, -- 实际到货日期
    delivery_note_no VARCHAR(100), -- 送货单号
    vehicle_number VARCHAR(50), -- 车牌号
    driver_name VARCHAR(100), -- 司机姓名
    driver_phone VARCHAR(50), -- 司机电话
    total_packages INT, -- 总包装数
    total_quantity DECIMAL(15,3), -- 总数量
    received_quantity DECIMAL(15,3), -- 实收数量
    qualified_quantity DECIMAL(15,3), -- 合格数量
    rejected_quantity DECIMAL(15,3), -- 拒收数量
    inspector_id BIGINT, -- 检验员
    warehouse_keeper_id BIGINT, -- 仓库管理员
    inspection_result VARCHAR(20), -- QUALIFIED, UNQUALIFIED, PARTIAL
    quality_issues TEXT, -- 质量问题
    handling_instructions TEXT, -- 处理说明
    receipt_photos JSON, -- 到货照片
    inspection_report TEXT, -- 检验报告
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_order_date (order_id, actual_date),
    INDEX idx_supplier_date (supplier_id, actual_date),
    FOREIGN KEY (order_id) REFERENCES pm_procurement_order(id)
);
```

#### 4.1.9 MRP需求计算实体（MrpCalculation）
```sql
CREATE TABLE pm_mrp_calculation (
    id BIGINT PRIMARY KEY,
    calculation_code VARCHAR(100) UNIQUE NOT NULL,
    calculation_name VARCHAR(200) NOT NULL,
    calculation_type VARCHAR(50) NOT NULL, -- FULL, INCREMENTAL, EMERGENCY
    calculation_scope VARCHAR(50) NOT NULL, -- ALL, PRODUCT_LINE, PROJECT, MATERIAL_GROUP
    calculation_status VARCHAR(20) NOT NULL, -- RUNNING, COMPLETED, FAILED, CANCELLED
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    calculation_period_from DATE NOT NULL, -- 计算周期开始
    calculation_period_to DATE NOT NULL, -- 计算周期结束
    demand_sources JSON, -- 需求来源配置
    calculation_parameters JSON, -- 计算参数
    bom_version VARCHAR(20), -- BOM版本
    inventory_snapshot_time DATETIME, -- 库存快照时间
    total_requirements INT DEFAULT 0, -- 生成需求总数
    total_suggestions INT DEFAULT 0, -- 生成建议总数
    calculation_log TEXT, -- 计算日志
    error_messages TEXT, -- 错误信息
    created_by BIGINT NOT NULL,
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_calculation_status (calculation_status),
    INDEX idx_created_by_time (created_by, created_time)
);
```

### 4.2 实体关系设计

#### 4.2.1 核心实体关系图
```
供应商管理域：
Supplier (1) ←→ (N) ProcurementOrder
Supplier (1) ←→ (N) SubcontractOrder  
Supplier (1) ←→ (N) SupplierPerformance
Supplier (1) ←→ (N) GoodsReceipt

需求计算域：
MrpCalculation (1) ←→ (N) ProcurementRequirement
ProcurementRequirement (1) ←→ (N) ProcurementRequestItem

采购执行域：
ProcurementRequest (1) ←→ (N) ProcurementRequestItem
ProcurementRequest (1) ←→ (1) ProcurementOrder
ProcurementOrder (1) ←→ (N) ProcurementOrderItem
ProcurementOrder (1) ←→ (N) GoodsReceipt

外协管理域：
SubcontractOrder (1) ←→ (N) SubcontractOrderItem
SubcontractOrder (1) ←→ (N) GoodsReceipt
```

#### 4.2.2 跨子系统关系
- **与销售管理子系统**：SalesOrder → ProcurementRequirement（销售订单驱动采购需求）
- **与项目制管理子系统**：Project → ProcurementRequirement（项目需求驱动采购）
- **与库存管理子系统**：Inventory ↔ ProcurementRequirement（库存状况影响采购需求）
- **与生产管理子系统**：ProductionPlan → SubcontractOrder（生产计划驱动外协需求）
- **与财务管理子系统**：ProcurementOrder → AccountsPayable（采购订单生成应付账款）

## 5. API接口规范

### 5.1 MRP需求计算接口

#### 5.1.1 启动MRP计算
```http
POST /api/procurement/mrp/calculate
Content-Type: application/json

{
  "calculationName": "2024年3月MRP计算",
  "calculationType": "FULL",
  "calculationScope": "ALL",
  "periodFrom": "2024-03-01",
  "periodTo": "2024-05-31",
  "demandSources": {
    "salesOrders": true,
    "projects": true,
    "stockReplenishment": true,
    "forecast": false
  },
  "parameters": {
    "leadTimeDays": 15,
    "safetyStockDays": 7,
    "lossRate": 0.05,
    "batchOptimization": true
  }
}

Response:
{
  "code": 200,
  "message": "MRP计算已启动",
  "data": {
    "calculationId": "MRP202403001",
    "status": "RUNNING",
    "estimatedDuration": "10分钟"
  }
}
```

#### 5.1.2 查询MRP计算结果
```http
GET /api/procurement/mrp/requirements?calculationId=MRP202403001&page=1&size=20

Response:
{
  "code": 200,
  "data": {
    "total": 156,
    "requirements": [
      {
        "requirementCode": "REQ202403001",
        "materialCode": "GLASS-6MM-CLEAR",
        "materialName": "6mm透明浮法玻璃",
        "requiredQuantity": 1200.00,
        "unit": "平方米",
        "requiredDate": "2024-03-15",
        "priorityLevel": "HIGH",
        "suggestedSuppliers": [
          {
            "supplierId": "SUP001",
            "supplierName": "信义玻璃",
            "quotedPrice": 45.50,
            "leadTime": 10
          }
        ]
      }
    ]
  }
}
```

### 5.2 供应商管理接口

#### 5.2.1 创建供应商
```http
POST /api/procurement/suppliers
Content-Type: application/json

{
  "supplierCode": "SUP202403001",
  "supplierName": "华南玻璃制品有限公司",
  "supplierType": "MATERIAL",
  "supplierCategory": "GLASS_SHEET",
  "businessLicense": "91440300123456789X",
  "contactPerson": "张经理",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "companyAddress": "广东省深圳市宝安区工业园区",
  "technicalCapability": {
    "glassTypes": ["浮法玻璃", "钢化玻璃", "夹胶玻璃"],
    "thicknessRange": "3-19mm",
    "maxSize": "3300x2440mm",
    "monthlyCapacity": 50000
  }
}

Response:
{
  "code": 200,
  "message": "供应商创建成功",
  "data": {
    "supplierId": "SUP202403001",
    "supplierStatus": "POTENTIAL"
  }
}
```

#### 5.2.2 供应商绩效评价
```http
POST /api/procurement/suppliers/{supplierId}/performance
Content-Type: application/json

{
  "evaluationPeriod": "MONTHLY",
  "evaluationYear": 2024,
  "evaluationMonth": 3,
  "qualityMetrics": {
    "totalOrders": 12,
    "qualifiedOrders": 11,
    "qualityIssues": 1,
    "passRate": 0.9167
  },
  "deliveryMetrics": {
    "totalDeliveries": 12,
    "onTimeDeliveries": 11,
    "averageDelay": 1.2,
    "onTimeRate": 0.9167
  },
  "evaluatorId": "USER001"
}

Response:
{
  "code": 200,
  "message": "绩效评价完成",
  "data": {
    "overallScore": 88.5,
    "performanceGrade": "B+",
    "qualityScore": 90.0,
    "deliveryScore": 87.0,
    "costScore": 85.0,
    "serviceScore": 92.0
  }
}
```

### 5.3 采购订单接口

#### 5.3.1 创建采购订单
```http
POST /api/procurement/orders
Content-Type: application/json

{
  "requestId": "REQ202403001",
  "supplierId": "SUP001",
  "orderType": "MATERIAL",
  "requiredDate": "2024-03-20",
  "paymentTerms": "货到付款",
  "deliveryTerms": "送货上门",
  "deliveryAddress": "深圳市宝安区生产基地",
  "items": [
    {
      "materialId": "MAT001",
      "materialCode": "GLASS-6MM-CLEAR",
      "orderedQuantity": 1000.00,
      "unit": "平方米",
      "unitPrice": 45.50,
      "requiredDate": "2024-03-20",
      "qualityRequirements": "GB/T 11614标准，一等品"
    }
  ]
}

Response:
{
  "code": 200,
  "message": "采购订单创建成功",
  "data": {
    "orderId": "PO202403001",
    "orderCode": "PO202403001",
    "totalAmount": 45500.00,
    "orderStatus": "DRAFT"
  }
}
```

#### 5.3.2 订单执行跟踪
```http
GET /api/procurement/orders/{orderId}/tracking

Response:
{
  "code": 200,
  "data": {
    "orderId": "PO202403001",
    "orderStatus": "PRODUCING",
    "progressPercentage": 65,
    "milestones": [
      {
        "milestone": "订单确认",
        "plannedDate": "2024-03-05",
        "actualDate": "2024-03-05",
        "status": "COMPLETED"
      },
      {
        "milestone": "生产开始",
        "plannedDate": "2024-03-08",
        "actualDate": "2024-03-08",
        "status": "COMPLETED"
      },
      {
        "milestone": "生产完成",
        "plannedDate": "2024-03-18",
        "actualDate": null,
        "status": "IN_PROGRESS"
      }
    ]
  }
}
```

### 5.4 外协管理接口

#### 5.4.1 创建外协订单
```http
POST /api/procurement/subcontracts
Content-Type: application/json

{
  "productionTaskId": "TASK202403001",
  "subcontractorId": "SUP002",
  "orderType": "TEMPERING",
  "requiredDate": "2024-03-25",
  "totalQuantity": 500.00,
  "unitPrice": 15.00,
  "qualityRequirements": "钢化温度650℃，应力均匀",
  "processRequirements": "按GB 15763.2标准执行",
  "materialProvided": true,
  "pickupAddress": "深圳市宝安区生产基地"
}

Response:
{
  "code": 200,
  "message": "外协订单创建成功",
  "data": {
    "orderId": "SC202403001",
    "orderCode": "SC202403001",
    "totalAmount": 7500.00,
    "orderStatus": "DRAFT"
  }
}
```

## 6. 子系统特有非功能需求

### 6.1 性能需求

#### 6.1.1 MRP计算性能
- **计算速度要求**：
  - 全量MRP计算：10000个物料需求在30分钟内完成
  - 增量MRP计算：1000个物料需求在5分钟内完成
  - 紧急MRP计算：100个物料需求在1分钟内完成
- **并发处理能力**：
  - 支持同时进行3个MRP计算任务
  - 支持50个用户同时查询MRP结果
  - 支持100个用户同时访问采购概览页面
- **数据处理能力**：
  - 支持100万条BOM数据的展开计算
  - 支持10万条库存记录的实时查询
  - 支持1万个供应商的绩效计算

#### 6.1.2 查询响应性能
- **页面加载时间**：
  - 采购概览页面：≤3秒
  - 供应商管理页面：≤2秒
  - 采购订单列表：≤2秒（100条记录）
  - MRP需求列表：≤3秒（500条记录）
- **报表生成时间**：
  - 月度采购分析报表：≤30秒
  - 供应商绩效报表：≤20秒
  - 成本分析报表：≤45秒
- **数据导出时间**：
  - 1万条采购订单导出：≤60秒
  - 5000条供应商数据导出：≤30秒

### 6.2 可靠性需求

#### 6.2.1 系统可用性
- **系统可用率**：≥99.5%（年停机时间≤43.8小时）
- **故障恢复时间**：
  - 系统故障恢复：≤30分钟
  - 数据库故障恢复：≤60分钟
  - 网络故障恢复：≤15分钟
- **数据备份策略**：
  - 实时数据备份：关键业务数据实时同步
  - 增量备份：每4小时进行增量备份
  - 全量备份：每日凌晨进行全量备份

#### 6.2.2 数据完整性
- **事务一致性**：
  - 采购订单创建的原子性保证
  - MRP计算过程的数据一致性
  - 供应商绩效计算的准确性
- **数据校验机制**：
  - 采购申请数据完整性校验
  - 供应商资质有效性校验
  - 价格合理性区间校验
- **并发控制**：
  - 采购订单并发修改的乐观锁控制
  - 库存数据并发更新的悲观锁控制
  - MRP计算的排他性控制

### 6.3 安全性需求

#### 6.3.1 数据安全
- **敏感数据保护**：
  - 供应商商业机密信息加密存储
  - 采购价格信息访问权限控制
  - 合同条款信息脱敏处理
- **数据传输安全**：
  - API接口HTTPS加密传输
  - 供应商EDI数据加密传输
  - 文件上传下载加密处理
- **数据访问控制**：
  - 基于角色的数据访问权限
  - 供应商数据的部门级权限控制
  - 采购金额的级别权限控制

#### 6.3.2 操作安全
- **身份认证**：
  - 用户登录双因子认证
  - 供应商门户访问认证
  - API接口Token认证
- **操作审计**：
  - 关键操作日志记录
  - 数据修改痕迹追踪
  - 异常操作预警机制
- **权限控制**：
  - 采购订单审批权限分级
  - 供应商信息修改权限控制
  - 价格信息查看权限限制

### 6.4 可扩展性需求

#### 6.4.1 业务扩展性
- **供应商规模扩展**：
  - 支持从1000个供应商扩展到10000个
  - 支持供应商分类体系的灵活扩展
  - 支持新的供应商认证标准
- **物料类型扩展**：
  - 支持新的玻璃产品类型
  - 支持新的五金配件类型
  - 支持新的外协服务类型
- **业务流程扩展**：
  - 支持新的采购模式
  - 支持新的审批流程
  - 支持新的结算方式

#### 6.4.2 技术扩展性
- **系统架构扩展**：
  - 微服务架构支持水平扩展
  - 数据库分库分表支持
  - 缓存集群扩展支持
- **集成扩展性**：
  - 支持新的ERP系统集成
  - 支持新的供应商系统对接
  - 支持新的电商平台集成

### 6.5 易用性需求

#### 6.5.1 用户界面易用性
- **界面设计标准**：
  - 遵循Material Design设计规范
  - 支持响应式设计，适配不同屏幕尺寸
  - 提供暗色主题和亮色主题切换
- **操作便捷性**：
  - 常用功能不超过3次点击到达
  - 提供快捷键支持
  - 支持批量操作功能
- **信息展示**：
  - 关键信息突出显示
  - 提供数据可视化图表
  - 支持个性化仪表盘配置

#### 6.5.2 学习成本控制
- **操作指导**：
  - 提供新手引导功能
  - 关键操作提供帮助提示
  - 复杂流程提供操作向导
- **培训支持**：
  - 提供在线帮助文档
  - 提供操作视频教程
  - 提供模拟训练环境

## 7. Product Backlog

### 7.1 史诗（Epic）

#### 7.1.1 史诗1：MRP需求计算系统
- **史诗描述**：建立智能化的物料需求计算系统，支持多维度需求来源整合和优化算法
- **业务价值**：提高采购计划准确性，减少库存积压，降低缺料风险
- **验收标准**：MRP计算准确率≥95%，计算时间≤30分钟，支持10000个物料需求计算
- **优先级**：高
- **预估工作量**：40个故事点

#### 7.1.2 史诗2：供应商全生命周期管理
- **史诗描述**：构建供应商从认证、合作、绩效评价到淘汰的全生命周期管理体系
- **业务价值**：提升供应商质量，降低供应风险，优化供应商结构
- **验收标准**：支持1000个供应商管理，绩效评价自动化率≥80%，供应商满意度≥85%
- **优先级**：高
- **预估工作量**：35个故事点

#### 7.1.3 史诗3：采购执行数字化
- **史诗描述**：实现从采购申请到订单执行的全流程数字化管理
- **业务价值**：提高采购效率，加强采购控制，降低采购成本
- **验收标准**：采购周期缩短30%，采购成本降低5%，订单准确率≥99%
- **优先级**：高
- **预估工作量**：45个故事点

#### 7.1.4 史诗4：外协管理专业化
- **史诗描述**：建立专业化的外协管理体系，支持玻璃深加工外协业务
- **业务价值**：优化外协资源配置，提高外协质量，降低外协成本
- **验收标准**：外协质量合格率≥98%，外协成本降低10%，外协周期缩短20%
- **优先级**：中
- **预估工作量**：30个故事点

#### 7.1.5 史诗5：采购分析决策支持
- **史诗描述**：提供多维度的采购数据分析和决策支持功能
- **业务价值**：支持采购决策，优化采购策略，提升采购绩效
- **验收标准**：提供20个标准分析报表，支持自定义分析，决策支持准确率≥90%
- **优先级**：中
- **预估工作量**：25个故事点

### 7.2 用户故事（User Story）

#### 7.2.1 MRP需求计算相关故事

**故事1：基础MRP计算**
- **作为**：采购计划员
- **我希望**：系统能够基于销售订单自动计算物料需求
- **以便于**：快速生成准确的采购计划
- **验收标准**：
  - 支持销售订单BOM展开
  - 考虑库存扣减和损耗率
  - 生成采购需求建议
- **优先级**：高
- **预估工作量**：8个故事点

**故事2：需求优化算法**
- **作为**：采购计划员
- **我希望**：系统能够优化采购批量和供应商分配
- **以便于**：降低采购成本和库存成本
- **验收标准**：
  - 支持EOQ批量优化
  - 支持多供应商分配
  - 考虑运输成本优化
- **优先级**：高
- **预估工作量**：13个故事点

**故事3：紧急需求处理**
- **作为**：采购员
- **我希望**：系统能够快速处理紧急采购需求
- **以便于**：及时响应生产紧急需求
- **验收标准**：
  - 紧急需求优先级处理
  - 快速供应商推荐
  - 应急采购方案生成
- **优先级**：中
- **预估工作量**：5个故事点

#### 7.2.2 供应商管理相关故事

**故事4：供应商档案管理**
- **作为**：采购员
- **我希望**：能够完整管理供应商档案信息
- **以便于**：全面了解供应商情况
- **验收标准**：
  - 支持供应商基础信息管理
  - 支持资质证照管理
  - 支持供应商分类分级
- **优先级**：高
- **预估工作量**：8个故事点

**故事5：供应商绩效评价**
- **作为**：采购经理
- **我希望**：系统能够自动计算供应商绩效
- **以便于**：客观评价供应商表现
- **验收标准**：
  - 支持多维度绩效指标
  - 自动数据收集和计算
  - 生成绩效分析报告
- **优先级**：高
- **预估工作量**：10个故事点

**故事6：供应商认证管理**
- **作为**：质检员
- **我希望**：能够管理供应商认证流程
- **以便于**：确保供应商资质合规
- **验收标准**：
  - 支持认证流程管理
  - 支持现场审核记录
  - 支持认证结果管理
- **优先级**：中
- **预估工作量**：8个故事点

#### 7.2.3 采购执行相关故事

**故事7：采购申请管理**
- **作为**：需求部门员工
- **我希望**：能够便捷地提交采购申请
- **以便于**：及时获得所需物料
- **验收标准**：
  - 支持在线采购申请
  - 支持预算检查
  - 支持审批流程
- **优先级**：高
- **预估工作量**：10个故事点

**故事8：采购订单管理**
- **作为**：采购员
- **我希望**：能够高效管理采购订单
- **以便于**：确保采购任务按时完成
- **验收标准**：
  - 支持订单创建和修改
  - 支持订单执行跟踪
  - 支持订单变更管理
- **优先级**：高
- **预估工作量**：12个故事点

**故事9：到货验收管理**
- **作为**：仓库管理员
- **我希望**：能够规范化处理到货验收
- **以便于**：确保入库物料质量合格
- **验收标准**：
  - 支持到货通知处理
  - 支持质量检验记录
  - 支持验收结果处理
- **优先级**：高
- **预估工作量**：8个故事点

#### 7.2.4 外协管理相关故事

**故事10：外协需求管理**
- **作为**：生产计划员
- **我希望**：能够识别和管理外协需求
- **以便于**：合理安排外协计划
- **验收标准**：
  - 支持外协工序识别
  - 支持外协需求计算
  - 支持外协成本评估
- **优先级**：中
- **预估工作量**：6个故事点

**故事11：外协订单管理**
- **作为**：外协专员
- **我希望**：能够全面管理外协订单
- **以便于**：确保外协任务按质按时完成
- **验收标准**：
  - 支持外协订单创建
  - 支持外协进度跟踪
  - 支持外协质量控制
- **优先级**：中
- **预估工作量**：10个故事点

### 7.3 任务优先级排序

#### 7.3.1 第一优先级（必须有）
1. 基础MRP计算（故事1）
2. 供应商档案管理（故事4）
3. 采购申请管理（故事7）
4. 采购订单管理（故事8）
5. 到货验收管理（故事9）

#### 7.3.2 第二优先级（应该有）
1. 需求优化算法（故事2）
2. 供应商绩效评价（故事5）
3. 供应商认证管理（故事6）
4. 外协订单管理（故事11）

#### 7.3.3 第三优先级（可以有）
1. 紧急需求处理（故事3）
2. 外协需求管理（故事10）
3. 采购分析报表
4. 移动端支持

### 7.4 发布计划

#### 7.4.1 第一阶段发布（MVP）
- **发布时间**：开发完成后3个月
- **包含功能**：
  - 基础MRP计算
  - 供应商档案管理
  - 采购申请和订单管理
  - 到货验收管理
- **发布目标**：满足基本采购业务需求

#### 7.4.2 第二阶段发布
- **发布时间**：第一阶段后2个月
- **包含功能**：
  - 需求优化算法
  - 供应商绩效评价
  - 外协订单管理
- **发布目标**：提升采购效率和质量

#### 7.4.3 第三阶段发布
- **发布时间**：第二阶段后2个月
- **包含功能**：
  - 采购分析决策支持
  - 移动端应用
  - 高级报表功能
- **发布目标**：完善采购管理体系

## 8. 验收标准

### 8.1 功能验收标准

#### 8.1.1 MRP需求计算验收标准
- **计算准确性验收**：
  - MRP计算结果与手工计算误差≤2%
  - BOM展开层级支持≥5层
  - 库存扣减计算准确率100%
  - 损耗率计算准确率100%
- **计算性能验收**：
  - 1000个物料需求计算时间≤5分钟
  - 10000个物料需求计算时间≤30分钟
  - 支持并发计算任务≥3个
- **优化算法验收**：
  - EOQ批量优化准确率≥95%
  - 多供应商分配算法有效性≥90%
  - 运输成本优化效果≥5%

#### 8.1.2 供应商管理验收标准
- **档案管理验收**：
  - 支持供应商信息字段≥50个
  - 支持供应商分类≥10种
  - 支持资质证照管理≥20种
  - 供应商信息完整性检查100%
- **绩效评价验收**：
  - 支持绩效指标≥20个
  - 绩效数据自动收集率≥80%
  - 绩效计算准确率100%
  - 绩效报告生成时间≤30秒
- **认证管理验收**：
  - 支持认证流程步骤≥8个
  - 认证审核时间跟踪准确率100%
  - 认证结果管理完整性100%

#### 8.1.3 采购执行验收标准
- **申请管理验收**：
  - 采购申请创建成功率100%
  - 预算检查准确率100%
  - 审批流程执行正确率100%
  - 申请处理平均时间≤2天
- **订单管理验收**：
  - 订单创建成功率100%
  - 订单信息完整性检查100%
  - 订单状态跟踪准确率100%
  - 订单变更处理成功率≥95%
- **验收管理验收**：
  - 到货验收记录完整性100%
  - 质量检验数据准确率100%
  - 验收结果处理及时率≥95%
  - 不合格品处理流程完整性100%

#### 8.1.4 外协管理验收标准
- **需求管理验收**：
  - 外协工序识别准确率≥95%
  - 外协需求计算准确率≥98%
  - 外协成本评估准确率≥95%
- **订单管理验收**：
  - 外协订单创建成功率100%
  - 外协进度跟踪准确率≥95%
  - 外协质量控制有效性≥90%
  - 外协交付及时率≥95%

### 8.2 性能验收标准

#### 8.2.1 响应时间验收标准
- **页面加载时间**：
  - 采购概览页面≤3秒
  - 供应商管理页面≤2秒
  - 采购订单列表≤2秒
  - MRP需求列表≤3秒
- **查询响应时间**：
  - 简单查询≤1秒
  - 复杂查询≤5秒
  - 统计查询≤10秒
  - 报表查询≤30秒
- **操作响应时间**：
  - 数据保存≤2秒
  - 数据删除≤1秒
  - 批量操作≤10秒

#### 8.2.2 并发性能验收标准
- **用户并发**：
  - 支持同时在线用户≥100个
  - 支持同时操作用户≥50个
  - 支持高峰期用户≥200个
- **数据并发**：
  - 支持同时MRP计算≥3个
  - 支持同时订单创建≥20个
  - 支持同时数据查询≥100个

#### 8.2.3 数据处理验收标准
- **数据容量**：
  - 支持供应商数量≥10000个
  - 支持采购订单≥100万条
  - 支持物料需求≥50万条
- **数据处理速度**：
  - 数据导入速度≥1000条/分钟
  - 数据导出速度≥5000条/分钟
  - 数据同步延迟≤5秒

### 8.3 质量验收标准

#### 8.3.1 功能质量标准
- **功能完整性**：
  - 需求功能实现率100%
  - 功能测试通过率≥99%
  - 业务流程覆盖率100%
- **功能正确性**：
  - 计算结果准确率≥99.5%
  - 数据处理正确率100%
  - 业务逻辑正确率100%
- **功能稳定性**：
  - 功能故障率≤0.1%
  - 数据丢失率0%
  - 系统崩溃率≤0.01%

#### 8.3.2 非功能质量标准
- **可用性标准**：
  - 系统可用率≥99.5%
  - 故障恢复时间≤30分钟
  - 数据备份成功率100%
- **安全性标准**：
  - 安全漏洞数量0个
  - 数据加密覆盖率100%
  - 权限控制有效性100%
- **易用性标准**：
  - 用户满意度≥85%
  - 操作错误率≤5%
  - 学习成本≤2天

#### 8.3.3 集成质量标准
- **系统集成**：
  - 与销售系统集成成功率100%
  - 与库存系统集成成功率100%
  - 与财务系统集成成功率100%
- **数据集成**：
  - 数据同步准确率100%
  - 数据一致性检查通过率100%
  - 接口调用成功率≥99.9%

### 8.4 用户验收标准

#### 8.4.1 业务用户验收
- **采购总监验收**：
  - 采购策略制定支持有效性≥90%
  - 采购绩效监控准确性≥95%
  - 供应商关系管理满意度≥85%
- **采购经理验收**：
  - 采购计划制定效率提升≥30%
  - 采购团队管理便捷性≥85%
  - 供应商问题处理及时性≥90%
- **采购员验收**：
  - 日常采购操作便捷性≥90%
  - 供应商沟通效率提升≥25%
  - 
  - 地图组件：供应商地理分布（供应商位置、覆盖范围、物流路径、区域分析）
- **输入/输出字段**：
  - 字段名：supplierManagement
  - 类型：object
  - 校验规则：供应商信息完整性校验，资质有效性校验，绩效数据准确性校验
  - 依赖关系：依赖采购订单、质量数据、财务数据、合同数据

#### 3.2.5 采购申请页面
- **功能模块**：采购申请创建和审批管理
- **主要界面元素**：
  - 表单组件：采购申请表单（申请信息、物料明细、技术要求、交期要求）
  - 审批组件：审批流程组件（审批状态、审批意见、审批历史、流程图）
  - 预算组件：预算检查组件（可用预算、预算占用、超预算预警）
  - 推荐组件：供应商推荐（历史供应商、价格对比、能力匹配、推荐理由）
  - 附件组件：附件管理（技术图纸、规格说明、样品照片、相关文档）
- **输入/输出字段**：
  - 字段名：procurementRequest
  - 类型：object
  - 校验规则：申请信息完整性校验，预算可用性校验，技术要求合理性校验
  - 依赖关系：依赖MRP需求、预算数据、供应商数据、技术标准

#### 3.2.6 采购订单页面
- **功能模块**：采购订单创建和执行管理
- **主要界面元素**：
  - 表单组件：订单创建表单（供应商信息、物料明细、价格条款、交期安排）
  - 跟踪组件：订单执行跟踪（生产进度、质量状态、物流信息、到货预期）
  - 变更组件：订单变更管理（变更申请、影响评估、协商确认、变更执行）
  - 验收组件：到货验收管理（验收计划、验收标准、验收结果、异常处理）
  - 结算组件：订单结算管理（发票核对、付款申请、结算状态、财务确认）
- **输入/输出字段**：
  - 字段名：procurementOrder
  - 类型：object
  - 校验规则：订单信息完整性校验，价格合理性校验，交期可行性校验
  - 依赖关系：依赖采购申请、供应商数据、合同条款、财务数据

#### 3.2.7 外协管理页面
- **功能模块**：外协业务全流程管理
- **主要界面元素**：
  - 需求组件：外协需求管理（工序识别、能力评估、外协计划、成本分析）
  - 供应商组件：外协商管理（外协商档案、能力认证、绩效评价、关系管理）
  - 订单组件：外协订单管理（订单创建、执行跟踪、质量监控、交付验收）
  - 质量组件：外协质量管理（质量标准、过程控制、检验验收、问题处理）
  - 成本组件：外协成本管理（成本核算、价格对比、结算管理、成本分析）
- **输入/输出字段**：
  - 字段名：subcontractManagement
  - 类型：object
  - 校验规则：外协需求合理性校验，外协商能力校验，质量标准符合性校验
  - 依赖关系：依赖生产计划、工艺数据、质量标准、成本数据

#### 3.2.8 采购分析页面
- **功能模块**：采购数据分析和决策支持
- **主要界面元素**：
  - 仪表盘组件：采购绩效仪表盘（关键指标、趋势分析、对比分析、预警信息）
  - 图表组件：多维度分析图表（成本分析、供应商分析、品类分析、时间分析）
  - 报表组件：标准分析报表（月度报表、季度报表、年度报表、专项报表）
  - 筛选组件：分析条件筛选（时间范围、供应商筛选、品类筛选、自定义条件）
  - 导出组件：分析结果导出（Excel导出、PDF报告、图表导出、数据导出）
- **输入/输出字段**：
  - 字段名：procurementAnalysis
  - 类型：object
  - 校验规则：分析参数校验，数据权限校验，计算结果准确性校验
  - 依赖关系：依赖采购订单、供应商数据、成本数据、质量数据

## 4. 用户场景与流程

### 4.1 玻璃原片采购全流程场景

#### 4.1.1 MRP需求计算阶段
- **用户角色**：采购计划员、生产计划员
- **场景描述**：基于销售订单和库存状况计算玻璃原片采购需求
- **前置条件**：销售订单已确认，BOM数据完整，库存数据准确
- **操作流程**：
  1. 系统自动收集需求来源：
     - 确认销售订单：500万元建筑玻璃订单
     - 项目制需求：防火窗项目需求
     - 库存补充：安全库存低于最低库存
  2. MRP计算引擎运行：
     - BOM展开：6+12A+6中空玻璃需要6mm原片2000平方米
     - 库存扣减：现有库存800平方米，在途库存300平方米
     - 净需求计算：2000-800-300=900平方米
     - 考虑损耗率5%：900×1.05=945平方米
  3. 批量优化计算：
     - 供应商最小起订量：1000平方米
     - 运输成本优化：整车运输更经济
     - 最终采购建议：1200平方米
  4. 供应商分配建议：
     - 主供应商：信义玻璃800平方米
     - 备选供应商：南玻集团400平方米
     - 风险分散：避免单一供应商依赖
- **预期结果**：生成准确的采购需求计划，为采购执行提供依据
- **异常处理**：BOM数据缺失时提示补充，库存数据异常时重新核实

#### 4.1.2 供应商选择阶段
- **用户角色**：采购员、质检员
- **场景描述**：为玻璃原片采购选择合适的供应商
- **操作流程**：
  1. 供应商资格筛选：
     - 资质要求：生产许可证、ISO9001认证、产品认证
     - 技术能力：6mm浮法玻璃生产能力
     - 供应能力：月产能≥5000平方米
     - 地理位置：运输距离≤500公里
  2. 供应商绩效评估：
     - 历史绩效：信义玻璃综合评分95分，南玻集团92分
     - 质量表现：合格率99.5%以上，质量稳定性好
     - 交付表现：准时交付率98%以上
     - 价格竞争力：价格合理，有一定优势
  3. 供应商比较分析：
     - 价格对比：信义玻璃45元/平方米，南玻集团46元/平方米
     - 质量对比：两家质量水平相当，信义稍有优势
     - 服务对比：信义服务响应更快，技术支持更好
     - 风险评估：信义产能更大，供应更稳定
  4. 供应商选择决策：
     - 主供应商：选择信义玻璃800平方米
     - 备选供应商：选择南玻集团400平方米
     - 决策依据：综合考虑价格、质量、服务、风险
- **预期结果**：选择最适合的供应商组合，确保采购成功
- **异常处理**：供应商能力不足时寻找替代方案

#### 4.1.3 采购订单执行阶段
- **用户角色**：采购员、供应商、仓库管理员
- **场景描述**：玻璃原片采购订单的执行和跟踪
- **操作流程**：
  1. 采购订单创建：
     - 订单信息：6mm透明浮法玻璃1200平方米
     - 技术要求：GB/T 11614标准，光学性能一等品
     - 交期要求：15天内交货
     - 价格条款：45.5元/平方米，含运费
  2. 供应商订单确认：
     - 供应商接收订单并确认
     - 确认生产计划和交期安排
     - 确认技术要求和质量标准
     - 签署订单确认书
  3. 生产进度跟踪：
     - 第3天：原料准备完成，开始生产
     - 第7天：生产进度50%，质量检验正常
     - 第12天：生产完成，包装准备发货
     - 第14天：发货完成，预计明日到货
  4. 到货验收处理：
     - 到货通知：供应商提前通知到货时间
     - 验收准备：安排验收人员和卸货设备
     - 质量检验：抽样检验外观质量和尺寸精度
     - 入库处理：验收合格后安排入库上架
- **预期结果**：按时按质完成采购，满足生产需求
- **异常处理**：质量不合格时与供应商协商处理

### 4.2 外协加工管理场景

#### 4.2.1 外协需求识别
- **用户角色**：生产计划员、工艺工程师
- **场景描述**：识别需要外协的加工工序和技术要求
- **前置条件**：生产订单已下达，工艺路线已确定
- **操作流程**：
  1. 工艺能力分析：
     - 内部能力评估：钢化炉产能不足，无法满足交期
     - 外协工序确定：钢化工序需要外协加工
     - 技术要求：钢化温度650℃，冷却时间8分钟
     - 质量标准：GB 15763.2钢化玻璃标准
  2. 外协需求计算：
     - 外协数量：1000平方米钢化玻璃
     - 外协规格：6mm、8mm、10mm多种厚度
     - 交期要求：10天内完成
     - 质量要求：应力均匀，无光学缺陷
  3. 外协成本评估：
     - 外协单价：15元/平方米
     - 运输成本：2元/平方米
     - 总外协成本：17000元
     - 与自制成本对比：外协更经济
- **预期结果**：明确外协需求和技术要求
- **异常处理**：技术要求不明确时与技术部门确认

#### 4.2.2 外协商选择和管理
- **用户角色**：外协专员、质检员
- **场景描述**：选择合适的外协供应商并管理合作关系
- **操作流程**：
  1. 外协商资格评估：
     - 技术能力：拥有先进钢化设备，技术水平高
     - 质量体系：通过ISO9001认证，质量管理规范
     - 产能评估：日产能500平方米，能满足交期
     - 现场审核：实地考察生产现场和质量控制
  2. 外协商绩效分析：
     - 历史合作：合作3年，表现良好
     - 质量表现：合格率99%，质量稳定
     - 交期表现：准时完成率95%
     - 价格水平：价格合理，有竞争优势
  3. 外协商选择决策：
     - 主外协商：选择A公司600平方米
     - 备选外协商：选择B公司400平方米
     - 风险分散：避免单一外协商依赖
     - 签署框架协议：确定长期合作关系
- **预期结果**：建立稳定的外协供应商关系
- **异常处理**：外协商能力不足时寻找替代方案

#### 4.2.3 外协订单执行和质量控制
- **用户角色**：外协专员、质检员、外协商
- **场景描述**：外协订单的执行跟踪和质量控制
- **操作流程**：
  1. 外协订单下达：
     - 技术要求：详细的工艺参数和质量标准
     - 原料配送：将待加工玻璃送至外协商
     - 进度安排：确定加工时间和交付计划
     - 质量协议：签署质量保证协议
  2. 外协过程监控：
     - 第2天：原料到达外协商，开始加工
     - 第4天：加工进度50%，首件检验合格
     - 第7天：加工完成80%，质量稳定
     - 第9天：全部完成，准备交付
  3. 质量检验控制：
     - 过程检验：外协商自检和我方巡检
     - 交付检验：成品交付时的全面检验
     - 质量记录：详细记录检验数据和结果
     - 问题处理：发现问题及时沟通解决
  4. 外协交付验收：
     - 成品验收：按照质量标准验收成品
     - 数量核对：核对加工数量和交付数量
     - 质量确认：确认产品质量符合要求
     - 入库处理：验收合格后安排入库
- **预期结果**：按时按质完成外协加工，满足生产需求
- **异常处理**：质量问题时要求外协商返工或赔偿

### 4.3 供应商绩效评价场景

#### 4.3.1 绩效数据收集
- **用户角色**：采购员、质检员、仓库管理员
- **场景描述**：收集供应商绩效评价所需的各类数据
- **前置条件**：与供应商有业务往来，有完整的业务记录
- **操作流程**：
  1. 质量数据收集：
     - 到货检验数据：合格率、不合格项、质量等级
     - 使用过程反馈：产品使用中的质量表现
     - 客户投诉：因供应商原因导致的客户投诉
     - 质量改进：供应商质量改进的效果
  2. 交付数据收集：
     - 准时交付率：按承诺时间交付的比例
     - 交付数量准确性：交付数量与订单数量的匹配度
     - 应急响应：紧急需求的响应速度和能力
     - 包装质量：包装完整性和标识正确性
  3. 成本数据收集：
     - 价格竞争力：与市场价格和其他供应商的对比
     - 价格稳定性：价格波动的频率和幅度
     - 隐性成本：质量成本、管理成本、风险成本
     - 降本贡献：供应商主动降本的贡献
  4. 服务数据收集：
     - 沟通配合：日常沟通的及时性和有效性
     - 技术支持：技术问题的支持和解决能力
     - 售后服务：问题处理的及时性和满意度
     - 合作态度：合作过程中的积极性和主动性
- **预期结果**：获得全面准确的供应商绩效数据
- **异常处理**：数据缺失时通过多渠道补充收集

#### 4.3.2 绩效评价计算
- **用户角色**：采购经理、数据分析员
- **场景描述**：基于收集的数据计算供应商综合绩效评分
- **操作流程**：
  1. 绩效指标权重设定：
     - 质量绩效：40%权重（最重要）
     - 交付绩效：30%权重（次重要）
     - 成本绩效：20%权重（重要）
     - 服务绩效：10%权重（一般重要）
  2. 单项绩效计算：
     - 质量绩效：合格率99.5%，得分95分
     - 交付绩效：准时率98%，得分90分
     - 成本绩效：价格优势5%，得分85分
     - 服务绩效：服务满意度90%，得分88分
  3. 综合绩效计算：
     - 综合得分=95×40%+90×30%+85×20%+88×10%
     - 综合得分=38+27+17+8.8=90.8分
     - 绩效等级：优秀（90分以上）
  4. 绩效排名和分级：
     - 在同类供应商中排名第2位
     - 绩效等级：A级供应商
     - 合作建议：继续深化合作关系
- **预期结果**：得到客观准确的供应商绩效评价结果
- **异常处理**：计算异常时检查数据和算法

#### 4.3.3 绩效改进管理
- **用户角色**：采购经理、供应商代表
- **场景描述**：基于绩效评价结果制定改进计划并跟踪执行
- **操作流程**：
  1. 绩效反馈沟通：
     - 绩效报告：向供应商提供详细的绩效评价报告
     - 问题分析：分析绩效不佳的原因和影响因素
     - 改进建议：提出具体的改进建议和措施
     - 目标设定：设定下一阶段的绩效改进目标
  2. 改进计划制定：
     - 供应商制定改进计划：针对薄弱环节制定改进措施
     - 改进时间安排：确定改进措施的实施时间表
     - 资源投入：确定改进所需的资源投入
     - 效果预期：预期改进措施的效果和目标
  3. 改进执行跟踪：
     - 定期检查：每月检查改进计划的执行情况
     - 数据监控：持续监控相关绩效指标的变化
     - 问题协调：及时协调改进过程中的问题
     - 效果评估：评估改进措施的实际效果
  4. 改进结果确认：
     - 绩效提升确认：确认绩效指标的实际提升
     - 改进效果评价：评价改进措施的有效性
     - 经验总结：总结改进过程中的经验和教训
     - 持续改进：建立持续改进的长效机制
- **预期结果**：供应商绩效得到持续改进，合作关系更加稳固
- **异常处理**：改进效果不佳时调整改进策略

## 5. 数据模型设计

### 5.1 核心实体模型

#### 5.1.1 供应商实体（Supplier）
```sql
CREATE TABLE pm_supplier (
    id BIGINT PRIMARY KEY,
    supplier_code VARCHAR(50) UNIQUE NOT NULL,
    supplier_name VARCHAR(200) NOT NULL,
    supplier_type VARCHAR(50) NOT NULL, -- MATERIAL, COMPONENT, SUBCONTRACT, SERVICE
    supplier_category VARCHAR(50), -- GLASS_SHEET, HARDWARE, SEALANT, PACKAGING
    supplier_level VARCHAR(20) NOT NULL, -- STRATEGIC, IMPORTANT, GENERAL, BACKUP
    supplier_status VARCHAR(20) NOT NULL, -- POTENTIAL, CERTIFIED, QUALIFIED, EXCELLENT, STRATEGIC, ELIMINATED
    business_license VARCHAR(100), -- 营业执照号
    tax_number VARCHAR(50), -- 税号
    legal_representative VARCHAR(100), -- 法人代表
    registered_capital DECIMAL(20,4), -- 注册资本
    established_date DATE, -- 成立日期
    business_scope TEXT, -- 经营范围
    company_address TEXT, -- 公司地址
    company_location POINT, -- 公司地理坐标
    contact_person VARCHAR(100), -- 联系人
    contact_phone VARCHAR(50), -- 联系电话
    contact_email VARCHAR(100), -- 联系邮箱
    website VARCHAR(200), -- 公司网站
    annual_revenue DECIMAL(20,4), -- 年营业额
    employee_count INT, -- 员工数量
    credit_rating VARCHAR(20), -- 信用等级
    payment_terms VARCHAR(100), -- 付款条件
    delivery_terms VARCHAR(100), -- 交货条件
    quality_certification JSON, -- 质量认证
    technical_capability JSON, -- 技术能力
    production_capacity JSON, -- 生产能力
    main_products TEXT, -- 主要产品
    service_scope TEXT, -- 服务范围
    cooperation_history TEXT, -- 合作历史
    risk_assessment JSON, -- 风险评估
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_supplier_type (supplier_type),
    INDEX idx_supplier_status (supplier_status)
);
```

#### 5.1.2 采购需求实体（ProcurementRequirement）
```sql
CREATE TABLE pm_procurement_requirement (
    id BIGINT PRIMARY KEY,
    requirement_code VARCHAR(100) UNIQUE NOT NULL,
    requirement_source VARCHAR(50) NOT NULL, -- SALES_ORDER, PROJECT, STOCK_REPLENISHMENT, EMERGENCY
    source_id BIGINT, -- 来源ID（订单ID、项目ID等）
    requirement_type VARCHAR(50) NOT NULL, -- MATERIAL, COMPONENT, SUBCONTRACT, SERVICE
    material_id BIGINT, -- 物料ID
    material_code VARCHAR(100), -- 物料编码
    material_name VARCHAR(200), -- 物料名称
    material_spec TEXT, -- 物料规格
    required_quantity DECIMAL(15,3) NOT NULL, -- 需求数量
    unit VARCHAR(20) NOT NULL, -- 单位
    required_date DATE NOT NULL, -- 需求日期
    latest_date DATE, -- 最晚需求日期
    priority_level VARCHAR(20) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    quality_requirements TEXT, -- 质量要求
    technical_requirements TEXT, -- 技术要求
    special_requirements TEXT, -- 特殊要求
    estimated_price DECIMAL(15,4), -- 预估价格
    budget_amount DECIMAL(15,4), -- 预算金额
    current_stock DECIMAL(15,3), -- 当前库存
    available_stock DECIMAL(15,3), -- 可用库存
    in_transit_stock DECIMAL(15,3), -- 在途库存
    net_requirement DECIMAL(15,3), -- 净需求
    suggested_quantity DECIMAL(15,3), -- 建议采购数量
    suggested_suppliers JSON, -- 建议供应商
    requirement_status VARCHAR(20) NOT NULL, -- PENDING, APPROVED, PURCHASING, COMPLETED, CANCELLED
    created_by BIGINT NOT NULL,
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_requirement_source (requirement_source, source_id),
    INDEX idx_material_date (material_id, required_date)
);
```

#### 5.1.3 采购申请实体（ProcurementRequest）
```sql
CREATE TABLE pm_procurement_request (
    id BIGINT PRIMARY KEY,
    request_code VARCHAR(100) UNIQUE NOT NULL,
    request_title VARCHAR(200) NOT NULL,
    request_type VARCHAR(50) NOT NULL, -- MATERIAL, COMPONENT, SUBCONTRACT, SERVICE, EMERGENCY
    request_category VARCHAR(50), -- PRODUCTION, PROJECT, MAINTENANCE, OFFICE
    requesting_department VARCHAR(100) NOT NULL, -- 申请部门
    requester_id BIGINT NOT NULL, -- 申请人
    request_date DATE NOT NULL, -- 申请日期
    required_date DATE NOT NULL, -- 需求日期
    request_reason TEXT, -- 申请原因
    request_description TEXT, -- 申请描述
    total_amount DECIMAL(20,4), -- 申请总金额
    budget_code VARCHAR(100), -- 预算科目
    available_budget DECIMAL(20,4), -- 可用预算
    request_status VARCHAR(20) NOT NULL, -- DRAFT, SUBMITTED, REVIEWING, APPROVED, REJECTED, CANCELLED
    approval_level INT DEFAULT 1, -- 当前审批级别
    current_approver BIGINT, -- 当前审批人
    approval_comments TEXT, -- 审批意见
    rejection_reason TEXT, -- 拒绝原因
    submitted_time DATETIME, -- 提交时间
    approved_time DATETIME, -- 批准时间
    created_time DATETIME,
    updated_time DATETIME,
    INDEX idx_requester_date (requester_id, request_date),
    INDEX idx_status_approver (request_status, current_approver)
);
```

#### 5.1.4 采购申请明细实体（ProcurementRequestItem）
```sql
CREATE TABLE pm_procurement_request_item (
    id BIGINT PRIMARY KEY,
    request_id BIGINT NOT NULL,
    requirement_id BIGINT, -- 关联需求ID
    line_number INT NOT NULL, -- 行号
    material_id BIGINT, -- 物料ID
    material_code VARCHAR(100), --
