
**子系统：**
*   [x] **基础管理子系统** - 用户权限、组织架构、基础数据字典、系统配置
*   [x] **CRM子系统** - 客户档案、销售机会、客户跟进、售后服务管理
*   [x] **销售管理子系统** - 专注玻璃产品订单管理，包含复杂规格处理、批量订单、价格计算
*   [x] **项目制管理子系统** - 防火窗、酒店隔断、幕墙工程等大型项目的全生命周期管理
*   [x] **采购管理子系统** - MRP需求计算、采购计划、采购申请、采购订单、供应商管理、外协管理
*   [x] **工艺管理子系统** - 产品结构设计、工艺路线、工艺BOM、技术文档管理
*   [x] **生产管理子系统** - 符合工业4.0标准的智能制造，包含切割优化、生产排程、质量控制
*   [x] **人事管理子系统** - 重点突出玻璃深加工行业的计件工资计算和绩效管理
*   [x] **仓储管理子系统** - 智能仓储管理，支持传统玻璃铁架和智能玻璃架的存储管理
*   [x] **财务管理子系统** - 业财一体化，为其他子系统提供应收应付和收/付款、成本核算、财务分析能力

**其他系统：**
*   [ ] **数据中心** - 数据集成、报表分析、决策支持、数据可视化

**输出要求：**
1. 严格按照 @.augment/rules/requirement-doc.md 中定义的标准模板格式输出每个子系统的需求文档
2. 明确定义各子系统的功能边界，避免功能重叠和遗漏
3. 详细描述子系统间的数据流转和业务协同关系
4. 突出玻璃深加工行业的特殊业务需求和技术要求
5. 每个子系统需包含完整的页面功能明细、用户场景、Product Backlog
6. 标注各子系统与其他子系统的接口依赖关系

**实施方式：**
请按步骤逐个输出每个子系统的完整需求文档，每次输出一个子系统，确保内容完整性和专业性。从基础管理子系统开始，按照系统依赖关系的顺序进行输出。
