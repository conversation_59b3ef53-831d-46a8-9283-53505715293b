# 项目名称：玻璃深加工行业ERP管理系统

## 1. 项目概述

### 1.1 项目背景
玻璃深加工企业目前面临信息化程度低、管理效率低下的问题，主要使用Excel进行管理，导致数据容易错误、遗漏。建筑玻璃订单需求量大、规格复杂，单个订单可包含数百种不同规格产品。酒店隔断、防火窗、幕墙等业务需要采用项目制管理，各有不同的管理流程。急需一套针对玻璃深加工行业的综合管理系统。

### 1.2 项目目标
- 构建涵盖ERP、MES、WMS、CRM、PLM、财务一体化的综合管理平台
- 解决玻璃深加工行业复杂规格管理、项目制管理、外协管理等特殊需求
- 提升企业信息化水平，提高管理效率，降低错误率
- 支持建筑玻璃、家具玻璃、装饰玻璃、特种玻璃等多种产品类型
- 实现生产中心独立核算与合并报表的财务管理

### 1.3 目标用户
- **主要用户**：玻璃深加工企业（建筑玻璃、家具玻璃、装饰玻璃、特种玻璃）
- **相关企业**：酒店隔断企业、防火窗企业、幕墙企业
- **用户角色**：企业管理者、生产管理员、销售人员、采购人员、财务人员、质检员、仓库管理员

## 2. 页面与功能映射

### 2.1 页面列表
- 系统首页/仪表板
- 订单管理页面
- 产品管理页面
- 生产管理页面
- 库存管理页面
- 客户管理页面
- 项目管理页面
- 财务管理页面
- 系统设置页面

### 2.2 页面功能明细

#### 2.2.1 系统首页/仪表板
- 功能模块：数据概览与监控
  - 功能描述：展示关键业务指标、生产状态、订单状态等实时信息
  - 主要界面元素：
    - 卡片组件 KPI指标卡片（订单数量、生产进度、库存状态、财务数据）
    - 图表组件 趋势图表（销售趋势、生产效率、质量指标）
    - 表格组件 待处理事项列表
    - 按钮组件 快速操作按钮
  - 输入/输出字段：
    - 字段名：dateRange
    - 类型：array<date>
    - 校验规则：必填，日期范围不超过1年
    - 依赖关系：影响所有图表数据展示
  - 交互流程：
    - 页面加载 -> 获取仪表板数据 -> 渲染图表和指标 -> 定时刷新数据
  - 相关API：
    - 接口名：getDashboardData
    - 请求参数：{dateRange: [startDate, endDate], userId: string}
    - 响应结构：{kpi: KPIData, charts: ChartData[], alerts: Alert[]}
    - 错误处理：网络错误时显示缓存数据，数据异常时显示默认值
  - 权限控制：所有登录用户可见，数据范围根据用户角色过滤

#### 2.2.2 订单管理页面
- 功能模块：复杂规格订单管理
  - 功能描述：处理建筑玻璃等复杂规格订单，支持批量规格录入和管理
  - 主要界面元素：
    - 表格组件 订单列表表格（支持分页、排序、筛选）
    - 表单组件 订单创建/编辑表单
    - 弹窗组件 规格批量录入弹窗
    - 按钮组件 操作按钮（新增、编辑、删除、审核、导出）
  - 输入/输出字段：
    - 字段名：orderSpecs
    - 类型：array<OrderSpec>
    - 校验规则：必填，每个规格必须包含尺寸、类型、厚度
    - 选项：玻璃类型（钢化玻璃、中空玻璃、夹层玻璃等）
    - 依赖关系：规格变化影响价格计算和库存检查
  - 交互流程：
    - 点击新增订单 -> 打开订单表单 -> 录入基本信息 -> 批量录入规格 -> 价格计算 -> 库存检查 -> 提交审核
  - 相关API：
    - 接口名：createOrder, updateOrder, getOrderList
    - 请求参数：{orderData: OrderData, specs: OrderSpec[]}
    - 响应结构：{orderId: string, status: string, totalAmount: number}
    - 错误处理：规格冲突时提示用户，库存不足时标记异常
  - 权限控制：销售人员可创建和编辑，管理员可审核和删除

#### 2.2.2 产品管理页面 **【增强】**
- 功能模块：产品信息与规格管理
  - 功能描述：管理玻璃产品的多维度规格参数、工艺路线、BOM清单、质量标准
  - 主要界面元素：
    - 表格组件 产品列表（支持多维度筛选）
    - 表单组件 产品信息表单（规格参数化配置）
    - 标签页组件 产品详情标签页（基本信息、规格参数、工艺路线、BOM、质量标准）
    - 树形组件 工艺路线设计器
    - 参数配置组件 多维度规格参数设置
  - 输入/输出字段：
    - 字段名：productSpecs **【新增】**
    - 类型：object
    - 校验规则：尺寸范围校验（长度≤3000mm，宽度≤2000mm），厚度规格校验（3-19mm），工艺参数合理性校验
    - 选项：玻璃类型（钢化、夹层、中空、镀膜），表面处理（抛光、磨砂、丝印），特殊功能（隔音、隔热、防弹、自清洁）
    - 依赖关系：规格参数影响工艺路线选择，工艺路线影响BOM构成
  - 交互流程：
    - 创建产品 -> 配置基础信息 -> 设置规格参数 -> 设计工艺路线 -> 关联BOM -> 设置质量标准 -> 测试验证
  - 相关API：
    - 接口名：createProduct, updateProductSpecs, getProcessRoute, validateSpecs **【新增】**
    - 请求参数：{productData: ProductData, specs: ProductSpecs, processRoute: ProcessRoute}
    - 响应结构：{productId: string, specsValidation: ValidationResult, processRoute: ProcessRoute[]}
    - 错误处理：规格冲突时提示调整方案，工艺不可行时推荐替代方案
  - 权限控制：产品工程师可配置产品，生产人员可查看工艺信息

#### 2.2.3 生产管理页面 **【增强】**
- 功能模块：生产计划与工艺控制
  - 功能描述：生产排程、切割优化、工艺参数控制、质量检验、设备监控
  - 主要界面元素：
    - 甘特图组件 生产排程甘特图
    - 表格组件 生产工单列表
    - 图形组件 切割优化方案展示 **【新增】**
    - 仪表盘组件 设备状态监控 **【新增】**
    - 表单组件 工艺参数设置表单 **【新增】**
    - 时间轴组件 质量检验记录 **【新增】**
  - 输入/输出字段：
    - 字段名：cuttingOptimization **【新增】**
    - 类型：object
    - 校验规则：原片尺寸校验，切割方案可行性校验，利用率阈值校验（≥90%）
    - 选项：优化算法（降维启发式、模拟退火、遗传算法），切割设备类型
    - 依赖关系：订单需求影响切割方案，原片库存影响方案选择
  - 交互流程：
    - 接收订单 -> 分析规格需求 -> 切割优化计算 -> 生成生产工单 -> 工艺参数设置 -> 生产执行 -> 质量检验 -> 成品入库
  - 相关API：
    - 接口名：optimizeCutting, generateWorkOrder, setProcessParams, recordQualityCheck **【新增】**
    - 请求参数：{orderSpecs: OrderSpecs, glassStock: GlassStock, processParams: ProcessParams}
    - 响应结构：{cuttingPlan: CuttingPlan, utilizationRate: number, workOrders: WorkOrder[]}
    - 错误处理：优化失败时提供手动方案，参数超限时自动调整
  - 权限控制：生产计划员可制定计划，操作工可查看工艺参数，质检员可录入检验结果

#### 2.2.4 库存管理页面 **【增强】**
- 功能模块：玻璃原片与成品库存管理
  - 功能描述：原片存储条件管理、精准出入库、库存预警、损耗统计、盘点管理
  - 主要界面元素：
    - 表格组件 库存列表（支持原片规格筛选）
    - 地图组件 仓库布局和存储位置 **【新增】**
    - 表单组件 出入库单据表单
    - 图表组件 库存分析图表（周转率、损耗率） **【新增】**
    - 预警组件 库存预警提醒 **【新增】**
    - 扫码组件 条码扫描器集成 **【新增】**
  - 输入/输出字段：
    - 字段名：glassStorage **【新增】**
    - 类型：object
    - 校验规则：存储条件校验（温度18-25℃，湿度45-65%），存储位置容量校验
    - 选项：存储区域（原片区、半成品区、成品区），存储条件（防碎、防潮、恒温）
    - 依赖关系：原片规格影响存储位置分配，存储条件影响库存质量
  - 交互流程：
    - 查看库存状态 -> 创建出入库单 -> 扫码确认 -> 分配存储位置 -> 更新库存 -> 生成库存报表
  - 相关API：
    - 接口名：getInventoryList, createStockMovement, updateInventory, allocateStorageLocation **【新增】**
    - 请求参数：{warehouseId: string, itemId: string, quantity: number, storageConditions: StorageConditions}
    - 响应结构：{currentStock: number, movements: Movement[], alerts: Alert[], storageLocation: Location}
    - 错误处理：库存不足时阻止出库，存储条件异常时发出预警，盘点差异时需要审核
  - 权限控制：仓库管理员可操作，财务人员可查看成本信息，质检员可查看质量状态

#### 2.2.6 客户管理页面
- 功能模块：客户信息与销售管理
  - 功能描述：管理客户档案、销售机会、合同管理、售后服务
  - 主要界面元素：
    - 表格组件 客户列表
    - 表单组件 客户信息表单
    - 标签页组件 客户详情标签页（基本信息、订单历史、售后记录）
    - 时间轴组件 客户跟进记录
  - 输入/输出字段：
    - 字段名：customerInfo
    - 类型：object
    - 校验规则：联系方式格式校验，信用等级范围校验
    - 选项：客户类型（建筑商、装饰公司、玻璃经销商）
    - 依赖关系：客户等级影响价格策略和信用额度
  - 交互流程：
    - 新增客户 -> 完善客户信息 -> 设置信用额度 -> 跟进销售机会 -> 签订合同 -> 售后服务
  - 相关API：
    - 接口名：createCustomer, updateCustomer, getCustomerOrders
    - 请求参数：{customerData: CustomerData, creditLimit: number}
    - 响应结构：{customerId: string, creditStatus: string, orders: Order[]}
    - 错误处理：重复客户检查，信用额度超限提醒
  - 权限控制：销售人员可管理分配客户，管理员可查看所有客户

#### 2.2.7 项目管理页面 **【增强】**
- 功能模块：项目制管理（防火窗、酒店隔断、幕墙工程）
  - 功能描述：管理大型项目的层级结构、设计确认、分阶段交付、现场安装、售后服务
  - 主要界面元素：
    - 树形组件 项目层级树（项目→楼栋→楼层→房间→户型）
    - 表格组件 项目任务列表
    - 甘特图组件 项目进度甘特图
    - 表单组件 设计确认表单
    - 地图组件 项目地理位置和安装进度 **【新增】**
    - 文档组件 设计图纸管理 **【新增】**
    - 协同组件 内外协生产协调 **【新增】**
  - 输入/输出字段：
    - 字段名：projectStructure
    - 类型：object
    - 校验规则：项目层级完整性校验，交付时间逻辑校验，设计变更影响评估 **【新增】**
    - 选项：项目类型（防火窗、酒店隔断、幕墙、装饰玻璃），交付方式（分批、整体）
    - 依赖关系：上级节点状态影响下级节点操作权限，设计变更影响生产计划和成本
  - 交互流程：
    - 创建项目 -> 建立层级结构 -> 设计确认 -> 现场复尺 -> 分解生产任务 -> 协调内外协生产 -> 分阶段交付 -> 现场安装 -> 项目验收 -> 售后服务
  - 相关API：
    - 接口名：createProject, updateProjectStructure, confirmDesign, coordinateProduction **【新增】**
    - 请求参数：{projectData: ProjectData, structure: ProjectStructure, designChanges: DesignChanges}
    - 响应结构：{projectId: string, phases: ProjectPhase[], status: string, costImpact: CostImpact}
    - 错误处理：设计变更时影响评估，交付延期时自动调整计划，质量问题时暂停相关工序
  - 权限控制：项目经理可管理项目，设计师可确认设计，施工人员可更新进度，客户可查看进度

#### 2.2.8 财务管理页面
- 功能模块：财务核算与报表
  - 功能描述：生产中心独立核算、合并报表、成本分析、财务监控
  - 主要界面元素：
    - 表格组件 财务报表表格
    - 图表组件 成本分析图表
    - 表单组件 财务数据录入表单
    - 标签页组件 不同报表类型切换
  - 输入/输出字段：
    - 字段名：financialData
    - 类型：object
    - 校验规则：金额精度校验，会计期间校验
    - 依赖关系：生产成本影响产品定价，销售收入影响利润分析
  - 交互流程：
    - 选择报表类型 -> 设置查询条件 -> 生成报表 -> 数据分析 -> 导出报表
  - 相关API：
    - 接口名：getFinancialReport, calculateCost, generateStatement
    - 请求参数：{reportType: string, period: DateRange, centerId: string}
    - 响应结构：{reportData: ReportData, summary: FinancialSummary}
    - 错误处理：数据不完整时提示补充，计算异常时显示错误详情
  - 权限控制：财务人员可操作，管理员可查看所有中心数据

#### 2.2.9 系统设置页面
- 功能模块：系统配置与用户管理
  - 功能描述：用户权限管理、系统参数配置、数据字典维护
  - 主要界面元素：
    - 表格组件 用户列表
    - 表单组件 用户信息表单
    - 树形组件 权限配置树
    - 标签页组件 系统配置标签页
  - 输入/输出字段：
    - 字段名：userPermissions
    - 类型：array<string>
    - 校验规则：权限代码格式校验，角色权限一致性校验
    - 依赖关系：角色变更影响用户权限，权限变更影响功能可见性
  - 交互流程：
    - 用户管理 -> 分配角色 -> 配置权限 -> 系统参数设置 -> 保存配置
  - 相关API：
    - 接口名：getUserList, updateUserRole, getSystemConfig
    - 请求参数：{userId: string, roleIds: string[], permissions: string[]}
    - 响应结构：{users: User[], roles: Role[], config: SystemConfig}
    - 错误处理：权限冲突时提示解决方案，配置错误时回滚设置
  - 权限控制：仅系统管理员可操作

## 3. 用户场景与流程

### 3.1 建筑玻璃复杂订单处理场景
- 用户角色：销售人员
- 场景描述：处理包含数百种不同规格的建筑玻璃订单
- 操作步骤：
  1. 登录系统 -> 进入订单管理页面
  2. 点击新增订单 -> 录入客户信息和基本订单信息
  3. 使用批量规格录入功能 -> 导入Excel规格清单或手动录入
  4. 系统自动校验规格合理性 -> 计算价格和交期
  5. 检查库存可用性 -> 标记需要采购的原料
  6. 提交订单审核 -> 通知生产部门安排生产
- 状态变化与交互说明：
  - 规格录入时实时校验尺寸范围和工艺可行性
  - 价格计算时根据数量自动应用阶梯价格
  - 库存检查时显示可用库存和预计补货时间
  - 提交后订单状态变为"待审核"，相关人员收到通知
- 期望结果：成功创建复杂规格订单，减少录入错误，提高处理效率

### 3.2 玻璃原片切割优化场景 **【新增】**
- 用户角色：生产计划员
- 场景描述：基于订单需求进行玻璃原片切割方案优化，最大化原片利用率
- 操作步骤：
  1. 进入生产管理页面 -> 选择待排产订单
  2. 系统自动分析订单中的玻璃规格需求
  3. 调用切割优化算法 -> 生成多种切割方案
  4. 对比方案的原片利用率和成本 -> 选择最优方案
  5. 确认切割方案 -> 生成生产工单和原片需求
  6. 打印切割图纸 -> 下发到切割工序
- 状态变化与交互说明：
  - 切割优化时考虑原片库存、设备能力、工艺要求
  - 方案对比显示利用率差异和成本影响
  - 确认后自动更新原片库存预占状态
  - 支持手动调整切割方案并重新计算利用率
- 期望结果：原片利用率提升至95%以上，降低原材料成本

### 3.3 玻璃产品质量追溯场景 **【新增】**
- 用户角色：质检员、客户服务人员
- 场景描述：当客户反馈产品质量问题时，快速追溯产品生产全过程
- 操作步骤：
  1. 扫描产品条码或输入产品编号
  2. 系统显示产品基本信息和生产批次
  3. 查看原片信息 -> 供应商、批次、检验记录
  4. 查看生产工艺 -> 各工序参数、操作人员、设备状态
  5. 查看质检记录 -> 各检验点的检测数据和结果
  6. 生成质量追溯报告 -> 分析问题原因
- 状态变化与交互说明：
  - 追溯信息按时间轴展示，支持工序级别钻取
  - 异常工序和参数用红色标识，便于快速定位
  - 支持批量追溯同批次产品的质量状况
  - 追溯报告可导出PDF格式供客户查看
- 期望结果：5分钟内完成质量追溯，快速定位问题原因

### 3.4 防火窗项目管理场景
- 用户角色：项目经理
- 场景描述：管理防火窗项目的三阶段交付流程
- 操作步骤：
  1. 创建项目 -> 建立项目层级结构（项目→楼栋→楼层→房间）
  2. 上传设计图纸 -> 分解产品构件（窗框、窗扇、固定玻璃）
  3. 制定生产计划 -> 安排分阶段生产（窗框→固定玻璃→窗扇）
  4. 跟踪生产进度 -> 协调内部生产和外协加工
  5. 组织分阶段交付 -> 现场安装指导
  6. 项目验收 -> 售后服务跟踪
- 状态变化与交互说明：
  - 项目层级创建时自动生成编码规则
  - 生产计划制定时考虑设备产能和外协周期
  - 交付状态实时更新，影响后续阶段的开始条件
  - 质量问题时自动暂停相关工序，通知质检部门
- 期望结果：项目按计划顺利交付，各阶段协调有序，客户满意度高

### 3.5 酒店隔断大型项目场景
- 用户角色：项目经理、设计师
- 场景描述：管理酒店隔断项目的全流程服务
- 操作步骤：
  1. 项目立项 -> 建立酒店→楼栋→楼层→房间→户型层级
  2. 按户型进行标准化设计 -> 设计确认和变更管理
  3. 按房间进行现场复尺 -> 生产确认和调整
  4. 协调玻璃制品和不锈钢制品生产（内部+外协）
  5. 安排安装队伍 -> 现场安装和调试
  6. 维护服务 -> 售后问题处理
- 状态变化与交互说明：
  - 设计变更时自动计算影响范围和成本变化
  - 复尺确认后锁定生产规格，防止后续变更
  - 内外协生产状态统一监控，异常时及时预警
  - 安装进度与客户需求匹配，支持分批交付
- 期望结果：大型项目管理规范化，交付质量稳定，成本控制有效

### 3.6 玻璃原片库存管理场景 **【新增】**
- 用户角色：仓库管理员
- 场景描述：管理玻璃原片的特殊存储条件和精准出入库
- 操作步骤：
  1. 原片入库 -> 扫描条码记录规格、供应商、批次信息
  2. 分配存储位置 -> 考虑防碎、防潮、恒温要求
  3. 定期盘点 -> 检查原片完整性和存储条件
  4. 生产领料 -> 按切割方案精准出库
  5. 库存预警 -> 监控安全库存和滞销原片
  6. 损耗管理 -> 记录破损原因和责任人
- 状态变化与交互说明：
  - 入库时自动分配最优存储位置
  - 出库时优先使用库存时间较长的原片
  - 破损原片自动标记为不可用状态
  - 库存预警信息推送给采购和生产部门
- 期望结果：原片完好率达到99%以上，库存周转率提升20%

### 3.7 多维度产品规格配置场景 **【新增】**
- 用户角色：产品工程师
- 场景描述：配置复杂的玻璃产品规格参数和工艺路线
- 操作步骤：
  1. 创建产品基础信息 -> 设置产品类别和用途
  2. 配置规格参数 -> 尺寸、厚度、颜色、镀膜类型等
  3. 设计工艺路线 -> 切割→磨边→清洗→镀膜→钢化→包装
  4. 设置工艺参数 -> 钢化温度、镀膜厚度、检验标准
  5. 关联BOM清单 -> 原片、辅料、包装材料
  6. 测试验证 -> 小批量试产验证工艺可行性
- 状态变化与交互说明：
  - 规格参数支持多维度组合和版本管理
  - 工艺路线变更时自动检查BOM一致性
  - 参数设置时实时校验工艺可行性
  - 支持从现有产品复制配置并修改
- 期望结果：产品配置准确率100%，新产品开发周期缩短30%

### 3.8 订单变更管理场景 **【新增】**
- 用户角色：销售人员、生产计划员
- 场景描述：处理客户订单的规格调整、数量增减、交期变更
- 操作步骤：
  1. 接收客户变更需求 -> 评估变更可行性
  2. 技术评审 -> 检查新规格的工艺要求
  3. 成本评估 -> 计算变更对成本和交期的影响
  4. 生产影响分析 -> 检查已投产订单的调整方案
  5. 客户确认 -> 签署变更确认单
  6. 系统更新 -> 修改订单信息并通知相关部门
- 状态变化与交互说明：
  - 变更评估时显示对原计划的具体影响
  - 已投产订单变更需要特殊审批流程
  - 变更历史完整记录，支持版本对比
  - 自动通知采购、生产、质检等相关部门
- 期望结果：订单变更响应时间缩短至4小时内，变更准确率100%

## 4. 非功能需求

### 4.1 通用性能需求
- **系统响应时间**：页面加载时间 < 3秒，查询响应时间 < 2秒
- **并发用户数**：支持500个并发用户同时操作
- **系统可用性**：99.5%以上，计划停机时间 < 4小时/月

### 4.2 通用兼容性要求
- **操作系统**：Windows 10+, macOS 10.15+, Linux Ubuntu 18.04+
- **浏览器**：Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **移动设备**：iOS 13+, Android 8.0+
- **分辨率**：最小支持1366x768，推荐1920x1080

### 4.3 通用安全要求
- **数据加密**：敏感数据采用AES-256加密存储
- **身份认证**：支持JWT Token认证，密码复杂度要求
- **权限控制**：基于RBAC的细粒度权限管理
- **数据备份**：每日自动备份，支持异地备份
- **审计日志**：记录所有关键操作，支持日志查询和分析

### 4.4 通用可用性要求
- **界面友好**：符合现代UI设计规范，操作直观简便
- **多语言**：支持中文、英文界面切换
- **帮助系统**：提供在线帮助文档和操作指南
- **错误处理**：友好的错误提示和异常恢复机制
- **可访问性**：支持键盘导航，符合WCAG 2.1 AA标准

### 4.5 玻璃深加工行业特殊技术要求

#### 4.5.1 切割优化算法要求
- **算法类型**：支持降维启发式算法、模拟退火算法、遗传算法
- **优化目标**：原片利用率≥95%，切割方案生成时间≤30秒
- **约束条件**：考虑设备能力、工艺要求、质量标准

#### 4.5.2 防错机制要求
- **条码系统**：支持一维码、二维码、RFID标签识别
- **自动校验**：规格参数自动校验，工艺可行性检查
- **防呆设计**：关键操作需要二次确认，防止误操作

#### 4.5.3 质量追溯系统要求
- **追溯粒度**：支持到单片玻璃级别的质量追溯
- **数据完整性**：记录原片信息、工艺参数、检验数据、操作人员
- **追溯速度**：5分钟内完成完整质量追溯链查询
- **报告生成**：自动生成质量追溯报告，支持PDF导出
- **批次管理**：支持同批次产品的批量质量分析

#### 4.5.4 智能仓储系统要求
- **存储条件监控**：实时监控温度、湿度、光照等环境参数
- **位置管理**：精确到货架位置的库存定位
- **自动预警**：库存异常、环境异常、设备故障自动预警
- **盘点支持**：支持RFID批量盘点，提高盘点效率
- **损耗分析**：自动统计和分析库存损耗原因

## 5. Product Backlog **【增强】**

### 5.1 史诗 玻璃原片管理系统 **【新增史诗】**
- 关联页面：库存管理页面、生产管理页面
- 关联功能模块：原片库存、切割优化、质量管理
- 史诗描述：建立完整的玻璃原片管理体系，包括存储条件管理、切割优化、质量追溯

#### 5.1.1 用户故事 原片入库管理
- 角色：仓库管理员
- 目标：准确记录原片信息并分配合适的存储位置
- 关联页面/功能/界面元素：库存管理页面/原片入库/入库表单、存储位置分配
- 交互动作：扫描原片条码、录入规格信息、选择存储区域、确认入库
- 输入/输出字段：glassSpecs(object)、storageLocation(string)、supplierInfo(object)
- 状态变化/权限控制/数据交互：入库后更新库存数量，分配最优存储位置，记录供应商信息
- 验收标准：原片信息准确录入，存储位置合理分配，库存数据实时更新

#### 5.1.2 用户故事 切割方案优化
- 角色：生产计划员
- 目标：基于订单需求生成最优切割方案，提高原片利用率
- 关联页面/功能/界面元素：生产管理页面/切割优化/方案对比界面
- 交互动作：选择订单、设置优化参数、运行算法、对比方案、确认方案
- 输入/输出字段：orderSpecs(array)、optimizationParams(object)、cuttingPlan(object)
- 状态变化/权限控制/数据交互：生成多个切割方案，显示利用率对比，确认后生成工单
- 验收标准：切割方案利用率≥95%，方案生成时间≤30秒，支持手动调整

### 5.2 史诗 产品规格参数化管理 **【新增史诗】**
- 关联页面：产品管理页面、订单管理页面
- 关联功能模块：产品配置、规格管理、工艺设计
- 史诗描述：建立多维度产品规格参数管理体系，支持复杂规格配置和工艺关联

#### 5.2.1 用户故事 多维度规格配置
- 角色：产品工程师
- 目标：配置复杂的玻璃产品规格参数和工艺路线
- 关联页面/功能/界面元素：产品管理页面/规格配置/参数设置表单、工艺路线设计器
- 交互动作：设置基础参数、配置规格组合、设计工艺路线、关联BOM、验证配置
- 输入/输出字段：productSpecs(object)、processRoute(array)、bomList(array)
- 状态变化/权限控制/数据交互：规格参数影响工艺选择，工艺路线影响BOM构成，配置完成后可用于订单
- 验收标准：支持多维度参数配置，工艺路线与规格匹配，BOM自动关联

#### 5.2.2 用户故事 规格参数校验
- 角色：销售人员
- 目标：在录入订单时自动校验规格参数的合理性和可行性
- 关联页面/功能/界面元素：订单管理页面/规格录入/参数校验提示
- 交互动作：录入产品规格、触发自动校验、查看校验结果、调整参数
- 输入/输出字段：orderSpecs(object)、validationResult(object)、suggestions(array)
- 状态变化/权限控制/数据交互：实时校验规格合理性，显示不合理参数，提供调整建议
- 验收标准：规格校验准确率100%，不合理参数及时提示，提供可行性建议

### 5.3 史诗 项目制管理系统 **【新增史诗】**
- 关联页面：项目管理页面、订单管理页面
- 关联功能模块：项目管理、设计管理、安装管理
- 史诗描述：建立完整的项目制管理体系，支持大型工程项目的全流程管理

#### 5.3.1 用户故事 项目层级管理
- 角色：项目经理
- 目标：建立项目的层级结构，支持分阶段管理和交付
- 关联页面/功能/界面元素：项目管理页面/层级管理/项目树形结构
- 交互动作：创建项目、建立层级、分配任务、设置里程碑、跟踪进度
- 输入/输出字段：projectStructure(object)、milestones(array)、taskAssignments(array)
- 状态变化/权限控制/数据交互：层级创建后自动生成编码，任务分配后通知相关人员，进度更新影响整体计划
- 验收标准：支持多级项目层级，任务分配清晰，进度跟踪准确

#### 5.3.2 用户故事 设计变更管理
- 角色：设计师、项目经理
- 目标：管理项目设计变更，评估变更影响并及时调整计划
- 关联页面/功能/界面元素：项目管理页面/设计管理/变更影响评估
- 交互动作：提交设计变更、评估影响范围、计算成本变化、客户确认、更新计划
- 输入/输出字段：designChanges(object)、impactAssessment(object)、costImpact(object)
- 状态变化/权限控制/数据交互：变更提交后进入评估流程，影响评估完成后需客户确认，确认后自动更新相关计划
- 验收标准：变更影响评估准确，成本计算正确，计划调整及时

### 5.4 史诗 用户认证与权限管理 **【原有史诗保持】**
- 关联页面：系统设置页面、登录页面
- 关联功能模块：用户管理、角色管理、权限控制
- 史诗描述：建立完整的用户认证体系和基于角色的权限管理系统

#### 5.4.1 用户故事 用户登录认证
- 角色：系统用户
- 目标：安全登录系统并获得相应权限
- 关联页面/功能/界面元素：登录页面/用户认证/登录表单
- 交互动作：输入用户名密码、点击登录按钮、验证码输入
- 输入/输出字段：username(string)、password(string)、captcha(string)
- 状态变化/权限控制/数据交互：登录成功后获得JWT Token，根据角色加载对应权限
- 验收标准：用户能够成功登录，获得正确权限，登录失败有明确提示

### 5.5 史诗 质量管理与追溯系统 **【新增史诗】**
- 关联页面：生产管理页面、产品管理页面
- 关联功能模块：质量检验、质量追溯、不合格品管理
- 史诗描述：建立完整的质量管理体系，实现从原片到成品的全程质量追溯

#### 5.5.1 用户故事 质量检验管理
- 角色：质检员
- 目标：按照质量标准进行各工序质量检验，记录检验数据
- 关联页面/功能/界面元素：生产管理页面/质量检验/检验数据录入表单
- 交互动作：扫描产品条码、选择检验项目、录入检验数据、判定检验结果
- 输入/输出字段：inspectionData(object)、qualityStandard(object)、inspectionResult(string)
- 状态变化/权限控制/数据交互：检验完成后更新产品状态，不合格品自动隔离，检验数据永久保存
- 验收标准：检验数据准确录入，不合格品及时处理，检验结果可追溯

#### 5.5.2 用户故事 质量追溯查询
- 角色：质检员、客户服务人员
- 目标：快速追溯产品质量信息，定位质量问题原因
- 关联页面/功能/界面元素：产品管理页面/质量追溯/追溯信息展示
- 交互动作：扫描产品条码、查看追溯信息、分析质量数据、生成追溯报告
- 输入/输出字段：productCode(string)、traceabilityData(object)、qualityReport(object)
- 状态变化/权限控制/数据交互：追溯查询不影响数据状态，追溯报告可导出分享
- 验收标准：5分钟内完成质量追溯，信息完整准确，报告格式规范
