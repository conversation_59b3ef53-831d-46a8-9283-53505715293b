# Glass4 ERP Mock 认证系统

## 概述

本 Mock 认证系统为 Glass4 ERP 提供了完整的 JWT 认证功能，包括登录、登出、Token 刷新和用户信息获取。系统遵循 Context7 最佳实践，提供安全、可靠的认证服务。

## 功能特性

### 🔐 核心认证功能
- **JWT Token 认证**: 基于 HMAC-SHA256 的 JWT 实现
- **Access Token**: 1小时有效期，用于 API 访问
- **Refresh Token**: 7天有效期，用于刷新 Access Token
- **Token 黑名单**: 登出时撤销 Token，防止重复使用

### 🛡️ 安全特性
- **登录失败限制**: 5次失败后锁定账户15分钟
- **密码验证**: 最小长度要求
- **账户状态检查**: 支持 active/inactive/locked 状态
- **Token 验证**: 签名验证、过期检查、黑名单检查

### 👥 用户管理
- **多角色支持**: admin、manager、user
- **权限系统**: 基于角色的权限控制
- **用户状态**: 活跃、禁用、锁定状态管理

## API 接口

### 1. 用户登录
```http
POST /api/v1/sys/user/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**成功响应:**
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "name": "系统管理员",
      "role": "admin",
      "permissions": ["user:read", "user:write", ...]
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "732983250da0a11bcbd...",
    "expiresIn": 3600,
    "tokenType": "Bearer"
  }
}
```

### 2. 获取当前用户信息
```http
GET /api/v1/sys/user/me
Authorization: Bearer {accessToken}
```

### 3. 刷新 Token
```http
POST /api/v1/sys/user/refresh
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "refreshToken": "732983250da0a11bcbd..."
}
```

### 4. 用户登出
```http
POST /api/v1/sys/user/logout
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "refreshToken": "732983250da0a11bcbd..."
}
```

## 测试账户

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | admin123 | admin | 全部权限 |
| manager | manager123 | manager | 管理权限 |
| user | user123 | user | 基础权限 |
| locked_user | locked123 | user | 已锁定账户 |

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 认证失败（用户名密码错误、Token 无效）
- `403`: 权限不足（账户被禁用）
- `404`: 用户不存在
- `423`: 账户被锁定
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": 401,
    "message": "用户名或密码错误",
    "details": "INVALID_CREDENTIALS",
    "timestamp": "2025-08-03T10:00:00.000Z"
  }
}
```

## 开发指南

### 启动服务
```bash
# 启动 Mock API 服务
cd mock
npm run dev

# 启动前端服务（带代理）
npm run dev:vite

# 同时启动两个服务
npm run dev
```

### 测试集成
```bash
# 运行集成测试
node test-auth-integration.js
```

### 前端集成示例

```typescript
// 登录
const loginResponse = await fetch('/api/v1/sys/user/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username, password })
});

// 获取用户信息
const userResponse = await fetch('/api/v1/sys/user/me', {
  headers: { 'Authorization': `Bearer ${accessToken}` }
});

// 刷新 Token
const refreshResponse = await fetch('/api/v1/sys/user/refresh', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${accessToken}`
  },
  body: JSON.stringify({ refreshToken })
});
```

## 文件结构

```
mock/
├── data/
│   └── auth.ts              # 用户数据和认证配置
├── utils/
│   └── jwt.ts               # JWT 工具函数
├── routes/api/v1/sys/user/
│   ├── login.post.ts        # 登录接口
│   ├── me.get.ts           # 用户信息接口
│   ├── logout.post.ts      # 登出接口
│   └── refresh.post.ts     # 刷新 Token 接口
└── README-AUTH.md          # 本文档
```

## 注意事项

1. **开发环境专用**: 此 Mock 系统仅用于开发环境，不可用于生产环境
2. **密码安全**: 测试密码为明文存储，生产环境需要使用 bcrypt 等加密
3. **Token 存储**: 当前使用内存存储，重启服务会清空所有 Token
4. **CORS 配置**: 已配置 CORS 支持跨域请求
5. **端口配置**: Mock API 默认运行在 3002 端口

## Context7 最佳实践

本系统遵循以下 Context7 认证最佳实践：

1. **JWT 结构标准**: 使用标准的 Header.Payload.Signature 结构
2. **Token 生命周期**: 短期 Access Token + 长期 Refresh Token
3. **安全头部**: 正确设置 Authorization Bearer 头部
4. **错误处理**: 统一的错误响应格式
5. **状态管理**: 完整的用户状态和权限管理
6. **登出处理**: 主动撤销 Token 而非仅依赖过期

## 故障排除

### 常见问题

1. **端口冲突**: 如果 3002 端口被占用，Nitro 会自动选择其他端口
2. **代理失败**: 检查 vite.config.ts 中的代理配置是否正确
3. **CORS 错误**: 确保 Mock API 服务正在运行
4. **Token 失效**: 检查系统时间是否正确

### 调试技巧

1. 查看 Mock API 控制台输出
2. 使用浏览器开发者工具检查网络请求
3. 运行 `node test-auth-integration.js` 验证 API 功能
4. 检查 Vite 代理日志输出
