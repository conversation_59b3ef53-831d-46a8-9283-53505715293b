// Nitro 全局类型定义
import type { EventHandler, H3Event } from 'h3';

declare global {
  // Nitro 全局函数
  function defineEventHandler<T = any>(handler: EventHandler<T>): EventHandler<T>;
  function getMethod(event: H3Event): string;
  function readBody<T = any>(event: H3Event): Promise<T>;
  function getHeader(event: H3Event, name: string): string | undefined;
  function getHeaders(event: H3Event): Record<string, string | string[]>;
  function getQuery(event: H3Event): Record<string, string | string[]>;
  function createError(options: {
    statusCode: number;
    statusMessage: string;
    data?: any;
  }): Error;
}

export {};
