{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "strict": true, "noEmit": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "types": ["node"], "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["**/*.ts", "**/*.js", "types/**/*.d.ts"], "exclude": ["node_modules", "dist", ".nitro"]}