import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import { tmpdir } from 'node:os';
import destr from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/destr@2.0.5/node_modules/destr/dist/index.mjs';
import { defineEventHandler, handleCacheHeaders, splitCookiesString, createEvent, fetchWithEvent, isEvent, eventHandler, setHeaders, sendRedirect, proxyRequest, getRequestURL, getRequestHeader, getResponseHeader, getRequestHeaders, setResponseHeaders, setResponseStatus, send, appendResponseHeader, removeResponseHeader, createError, setResponseHeader, createApp, createRouter as createRouter$1, toNodeListener, lazyEventHandler, getRouterParam, readBody, getQuery as getQuery$1, getHeaders, getHeader, getMethod } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/h3@1.15.4/node_modules/h3/dist/index.mjs';
import { createHooks } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/hookable@5.5.3/node_modules/hookable/dist/index.mjs';
import { createFetch, Headers as Headers$1 } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch/dist/node.mjs';
import { fetchNodeRequestHandler, callNodeRequestHandler } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/node-mock-http@1.0.2/node_modules/node-mock-http/dist/index.mjs';
import { parseURL, withoutBase, joinURL, getQuery, withQuery, decodePath, withLeadingSlash, withoutTrailingSlash } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/ufo@1.6.1/node_modules/ufo/dist/index.mjs';
import { createStorage, prefixStorage } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/unstorage@1.16.1_@netlify+blobs@9.1.2_db0@0.3.2_ioredis@5.7.0/node_modules/unstorage/dist/index.mjs';
import unstorage_47drivers_47fs from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/unstorage@1.16.1_@netlify+blobs@9.1.2_db0@0.3.2_ioredis@5.7.0/node_modules/unstorage/drivers/fs.mjs';
import { digest } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/ohash@2.0.11/node_modules/ohash/dist/index.mjs';
import { klona } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/klona@2.0.6/node_modules/klona/dist/index.mjs';
import defu, { defuFn } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/defu@6.1.4/node_modules/defu/dist/defu.mjs';
import { snakeCase } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/scule@1.3.0/node_modules/scule/dist/index.mjs';
import { getContext } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/unctx@2.4.1/node_modules/unctx/dist/index.mjs';
import { toRouteMatcher, createRouter } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/radix3@1.1.2/node_modules/radix3/dist/index.mjs';
import { readFile } from 'node:fs/promises';
import { resolve, dirname, join } from 'node:path';
import consola from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/consola@3.4.2/node_modules/consola/dist/index.mjs';
import { ErrorParser } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/youch-core@0.3.3/node_modules/youch-core/build/index.js';
import { Youch } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/youch@4.1.0-beta.8/node_modules/youch/build/index.js';
import { SourceMapConsumer } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/source-map@0.7.6/node_modules/source-map/source-map.js';
import { promises, readFileSync, writeFileSync } from 'node:fs';
import { fileURLToPath } from 'node:url';
import { dirname as dirname$1, resolve as resolve$1 } from 'file:///Users/<USER>/SynologyDrive/works/glass4_erp_0802/node_modules/.pnpm/pathe@2.0.3/node_modules/pathe/dist/index.mjs';
import { Server } from 'node:http';
import nodeCrypto, { randomBytes, createHmac } from 'node:crypto';
import { parentPort, threadId } from 'node:worker_threads';

const serverAssets = [{"baseName":"server","dir":"/Users/<USER>/SynologyDrive/works/glass4_erp_0802/mock/assets"}];

const assets$1 = createStorage();

for (const asset of serverAssets) {
  assets$1.mount(asset.baseName, unstorage_47drivers_47fs({ base: asset.dir, ignore: (asset?.ignore || []) }));
}

const storage = createStorage({});

storage.mount('/assets', assets$1);

storage.mount('root', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"/Users/<USER>/SynologyDrive/works/glass4_erp_0802/mock"}));
storage.mount('src', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"/Users/<USER>/SynologyDrive/works/glass4_erp_0802/mock"}));
storage.mount('build', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"/Users/<USER>/SynologyDrive/works/glass4_erp_0802/mock/.nitro"}));
storage.mount('cache', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"/Users/<USER>/SynologyDrive/works/glass4_erp_0802/mock/.nitro/cache"}));
storage.mount('data', unstorage_47drivers_47fs({"driver":"fs","base":"/Users/<USER>/SynologyDrive/works/glass4_erp_0802/mock/.data/kv"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage, base) : storage;
}

const Hasher = /* @__PURE__ */ (() => {
  class Hasher2 {
    buff = "";
    #context = /* @__PURE__ */ new Map();
    write(str) {
      this.buff += str;
    }
    dispatch(value) {
      const type = value === null ? "null" : typeof value;
      return this[type](value);
    }
    object(object) {
      if (object && typeof object.toJSON === "function") {
        return this.object(object.toJSON());
      }
      const objString = Object.prototype.toString.call(object);
      let objType = "";
      const objectLength = objString.length;
      objType = objectLength < 10 ? "unknown:[" + objString + "]" : objString.slice(8, objectLength - 1);
      objType = objType.toLowerCase();
      let objectNumber = null;
      if ((objectNumber = this.#context.get(object)) === void 0) {
        this.#context.set(object, this.#context.size);
      } else {
        return this.dispatch("[CIRCULAR:" + objectNumber + "]");
      }
      if (typeof Buffer !== "undefined" && Buffer.isBuffer && Buffer.isBuffer(object)) {
        this.write("buffer:");
        return this.write(object.toString("utf8"));
      }
      if (objType !== "object" && objType !== "function" && objType !== "asyncfunction") {
        if (this[objType]) {
          this[objType](object);
        } else {
          this.unknown(object, objType);
        }
      } else {
        const keys = Object.keys(object).sort();
        const extraKeys = [];
        this.write("object:" + (keys.length + extraKeys.length) + ":");
        const dispatchForKey = (key) => {
          this.dispatch(key);
          this.write(":");
          this.dispatch(object[key]);
          this.write(",");
        };
        for (const key of keys) {
          dispatchForKey(key);
        }
        for (const key of extraKeys) {
          dispatchForKey(key);
        }
      }
    }
    array(arr, unordered) {
      unordered = unordered === void 0 ? false : unordered;
      this.write("array:" + arr.length + ":");
      if (!unordered || arr.length <= 1) {
        for (const entry of arr) {
          this.dispatch(entry);
        }
        return;
      }
      const contextAdditions = /* @__PURE__ */ new Map();
      const entries = arr.map((entry) => {
        const hasher = new Hasher2();
        hasher.dispatch(entry);
        for (const [key, value] of hasher.#context) {
          contextAdditions.set(key, value);
        }
        return hasher.toString();
      });
      this.#context = contextAdditions;
      entries.sort();
      return this.array(entries, false);
    }
    date(date) {
      return this.write("date:" + date.toJSON());
    }
    symbol(sym) {
      return this.write("symbol:" + sym.toString());
    }
    unknown(value, type) {
      this.write(type);
      if (!value) {
        return;
      }
      this.write(":");
      if (value && typeof value.entries === "function") {
        return this.array(
          [...value.entries()],
          true
          /* ordered */
        );
      }
    }
    error(err) {
      return this.write("error:" + err.toString());
    }
    boolean(bool) {
      return this.write("bool:" + bool);
    }
    string(string) {
      this.write("string:" + string.length + ":");
      this.write(string);
    }
    function(fn) {
      this.write("fn:");
      if (isNativeFunction(fn)) {
        this.dispatch("[native]");
      } else {
        this.dispatch(fn.toString());
      }
    }
    number(number) {
      return this.write("number:" + number);
    }
    null() {
      return this.write("Null");
    }
    undefined() {
      return this.write("Undefined");
    }
    regexp(regex) {
      return this.write("regex:" + regex.toString());
    }
    arraybuffer(arr) {
      this.write("arraybuffer:");
      return this.dispatch(new Uint8Array(arr));
    }
    url(url) {
      return this.write("url:" + url.toString());
    }
    map(map) {
      this.write("map:");
      const arr = [...map];
      return this.array(arr, false);
    }
    set(set) {
      this.write("set:");
      const arr = [...set];
      return this.array(arr, false);
    }
    bigint(number) {
      return this.write("bigint:" + number.toString());
    }
  }
  for (const type of [
    "uint8array",
    "uint8clampedarray",
    "unt8array",
    "uint16array",
    "unt16array",
    "uint32array",
    "unt32array",
    "float32array",
    "float64array"
  ]) {
    Hasher2.prototype[type] = function(arr) {
      this.write(type + ":");
      return this.array([...arr], false);
    };
  }
  function isNativeFunction(f) {
    if (typeof f !== "function") {
      return false;
    }
    return Function.prototype.toString.call(f).slice(
      -15
      /* "[native code] }".length */
    ) === "[native code] }";
  }
  return Hasher2;
})();
function serialize(object) {
  const hasher = new Hasher();
  hasher.dispatch(object);
  return hasher.buff;
}
function hash(value) {
  return digest(typeof value === "string" ? value : serialize(value)).replace(/[-_]/g, "").slice(0, 10);
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.waitUntil = incomingEvent.waitUntil;
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

const inlineAppConfig = {};



const appConfig = defuFn(inlineAppConfig);

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /\{\{([^{}]*)\}\}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/"
  },
  "nitro": {
    "routeRules": {
      "/api/**": {
        "cors": true,
        "headers": {
          "access-control-allow-origin": "*",
          "access-control-allow-methods": "*",
          "access-control-allow-headers": "*",
          "access-control-max-age": "0",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
      }
    }
  }
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  {
    return _sharedRuntimeConfig;
  }
}
_deepFreeze(klona(appConfig));
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

getContext("nitro-app", {
  asyncContext: undefined,
  AsyncLocalStorage: void 0
});

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

function _captureError(error, type) {
  console.error(`[${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

function defineNitroErrorHandler(handler) {
  return handler;
}

const errorHandler$0 = defineNitroErrorHandler(
  async function defaultNitroErrorHandler(error, event) {
    const res = await defaultHandler(error, event);
    if (!event.node?.res.headersSent) {
      setResponseHeaders(event, res.headers);
    }
    setResponseStatus(event, res.status, res.statusText);
    return send(
      event,
      typeof res.body === "string" ? res.body : JSON.stringify(res.body, null, 2)
    );
  }
);
async function defaultHandler(error, event, opts) {
  const isSensitive = error.unhandled || error.fatal;
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage || "Server Error";
  const url = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
  if (statusCode === 404) {
    const baseURL = "/";
    if (/^\/[^/]/.test(baseURL) && !url.pathname.startsWith(baseURL)) {
      const redirectTo = `${baseURL}${url.pathname.slice(1)}${url.search}`;
      return {
        status: 302,
        statusText: "Found",
        headers: { location: redirectTo },
        body: `Redirecting...`
      };
    }
  }
  await loadStackTrace(error).catch(consola.error);
  const youch = new Youch();
  if (isSensitive && !opts?.silent) {
    const tags = [error.unhandled && "[unhandled]", error.fatal && "[fatal]"].filter(Boolean).join(" ");
    const ansiError = await (await youch.toANSI(error)).replaceAll(process.cwd(), ".");
    consola.error(
      `[request error] ${tags} [${event.method}] ${url}

`,
      ansiError
    );
  }
  const useJSON = opts?.json || !getRequestHeader(event, "accept")?.includes("text/html");
  const headers = {
    "content-type": useJSON ? "application/json" : "text/html",
    // Prevent browser from guessing the MIME types of resources.
    "x-content-type-options": "nosniff",
    // Prevent error page from being embedded in an iframe
    "x-frame-options": "DENY",
    // Prevent browsers from sending the Referer header
    "referrer-policy": "no-referrer",
    // Disable the execution of any js
    "content-security-policy": "script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"
  };
  if (statusCode === 404 || !getResponseHeader(event, "cache-control")) {
    headers["cache-control"] = "no-cache";
  }
  const body = useJSON ? {
    error: true,
    url,
    statusCode,
    statusMessage,
    message: error.message,
    data: error.data,
    stack: error.stack?.split("\n").map((line) => line.trim())
  } : await youch.toHTML(error, {
    request: {
      url: url.href,
      method: event.method,
      headers: getRequestHeaders(event)
    }
  });
  return {
    status: statusCode,
    statusText: statusMessage,
    headers,
    body
  };
}
async function loadStackTrace(error) {
  if (!(error instanceof Error)) {
    return;
  }
  const parsed = await new ErrorParser().defineSourceLoader(sourceLoader).parse(error);
  const stack = error.message + "\n" + parsed.frames.map((frame) => fmtFrame(frame)).join("\n");
  Object.defineProperty(error, "stack", { value: stack });
  if (error.cause) {
    await loadStackTrace(error.cause).catch(consola.error);
  }
}
async function sourceLoader(frame) {
  if (!frame.fileName || frame.fileType !== "fs" || frame.type === "native") {
    return;
  }
  if (frame.type === "app") {
    const rawSourceMap = await readFile(`${frame.fileName}.map`, "utf8").catch(() => {
    });
    if (rawSourceMap) {
      const consumer = await new SourceMapConsumer(rawSourceMap);
      const originalPosition = consumer.originalPositionFor({ line: frame.lineNumber, column: frame.columnNumber });
      if (originalPosition.source && originalPosition.line) {
        frame.fileName = resolve(dirname(frame.fileName), originalPosition.source);
        frame.lineNumber = originalPosition.line;
        frame.columnNumber = originalPosition.column || 0;
      }
    }
  }
  const contents = await readFile(frame.fileName, "utf8").catch(() => {
  });
  return contents ? { contents } : void 0;
}
function fmtFrame(frame) {
  if (frame.type === "native") {
    return frame.raw;
  }
  const src = `${frame.fileName || ""}:${frame.lineNumber}:${frame.columnNumber})`;
  return frame.functionName ? `at ${frame.functionName} (${src}` : `at ${src}`;
}

const errorHandlers = [errorHandler$0];

async function errorHandler(error, event) {
  for (const handler of errorHandlers) {
    try {
      await handler(error, event, { defaultHandler });
      if (event.handled) {
        return; // Response handled
      }
    } catch(error) {
      // Handler itself thrown, log and continue
      console.error(error);
    }
  }
  // H3 will handle fallback
}

const plugins = [
  
];

const assets = {};

function readAsset (id) {
  const serverDir = dirname$1(fileURLToPath(globalThis._importMeta_.url));
  return promises.readFile(resolve$1(serverDir, assets[id].path))
}

const publicAssetBases = {};

function isPublicAssetURL(id = '') {
  if (assets[id]) {
    return true
  }
  for (const base in publicAssetBases) {
    if (id.startsWith(base)) { return true }
  }
  return false
}

function getAsset (id) {
  return assets[id]
}

const METHODS = /* @__PURE__ */ new Set(["HEAD", "GET"]);
const EncodingMap = { gzip: ".gz", br: ".br" };
const _O5lzJQ = eventHandler((event) => {
  if (event.method && !METHODS.has(event.method)) {
    return;
  }
  let id = decodePath(
    withLeadingSlash(withoutTrailingSlash(parseURL(event.path).pathname))
  );
  let asset;
  const encodingHeader = String(
    getRequestHeader(event, "accept-encoding") || ""
  );
  const encodings = [
    ...encodingHeader.split(",").map((e) => EncodingMap[e.trim()]).filter(Boolean).sort(),
    ""
  ];
  if (encodings.length > 1) {
    appendResponseHeader(event, "Vary", "Accept-Encoding");
  }
  for (const encoding of encodings) {
    for (const _id of [id + encoding, joinURL(id, "index.html" + encoding)]) {
      const _asset = getAsset(_id);
      if (_asset) {
        asset = _asset;
        id = _id;
        break;
      }
    }
  }
  if (!asset) {
    if (isPublicAssetURL(id)) {
      removeResponseHeader(event, "Cache-Control");
      throw createError({ statusCode: 404 });
    }
    return;
  }
  const ifNotMatch = getRequestHeader(event, "if-none-match") === asset.etag;
  if (ifNotMatch) {
    setResponseStatus(event, 304, "Not Modified");
    return "";
  }
  const ifModifiedSinceH = getRequestHeader(event, "if-modified-since");
  const mtimeDate = new Date(asset.mtime);
  if (ifModifiedSinceH && asset.mtime && new Date(ifModifiedSinceH) >= mtimeDate) {
    setResponseStatus(event, 304, "Not Modified");
    return "";
  }
  if (asset.type && !getResponseHeader(event, "Content-Type")) {
    setResponseHeader(event, "Content-Type", asset.type);
  }
  if (asset.etag && !getResponseHeader(event, "ETag")) {
    setResponseHeader(event, "ETag", asset.etag);
  }
  if (asset.mtime && !getResponseHeader(event, "Last-Modified")) {
    setResponseHeader(event, "Last-Modified", mtimeDate.toUTCString());
  }
  if (asset.encoding && !getResponseHeader(event, "Content-Encoding")) {
    setResponseHeader(event, "Content-Encoding", asset.encoding);
  }
  if (asset.size > 0 && !getResponseHeader(event, "Content-Length")) {
    setResponseHeader(event, "Content-Length", asset.size);
  }
  return readAsset(id);
});

const _lazy_XtRF48 = () => Promise.resolve().then(function () { return health_get$1; });
const _lazy_COcVOc = () => Promise.resolve().then(function () { return tree_get$3; });
const _lazy_eRbdlk = () => Promise.resolve().then(function () { return tree_get$1; });
const _lazy_ezro8o = () => Promise.resolve().then(function () { return _id__get$7; });
const _lazy_7ANHAb = () => Promise.resolve().then(function () { return index_get$3; });
const _lazy_rZ5VvA = () => Promise.resolve().then(function () { return index_post$1; });
const _lazy_p4UHH2 = () => Promise.resolve().then(function () { return assignPermissions_post$1; });
const _lazy_a7wNIx = () => Promise.resolve().then(function () { return permissions_get$1; });
const _lazy_jPNkqo = () => Promise.resolve().then(function () { return available_get$1; });
const _lazy_J346SF = () => Promise.resolve().then(function () { return create_post$3; });
const _lazy_N6o4Dm = () => Promise.resolve().then(function () { return _id__delete$3; });
const _lazy_3eDqen = () => Promise.resolve().then(function () { return _id__get$5; });
const _lazy_VfzVwc = () => Promise.resolve().then(function () { return query_get$3; });
const _lazy_Z2d1zj = () => Promise.resolve().then(function () { return statistics_get$3; });
const _lazy_ufN_Qn = () => Promise.resolve().then(function () { return _id__put$3; });
const _lazy_UHfcZn = () => Promise.resolve().then(function () { return validateCode_post$1; });
const _lazy_0zU_Od = () => Promise.resolve().then(function () { return validateName_post$1; });
const _lazy_0oqwYt = () => Promise.resolve().then(function () { return delay_get$1; });
const _lazy_1Jf1F3 = () => Promise.resolve().then(function () { return error_get$1; });
const _lazy_QGeosA = () => Promise.resolve().then(function () { return _id__get$3; });
const _lazy_FqPm22 = () => Promise.resolve().then(function () { return assignRole_post$1; });
const _lazy_xSldAX = () => Promise.resolve().then(function () { return _roleId__delete$1; });
const _lazy_wPt2hK = () => Promise.resolve().then(function () { return resetPassword_post$1; });
const _lazy_zehGoT = () => Promise.resolve().then(function () { return roles_get$1; });
const _lazy_ZQcRKb = () => Promise.resolve().then(function () { return batchOperation_post$1; });
const _lazy_aBSHvp = () => Promise.resolve().then(function () { return create_post$1; });
const _lazy_d8Gia4 = () => Promise.resolve().then(function () { return _id__delete$1; });
const _lazy_YzkopB = () => Promise.resolve().then(function () { return _id__get$1; });
const _lazy_wWWNwZ = () => Promise.resolve().then(function () { return index_get$1; });
const _lazy_v3tomd = () => Promise.resolve().then(function () { return query_get$1; });
const _lazy_fwGTBX = () => Promise.resolve().then(function () { return statistics_get$1; });
const _lazy_3N5r0X = () => Promise.resolve().then(function () { return _id__put$1; });
const _lazy_zVIJSV = () => Promise.resolve().then(function () { return validateEmail_post$1; });
const _lazy_KYGCEx = () => Promise.resolve().then(function () { return validateEmployeeId_post$1; });
const _lazy_r9X86r = () => Promise.resolve().then(function () { return validateUsername_post$1; });
const _lazy_ZmCf5k = () => Promise.resolve().then(function () { return login_post$1; });
const _lazy_DMz5h2 = () => Promise.resolve().then(function () { return logout_post$1; });
const _lazy_WwV5Qj = () => Promise.resolve().then(function () { return me_get$1; });
const _lazy_z8NUgF = () => Promise.resolve().then(function () { return refresh_post$1; });

const handlers = [
  { route: '', handler: _O5lzJQ, lazy: false, middleware: true, method: undefined },
  { route: '/api/api/health', handler: _lazy_XtRF48, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/organizations/tree', handler: _lazy_COcVOc, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/permissions/tree', handler: _lazy_eRbdlk, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/products/:id', handler: _lazy_ezro8o, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/products', handler: _lazy_7ANHAb, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/products', handler: _lazy_rZ5VvA, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/roles/:id/assign-permissions', handler: _lazy_p4UHH2, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/roles/:id/permissions', handler: _lazy_a7wNIx, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/roles/available', handler: _lazy_jPNkqo, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/roles/create', handler: _lazy_J346SF, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/roles/delete/:id', handler: _lazy_N6o4Dm, lazy: true, middleware: false, method: "delete" },
  { route: '/api/api/roles/get/:id', handler: _lazy_3eDqen, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/roles/query', handler: _lazy_VfzVwc, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/roles/statistics', handler: _lazy_Z2d1zj, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/roles/update/:id', handler: _lazy_ufN_Qn, lazy: true, middleware: false, method: "put" },
  { route: '/api/api/roles/validate-code', handler: _lazy_UHfcZn, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/roles/validate-name', handler: _lazy_0zU_Od, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/test/delay', handler: _lazy_0oqwYt, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/test/error', handler: _lazy_1Jf1F3, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users/:id', handler: _lazy_QGeosA, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users/:id/assign-role', handler: _lazy_FqPm22, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/users/:id/remove-role/:roleId', handler: _lazy_xSldAX, lazy: true, middleware: false, method: "delete" },
  { route: '/api/api/users/:id/reset-password', handler: _lazy_wPt2hK, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/users/:id/roles', handler: _lazy_zehGoT, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users/batch-operation', handler: _lazy_ZQcRKb, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/users/create', handler: _lazy_aBSHvp, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/users/delete/:id', handler: _lazy_d8Gia4, lazy: true, middleware: false, method: "delete" },
  { route: '/api/api/users/get/:id', handler: _lazy_YzkopB, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users', handler: _lazy_wWWNwZ, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users/query', handler: _lazy_v3tomd, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users/statistics', handler: _lazy_fwGTBX, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/users/update/:id', handler: _lazy_3N5r0X, lazy: true, middleware: false, method: "put" },
  { route: '/api/api/users/validate-email', handler: _lazy_zVIJSV, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/users/validate-employee-id', handler: _lazy_KYGCEx, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/users/validate-username', handler: _lazy_r9X86r, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/v1/sys/user/login', handler: _lazy_ZmCf5k, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/v1/sys/user/logout', handler: _lazy_DMz5h2, lazy: true, middleware: false, method: "post" },
  { route: '/api/api/v1/sys/user/me', handler: _lazy_WwV5Qj, lazy: true, middleware: false, method: "get" },
  { route: '/api/api/v1/sys/user/refresh', handler: _lazy_z8NUgF, lazy: true, middleware: false, method: "post" },
  { route: '/api/health', handler: _lazy_XtRF48, lazy: true, middleware: false, method: "get" },
  { route: '/api/organizations/tree', handler: _lazy_COcVOc, lazy: true, middleware: false, method: "get" },
  { route: '/api/permissions/tree', handler: _lazy_eRbdlk, lazy: true, middleware: false, method: "get" },
  { route: '/api/products/:id', handler: _lazy_ezro8o, lazy: true, middleware: false, method: "get" },
  { route: '/api/products', handler: _lazy_7ANHAb, lazy: true, middleware: false, method: "get" },
  { route: '/api/products', handler: _lazy_rZ5VvA, lazy: true, middleware: false, method: "post" },
  { route: '/api/roles/:id/assign-permissions', handler: _lazy_p4UHH2, lazy: true, middleware: false, method: "post" },
  { route: '/api/roles/:id/permissions', handler: _lazy_a7wNIx, lazy: true, middleware: false, method: "get" },
  { route: '/api/roles/available', handler: _lazy_jPNkqo, lazy: true, middleware: false, method: "get" },
  { route: '/api/roles/create', handler: _lazy_J346SF, lazy: true, middleware: false, method: "post" },
  { route: '/api/roles/delete/:id', handler: _lazy_N6o4Dm, lazy: true, middleware: false, method: "delete" },
  { route: '/api/roles/get/:id', handler: _lazy_3eDqen, lazy: true, middleware: false, method: "get" },
  { route: '/api/roles/query', handler: _lazy_VfzVwc, lazy: true, middleware: false, method: "get" },
  { route: '/api/roles/statistics', handler: _lazy_Z2d1zj, lazy: true, middleware: false, method: "get" },
  { route: '/api/roles/update/:id', handler: _lazy_ufN_Qn, lazy: true, middleware: false, method: "put" },
  { route: '/api/roles/validate-code', handler: _lazy_UHfcZn, lazy: true, middleware: false, method: "post" },
  { route: '/api/roles/validate-name', handler: _lazy_0zU_Od, lazy: true, middleware: false, method: "post" },
  { route: '/api/test/delay', handler: _lazy_0oqwYt, lazy: true, middleware: false, method: "get" },
  { route: '/api/test/error', handler: _lazy_1Jf1F3, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/:id', handler: _lazy_QGeosA, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/:id/assign-role', handler: _lazy_FqPm22, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/:id/remove-role/:roleId', handler: _lazy_xSldAX, lazy: true, middleware: false, method: "delete" },
  { route: '/api/users/:id/reset-password', handler: _lazy_wPt2hK, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/:id/roles', handler: _lazy_zehGoT, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/batch-operation', handler: _lazy_ZQcRKb, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/create', handler: _lazy_aBSHvp, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/delete/:id', handler: _lazy_d8Gia4, lazy: true, middleware: false, method: "delete" },
  { route: '/api/users/get/:id', handler: _lazy_YzkopB, lazy: true, middleware: false, method: "get" },
  { route: '/api/users', handler: _lazy_wWWNwZ, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/query', handler: _lazy_v3tomd, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/statistics', handler: _lazy_fwGTBX, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/update/:id', handler: _lazy_3N5r0X, lazy: true, middleware: false, method: "put" },
  { route: '/api/users/validate-email', handler: _lazy_zVIJSV, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/validate-employee-id', handler: _lazy_KYGCEx, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/validate-username', handler: _lazy_r9X86r, lazy: true, middleware: false, method: "post" },
  { route: '/api/v1/sys/user/login', handler: _lazy_ZmCf5k, lazy: true, middleware: false, method: "post" },
  { route: '/api/v1/sys/user/logout', handler: _lazy_DMz5h2, lazy: true, middleware: false, method: "post" },
  { route: '/api/v1/sys/user/me', handler: _lazy_WwV5Qj, lazy: true, middleware: false, method: "get" },
  { route: '/api/v1/sys/user/refresh', handler: _lazy_z8NUgF, lazy: true, middleware: false, method: "post" }
];

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(true),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const fetchContext = event.node.req?.__unenv__;
      if (fetchContext?._platform) {
        event.context = {
          _platform: fetchContext?._platform,
          // #3335
          ...fetchContext._platform,
          ...event.context
        };
      }
      if (!event.context.waitUntil && fetchContext?.waitUntil) {
        event.context.waitUntil = fetchContext.waitUntil;
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (event.context.waitUntil) {
          event.context.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
      await nitroApp$1.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter$1({
    preemptive: true
  });
  const nodeHandler = toNodeListener(h3App);
  const localCall = (aRequest) => callNodeRequestHandler(nodeHandler, aRequest);
  const localFetch = (input, init) => {
    if (!input.toString().startsWith("/")) {
      return globalThis.fetch(input, init);
    }
    return fetchNodeRequestHandler(
      nodeHandler,
      input,
      init
    ).then((response) => normalizeFetchResponse(response));
  };
  const $fetch = createFetch({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp$1 = createNitroApp();
function useNitroApp() {
  return nitroApp$1;
}
runNitroPlugins(nitroApp$1);

const scheduledTasks = false;

const tasks = {
  
};

const __runningTasks__ = {};
async function runTask(name, {
  payload = {},
  context = {}
} = {}) {
  if (__runningTasks__[name]) {
    return __runningTasks__[name];
  }
  if (!(name in tasks)) {
    throw createError({
      message: `Task \`${name}\` is not available!`,
      statusCode: 404
    });
  }
  if (!tasks[name].resolve) {
    throw createError({
      message: `Task \`${name}\` is not implemented!`,
      statusCode: 501
    });
  }
  const handler = await tasks[name].resolve();
  const taskEvent = { name, payload, context };
  __runningTasks__[name] = handler.run(taskEvent);
  try {
    const res = await __runningTasks__[name];
    return res;
  } finally {
    delete __runningTasks__[name];
  }
}

if (!globalThis.crypto) {
  globalThis.crypto = nodeCrypto;
}
const { NITRO_NO_UNIX_SOCKET, NITRO_DEV_WORKER_ID } = process.env;
trapUnhandledNodeErrors();
parentPort?.on("message", (msg) => {
  if (msg && msg.event === "shutdown") {
    shutdown();
  }
});
const nitroApp = useNitroApp();
const server = new Server(toNodeListener(nitroApp.h3App));
let listener;
listen().catch(() => listen(
  true
  /* use random port */
)).catch((error) => {
  console.error("Dev worker failed to listen:", error);
  return shutdown();
});
nitroApp.router.get(
  "/_nitro/tasks",
  defineEventHandler(async (event) => {
    const _tasks = await Promise.all(
      Object.entries(tasks).map(async ([name, task]) => {
        const _task = await task.resolve?.();
        return [name, { description: _task?.meta?.description }];
      })
    );
    return {
      tasks: Object.fromEntries(_tasks),
      scheduledTasks
    };
  })
);
nitroApp.router.use(
  "/_nitro/tasks/:name",
  defineEventHandler(async (event) => {
    const name = getRouterParam(event, "name");
    const payload = {
      ...getQuery$1(event),
      ...await readBody(event).then((r) => r?.payload).catch(() => ({}))
    };
    return await runTask(name, { payload });
  })
);
function listen(useRandomPort = Boolean(
  NITRO_NO_UNIX_SOCKET || process.versions.webcontainer || "Bun" in globalThis && process.platform === "win32"
)) {
  return new Promise((resolve, reject) => {
    try {
      listener = server.listen(useRandomPort ? 0 : getSocketAddress(), () => {
        const address = server.address();
        parentPort?.postMessage({
          event: "listen",
          address: typeof address === "string" ? { socketPath: address } : { host: "localhost", port: address?.port }
        });
        resolve();
      });
    } catch (error) {
      reject(error);
    }
  });
}
function getSocketAddress() {
  const socketName = `nitro-worker-${process.pid}-${threadId}-${NITRO_DEV_WORKER_ID}-${Math.round(Math.random() * 1e4)}.sock`;
  if (process.platform === "win32") {
    return join(String.raw`\\.\pipe`, socketName);
  }
  if (process.platform === "linux") {
    const nodeMajor = Number.parseInt(process.versions.node.split(".")[0], 10);
    if (nodeMajor >= 20) {
      return `\0${socketName}`;
    }
  }
  return join(tmpdir(), socketName);
}
async function shutdown() {
  server.closeAllConnections?.();
  await Promise.all([
    new Promise((resolve) => listener?.close(resolve)),
    nitroApp.hooks.callHook("close").catch(console.error)
  ]);
  parentPort?.postMessage({ event: "exit" });
}

function readJsonData(filename) {
  try {
    const filePath = join(process.cwd(), "data", filename);
    const data = readFileSync(filePath, "utf-8");
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
}
function writeJsonData(filename, data) {
  try {
    const filePath = join(process.cwd(), "data", filename);
    const jsonData = JSON.stringify(data, null, 2);
    writeFileSync(filePath, jsonData, "utf-8");
  } catch (error) {
    console.error(`Error writing ${filename}:`, error);
    throw error;
  }
}
function simulateDelay(ms = 500) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function createSuccessResponse(data, message = "Success") {
  return {
    code: 200,
    msg: message,
    data
  };
}
function createErrorResponse(message, code = 500, details, extraData) {
  const errorResponse = {
    code,
    msg: message,
    data: extraData || null,
    details
  };
  return errorResponse;
}
function shouldSimulateError(event) {
  const query = getQuery$1(event);
  const headers = getHeaders(event);
  if (query.error === "true") {
    const errorCode = parseInt(query.errorCode) || 500;
    const errorMessage = query.errorMessage || "Simulated error";
    return { shouldError: true, errorCode, errorMessage };
  }
  if (headers["x-simulate-error"] === "true") {
    const errorCode = parseInt(headers["x-error-code"]) || 500;
    const errorMessage = headers["x-error-message"] || "Simulated error";
    return { shouldError: true, errorCode, errorMessage };
  }
  return { shouldError: false };
}
function generateId() {
  return Date.now() + Math.floor(Math.random() * 1e3);
}

const health_get = defineEventHandler(async (event) => {
  await simulateDelay(100);
  const uptime = process.uptime();
  const timestamp = (/* @__PURE__ */ new Date()).toISOString();
  return createSuccessResponse({
    status: "healthy",
    uptime: `${Math.floor(uptime)}s`,
    timestamp,
    version: "1.0.0",
    environment: "development"
  }, "Mock API is running");
});

const health_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: health_get
});

const tree_get$2 = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const organizations = readJsonData("organizations.json");
    const buildTree = (items, parentId = null, level = 0) => {
      return items.filter((item) => item.parent_id === parentId).map((item) => ({
        ...item,
        level,
        children: buildTree(items, item.org_id, level + 1)
      }));
    };
    const tree = buildTree(organizations);
    return createSuccessResponse(tree, "Organization tree retrieved successfully");
  } catch (error) {
    console.error("Error in organization tree API:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const tree_get$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: tree_get$2
});

const tree_get = defineEventHandler(async (event) => {
  const permissions = await readJsonData("permissions.json");
  return {
    code: 200,
    message: "success",
    data: permissions
  };
});

const tree_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: tree_get
});

const _id__get$6 = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const productId = getRouterParam(event, "id");
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: "Product ID is required"
      });
    }
    const products = readJsonData("products.json");
    const product = products.find((p) => p.id === parseInt(productId));
    if (!product) {
      throw createError({
        statusCode: 404,
        statusMessage: `Product with ID ${productId} not found`
      });
    }
    return createSuccessResponse(product, "Product retrieved successfully");
  } catch (error) {
    console.error("Error in product detail API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _id__get$7 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__get$6
});

const index_get$2 = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(400);
  try {
    const products = readJsonData("products.json");
    const query = getQuery$1(event);
    let filteredProducts = products;
    if (query.category) {
      filteredProducts = products.filter(
        (product) => product.category.toLowerCase().includes(query.category.toLowerCase())
      );
    }
    if (query.status) {
      filteredProducts = filteredProducts.filter((product) => product.status === query.status);
    }
    if (query.supplier) {
      filteredProducts = filteredProducts.filter(
        (product) => product.supplier.toLowerCase().includes(query.supplier.toLowerCase())
      );
    }
    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      filteredProducts = filteredProducts.filter(
        (product) => product.name.toLowerCase().includes(searchTerm) || product.code.toLowerCase().includes(searchTerm)
      );
    }
    if (query.minPrice) {
      filteredProducts = filteredProducts.filter(
        (product) => product.price >= parseFloat(query.minPrice)
      );
    }
    if (query.maxPrice) {
      filteredProducts = filteredProducts.filter(
        (product) => product.price <= parseFloat(query.maxPrice)
      );
    }
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    return createSuccessResponse({
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        totalPages: Math.ceil(filteredProducts.length / limit)
      }
    }, "Products retrieved successfully");
  } catch (error) {
    console.error("Error in products API:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const index_get$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_get$2
});

const index_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(600);
  try {
    const body = await readBody(event);
    const requiredFields = ["name", "code", "category", "price", "stock", "unit", "supplier"];
    const missingFields = requiredFields.filter((field) => !body[field]);
    if (missingFields.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `Missing required fields: ${missingFields.join(", ")}`
      });
    }
    if (typeof body.price !== "number" || body.price <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "Price must be a positive number"
      });
    }
    if (typeof body.stock !== "number" || body.stock < 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "Stock must be a non-negative number"
      });
    }
    const newProduct = {
      id: generateId(),
      name: body.name,
      code: body.code,
      category: body.category,
      specification: body.specification || "",
      price: body.price,
      stock: body.stock,
      unit: body.unit,
      supplier: body.supplier,
      status: body.stock > 0 ? "available" : "out_of_stock",
      description: body.description || "",
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    return createSuccessResponse({
      id: newProduct.id,
      product: newProduct
    }, "Product created successfully");
  } catch (error) {
    console.error("Error in product creation API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const index_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_post
});

const assignPermissions_post = defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, "id");
  const body = await readBody(event);
  const roles = await readJsonData("roles.json");
  const roleIndex = roles.findIndex((r) => r.role_id === roleId);
  if (roleIndex === -1) {
    return {
      code: 404,
      message: "\u89D2\u8272\u4E0D\u5B58\u5728",
      data: null
    };
  }
  if (!body.permission_ids || !Array.isArray(body.permission_ids)) {
    return {
      code: 400,
      message: "\u6743\u9650ID\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A",
      data: null
    };
  }
  roles[roleIndex].permission_count = body.permission_ids.length;
  roles[roleIndex].updated_date = (/* @__PURE__ */ new Date()).toISOString();
  await writeJsonData("roles.json", roles);
  return {
    code: 200,
    message: "\u6743\u9650\u5206\u914D\u6210\u529F",
    data: null
  };
});

const assignPermissions_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: assignPermissions_post
});

const permissions_get = defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, "id");
  const roles = await readJsonData("roles.json");
  const permissions = await readJsonData("permissions.json");
  const role = roles.find((r) => r.role_id === roleId);
  if (!role) {
    return {
      code: 404,
      message: "\u89D2\u8272\u4E0D\u5B58\u5728",
      data: null
    };
  }
  const rolePermissions = permissions.slice(0, Math.floor(Math.random() * 8) + 3).map((p) => ({
    role_id: roleId,
    permission_id: p.permission_id,
    permission_name: p.permission_name,
    permission_code: p.permission_code,
    resource_type: p.resource_type,
    assigned_date: (/* @__PURE__ */ new Date()).toISOString()
  }));
  return {
    code: 200,
    message: "success",
    data: rolePermissions
  };
});

const permissions_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: permissions_get
});

const available_get = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const roles = readJsonData("roles.json");
    const availableRoles = roles.filter((role) => role.status === "ACTIVE");
    return createSuccessResponse(availableRoles, "Available roles retrieved successfully");
  } catch (error) {
    console.error("Error in available roles API:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const available_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: available_get
});

const create_post$2 = defineEventHandler(async (event) => {
  const body = await readBody(event);
  const roles = await readJsonData("roles.json");
  if (!body.role_name || !body.role_code) {
    return {
      code: 400,
      message: "\u89D2\u8272\u540D\u79F0\u548C\u89D2\u8272\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A",
      data: null
    };
  }
  const existingByName = roles.find((r) => r.role_name === body.role_name);
  if (existingByName) {
    return {
      code: 400,
      message: "\u89D2\u8272\u540D\u79F0\u5DF2\u5B58\u5728",
      data: null
    };
  }
  const existingByCode = roles.find((r) => r.role_code === body.role_code);
  if (existingByCode) {
    return {
      code: 400,
      message: "\u89D2\u8272\u7F16\u7801\u5DF2\u5B58\u5728",
      data: null
    };
  }
  const newRole = {
    role_id: `r${String(Date.now()).slice(-6)}`,
    role_name: body.role_name,
    role_code: body.role_code,
    description: body.description || "",
    status: body.status || "ACTIVE",
    user_count: 0,
    permission_count: 0,
    created_date: (/* @__PURE__ */ new Date()).toISOString(),
    updated_date: (/* @__PURE__ */ new Date()).toISOString()
  };
  roles.push(newRole);
  await writeJsonData("roles.json", roles);
  return {
    code: 200,
    message: "\u89D2\u8272\u521B\u5EFA\u6210\u529F",
    data: newRole
  };
});

const create_post$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: create_post$2
});

const _id__delete$2 = defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, "id");
  const roles = await readJsonData("roles.json");
  const roleIndex = roles.findIndex((r) => r.role_id === roleId);
  if (roleIndex === -1) {
    return {
      code: 404,
      message: "\u89D2\u8272\u4E0D\u5B58\u5728",
      data: null
    };
  }
  const role = roles[roleIndex];
  if (role.user_count > 0) {
    return {
      code: 400,
      message: `\u65E0\u6CD5\u5220\u9664\u89D2\u8272\uFF0C\u8FD8\u6709 ${role.user_count} \u4E2A\u7528\u6237\u6B63\u5728\u4F7F\u7528\u8BE5\u89D2\u8272`,
      data: null
    };
  }
  roles.splice(roleIndex, 1);
  await writeJsonData("roles.json", roles);
  return {
    code: 200,
    message: "\u89D2\u8272\u5220\u9664\u6210\u529F",
    data: null
  };
});

const _id__delete$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__delete$2
});

const _id__get$4 = defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, "id");
  const roles = await readJsonData("roles.json");
  const permissions = await readJsonData("permissions.json");
  const users = await readJsonData("users.json");
  const role = roles.find((r) => r.role_id === roleId);
  if (!role) {
    return {
      code: 404,
      message: "\u89D2\u8272\u4E0D\u5B58\u5728",
      data: null
    };
  }
  const rolePermissions = permissions.slice(0, Math.floor(Math.random() * 8) + 3).map((p) => ({
    permission_id: p.permission_id,
    permission_name: p.permission_name,
    permission_code: p.permission_code,
    resource_type: p.resource_type,
    description: p.description
  }));
  const roleUsers = users.slice(0, role.user_count || 0).map((u) => ({
    user_id: u.user_id,
    username: u.username,
    name: u.name,
    department_name: u.department_name,
    status: u.status
  }));
  return {
    code: 200,
    message: "success",
    data: {
      ...role,
      permissions: rolePermissions,
      users: roleUsers
    }
  };
});

const _id__get$5 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__get$4
});

const query_get$2 = defineEventHandler(async (event) => {
  const query = getQuery$1(event);
  const roles = await readJsonData("roles.json");
  const offset = parseInt(query.offset) || 0;
  const limit = parseInt(query.limit) || 20;
  const roleName = query.role_name;
  const roleCode = query.role_code;
  const status = query.status;
  const description = query.description;
  let filteredRoles = roles.filter((role) => {
    var _a;
    if (roleName && !role.role_name.toLowerCase().includes(roleName.toLowerCase())) {
      return false;
    }
    if (roleCode && !role.role_code.toLowerCase().includes(roleCode.toLowerCase())) {
      return false;
    }
    if (status && role.status !== status) {
      return false;
    }
    if (description && !((_a = role.description) == null ? void 0 : _a.toLowerCase().includes(description.toLowerCase()))) {
      return false;
    }
    return true;
  });
  const total = filteredRoles.length;
  const paginatedRoles = filteredRoles.slice(offset, offset + limit);
  const hasMore = offset + limit < total;
  return {
    code: 200,
    message: "success",
    data: {
      items: paginatedRoles,
      total,
      has_more: hasMore,
      offset,
      limit
    }
  };
});

const query_get$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: query_get$2
});

const statistics_get$2 = defineEventHandler(async (event) => {
  const roles = await readJsonData("roles.json");
  const permissions = await readJsonData("permissions.json");
  const totalRoles = roles.length;
  const activeRoles = roles.filter((r) => r.status === "ACTIVE").length;
  const inactiveRoles = roles.filter((r) => r.status === "INACTIVE").length;
  const rolesByType = [
    { resource_type: "menu", role_count: 4 },
    { resource_type: "action", role_count: 3 },
    { resource_type: "data", role_count: 2 },
    { resource_type: "api", role_count: 1 }
  ];
  const permissionUsage = permissions.slice(0, 10).map((p) => ({
    permission_id: p.permission_id,
    permission_name: p.permission_name,
    usage_count: Math.floor(Math.random() * 5) + 1
  }));
  return {
    code: 200,
    message: "success",
    data: {
      total_roles: totalRoles,
      active_roles: activeRoles,
      inactive_roles: inactiveRoles,
      roles_by_type: rolesByType,
      permission_usage: permissionUsage
    }
  };
});

const statistics_get$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: statistics_get$2
});

const _id__put$2 = defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, "id");
  const body = await readBody(event);
  const roles = await readJsonData("roles.json");
  const roleIndex = roles.findIndex((r) => r.role_id === roleId);
  if (roleIndex === -1) {
    return {
      code: 404,
      message: "\u89D2\u8272\u4E0D\u5B58\u5728",
      data: null
    };
  }
  const currentRole = roles[roleIndex];
  if (body.role_name && body.role_name !== currentRole.role_name) {
    const existingByName = roles.find((r) => r.role_name === body.role_name && r.role_id !== roleId);
    if (existingByName) {
      return {
        code: 400,
        message: "\u89D2\u8272\u540D\u79F0\u5DF2\u5B58\u5728",
        data: null
      };
    }
  }
  if (body.role_code && body.role_code !== currentRole.role_code) {
    const existingByCode = roles.find((r) => r.role_code === body.role_code && r.role_id !== roleId);
    if (existingByCode) {
      return {
        code: 400,
        message: "\u89D2\u8272\u7F16\u7801\u5DF2\u5B58\u5728",
        data: null
      };
    }
  }
  const updatedRole = {
    ...currentRole,
    ...body,
    role_id: roleId,
    // 确保ID不被修改
    updated_date: (/* @__PURE__ */ new Date()).toISOString()
  };
  roles[roleIndex] = updatedRole;
  await writeJsonData("roles.json", roles);
  return {
    code: 200,
    message: "\u89D2\u8272\u66F4\u65B0\u6210\u529F",
    data: updatedRole
  };
});

const _id__put$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__put$2
});

const validateCode_post = defineEventHandler(async (event) => {
  const body = await readBody(event);
  const roles = await readJsonData("roles.json");
  if (!body.role_code) {
    return {
      code: 400,
      message: "\u89D2\u8272\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A",
      data: { available: false }
    };
  }
  const existing = roles.find(
    (r) => r.role_code === body.role_code && r.role_id !== body.exclude_role_id
  );
  return {
    code: 200,
    message: "success",
    data: { available: !existing }
  };
});

const validateCode_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: validateCode_post
});

const validateName_post = defineEventHandler(async (event) => {
  const body = await readBody(event);
  const roles = await readJsonData("roles.json");
  if (!body.role_name) {
    return {
      code: 400,
      message: "\u89D2\u8272\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",
      data: { available: false }
    };
  }
  const existing = roles.find(
    (r) => r.role_name === body.role_name && r.role_id !== body.exclude_role_id
  );
  return {
    code: 200,
    message: "success",
    data: { available: !existing }
  };
});

const validateName_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: validateName_post
});

const delay_get = defineEventHandler(async (event) => {
  const query = getQuery$1(event);
  const delay = parseInt(query.ms) || 1e3;
  const maxDelay = 1e4;
  const actualDelay = Math.min(delay, maxDelay);
  const startTime = Date.now();
  await simulateDelay(actualDelay);
  const endTime = Date.now();
  const actualDuration = endTime - startTime;
  return createSuccessResponse({
    requestedDelay: delay,
    actualDelay: actualDuration,
    maxAllowedDelay: maxDelay,
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  }, `Delayed response after ${actualDuration}ms`);
});

const delay_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: delay_get
});

const error_get = defineEventHandler(async (event) => {
  const query = getQuery$1(event);
  const errorCode = parseInt(query.code) || 500;
  const errorMessage = query.message || "Test error";
  const delay = parseInt(query.delay) || 500;
  await simulateDelay(delay);
  throw createError({
    statusCode: errorCode,
    statusMessage: errorMessage
  });
});

const error_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: error_get
});

const _id__get$2 = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(250);
  try {
    const userId = getRouterParam(event, "id");
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    const users = readJsonData("users.json");
    const user = users.find((u) => u.id === parseInt(userId));
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: `User with ID ${userId} not found`
      });
    }
    return createSuccessResponse(user, "User retrieved successfully");
  } catch (error) {
    console.error("Error in user detail API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _id__get$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__get$2
});

const assignRole_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(400);
  try {
    const userId = getRouterParam(event, "id");
    const body = await readBody(event);
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    if (!body.role_id) {
      throw createError({
        statusCode: 400,
        statusMessage: "Role ID is required"
      });
    }
    const users = readJsonData("users.json");
    const roles = readJsonData("roles.json");
    const userIndex = users.findIndex((u) => u.user_id === userId);
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    const role = roles.find((r) => r.role_id === body.role_id);
    if (!role) {
      throw createError({
        statusCode: 404,
        statusMessage: "Role not found"
      });
    }
    if (role.status !== "ACTIVE") {
      throw createError({
        statusCode: 400,
        statusMessage: "Cannot assign inactive role"
      });
    }
    const user = users[userIndex];
    if (!user.role_ids) {
      user.role_ids = [];
    }
    if (user.role_ids.includes(body.role_id)) {
      throw createError({
        statusCode: 409,
        statusMessage: "User already has this role"
      });
    }
    user.role_ids.push(body.role_id);
    user.updated_date = (/* @__PURE__ */ new Date()).toISOString();
    writeJsonData("users.json", users);
    return createSuccessResponse(null, "Role assigned successfully");
  } catch (error) {
    console.error("Error in assign role API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const assignRole_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: assignRole_post
});

const _roleId__delete = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const userId = getRouterParam(event, "id");
    const roleId = getRouterParam(event, "roleId");
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    if (!roleId) {
      throw createError({
        statusCode: 400,
        statusMessage: "Role ID is required"
      });
    }
    const users = readJsonData("users.json");
    const userIndex = users.findIndex((u) => u.user_id === userId);
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    const user = users[userIndex];
    if (!user.role_ids || !user.role_ids.includes(roleId)) {
      throw createError({
        statusCode: 404,
        statusMessage: "User does not have this role"
      });
    }
    user.role_ids = user.role_ids.filter((id) => id !== roleId);
    user.updated_date = (/* @__PURE__ */ new Date()).toISOString();
    writeJsonData("users.json", users);
    return createSuccessResponse(null, "Role removed successfully");
  } catch (error) {
    console.error("Error in remove role API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _roleId__delete$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _roleId__delete
});

const resetPassword_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(400);
  try {
    const userId = getRouterParam(event, "id");
    const body = await readBody(event);
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    if (!body.new_password) {
      throw createError({
        statusCode: 400,
        statusMessage: "New password is required"
      });
    }
    if (body.new_password.length < 8) {
      throw createError({
        statusCode: 400,
        statusMessage: "Password must be at least 8 characters long"
      });
    }
    const users = readJsonData("users.json");
    const userIndex = users.findIndex((u) => u.user_id === userId);
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    users[userIndex].updated_date = (/* @__PURE__ */ new Date()).toISOString();
    writeJsonData("users.json", users);
    return createSuccessResponse(null, "Password reset successfully");
  } catch (error) {
    console.error("Error in password reset API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const resetPassword_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: resetPassword_post
});

const roles_get = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const userId = getRouterParam(event, "id");
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    const users = readJsonData("users.json");
    const roles = readJsonData("roles.json");
    const user = users.find((u) => u.user_id === userId);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    const userRoles = roles.filter(
      (role) => user.role_ids && user.role_ids.includes(role.role_id)
    ).map((role) => ({
      role_id: role.role_id,
      role_name: role.role_name,
      role_code: role.role_code,
      assigned_date: user.created_date
      // 简化处理
    }));
    return createSuccessResponse(userRoles, "User roles retrieved successfully");
  } catch (error) {
    console.error("Error in user roles API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const roles_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: roles_get
});

const JWT_CONFIG = {
  SECRET: "mock-jwt-secret-key-for-development-only",
  ACCESS_TOKEN_EXPIRES_IN: 60 * 60,
  ALGORITHM: "HS256"
};
const LOGIN_SECURITY = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1e3,
  // 15 minutes in milliseconds
  PASSWORD_MIN_LENGTH: 6
};
const MOCK_USERS = [
  {
    id: 1,
    username: "admin",
    password: "admin123",
    // 实际应用中应该是 bcrypt 加密
    email: "<EMAIL>",
    name: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
    avatar: "https://avatars.githubusercontent.com/u/1?v=4",
    role: "admin",
    permissions: [
      "user:read",
      "user:write",
      "user:delete",
      "system:read",
      "system:write",
      "system:config",
      "report:read",
      "report:write",
      "dashboard:read"
    ],
    status: "active",
    loginAttempts: 0,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 2,
    username: "manager",
    password: "manager123",
    email: "<EMAIL>",
    name: "\u90E8\u95E8\u7ECF\u7406",
    avatar: "https://avatars.githubusercontent.com/u/2?v=4",
    role: "manager",
    permissions: [
      "user:read",
      "user:write",
      "report:read",
      "report:write",
      "dashboard:read"
    ],
    status: "active",
    loginAttempts: 0,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 3,
    username: "user",
    password: "user123",
    email: "<EMAIL>",
    name: "\u666E\u901A\u7528\u6237",
    avatar: "https://avatars.githubusercontent.com/u/3?v=4",
    role: "user",
    permissions: [
      "dashboard:read",
      "report:read"
    ],
    status: "active",
    loginAttempts: 0,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 4,
    username: "locked_user",
    password: "locked123",
    email: "<EMAIL>",
    name: "\u88AB\u9501\u5B9A\u7528\u6237",
    role: "user",
    permissions: ["dashboard:read"],
    status: "locked",
    loginAttempts: 5,
    lockUntil: new Date(Date.now() + LOGIN_SECURITY.LOCKOUT_DURATION).toISOString(),
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: (/* @__PURE__ */ new Date()).toISOString()
  }
];
const ACTIVE_REFRESH_TOKENS = /* @__PURE__ */ new Set();
const REVOKED_ACCESS_TOKENS = /* @__PURE__ */ new Set();
function findUserByUsername(username) {
  return MOCK_USERS.find((user) => user.username === username);
}
function findUserById(id) {
  return MOCK_USERS.find((user) => user.id === id);
}
function validatePassword(plainPassword, hashedPassword) {
  return plainPassword === hashedPassword;
}
function isUserLocked(user) {
  if (user.status === "locked" && user.lockUntil) {
    const lockUntil = new Date(user.lockUntil);
    return Date.now() < lockUntil.getTime();
  }
  return user.status === "locked";
}
function incrementLoginAttempts(user) {
  user.loginAttempts += 1;
  user.updatedAt = (/* @__PURE__ */ new Date()).toISOString();
  if (user.loginAttempts >= LOGIN_SECURITY.MAX_LOGIN_ATTEMPTS) {
    user.status = "locked";
    user.lockUntil = new Date(Date.now() + LOGIN_SECURITY.LOCKOUT_DURATION).toISOString();
  }
}
function resetLoginAttempts(user) {
  user.loginAttempts = 0;
  user.status = "active";
  user.lockUntil = void 0;
  user.lastLoginTime = (/* @__PURE__ */ new Date()).toISOString();
  user.updatedAt = (/* @__PURE__ */ new Date()).toISOString();
}

function base64UrlEncode(str) {
  return Buffer.from(str).toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}
function base64UrlDecode(str) {
  str += "=".repeat((4 - str.length % 4) % 4);
  return Buffer.from(str.replace(/-/g, "+").replace(/_/g, "/"), "base64").toString();
}
function createJWTHeader() {
  const header = {
    alg: JWT_CONFIG.ALGORITHM,
    typ: "JWT"
  };
  return base64UrlEncode(JSON.stringify(header));
}
function createJWTPayload(user, expiresIn) {
  const now = Math.floor(Date.now() / 1e3);
  return {
    sub: user.id.toString(),
    username: user.username,
    role: user.role,
    permissions: user.permissions,
    iat: now,
    exp: now + expiresIn,
    jti: generateJTI()
  };
}
function generateJTI() {
  return randomBytes(16).toString("hex");
}
function createSignature(data, secret) {
  return createHmac("sha256", secret).update(data).digest("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}
function generateAccessToken(user) {
  const header = createJWTHeader();
  const payload = base64UrlEncode(JSON.stringify(createJWTPayload(user, JWT_CONFIG.ACCESS_TOKEN_EXPIRES_IN)));
  const data = `${header}.${payload}`;
  const signature = createSignature(data, JWT_CONFIG.SECRET);
  return `${data}.${signature}`;
}
function generateRefreshToken() {
  const token = randomBytes(32).toString("hex");
  ACTIVE_REFRESH_TOKENS.add(token);
  return token;
}
function verifySignature(token) {
  const parts = token.split(".");
  if (parts.length !== 3) return false;
  const [header, payload, signature] = parts;
  const data = `${header}.${payload}`;
  const expectedSignature = createSignature(data, JWT_CONFIG.SECRET);
  return signature === expectedSignature;
}
function parseJWT(token) {
  try {
    if (!verifySignature(token)) {
      return null;
    }
    const parts = token.split(".");
    const payload = JSON.parse(base64UrlDecode(parts[1]));
    if (REVOKED_ACCESS_TOKENS.has(token)) {
      return null;
    }
    const now = Math.floor(Date.now() / 1e3);
    if (payload.exp && payload.exp < now) {
      return null;
    }
    return payload;
  } catch (error) {
    return null;
  }
}
function verifyAccessToken(token) {
  return parseJWT(token);
}
function verifyRefreshToken(token) {
  return ACTIVE_REFRESH_TOKENS.has(token);
}
function revokeAccessToken(token) {
  REVOKED_ACCESS_TOKENS.add(token);
}
function revokeRefreshToken(token) {
  ACTIVE_REFRESH_TOKENS.delete(token);
}
function extractTokenFromHeader(authHeader) {
  if (!authHeader) return null;
  const match = authHeader.match(/^Bearer\s+(.+)$/);
  return match ? match[1] : null;
}
function generateLoginTokens(user) {
  const accessToken = generateAccessToken(user);
  const refreshToken = generateRefreshToken();
  return {
    accessToken,
    refreshToken,
    expiresIn: JWT_CONFIG.ACCESS_TOKEN_EXPIRES_IN,
    tokenType: "Bearer"
  };
}

const batchOperation_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(600);
  try {
    const authHeader = getHeader(event, "authorization");
    const token = extractTokenFromHeader(authHeader);
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: "Missing authorization token"
      });
    }
    const payload = verifyAccessToken(token);
    if (!payload) {
      throw createError({
        statusCode: 401,
        statusMessage: "Invalid or expired token"
      });
    }
    const body = await readBody(event);
    if (!body.user_ids || !Array.isArray(body.user_ids) || body.user_ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "User IDs array is required"
      });
    }
    if (!body.action || !["activate", "deactivate", "delete"].includes(body.action)) {
      throw createError({
        statusCode: 400,
        statusMessage: "Valid action is required (activate, deactivate, delete)"
      });
    }
    const users = readJsonData("users.json");
    const userIds = body.user_ids;
    const action = body.action;
    const usersToUpdate = users.filter((user) => userIds.includes(user.user_id));
    if (usersToUpdate.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "No users found for the provided IDs"
      });
    }
    if (action === "delete") {
      const adminUsers = usersToUpdate.filter(
        (user) => user.username === "admin" || user.username === "superadmin"
      );
      if (adminUsers.length > 0) {
        throw createError({
          statusCode: 403,
          statusMessage: "Cannot delete system administrators"
        });
      }
    }
    let updatedUsers = [...users];
    if (action === "delete") {
      updatedUsers = users.filter((user) => !userIds.includes(user.user_id));
    } else {
      const newStatus = action === "activate" ? "ACTIVE" : "INACTIVE";
      updatedUsers = users.map((user) => {
        if (userIds.includes(user.user_id)) {
          return {
            ...user,
            status: newStatus,
            updated_date: (/* @__PURE__ */ new Date()).toISOString()
          };
        }
        return user;
      });
    }
    writeJsonData("users.json", updatedUsers);
    const successMessage = action === "delete" ? `${usersToUpdate.length} users deleted successfully` : `${usersToUpdate.length} users ${action}d successfully`;
    return createSuccessResponse({
      affected_count: usersToUpdate.length,
      action
    }, successMessage);
  } catch (error) {
    console.error("Error in batch operation API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const batchOperation_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: batchOperation_post
});

const create_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(500);
  try {
    const authHeader = getHeader(event, "authorization");
    const token = extractTokenFromHeader(authHeader);
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: "Missing authorization token"
      });
    }
    const payload = verifyAccessToken(token);
    if (!payload) {
      throw createError({
        statusCode: 401,
        statusMessage: "Invalid or expired token"
      });
    }
    const body = await readBody(event);
    if (!body.username || !body.password || !body.name) {
      throw createError({
        statusCode: 400,
        statusMessage: "Username, password and name are required"
      });
    }
    const users = readJsonData("users.json");
    const existingUser = users.find((u) => u.username === body.username);
    if (existingUser) {
      throw createError({
        statusCode: 409,
        statusMessage: "Username already exists"
      });
    }
    if (body.employee_id) {
      const existingEmployeeId = users.find((u) => u.employee_id === body.employee_id);
      if (existingEmployeeId) {
        throw createError({
          statusCode: 409,
          statusMessage: "Employee ID already exists"
        });
      }
    }
    if (body.email) {
      const existingEmail = users.find((u) => u.email === body.email);
      if (existingEmail) {
        throw createError({
          statusCode: 409,
          statusMessage: "Email already exists"
        });
      }
    }
    const newUserId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const organizations = readJsonData("organizations.json");
    const department = organizations.find((org) => org.org_id === body.department_id);
    const newUser = {
      user_id: newUserId,
      username: body.username,
      name: body.name,
      employee_id: body.employee_id || null,
      email: body.email || null,
      phone: body.phone || null,
      department_id: body.department_id || null,
      department_name: department ? department.org_name : null,
      status: body.status || "ACTIVE",
      role_ids: [],
      // 新用户默认没有角色
      last_login_date: null,
      created_date: (/* @__PURE__ */ new Date()).toISOString(),
      updated_date: (/* @__PURE__ */ new Date()).toISOString()
    };
    users.push(newUser);
    writeJsonData("users.json", users);
    const { password, ...userResponse } = newUser;
    return createSuccessResponse(userResponse, "User created successfully");
  } catch (error) {
    console.error("Error in user creation API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const create_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: create_post
});

const _id__delete = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const userId = getRouterParam(event, "id");
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    const users = readJsonData("users.json");
    const userIndex = users.findIndex((u) => u.user_id === userId);
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    const user = users[userIndex];
    if (user.username === "admin" || user.username === "superadmin") {
      throw createError({
        statusCode: 403,
        statusMessage: "Cannot delete system administrator"
      });
    }
    users.splice(userIndex, 1);
    writeJsonData("users.json", users);
    return createSuccessResponse(null, "User deleted successfully");
  } catch (error) {
    console.error("Error in user deletion API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _id__delete$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__delete
});

const _id__get = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const userId = getRouterParam(event, "id");
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    const users = readJsonData("users.json");
    const roles = readJsonData("roles.json");
    const organizations = readJsonData("organizations.json");
    const user = users.find((u) => u.user_id === userId);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    const userRoles = roles.filter(
      (role) => user.role_ids && user.role_ids.includes(role.role_id)
    ).map((role) => ({
      role_id: role.role_id,
      role_name: role.role_name,
      role_code: role.role_code,
      assigned_date: user.created_date
      // 简化处理，实际应该有专门的分配时间
    }));
    const department = organizations.find(
      (org) => org.org_id === user.department_id
    );
    const userDetail = {
      ...user,
      roles: userRoles,
      department: department || null
    };
    return createSuccessResponse(userDetail, "User detail retrieved successfully");
  } catch (error) {
    console.error("Error in user detail API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _id__get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__get
});

const index_get = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const users = readJsonData("users.json");
    const query = getQuery$1(event);
    let filteredUsers = users;
    if (query.department) {
      filteredUsers = users.filter(
        (user) => user.department.toLowerCase().includes(query.department.toLowerCase())
      );
    }
    if (query.status) {
      filteredUsers = filteredUsers.filter((user) => user.status === query.status);
    }
    if (query.role) {
      filteredUsers = filteredUsers.filter((user) => user.role === query.role);
    }
    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      filteredUsers = filteredUsers.filter(
        (user) => user.name.toLowerCase().includes(searchTerm) || user.email.toLowerCase().includes(searchTerm)
      );
    }
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    return createSuccessResponse({
      users: paginatedUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      }
    }, "Users retrieved successfully");
  } catch (error) {
    console.error("Error in users API:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const index_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_get
});

const query_get = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(300);
  try {
    const users = readJsonData("users.json");
    const query = getQuery$1(event);
    let filteredUsers = users;
    if (query.username) {
      filteredUsers = filteredUsers.filter(
        (user) => user.username.toLowerCase().includes(query.username.toLowerCase())
      );
    }
    if (query.name) {
      filteredUsers = filteredUsers.filter(
        (user) => user.name.toLowerCase().includes(query.name.toLowerCase())
      );
    }
    if (query.department_id) {
      filteredUsers = filteredUsers.filter(
        (user) => user.department_id === query.department_id
      );
    }
    if (query.status) {
      filteredUsers = filteredUsers.filter((user) => user.status === query.status);
    }
    if (query.employee_id) {
      filteredUsers = filteredUsers.filter(
        (user) => user.employee_id && user.employee_id.includes(query.employee_id)
      );
    }
    if (query.email) {
      filteredUsers = filteredUsers.filter(
        (user) => user.email && user.email.toLowerCase().includes(query.email.toLowerCase())
      );
    }
    if (query.phone) {
      filteredUsers = filteredUsers.filter(
        (user) => user.phone && user.phone.includes(query.phone)
      );
    }
    const offset = parseInt(query.offset) || 0;
    const limit = parseInt(query.limit) || 20;
    const endIndex = offset + limit;
    const paginatedUsers = filteredUsers.slice(offset, endIndex);
    return {
      code: 200,
      message: "success",
      data: {
        items: paginatedUsers,
        total: filteredUsers.length,
        has_more: endIndex < filteredUsers.length,
        offset,
        limit
      }
    };
  } catch (error) {
    console.error("Error in users query API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const query_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: query_get
});

const statistics_get = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(400);
  try {
    const users = readJsonData("users.json");
    const organizations = readJsonData("organizations.json");
    const totalUsers = users.length;
    const activeUsers = users.filter((user) => user.status === "ACTIVE").length;
    const inactiveUsers = users.filter((user) => user.status === "INACTIVE").length;
    const usersByDepartment = organizations.map((org) => {
      const userCount = users.filter((user) => user.department_id === org.org_id).length;
      return {
        department_id: org.org_id,
        department_name: org.org_name,
        user_count: userCount
      };
    }).filter((dept) => dept.user_count > 0);
    const recentLogins = users.filter((user) => user.last_login_date).sort((a, b) => new Date(b.last_login_date).getTime() - new Date(a.last_login_date).getTime()).slice(0, 10).map((user) => ({
      user_id: user.user_id,
      username: user.username,
      name: user.name,
      login_date: user.last_login_date
    }));
    const statistics = {
      total_users: totalUsers,
      active_users: activeUsers,
      inactive_users: inactiveUsers,
      users_by_department: usersByDepartment,
      recent_logins: recentLogins
    };
    return createSuccessResponse(statistics, "User statistics retrieved successfully");
  } catch (error) {
    console.error("Error in user statistics API:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const statistics_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: statistics_get
});

const _id__put = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(400);
  try {
    const userId = getRouterParam(event, "id");
    const body = await readBody(event);
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "User ID is required"
      });
    }
    const users = readJsonData("users.json");
    const userIndex = users.findIndex((u) => u.user_id === userId);
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: "User not found"
      });
    }
    if (body.employee_id && body.employee_id !== users[userIndex].employee_id) {
      const existingEmployeeId = users.find((u) => u.employee_id === body.employee_id);
      if (existingEmployeeId) {
        throw createError({
          statusCode: 409,
          statusMessage: "Employee ID already exists"
        });
      }
    }
    if (body.email && body.email !== users[userIndex].email) {
      const existingEmail = users.find((u) => u.email === body.email);
      if (existingEmail) {
        throw createError({
          statusCode: 409,
          statusMessage: "Email already exists"
        });
      }
    }
    const organizations = readJsonData("organizations.json");
    const department = body.department_id ? organizations.find((org) => org.org_id === body.department_id) : null;
    const updatedUser = {
      ...users[userIndex],
      name: body.name !== void 0 ? body.name : users[userIndex].name,
      employee_id: body.employee_id !== void 0 ? body.employee_id : users[userIndex].employee_id,
      email: body.email !== void 0 ? body.email : users[userIndex].email,
      phone: body.phone !== void 0 ? body.phone : users[userIndex].phone,
      department_id: body.department_id !== void 0 ? body.department_id : users[userIndex].department_id,
      department_name: department ? department.org_name : body.department_id === null ? null : users[userIndex].department_name,
      status: body.status !== void 0 ? body.status : users[userIndex].status,
      updated_date: (/* @__PURE__ */ new Date()).toISOString()
    };
    users[userIndex] = updatedUser;
    writeJsonData("users.json", users);
    return createSuccessResponse(updatedUser, "User updated successfully");
  } catch (error) {
    console.error("Error in user update API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _id__put$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__put
});

const validateEmail_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(200);
  try {
    const body = await readBody(event);
    if (!body.email) {
      throw createError({
        statusCode: 400,
        statusMessage: "Email is required"
      });
    }
    const users = readJsonData("users.json");
    const existingUser = users.find(
      (u) => u.email === body.email && (!body.exclude_user_id || u.user_id !== body.exclude_user_id)
    );
    const available = !existingUser;
    return createSuccessResponse({ available }, "Email validation completed");
  } catch (error) {
    console.error("Error in email validation API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const validateEmail_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: validateEmail_post
});

const validateEmployeeId_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(200);
  try {
    const body = await readBody(event);
    if (!body.employee_id) {
      throw createError({
        statusCode: 400,
        statusMessage: "Employee ID is required"
      });
    }
    const users = readJsonData("users.json");
    const existingUser = users.find(
      (u) => u.employee_id === body.employee_id && (!body.exclude_user_id || u.user_id !== body.exclude_user_id)
    );
    const available = !existingUser;
    return createSuccessResponse({ available }, "Employee ID validation completed");
  } catch (error) {
    console.error("Error in employee ID validation API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const validateEmployeeId_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: validateEmployeeId_post
});

const validateUsername_post = defineEventHandler(async (event) => {
  const errorCheck = shouldSimulateError(event);
  if (errorCheck.shouldError) {
    await simulateDelay(200);
    throw createError({
      statusCode: errorCheck.errorCode,
      statusMessage: errorCheck.errorMessage
    });
  }
  await simulateDelay(200);
  try {
    const body = await readBody(event);
    if (!body.username) {
      throw createError({
        statusCode: 400,
        statusMessage: "Username is required"
      });
    }
    const users = readJsonData("users.json");
    const existingUser = users.find(
      (u) => u.username === body.username && (!body.exclude_user_id || u.user_id !== body.exclude_user_id)
    );
    const available = !existingUser;
    return createSuccessResponse({ available }, "Username validation completed");
  } catch (error) {
    console.error("Error in username validation API:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const validateUsername_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: validateUsername_post
});

const login_post = defineEventHandler(async (event) => {
  await simulateDelay();
  if (getMethod(event) !== "POST") {
    throw createError({
      statusCode: 405,
      statusMessage: "Method Not Allowed"
    });
  }
  try {
    const body = await readBody(event);
    const { username, password } = body;
    if (!username || !password) {
      return createErrorResponse(
        "\u7528\u6237\u540D\u548C\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",
        400,
        "MISSING_CREDENTIALS"
      );
    }
    if (password.length < LOGIN_SECURITY.PASSWORD_MIN_LENGTH) {
      return createErrorResponse(
        `\u5BC6\u7801\u957F\u5EA6\u4E0D\u80FD\u5C11\u4E8E ${LOGIN_SECURITY.PASSWORD_MIN_LENGTH} \u4F4D`,
        400,
        "INVALID_PASSWORD_LENGTH"
      );
    }
    const user = findUserByUsername(username);
    if (!user) {
      return createErrorResponse(
        "\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF",
        401,
        "INVALID_CREDENTIALS"
      );
    }
    if (user.status === "inactive") {
      return createErrorResponse(
        "\u8D26\u6237\u5DF2\u88AB\u7981\u7528\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458",
        403,
        "ACCOUNT_DISABLED"
      );
    }
    if (isUserLocked(user)) {
      const lockUntil = user.lockUntil ? new Date(user.lockUntil) : null;
      const remainingTime = lockUntil ? Math.ceil((lockUntil.getTime() - Date.now()) / 1e3 / 60) : 0;
      return createErrorResponse(
        `\u8D26\u6237\u5DF2\u88AB\u9501\u5B9A\uFF0C\u8BF7 ${remainingTime} \u5206\u949F\u540E\u91CD\u8BD5`,
        423,
        "ACCOUNT_LOCKED",
        {
          lockUntil: user.lockUntil,
          remainingMinutes: remainingTime
        }
      );
    }
    if (!validatePassword(password, user.password)) {
      incrementLoginAttempts(user);
      const remainingAttempts = LOGIN_SECURITY.MAX_LOGIN_ATTEMPTS - user.loginAttempts;
      if (remainingAttempts <= 0) {
        return createErrorResponse(
          "\u767B\u5F55\u5931\u8D25\u6B21\u6570\u8FC7\u591A\uFF0C\u8D26\u6237\u5DF2\u88AB\u9501\u5B9A",
          423,
          "ACCOUNT_LOCKED_DUE_TO_ATTEMPTS",
          {
            lockUntil: user.lockUntil,
            remainingMinutes: Math.ceil(LOGIN_SECURITY.LOCKOUT_DURATION / 1e3 / 60)
          }
        );
      }
      return createErrorResponse(
        `\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF\uFF0C\u8FD8\u6709 ${remainingAttempts} \u6B21\u5C1D\u8BD5\u673A\u4F1A`,
        401,
        "INVALID_CREDENTIALS",
        { remainingAttempts }
      );
    }
    resetLoginAttempts(user);
    const tokens = generateLoginTokens(user);
    return createSuccessResponse({
      access_token: tokens.accessToken,
      token_type: tokens.tokenType,
      expires_in: tokens.expiresIn,
      refresh_token: tokens.refreshToken,
      user_id: user.id.toString()
    }, "\u767B\u5F55\u6210\u529F");
  } catch (error) {
    console.error("Login error:", error);
    return createErrorResponse(
      "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF",
      500,
      "INTERNAL_SERVER_ERROR"
    );
  }
});

const login_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: login_post
});

const logout_post = defineEventHandler(async (event) => {
  await simulateDelay();
  if (getMethod(event) !== "POST") {
    throw createError({
      statusCode: 405,
      statusMessage: "Method Not Allowed"
    });
  }
  try {
    const authHeader = getHeader(event, "authorization");
    const accessToken = extractTokenFromHeader(authHeader);
    if (!accessToken) {
      return createErrorResponse(
        "\u7F3A\u5C11\u8BBF\u95EE\u4EE4\u724C",
        401,
        "MISSING_TOKEN"
      );
    }
    const payload = verifyAccessToken(accessToken);
    if (!payload) {
      return createErrorResponse(
        "\u65E0\u6548\u7684\u8BBF\u95EE\u4EE4\u724C",
        401,
        "INVALID_TOKEN"
      );
    }
    let refreshToken;
    try {
      const body = await readBody(event);
      refreshToken = body == null ? void 0 : body.refreshToken;
    } catch {
    }
    revokeAccessToken(accessToken);
    if (refreshToken) {
      revokeRefreshToken(refreshToken);
    }
    return createSuccessResponse(
      null,
      "\u767B\u51FA\u6210\u529F"
    );
  } catch (error) {
    console.error("Logout error:", error);
    return createErrorResponse(
      "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF",
      500,
      "INTERNAL_SERVER_ERROR"
    );
  }
});

const logout_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: logout_post
});

const me_get = defineEventHandler(async (event) => {
  await simulateDelay();
  if (getMethod(event) !== "GET") {
    throw createError({
      statusCode: 405,
      statusMessage: "Method Not Allowed"
    });
  }
  try {
    const authHeader = getHeader(event, "authorization");
    const token = extractTokenFromHeader(authHeader);
    if (!token) {
      return createErrorResponse(
        "\u7F3A\u5C11\u8BBF\u95EE\u4EE4\u724C",
        401,
        "MISSING_TOKEN"
      );
    }
    const payload = verifyAccessToken(token);
    if (!payload) {
      return createErrorResponse(
        "\u65E0\u6548\u7684\u8BBF\u95EE\u4EE4\u724C",
        401,
        "INVALID_TOKEN"
      );
    }
    const userId = parseInt(payload.sub);
    const user = findUserById(userId);
    if (!user) {
      return createErrorResponse(
        "\u7528\u6237\u4E0D\u5B58\u5728",
        404,
        "USER_NOT_FOUND"
      );
    }
    if (user.status !== "active") {
      return createErrorResponse(
        "\u8D26\u6237\u5DF2\u88AB\u7981\u7528",
        403,
        "ACCOUNT_DISABLED"
      );
    }
    return createSuccessResponse({
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      nickname: user.name,
      avatar: user.avatar,
      phone: user.phone || "",
      status: user.status,
      is_active: user.status === "active",
      is_superuser: user.role === "admin",
      created_at: user.createdAt,
      updated_at: user.updatedAt,
      roles: [user.role],
      permissions: user.permissions
    }, "\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u6210\u529F");
  } catch (error) {
    console.error("Get user info error:", error);
    return createErrorResponse(
      "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF",
      500,
      "INTERNAL_SERVER_ERROR"
    );
  }
});

const me_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: me_get
});

const refresh_post = defineEventHandler(async (event) => {
  await simulateDelay();
  if (getMethod(event) !== "POST") {
    throw createError({
      statusCode: 405,
      statusMessage: "Method Not Allowed"
    });
  }
  try {
    const body = await readBody(event);
    const { refreshToken } = body;
    if (!refreshToken) {
      return createErrorResponse(
        "\u7F3A\u5C11\u5237\u65B0\u4EE4\u724C",
        400,
        "MISSING_REFRESH_TOKEN"
      );
    }
    if (!verifyRefreshToken(refreshToken)) {
      return createErrorResponse(
        "\u65E0\u6548\u7684\u5237\u65B0\u4EE4\u724C",
        401,
        "INVALID_REFRESH_TOKEN"
      );
    }
    const authHeader = getHeader(event, "authorization");
    const currentAccessToken = extractTokenFromHeader(authHeader);
    if (!currentAccessToken) {
      return createErrorResponse(
        "\u7F3A\u5C11\u8BBF\u95EE\u4EE4\u724C",
        401,
        "MISSING_ACCESS_TOKEN"
      );
    }
    let payload;
    try {
      const parts = currentAccessToken.split(".");
      if (parts.length !== 3) {
        throw new Error("Invalid token format");
      }
      const payloadStr = Buffer.from(parts[1].replace(/-/g, "+").replace(/_/g, "/"), "base64").toString();
      payload = JSON.parse(payloadStr);
    } catch {
      return createErrorResponse(
        "\u65E0\u6548\u7684\u8BBF\u95EE\u4EE4\u724C\u683C\u5F0F",
        401,
        "INVALID_ACCESS_TOKEN_FORMAT"
      );
    }
    const userId = parseInt(payload.sub);
    const user = findUserById(userId);
    if (!user) {
      return createErrorResponse(
        "\u7528\u6237\u4E0D\u5B58\u5728",
        404,
        "USER_NOT_FOUND"
      );
    }
    if (user.status !== "active") {
      return createErrorResponse(
        "\u8D26\u6237\u5DF2\u88AB\u7981\u7528",
        403,
        "ACCOUNT_DISABLED"
      );
    }
    revokeRefreshToken(refreshToken);
    const newTokens = generateLoginTokens(user);
    return createSuccessResponse({
      access_token: newTokens.accessToken,
      expires_in: newTokens.expiresIn
    }, "\u4EE4\u724C\u5237\u65B0\u6210\u529F");
  } catch (error) {
    console.error("Token refresh error:", error);
    return createErrorResponse(
      "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF",
      500,
      "INTERNAL_SERVER_ERROR"
    );
  }
});

const refresh_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: refresh_post
});
//# sourceMappingURL=index.mjs.map
