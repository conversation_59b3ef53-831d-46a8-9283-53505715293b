// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/api/health': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/health.get').default>>>>
    }
    '/api/api/organizations/tree': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/organizations/tree.get').default>>>>
    }
    '/api/api/permissions/tree': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/permissions/tree.get').default>>>>
    }
    '/api/api/products/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/products/[id].get').default>>>>
    }
    '/api/api/products': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/products/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/products/index.post').default>>>>
    }
    '/api/api/roles/:id/assign-permissions': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/[id]/assign-permissions.post').default>>>>
    }
    '/api/api/roles/:id/permissions': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/[id]/permissions.get').default>>>>
    }
    '/api/api/roles/available': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/available.get').default>>>>
    }
    '/api/api/roles/create': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/create.post').default>>>>
    }
    '/api/api/roles/delete/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/delete/[id].delete').default>>>>
    }
    '/api/api/roles/get/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/get/[id].get').default>>>>
    }
    '/api/api/roles/query': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/query.get').default>>>>
    }
    '/api/api/roles/statistics': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/statistics.get').default>>>>
    }
    '/api/api/roles/update/:id': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/update/[id].put').default>>>>
    }
    '/api/api/roles/validate-code': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/validate-code.post').default>>>>
    }
    '/api/api/roles/validate-name': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/validate-name.post').default>>>>
    }
    '/api/api/test/delay': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/test/delay.get').default>>>>
    }
    '/api/api/test/error': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/test/error.get').default>>>>
    }
    '/api/api/users/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id].get').default>>>>
    }
    '/api/api/users/:id/assign-role': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/assign-role.post').default>>>>
    }
    '/api/api/users/:id/remove-role/:roleId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/remove-role/[roleId].delete').default>>>>
    }
    '/api/api/users/:id/reset-password': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/reset-password.post').default>>>>
    }
    '/api/api/users/:id/roles': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/roles.get').default>>>>
    }
    '/api/api/users/batch-operation': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/batch-operation.post').default>>>>
    }
    '/api/api/users/create': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/create.post').default>>>>
    }
    '/api/api/users/delete/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/delete/[id].delete').default>>>>
    }
    '/api/api/users/get/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/get/[id].get').default>>>>
    }
    '/api/api/users': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/index.get').default>>>>
    }
    '/api/api/users/query': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/query.get').default>>>>
    }
    '/api/api/users/statistics': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/statistics.get').default>>>>
    }
    '/api/api/users/update/:id': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/update/[id].put').default>>>>
    }
    '/api/api/users/validate-email': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/validate-email.post').default>>>>
    }
    '/api/api/users/validate-employee-id': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/validate-employee-id.post').default>>>>
    }
    '/api/api/users/validate-username': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/validate-username.post').default>>>>
    }
    '/api/api/v1/sys/user/login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/login.post').default>>>>
    }
    '/api/api/v1/sys/user/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/logout.post').default>>>>
    }
    '/api/api/v1/sys/user/me': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/me.get').default>>>>
    }
    '/api/api/v1/sys/user/refresh': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/refresh.post').default>>>>
    }
    '/api/health': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/health.get').default>>>>
    }
    '/api/organizations/tree': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/organizations/tree.get').default>>>>
    }
    '/api/permissions/tree': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/permissions/tree.get').default>>>>
    }
    '/api/products/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/products/[id].get').default>>>>
    }
    '/api/products': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/products/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/products/index.post').default>>>>
    }
    '/api/roles/:id/assign-permissions': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/[id]/assign-permissions.post').default>>>>
    }
    '/api/roles/:id/permissions': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/[id]/permissions.get').default>>>>
    }
    '/api/roles/available': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/available.get').default>>>>
    }
    '/api/roles/create': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/create.post').default>>>>
    }
    '/api/roles/delete/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/delete/[id].delete').default>>>>
    }
    '/api/roles/get/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/get/[id].get').default>>>>
    }
    '/api/roles/query': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/query.get').default>>>>
    }
    '/api/roles/statistics': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/statistics.get').default>>>>
    }
    '/api/roles/update/:id': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/update/[id].put').default>>>>
    }
    '/api/roles/validate-code': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/validate-code.post').default>>>>
    }
    '/api/roles/validate-name': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/roles/validate-name.post').default>>>>
    }
    '/api/test/delay': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/test/delay.get').default>>>>
    }
    '/api/test/error': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/test/error.get').default>>>>
    }
    '/api/users/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id].get').default>>>>
    }
    '/api/users/:id/assign-role': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/assign-role.post').default>>>>
    }
    '/api/users/:id/remove-role/:roleId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/remove-role/[roleId].delete').default>>>>
    }
    '/api/users/:id/reset-password': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/reset-password.post').default>>>>
    }
    '/api/users/:id/roles': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/[id]/roles.get').default>>>>
    }
    '/api/users/batch-operation': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/batch-operation.post').default>>>>
    }
    '/api/users/create': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/create.post').default>>>>
    }
    '/api/users/delete/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/delete/[id].delete').default>>>>
    }
    '/api/users/get/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/get/[id].get').default>>>>
    }
    '/api/users': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/index.get').default>>>>
    }
    '/api/users/query': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/query.get').default>>>>
    }
    '/api/users/statistics': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/statistics.get').default>>>>
    }
    '/api/users/update/:id': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/update/[id].put').default>>>>
    }
    '/api/users/validate-email': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/validate-email.post').default>>>>
    }
    '/api/users/validate-employee-id': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/validate-employee-id.post').default>>>>
    }
    '/api/users/validate-username': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/users/validate-username.post').default>>>>
    }
    '/api/v1/sys/user/login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/login.post').default>>>>
    }
    '/api/v1/sys/user/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/logout.post').default>>>>
    }
    '/api/v1/sys/user/me': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/me.get').default>>>>
    }
    '/api/v1/sys/user/refresh': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/api/v1/sys/user/refresh.post').default>>>>
    }
  }
}
export {}