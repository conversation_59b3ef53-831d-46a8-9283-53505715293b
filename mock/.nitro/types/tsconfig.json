{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": false, "noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./nitro-imports"], "~/*": ["../../*"], "@/*": ["../../*"], "~~/*": ["../../*"], "@@/*": ["../../*"]}}, "include": ["./nitro.d.ts", "../../**/*"]}