import { createHmac, randomBytes } from 'crypto';
import { JWT_CONFIG, JWTPayload, User, ACTIVE_REFRESH_TOKENS, REVOKED_ACCESS_TOKENS } from '../data/auth';

// Base64 URL 编码
function base64UrlEncode(str: string): string {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// Base64 URL 解码
function base64UrlDecode(str: string): string {
  // 补齐 padding
  str += '='.repeat((4 - str.length % 4) % 4);
  return Buffer.from(str.replace(/-/g, '+').replace(/_/g, '/'), 'base64').toString();
}

// 生成 JWT Header
function createJWTHeader(): string {
  const header = {
    alg: JWT_CONFIG.ALGORITHM,
    typ: 'JWT'
  };
  return base64UrlEncode(JSON.stringify(header));
}

// 生成 JWT Payload
function createJWTPayload(user: User, expiresIn: number): JWTPayload {
  const now = Math.floor(Date.now() / 1000);
  return {
    sub: user.id.toString(),
    username: user.username,
    role: user.role,
    permissions: user.permissions,
    iat: now,
    exp: now + expiresIn,
    jti: generateJTI()
  };
}

// 生成 JWT ID
function generateJTI(): string {
  return randomBytes(16).toString('hex');
}

// 生成 HMAC 签名
function createSignature(data: string, secret: string): string {
  return createHmac('sha256', secret)
    .update(data)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// 生成 Access Token
export function generateAccessToken(user: User): string {
  const header = createJWTHeader();
  const payload = base64UrlEncode(JSON.stringify(createJWTPayload(user, JWT_CONFIG.ACCESS_TOKEN_EXPIRES_IN)));
  const data = `${header}.${payload}`;
  const signature = createSignature(data, JWT_CONFIG.SECRET);
  
  return `${data}.${signature}`;
}

// 生成 Refresh Token
export function generateRefreshToken(): string {
  const token = randomBytes(32).toString('hex');
  ACTIVE_REFRESH_TOKENS.add(token);
  return token;
}

// 验证 JWT 签名
function verifySignature(token: string): boolean {
  const parts = token.split('.');
  if (parts.length !== 3) return false;
  
  const [header, payload, signature] = parts;
  const data = `${header}.${payload}`;
  const expectedSignature = createSignature(data, JWT_CONFIG.SECRET);
  
  return signature === expectedSignature;
}

// 解析 JWT Token
export function parseJWT(token: string): JWTPayload | null {
  try {
    if (!verifySignature(token)) {
      return null;
    }
    
    const parts = token.split('.');
    const payload = JSON.parse(base64UrlDecode(parts[1]));
    
    // 检查是否在黑名单中
    if (REVOKED_ACCESS_TOKENS.has(token)) {
      return null;
    }
    
    // 检查是否过期
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return null;
    }
    
    return payload;
  } catch (error) {
    return null;
  }
}

// 验证 Access Token
export function verifyAccessToken(token: string): JWTPayload | null {
  return parseJWT(token);
}

// 验证 Refresh Token
export function verifyRefreshToken(token: string): boolean {
  return ACTIVE_REFRESH_TOKENS.has(token);
}

// 撤销 Access Token (添加到黑名单)
export function revokeAccessToken(token: string): void {
  REVOKED_ACCESS_TOKENS.add(token);
}

// 撤销 Refresh Token
export function revokeRefreshToken(token: string): void {
  ACTIVE_REFRESH_TOKENS.delete(token);
}

// 从 Authorization Header 中提取 Token
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader) return null;
  
  const match = authHeader.match(/^Bearer\s+(.+)$/);
  return match ? match[1] : null;
}

// 生成完整的登录响应 Token 信息
export function generateLoginTokens(user: User) {
  const accessToken = generateAccessToken(user);
  const refreshToken = generateRefreshToken();
  
  return {
    accessToken,
    refreshToken,
    expiresIn: JWT_CONFIG.ACCESS_TOKEN_EXPIRES_IN,
    tokenType: 'Bearer'
  };
}

// 清理过期的撤销 Token (定期清理，避免内存泄漏)
export function cleanupExpiredTokens(): void {
  // 在实际应用中，这应该是一个定时任务
  // 这里只是一个简单的实现示例
  const now = Math.floor(Date.now() / 1000);
  
  for (const token of REVOKED_ACCESS_TOKENS) {
    try {
      const payload = JSON.parse(base64UrlDecode(token.split('.')[1]));
      if (payload.exp && payload.exp < now) {
        REVOKED_ACCESS_TOKENS.delete(token);
      }
    } catch {
      // 如果解析失败，也删除这个无效的 token
      REVOKED_ACCESS_TOKENS.delete(token);
    }
  }
}

// 获取 Token 的剩余有效时间（秒）
export function getTokenRemainingTime(token: string): number {
  const payload = parseJWT(token);
  if (!payload || !payload.exp) return 0;
  
  const now = Math.floor(Date.now() / 1000);
  return Math.max(0, payload.exp - now);
}
