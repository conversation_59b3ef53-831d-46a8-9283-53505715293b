import { readFileSync, writeFileSync } from 'fs'
import { join } from 'path'

/**
 * 读取 JSON 数据文件
 */
export function readJsonData<T = any>(filename: string): T {
  try {
    const filePath = join(process.cwd(), 'data', filename)
    const data = readFileSync(filePath, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error(`Error reading ${filename}:`, error)
    return [] as T
  }
}

/**
 * 写入 JSON 数据文件
 */
export function writeJsonData<T = any>(filename: string, data: T): void {
  try {
    const filePath = join(process.cwd(), 'data', filename)
    const jsonData = JSON.stringify(data, null, 2)
    writeFileSync(filePath, jsonData, 'utf-8')
  } catch (error) {
    console.error(`Error writing ${filename}:`, error)
    throw error
  }
}

/**
 * 模拟网络延迟
 */
export function simulateDelay(ms: number = 500): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 生成成功响应 - 符合前端 ResponseModel<T> 格式
 */
export function createSuccessResponse<T = any>(data: T, message: string = 'Success') {
  return {
    code: 200,
    msg: message,
    data
  }
}

/**
 * 生成错误响应 - 符合前端 ResponseModel<T> 格式
 */
export function createErrorResponse(message: string, code: number = 500, details?: string, extraData?: any) {
  const errorResponse = {
    code,
    msg: message,
    data: extraData || null,
    details
  };

  return errorResponse;
}

/**
 * 检查是否应该模拟错误
 */
export function shouldSimulateError(event: any): { shouldError: boolean; errorCode?: number; errorMessage?: string } {
  const query = getQuery(event)
  const headers = getHeaders(event)
  
  // 通过查询参数触发错误
  if (query.error === 'true') {
    const errorCode = parseInt(query.errorCode as string) || 500
    const errorMessage = query.errorMessage as string || 'Simulated error'
    return { shouldError: true, errorCode, errorMessage }
  }
  
  // 通过请求头触发错误
  if (headers['x-simulate-error'] === 'true') {
    const errorCode = parseInt(headers['x-error-code'] as string) || 500
    const errorMessage = headers['x-error-message'] as string || 'Simulated error'
    return { shouldError: true, errorCode, errorMessage }
  }
  
  return { shouldError: false }
}

/**
 * 生成新的 ID
 */
export function generateId(): number {
  return Date.now() + Math.floor(Math.random() * 1000)
}
