import { simulateDelay, createSuccessResponse } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  
  // 获取延迟时间（毫秒）
  const delay = parseInt(query.ms as string) || 1000
  const maxDelay = 10000 // 最大延迟 10 秒
  
  const actualDelay = Math.min(delay, maxDelay)
  
  const startTime = Date.now()
  
  // 模拟延迟
  await simulateDelay(actualDelay)
  
  const endTime = Date.now()
  const actualDuration = endTime - startTime

  return createSuccessResponse({
    requestedDelay: delay,
    actualDelay: actualDuration,
    maxAllowedDelay: maxDelay,
    timestamp: new Date().toISOString()
  }, `Delayed response after ${actualDuration}ms`)
})
