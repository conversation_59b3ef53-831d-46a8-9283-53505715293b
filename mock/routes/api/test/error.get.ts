import { simulateDelay, createErrorResponse } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  
  // 获取错误代码和消息
  const errorCode = parseInt(query.code as string) || 500
  const errorMessage = query.message as string || 'Test error'
  const delay = parseInt(query.delay as string) || 500

  // 模拟延迟
  await simulateDelay(delay)

  // 抛出指定的错误
  throw createError({
    statusCode: errorCode,
    statusMessage: errorMessage
  })
})
