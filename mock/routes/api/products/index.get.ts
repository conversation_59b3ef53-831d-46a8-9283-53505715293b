import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(400)

  try {
    // 读取产品数据
    const products = readJsonData('products.json')
    
    // 获取查询参数
    const query = getQuery(event)
    let filteredProducts = products

    // 支持按分类筛选
    if (query.category) {
      filteredProducts = products.filter((product: any) => 
        product.category.toLowerCase().includes((query.category as string).toLowerCase())
      )
    }

    // 支持按状态筛选
    if (query.status) {
      filteredProducts = filteredProducts.filter((product: any) => product.status === query.status)
    }

    // 支持按供应商筛选
    if (query.supplier) {
      filteredProducts = filteredProducts.filter((product: any) => 
        product.supplier.toLowerCase().includes((query.supplier as string).toLowerCase())
      )
    }

    // 支持搜索（按名称或编码）
    if (query.search) {
      const searchTerm = (query.search as string).toLowerCase()
      filteredProducts = filteredProducts.filter((product: any) => 
        product.name.toLowerCase().includes(searchTerm) || 
        product.code.toLowerCase().includes(searchTerm)
      )
    }

    // 价格范围筛选
    if (query.minPrice) {
      filteredProducts = filteredProducts.filter((product: any) => 
        product.price >= parseFloat(query.minPrice as string)
      )
    }
    if (query.maxPrice) {
      filteredProducts = filteredProducts.filter((product: any) => 
        product.price <= parseFloat(query.maxPrice as string)
      )
    }

    // 分页支持
    const page = parseInt(query.page as string) || 1
    const limit = parseInt(query.limit as string) || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex)
    
    return createSuccessResponse({
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        totalPages: Math.ceil(filteredProducts.length / limit)
      }
    }, 'Products retrieved successfully')

  } catch (error) {
    console.error('Error in products API:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
