import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    // 获取产品 ID
    const productId = getRouterParam(event, 'id')
    
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Product ID is required'
      })
    }

    // 读取产品数据
    const products = readJsonData('products.json')
    
    // 查找指定产品
    const product = products.find((p: any) => p.id === parseInt(productId))
    
    if (!product) {
      throw createError({
        statusCode: 404,
        statusMessage: `Product with ID ${productId} not found`
      })
    }

    return createSuccessResponse(product, 'Product retrieved successfully')

  } catch (error: any) {
    console.error('Error in product detail API:', error)
    
    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error
    }
    
    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
