import { simulateDelay, createSuccessResponse, shouldSimulateError, generateId } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(600)

  try {
    // 读取请求体
    const body = await readBody(event)
    
    // 验证必填字段
    const requiredFields = ['name', 'code', 'category', 'price', 'stock', 'unit', 'supplier']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `Missing required fields: ${missingFields.join(', ')}`
      })
    }

    // 验证数据类型
    if (typeof body.price !== 'number' || body.price <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Price must be a positive number'
      })
    }

    if (typeof body.stock !== 'number' || body.stock < 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Stock must be a non-negative number'
      })
    }

    // 创建新产品对象
    const newProduct = {
      id: generateId(),
      name: body.name,
      code: body.code,
      category: body.category,
      specification: body.specification || '',
      price: body.price,
      stock: body.stock,
      unit: body.unit,
      supplier: body.supplier,
      status: body.stock > 0 ? 'available' : 'out_of_stock',
      description: body.description || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // 模拟保存成功
    return createSuccessResponse({
      id: newProduct.id,
      product: newProduct
    }, 'Product created successfully')

  } catch (error: any) {
    console.error('Error in product creation API:', error)
    
    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error
    }
    
    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
