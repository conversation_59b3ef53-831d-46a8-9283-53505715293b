import { findUserById } from '../../../../../data/auth';
import { 
  verifyRefreshToken, 
  revokeRefreshToken, 
  generateLoginTokens,
  verifyAccessToken,
  extractTokenFromHeader 
} from '../../../../../utils/jwt';
import { simulateDelay, createSuccessResponse, createErrorResponse } from '../../../../../utils/helpers';

export default defineEventHandler(async (event) => {
  // 模拟网络延迟
  await simulateDelay();

  // 只允许 POST 方法
  if (getMethod(event) !== 'POST') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    });
  }

  try {
    // 解析请求体
    const body = await readBody(event);
    const { refreshToken } = body;

    if (!refreshToken) {
      return createErrorResponse(
        '缺少刷新令牌',
        400,
        'MISSING_REFRESH_TOKEN'
      );
    }

    // 验证 Refresh Token
    if (!verifyRefreshToken(refreshToken)) {
      return createErrorResponse(
        '无效的刷新令牌',
        401,
        'INVALID_REFRESH_TOKEN'
      );
    }

    // 获取当前的 Access Token 来确定用户身份
    const authHeader = getHeader(event, 'authorization');
    const currentAccessToken = extractTokenFromHeader(authHeader);
    
    if (!currentAccessToken) {
      return createErrorResponse(
        '缺少访问令牌',
        401,
        'MISSING_ACCESS_TOKEN'
      );
    }

    // 验证当前的 Access Token（即使过期也要能解析出用户信息）
    // 这里我们需要解析 token 而不验证过期时间
    let payload;
    try {
      const parts = currentAccessToken.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid token format');
      }
      
      // 解码 payload（不验证签名和过期时间，因为可能已过期）
      const payloadStr = Buffer.from(parts[1].replace(/-/g, '+').replace(/_/g, '/'), 'base64').toString();
      payload = JSON.parse(payloadStr);
    } catch {
      return createErrorResponse(
        '无效的访问令牌格式',
        401,
        'INVALID_ACCESS_TOKEN_FORMAT'
      );
    }

    // 根据 Token 中的用户 ID 查找用户
    const userId = parseInt(payload.sub);
    const user = findUserById(userId);

    if (!user) {
      return createErrorResponse(
        '用户不存在',
        404,
        'USER_NOT_FOUND'
      );
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return createErrorResponse(
        '账户已被禁用',
        403,
        'ACCOUNT_DISABLED'
      );
    }

    // 撤销旧的 Refresh Token
    revokeRefreshToken(refreshToken);

    // 生成新的 Token 对
    const newTokens = generateLoginTokens(user);

    // 返回新的 Token - 符合前端期望格式
    return createSuccessResponse({
      access_token: newTokens.accessToken,
      expires_in: newTokens.expiresIn
    }, '令牌刷新成功');

  } catch (error) {
    console.error('Token refresh error:', error);
    
    return createErrorResponse(
      '服务器内部错误',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
