import { verifyAccessToken, extractTokenFromHeader, revokeAccessToken, revokeRefreshToken } from '../../../../../utils/jwt';
import { simulateDelay, createSuccessResponse, createErrorResponse } from '../../../../../utils/helpers';

export default defineEventHandler(async (event) => {
  // 模拟网络延迟
  await simulateDelay();

  // 只允许 POST 方法
  if (getMethod(event) !== 'POST') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    });
  }

  try {
    // 获取 Authorization Header
    const authHeader = getHeader(event, 'authorization');
    const accessToken = extractTokenFromHeader(authHeader);

    if (!accessToken) {
      return createErrorResponse(
        '缺少访问令牌',
        401,
        'MISSING_TOKEN'
      );
    }

    // 验证 Access Token
    const payload = verifyAccessToken(accessToken);
    if (!payload) {
      return createErrorResponse(
        '无效的访问令牌',
        401,
        'INVALID_TOKEN'
      );
    }

    // 解析请求体获取 refresh token（可选）
    let refreshToken: string | undefined;
    try {
      const body = await readBody(event);
      refreshToken = body?.refreshToken;
    } catch {
      // 如果没有请求体或解析失败，忽略
    }

    // 撤销 Access Token（添加到黑名单）
    revokeAccessToken(accessToken);

    // 如果提供了 Refresh Token，也撤销它
    if (refreshToken) {
      revokeRefreshToken(refreshToken);
    }

    // 返回成功响应
    return createSuccessResponse(
      null,
      '登出成功'
    );

  } catch (error) {
    console.error('Logout error:', error);
    
    return createErrorResponse(
      '服务器内部错误',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
