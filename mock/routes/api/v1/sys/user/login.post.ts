import { 
  findUserByUsername, 
  validatePassword, 
  isUserLocked, 
  incrementLoginAttempts, 
  resetLoginAttempts,
  LOGIN_SECURITY 
} from '../../../../../data/auth';
import { generateLoginTokens } from '../../../../../utils/jwt';
import { simulateDelay, createSuccessResponse, createErrorResponse } from '../../../../../utils/helpers';

export default defineEventHandler(async (event) => {
  // 模拟网络延迟
  await simulateDelay();

  // 只允许 POST 方法
  if (getMethod(event) !== 'POST') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    });
  }

  try {
    // 解析请求体
    const body = await readBody(event);
    const { username, password } = body;

    // 验证必填字段
    if (!username || !password) {
      return createErrorResponse(
        '用户名和密码不能为空',
        400,
        'MISSING_CREDENTIALS'
      );
    }

    // 验证密码长度
    if (password.length < LOGIN_SECURITY.PASSWORD_MIN_LENGTH) {
      return createErrorResponse(
        `密码长度不能少于 ${LOGIN_SECURITY.PASSWORD_MIN_LENGTH} 位`,
        400,
        'INVALID_PASSWORD_LENGTH'
      );
    }

    // 查找用户
    const user = findUserByUsername(username);
    if (!user) {
      return createErrorResponse(
        '用户名或密码错误',
        401,
        'INVALID_CREDENTIALS'
      );
    }

    // 检查用户状态
    if (user.status === 'inactive') {
      return createErrorResponse(
        '账户已被禁用，请联系管理员',
        403,
        'ACCOUNT_DISABLED'
      );
    }

    // 检查是否被锁定
    if (isUserLocked(user)) {
      const lockUntil = user.lockUntil ? new Date(user.lockUntil) : null;
      const remainingTime = lockUntil ? Math.ceil((lockUntil.getTime() - Date.now()) / 1000 / 60) : 0;
      
      return createErrorResponse(
        `账户已被锁定，请 ${remainingTime} 分钟后重试`,
        423,
        'ACCOUNT_LOCKED',
        { 
          lockUntil: user.lockUntil,
          remainingMinutes: remainingTime
        }
      );
    }

    // 验证密码
    if (!validatePassword(password, user.password)) {
      // 增加失败次数
      incrementLoginAttempts(user);
      
      const remainingAttempts = LOGIN_SECURITY.MAX_LOGIN_ATTEMPTS - user.loginAttempts;
      
      if (remainingAttempts <= 0) {
        return createErrorResponse(
          '登录失败次数过多，账户已被锁定',
          423,
          'ACCOUNT_LOCKED_DUE_TO_ATTEMPTS',
          {
            lockUntil: user.lockUntil,
            remainingMinutes: Math.ceil(LOGIN_SECURITY.LOCKOUT_DURATION / 1000 / 60)
          }
        );
      }
      
      return createErrorResponse(
        `用户名或密码错误，还有 ${remainingAttempts} 次尝试机会`,
        401,
        'INVALID_CREDENTIALS',
        { remainingAttempts }
      );
    }

    // 登录成功，重置失败次数
    resetLoginAttempts(user);

    // 生成 JWT Token
    const tokens = generateLoginTokens(user);

    // 返回成功响应 - 符合前端 LoginToken 接口格式
    return createSuccessResponse({
      access_token: tokens.accessToken,
      token_type: tokens.tokenType,
      expires_in: tokens.expiresIn,
      refresh_token: tokens.refreshToken,
      user_id: user.id.toString()
    }, '登录成功');

  } catch (error) {
    console.error('Login error:', error);
    
    return createErrorResponse(
      '服务器内部错误',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
