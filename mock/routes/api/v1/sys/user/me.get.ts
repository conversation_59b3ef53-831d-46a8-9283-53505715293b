import { findUserById } from '../../../../../data/auth';
import { verifyAccessToken, extractTokenFromHeader } from '../../../../../utils/jwt';
import { simulateDelay, createSuccessResponse, createErrorResponse } from '../../../../../utils/helpers';

export default defineEventHandler(async (event) => {
  // 模拟网络延迟
  await simulateDelay();

  // 只允许 GET 方法
  if (getMethod(event) !== 'GET') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    });
  }

  try {
    // 获取 Authorization Header
    const authHeader = getHeader(event, 'authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return createErrorResponse(
        '缺少访问令牌',
        401,
        'MISSING_TOKEN'
      );
    }

    // 验证 JWT Token
    const payload = verifyAccessToken(token);
    if (!payload) {
      return createErrorResponse(
        '无效的访问令牌',
        401,
        'INVALID_TOKEN'
      );
    }

    // 根据 Token 中的用户 ID 查找用户
    const userId = parseInt(payload.sub);
    const user = findUserById(userId);

    if (!user) {
      return createErrorResponse(
        '用户不存在',
        404,
        'USER_NOT_FOUND'
      );
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return createErrorResponse(
        '账户已被禁用',
        403,
        'ACCOUNT_DISABLED'
      );
    }

    // 返回用户信息（符合前端 User 接口格式）
    return createSuccessResponse({
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      nickname: user.name,
      avatar: user.avatar,
      phone: user.phone || '',
      status: user.status,
      is_active: user.status === 'active',
      is_superuser: user.role === 'admin',
      created_at: user.createdAt,
      updated_at: user.updatedAt,
      roles: [user.role],
      permissions: user.permissions
    }, '获取用户信息成功');

  } catch (error) {
    console.error('Get user info error:', error);
    
    return createErrorResponse(
      '服务器内部错误',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
