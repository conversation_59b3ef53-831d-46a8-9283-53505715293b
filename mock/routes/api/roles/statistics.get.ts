import { readJsonData } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const roles = await readJsonData('roles.json')
  const permissions = await readJsonData('permissions.json')
  
  // 统计角色数量
  const totalRoles = roles.length
  const activeRoles = roles.filter((r: any) => r.status === 'ACTIVE').length
  const inactiveRoles = roles.filter((r: any) => r.status === 'INACTIVE').length

  // 按资源类型统计角色
  const rolesByType = [
    { resource_type: 'menu', role_count: 4 },
    { resource_type: 'action', role_count: 3 },
    { resource_type: 'data', role_count: 2 },
    { resource_type: 'api', role_count: 1 }
  ]

  // 权限使用情况统计
  const permissionUsage = permissions.slice(0, 10).map((p: any) => ({
    permission_id: p.permission_id,
    permission_name: p.permission_name,
    usage_count: Math.floor(Math.random() * 5) + 1
  }))

  return {
    code: 200,
    message: 'success',
    data: {
      total_roles: totalRoles,
      active_roles: activeRoles,
      inactive_roles: inactiveRoles,
      roles_by_type: rolesByType,
      permission_usage: permissionUsage
    }
  }
})