import { readJsonData, writeJsonData } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, 'id')
  const roles = await readJsonData('roles.json')
  
  const roleIndex = roles.findIndex((r: any) => r.role_id === roleId)
  
  if (roleIndex === -1) {
    return {
      code: 404,
      message: '角色不存在',
      data: null
    }
  }

  const role = roles[roleIndex]

  // 检查是否有用户正在使用该角色
  if (role.user_count > 0) {
    return {
      code: 400,
      message: `无法删除角色，还有 ${role.user_count} 个用户正在使用该角色`,
      data: null
    }
  }

  // 删除角色
  roles.splice(roleIndex, 1)
  await writeJsonData('roles.json', roles)

  return {
    code: 200,
    message: '角色删除成功',
    data: null
  }
})