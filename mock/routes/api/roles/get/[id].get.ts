import { readJsonData } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, 'id')
  const roles = await readJsonData('roles.json')
  const permissions = await readJsonData('permissions.json')
  const users = await readJsonData('users.json')
  
  const role = roles.find((r: any) => r.role_id === roleId)
  
  if (!role) {
    return {
      code: 404,
      message: '角色不存在',
      data: null
    }
  }

  // 模拟角色的权限数据
  const rolePermissions = permissions.slice(0, Math.floor(Math.random() * 8) + 3).map((p: any) => ({
    permission_id: p.permission_id,
    permission_name: p.permission_name,
    permission_code: p.permission_code,
    resource_type: p.resource_type,
    description: p.description
  }))

  // 模拟使用该角色的用户
  const roleUsers = users.slice(0, role.user_count || 0).map((u: any) => ({
    user_id: u.user_id,
    username: u.username,
    name: u.name,
    department_name: u.department_name,
    status: u.status
  }))

  return {
    code: 200,
    message: 'success', 
    data: {
      ...role,
      permissions: rolePermissions,
      users: roleUsers
    }
  }
})