import { readJsonData } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const roles = await readJsonData('roles.json')
  
  // 解析查询参数
  const offset = parseInt(query.offset as string) || 0
  const limit = parseInt(query.limit as string) || 20
  const roleName = query.role_name as string
  const roleCode = query.role_code as string  
  const status = query.status as string
  const description = query.description as string

  // 过滤数据
  let filteredRoles = roles.filter((role: any) => {
    if (roleName && !role.role_name.toLowerCase().includes(roleName.toLowerCase())) {
      return false
    }
    if (roleCode && !role.role_code.toLowerCase().includes(roleCode.toLowerCase())) {
      return false  
    }
    if (status && role.status !== status) {
      return false
    }
    if (description && !role.description?.toLowerCase().includes(description.toLowerCase())) {
      return false
    }
    return true
  })

  // 分页
  const total = filteredRoles.length
  const paginatedRoles = filteredRoles.slice(offset, offset + limit)
  const hasMore = offset + limit < total

  return {
    code: 200,
    message: 'success',
    data: {
      items: paginatedRoles,
      total,
      has_more: hasMore,
      offset,
      limit
    }
  }
})