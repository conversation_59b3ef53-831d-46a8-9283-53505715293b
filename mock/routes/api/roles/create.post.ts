import { readJsonData, writeJsonData } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const roles = await readJsonData('roles.json')
  
  // 验证必填字段
  if (!body.role_name || !body.role_code) {
    return {
      code: 400,
      message: '角色名称和角色编码不能为空',
      data: null
    }
  }

  // 检查角色名称是否已存在
  const existingByName = roles.find((r: any) => r.role_name === body.role_name)
  if (existingByName) {
    return {
      code: 400,
      message: '角色名称已存在',
      data: null
    }
  }

  // 检查角色编码是否已存在
  const existingByCode = roles.find((r: any) => r.role_code === body.role_code)
  if (existingByCode) {
    return {
      code: 400,
      message: '角色编码已存在',
      data: null
    }
  }

  // 创建新角色
  const newRole = {
    role_id: `r${String(Date.now()).slice(-6)}`,
    role_name: body.role_name,
    role_code: body.role_code,
    description: body.description || '',
    status: body.status || 'ACTIVE',
    user_count: 0,
    permission_count: 0,
    created_date: new Date().toISOString(),
    updated_date: new Date().toISOString()
  }

  roles.push(newRole)
  await writeJsonData('roles.json', roles)

  return {
    code: 200,
    message: '角色创建成功',
    data: newRole
  }
})