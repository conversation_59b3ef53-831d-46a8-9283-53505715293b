import { readJsonData, writeJsonData } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, 'id')
  const body = await readBody(event)
  const roles = await readJsonData('roles.json')
  
  const roleIndex = roles.findIndex((r: any) => r.role_id === roleId)
  
  if (roleIndex === -1) {
    return {
      code: 404,
      message: '角色不存在',
      data: null
    }
  }

  const currentRole = roles[roleIndex]

  // 如果更新角色名称，检查是否与其他角色重复
  if (body.role_name && body.role_name !== currentRole.role_name) {
    const existingByName = roles.find((r: any) => r.role_name === body.role_name && r.role_id !== roleId)
    if (existingByName) {
      return {
        code: 400,
        message: '角色名称已存在',
        data: null
      }
    }
  }

  // 如果更新角色编码，检查是否与其他角色重复
  if (body.role_code && body.role_code !== currentRole.role_code) {
    const existingByCode = roles.find((r: any) => r.role_code === body.role_code && r.role_id !== roleId)
    if (existingByCode) {
      return {
        code: 400,
        message: '角色编码已存在',
        data: null
      }
    }
  }

  // 更新角色信息
  const updatedRole = {
    ...currentRole,
    ...body,
    role_id: roleId, // 确保ID不被修改
    updated_date: new Date().toISOString()
  }

  roles[roleIndex] = updatedRole
  await writeJsonData('roles.json', roles)

  return {
    code: 200,
    message: '角色更新成功',
    data: updatedRole
  }
})