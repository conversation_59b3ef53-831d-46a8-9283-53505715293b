import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    // 读取角色数据
    const roles = readJsonData('roles.json')
    
    // 只返回活跃的角色
    const availableRoles = roles.filter((role: any) => role.status === 'ACTIVE')
    
    return createSuccessResponse(availableRoles, 'Available roles retrieved successfully')

  } catch (error) {
    console.error('Error in available roles API:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})