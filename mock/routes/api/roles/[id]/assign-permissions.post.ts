import { readJsonData, writeJsonData } from '../../../../utils/helpers'

export default defineEventHandler(async (event: any) => {
  const roleId = getRouterParam(event, 'id')
  const body = await readBody(event)
  const roles = await readJsonData('roles.json')
  
  const roleIndex = roles.findIndex((r: any) => r.role_id === roleId)
  
  if (roleIndex === -1) {
    return {
      code: 404,
      message: '角色不存在',
      data: null
    }
  }

  if (!body.permission_ids || !Array.isArray(body.permission_ids)) {
    return {
      code: 400,
      message: '权限ID列表不能为空',
      data: null
    }
  }

  // 更新角色的权限数量
  roles[roleIndex].permission_count = body.permission_ids.length
  roles[roleIndex].updated_date = new Date().toISOString()
  
  await writeJsonData('roles.json', roles)

  return {
    code: 200,
    message: '权限分配成功',
    data: null
  }
})