import { readJsonData } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const roleId = getRouterParam(event, 'id')
  const roles = await readJsonData('roles.json')
  const permissions = await readJsonData('permissions.json')
  
  const role = roles.find((r: any) => r.role_id === roleId)
  
  if (!role) {
    return {
      code: 404,
      message: '角色不存在',
      data: null
    }
  }

  // 模拟角色的权限数据
  const rolePermissions = permissions.slice(0, Math.floor(Math.random() * 8) + 3).map((p: any) => ({
    role_id: roleId,
    permission_id: p.permission_id,
    permission_name: p.permission_name,
    permission_code: p.permission_code,
    resource_type: p.resource_type,
    assigned_date: new Date().toISOString()
  }))

  return {
    code: 200,
    message: 'success',
    data: rolePermissions
  }
})