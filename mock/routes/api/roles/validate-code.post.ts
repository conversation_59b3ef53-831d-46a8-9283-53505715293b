import { readJsonData } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const roles = await readJsonData('roles.json')
  
  if (!body.role_code) {
    return {
      code: 400,
      message: '角色编码不能为空',
      data: { available: false }
    }
  }

  // 检查角色编码是否已存在（排除指定ID的角色）
  const existing = roles.find((r: any) => 
    r.role_code === body.role_code && 
    r.role_id !== body.exclude_role_id
  )

  return {
    code: 200,
    message: 'success',
    data: { available: !existing }
  }
})