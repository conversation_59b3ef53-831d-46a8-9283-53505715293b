import { readJsonData, simulateDelay, createSuccessResponse, createErrorResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    // 读取用户数据
    const users = readJsonData('users.json')
    
    // 获取查询参数
    const query = getQuery(event)
    let filteredUsers = users

    // 支持按部门筛选
    if (query.department) {
      filteredUsers = users.filter((user: any) => 
        user.department.toLowerCase().includes((query.department as string).toLowerCase())
      )
    }

    // 支持按状态筛选
    if (query.status) {
      filteredUsers = filteredUsers.filter((user: any) => user.status === query.status)
    }

    // 支持按角色筛选
    if (query.role) {
      filteredUsers = filteredUsers.filter((user: any) => user.role === query.role)
    }

    // 支持搜索（按姓名或邮箱）
    if (query.search) {
      const searchTerm = (query.search as string).toLowerCase()
      filteredUsers = filteredUsers.filter((user: any) => 
        user.name.toLowerCase().includes(searchTerm) || 
        user.email.toLowerCase().includes(searchTerm)
      )
    }

    // 分页支持
    const page = parseInt(query.page as string) || 1
    const limit = parseInt(query.limit as string) || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex)
    
    return createSuccessResponse({
      users: paginatedUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      }
    }, 'Users retrieved successfully')

  } catch (error) {
    console.error('Error in users API:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
