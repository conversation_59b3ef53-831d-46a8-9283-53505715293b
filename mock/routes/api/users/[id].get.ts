import { readJsonData, simulateDelay, createSuccessResponse, createErrorResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(250)

  try {
    // 获取用户 ID
    const userId = getRouterParam(event, 'id')
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    
    // 查找指定用户
    const user = users.find((u: any) => u.id === parseInt(userId))
    
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: `User with ID ${userId} not found`
      })
    }

    return createSuccessResponse(user, 'User retrieved successfully')

  } catch (error: any) {
    console.error('Error in user detail API:', error)
    
    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error
    }
    
    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
