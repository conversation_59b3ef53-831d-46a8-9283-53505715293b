import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(400)

  try {
    // 读取用户数据
    const users = readJsonData('users.json')
    const organizations = readJsonData('organizations.json')
    
    // 计算总用户数
    const totalUsers = users.length
    
    // 计算活跃用户数
    const activeUsers = users.filter((user: any) => user.status === 'ACTIVE').length
    
    // 计算禁用用户数
    const inactiveUsers = users.filter((user: any) => user.status === 'INACTIVE').length
    
    // 按部门统计用户数
    const usersByDepartment = organizations.map((org: any) => {
      const userCount = users.filter((user: any) => user.department_id === org.org_id).length
      return {
        department_id: org.org_id,
        department_name: org.org_name,
        user_count: userCount
      }
    }).filter(dept => dept.user_count > 0)
    
    // 最近登录用户（模拟数据）
    const recentLogins = users
      .filter((user: any) => user.last_login_date)
      .sort((a: any, b: any) => new Date(b.last_login_date).getTime() - new Date(a.last_login_date).getTime())
      .slice(0, 10)
      .map((user: any) => ({
        user_id: user.user_id,
        username: user.username,
        name: user.name,
        login_date: user.last_login_date
      }))
    
    const statistics = {
      total_users: totalUsers,
      active_users: activeUsers,
      inactive_users: inactiveUsers,
      users_by_department: usersByDepartment,
      recent_logins: recentLogins
    }
    
    return createSuccessResponse(statistics, 'User statistics retrieved successfully')

  } catch (error) {
    console.error('Error in user statistics API:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})