import { readJsonData, writeJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'
import { verifyAccessToken, extractTokenFromHeader } from '../../../utils/jwt'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(600)

  try {
    // 验证认证
    const authHeader = getHeader(event, 'authorization')
    const token = extractTokenFromHeader(authHeader)

    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing authorization token'
      })
    }

    // 验证 JWT Token
    const payload = verifyAccessToken(token)
    if (!payload) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    const body = await readBody(event)
    
    if (!body.user_ids || !Array.isArray(body.user_ids) || body.user_ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User IDs array is required'
      })
    }

    if (!body.action || !['activate', 'deactivate', 'delete'].includes(body.action)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Valid action is required (activate, deactivate, delete)'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    const userIds = body.user_ids
    const action = body.action

    // 找到要操作的用户
    const usersToUpdate = users.filter((user: any) => userIds.includes(user.user_id))
    
    if (usersToUpdate.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No users found for the provided IDs'
      })
    }

    // 检查是否试图删除超级管理员
    if (action === 'delete') {
      const adminUsers = usersToUpdate.filter((user: any) => 
        user.username === 'admin' || user.username === 'superadmin'
      )
      if (adminUsers.length > 0) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Cannot delete system administrators'
        })
      }
    }

    let updatedUsers = [...users]

    // 执行批量操作
    if (action === 'delete') {
      // 删除用户
      updatedUsers = users.filter((user: any) => !userIds.includes(user.user_id))
    } else {
      // 更新用户状态
      const newStatus = action === 'activate' ? 'ACTIVE' : 'INACTIVE'
      updatedUsers = users.map((user: any) => {
        if (userIds.includes(user.user_id)) {
          return {
            ...user,
            status: newStatus,
            updated_date: new Date().toISOString()
          }
        }
        return user
      })
    }

    // 保存到文件
    writeJsonData('users.json', updatedUsers)
    
    const successMessage = action === 'delete' 
      ? `${usersToUpdate.length} users deleted successfully`
      : `${usersToUpdate.length} users ${action}d successfully`
    
    return createSuccessResponse({
      affected_count: usersToUpdate.length,
      action: action
    }, successMessage)

  } catch (error: any) {
    console.error('Error in batch operation API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})