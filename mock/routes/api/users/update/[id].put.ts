import { readJsonData, writeJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(400)

  try {
    const userId = getRouterParam(event, 'id')
    const body = await readBody(event)
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    const userIndex = users.findIndex((u: any) => u.user_id === userId)
    
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 检查员工编号是否已存在（如果提供了且发生变化）
    if (body.employee_id && body.employee_id !== users[userIndex].employee_id) {
      const existingEmployeeId = users.find((u: any) => u.employee_id === body.employee_id)
      if (existingEmployeeId) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Employee ID already exists'
        })
      }
    }

    // 检查邮箱是否已存在（如果提供了且发生变化）
    if (body.email && body.email !== users[userIndex].email) {
      const existingEmail = users.find((u: any) => u.email === body.email)
      if (existingEmail) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Email already exists'
        })
      }
    }

    // 获取部门信息
    const organizations = readJsonData('organizations.json')
    const department = body.department_id ? 
      organizations.find((org: any) => org.org_id === body.department_id) : null

    // 更新用户信息
    const updatedUser = {
      ...users[userIndex],
      name: body.name !== undefined ? body.name : users[userIndex].name,
      employee_id: body.employee_id !== undefined ? body.employee_id : users[userIndex].employee_id,
      email: body.email !== undefined ? body.email : users[userIndex].email,
      phone: body.phone !== undefined ? body.phone : users[userIndex].phone,
      department_id: body.department_id !== undefined ? body.department_id : users[userIndex].department_id,
      department_name: department ? department.org_name : (body.department_id === null ? null : users[userIndex].department_name),
      status: body.status !== undefined ? body.status : users[userIndex].status,
      updated_date: new Date().toISOString()
    }

    users[userIndex] = updatedUser
    
    // 保存到文件
    writeJsonData('users.json', users)
    
    return createSuccessResponse(updatedUser, 'User updated successfully')

  } catch (error) {
    console.error('Error in user update API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})