import { readJsonData, writeJsonD<PERSON>, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    const userId = getRouterParam(event, 'id')
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    const userIndex = users.findIndex((u: any) => u.user_id === userId)
    
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 检查是否为超级管理员（不能删除）
    const user = users[userIndex]
    if (user.username === 'admin' || user.username === 'superadmin') {
      throw createError({
        statusCode: 403,
        statusMessage: 'Cannot delete system administrator'
      })
    }

    // 删除用户
    users.splice(userIndex, 1)
    
    // 保存到文件
    writeJsonData('users.json', users)
    
    return createSuccessResponse(null, 'User deleted successfully')

  } catch (error) {
    console.error('Error in user deletion API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})