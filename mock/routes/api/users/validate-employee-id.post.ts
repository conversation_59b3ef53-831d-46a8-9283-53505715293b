import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(200)

  try {
    const body = await readBody(event)
    
    if (!body.employee_id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Employee ID is required'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    
    // 检查员工编号是否已存在（排除指定的用户ID）
    const existingUser = users.find((u: any) => 
      u.employee_id === body.employee_id && 
      (!body.exclude_user_id || u.user_id !== body.exclude_user_id)
    )
    
    const available = !existingUser
    
    return createSuccessResponse({ available }, 'Employee ID validation completed')

  } catch (error) {
    console.error('Error in employee ID validation API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})