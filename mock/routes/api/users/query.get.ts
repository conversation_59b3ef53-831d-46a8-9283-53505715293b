import { readJsonData, simulateDelay, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    // 读取用户数据
    const users = readJsonData('users.json')
    
    // 获取查询参数
    const query = getQuery(event)
    let filteredUsers = users

    // 支持按用户名搜索
    if (query.username) {
      filteredUsers = filteredUsers.filter((user: any) => 
        user.username.toLowerCase().includes((query.username as string).toLowerCase())
      )
    }

    // 支持按姓名搜索
    if (query.name) {
      filteredUsers = filteredUsers.filter((user: any) => 
        user.name.toLowerCase().includes((query.name as string).toLowerCase())
      )
    }

    // 支持按部门筛选
    if (query.department_id) {
      filteredUsers = filteredUsers.filter((user: any) => 
        user.department_id === query.department_id
      )
    }

    // 支持按状态筛选
    if (query.status) {
      filteredUsers = filteredUsers.filter((user: any) => user.status === query.status)
    }

    // 支持按员工编号搜索
    if (query.employee_id) {
      filteredUsers = filteredUsers.filter((user: any) => 
        user.employee_id && user.employee_id.includes(query.employee_id as string)
      )
    }

    // 支持按邮箱搜索
    if (query.email) {
      filteredUsers = filteredUsers.filter((user: any) => 
        user.email && user.email.toLowerCase().includes((query.email as string).toLowerCase())
      )
    }

    // 支持按手机号搜索
    if (query.phone) {
      filteredUsers = filteredUsers.filter((user: any) => 
        user.phone && user.phone.includes(query.phone as string)
      )
    }

    // 分页支持
    const offset = parseInt(query.offset as string) || 0
    const limit = parseInt(query.limit as string) || 20
    const endIndex = offset + limit
    
    const paginatedUsers = filteredUsers.slice(offset, endIndex)

    return {
      code: 200,
      message: 'success',
      data: {
        items: paginatedUsers,
        total: filteredUsers.length,
        has_more: endIndex < filteredUsers.length,
        offset,
        limit
      }
    }

  } catch (error: any) {
    console.error('Error in users query API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})