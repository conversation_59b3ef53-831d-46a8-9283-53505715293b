import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    const userId = getRouterParam(event, 'id')
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 读取用户数据和角色数据
    const users = readJsonData('users.json')
    const roles = readJsonData('roles.json')
    
    const user = users.find((u: any) => u.user_id === userId)
    
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 获取用户的角色
    const userRoles = roles.filter((role: any) => 
      user.role_ids && user.role_ids.includes(role.role_id)
    ).map((role: any) => ({
      role_id: role.role_id,
      role_name: role.role_name,
      role_code: role.role_code,
      assigned_date: user.created_date // 简化处理
    }))
    
    return createSuccessResponse(userRoles, 'User roles retrieved successfully')

  } catch (error) {
    console.error('Error in user roles API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})