import { readJsonData, writeJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../../utils/helpers'

export default defineEventHandler(async (event: any) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(400)

  try {
    const userId = getRouterParam(event, 'id')
    const body = await readBody(event)
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    if (!body.role_id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Role ID is required'
      })
    }

    // 读取用户数据和角色数据
    const users = readJsonData('users.json')
    const roles = readJsonData('roles.json')
    
    const userIndex = users.findIndex((u: any) => u.user_id === userId)
    
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    const role = roles.find((r: any) => r.role_id === body.role_id)
    
    if (!role) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Role not found'
      })
    }

    if (role.status !== 'ACTIVE') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Cannot assign inactive role'
      })
    }

    // 检查用户是否已经有这个角色
    const user = users[userIndex]
    if (!user.role_ids) {
      user.role_ids = []
    }

    if (user.role_ids.includes(body.role_id)) {
      throw createError({
        statusCode: 409,
        statusMessage: 'User already has this role'
      })
    }

    // 分配角色
    user.role_ids.push(body.role_id)
    user.updated_date = new Date().toISOString()
    
    // 保存到文件
    writeJsonData('users.json', users)
    
    return createSuccessResponse(null, 'Role assigned successfully')

  } catch (error: any) {
    console.error('Error in assign role API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})