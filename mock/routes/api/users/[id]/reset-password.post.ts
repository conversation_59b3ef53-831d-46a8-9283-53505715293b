import { readJsonData, writeJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(400)

  try {
    const userId = getRouterParam(event, 'id')
    const body = await readBody(event)
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    if (!body.new_password) {
      throw createError({
        statusCode: 400,
        statusMessage: 'New password is required'
      })
    }

    // 简单的密码验证
    if (body.new_password.length < 8) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Password must be at least 8 characters long'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    const userIndex = users.findIndex((u: any) => u.user_id === userId)
    
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 更新用户密码（在实际应用中，这里应该加密密码）
    users[userIndex].updated_date = new Date().toISOString()
    // 注意：在mock中我们不实际存储密码，只是更新时间戳
    
    // 保存到文件
    writeJsonData('users.json', users)
    
    return createSuccessResponse(null, 'Password reset successfully')

  } catch (error) {
    console.error('Error in password reset API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})