import { readJsonData, writeJsonData, simulateD<PERSON>y, createSuccessResponse, shouldSimulateError } from '../../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    const userId = getRouterParam(event, 'id')
    const roleId = getRouterParam(event, 'roleId')
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    if (!roleId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Role ID is required'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    
    const userIndex = users.findIndex((u: any) => u.user_id === userId)
    
    if (userIndex === -1) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    const user = users[userIndex]
    
    if (!user.role_ids || !user.role_ids.includes(roleId)) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User does not have this role'
      })
    }

    // 移除角色
    user.role_ids = user.role_ids.filter((id: string) => id !== roleId)
    user.updated_date = new Date().toISOString()
    
    // 保存到文件
    writeJsonData('users.json', users)
    
    return createSuccessResponse(null, 'Role removed successfully')

  } catch (error) {
    console.error('Error in remove role API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})