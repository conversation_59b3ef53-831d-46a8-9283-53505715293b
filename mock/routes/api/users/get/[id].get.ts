import { readJsonData, simulateDelay, createSuccessResponse, createErrorResponse, shouldSimulateError } from '../../../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    const userId = getRouterParam(event, 'id')
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 读取用户数据
    const users = readJsonData('users.json')
    const roles = readJsonData('roles.json')
    const organizations = readJsonData('organizations.json')
    
    const user = users.find((u: any) => u.user_id === userId)
    
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 构建用户详情，包含角色信息
    const userRoles = roles.filter((role: any) => 
      user.role_ids && user.role_ids.includes(role.role_id)
    ).map((role: any) => ({
      role_id: role.role_id,
      role_name: role.role_name,
      role_code: role.role_code,
      assigned_date: user.created_date // 简化处理，实际应该有专门的分配时间
    }))

    // 获取部门信息
    const department = organizations.find((org: any) => 
      org.org_id === user.department_id
    )

    const userDetail = {
      ...user,
      roles: userRoles,
      department: department || null
    }
    
    return createSuccessResponse(userDetail, 'User detail retrieved successfully')

  } catch (error) {
    console.error('Error in user detail API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})