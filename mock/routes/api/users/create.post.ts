import { readJsonData, writeJsonD<PERSON>, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'
import { verifyAccessToken, extractTokenFromHeader } from '../../../utils/jwt'

export default defineEventHandler(async (event) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(500)

  try {
    // 验证认证
    const authHeader = getHeader(event, 'authorization')
    const token = extractTokenFromHeader(authHeader)

    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing authorization token'
      })
    }

    // 验证 JWT Token
    const payload = verifyAccessToken(token)
    if (!payload) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    const body = await readBody(event)
    
    // 验证必填字段
    if (!body.username || !body.password || !body.name) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Username, password and name are required'
      })
    }

    // 读取现有用户数据
    const users = readJsonData('users.json')
    
    // 检查用户名是否已存在
    const existingUser = users.find((u: any) => u.username === body.username)
    if (existingUser) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Username already exists'
      })
    }

    // 检查员工编号是否已存在（如果提供了）
    if (body.employee_id) {
      const existingEmployeeId = users.find((u: any) => u.employee_id === body.employee_id)
      if (existingEmployeeId) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Employee ID already exists'
        })
      }
    }

    // 检查邮箱是否已存在（如果提供了）
    if (body.email) {
      const existingEmail = users.find((u: any) => u.email === body.email)
      if (existingEmail) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Email already exists'
        })
      }
    }

    // 生成新用户ID
    const newUserId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 获取部门信息
    const organizations = readJsonData('organizations.json')
    const department = organizations.find((org: any) => org.org_id === body.department_id)
    
    // 创建新用户对象
    const newUser = {
      user_id: newUserId,
      username: body.username,
      name: body.name,
      employee_id: body.employee_id || null,
      email: body.email || null,
      phone: body.phone || null,
      department_id: body.department_id || null,
      department_name: department ? department.org_name : null,
      status: body.status || 'ACTIVE',
      role_ids: [], // 新用户默认没有角色
      last_login_date: null,
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString()
    }

    // 添加到用户列表
    users.push(newUser)
    
    // 保存到文件
    writeJsonData('users.json', users)
    
    // 返回创建的用户（不包含密码）
    const { password, ...userResponse } = newUser
    
    return createSuccessResponse(userResponse, 'User created successfully')

  } catch (error: any) {
    console.error('Error in user creation API:', error)
    if (error.statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})