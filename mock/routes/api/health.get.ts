import { simulateDelay, createSuccessResponse } from '../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 轻微延迟
  await simulateDelay(100)

  const uptime = process.uptime()
  const timestamp = new Date().toISOString()

  return createSuccessResponse({
    status: 'healthy',
    uptime: `${Math.floor(uptime)}s`,
    timestamp,
    version: '1.0.0',
    environment: 'development'
  }, 'Mock API is running')
})
