import { readJsonData, simulateDelay, createSuccessResponse, shouldSimulateError } from '../../../utils/helpers'

export default defineEventHandler(async (event: any) => {
  // 检查是否需要模拟错误
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟网络延迟
  await simulateDelay(300)

  try {
    // 读取组织架构数据
    const organizations = readJsonData('organizations.json')
    
    // 构建树形结构
    const buildTree = (items: any[], parentId: string | null = null, level: number = 0): any[] => {
      return items
        .filter(item => item.parent_id === parentId)
        .map(item => ({
          ...item,
          level,
          children: buildTree(items, item.org_id, level + 1)
        }))
    }

    const tree = buildTree(organizations)
    
    return createSuccessResponse(tree, 'Organization tree retrieved successfully')

  } catch (error) {
    console.error('Error in organization tree API:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})