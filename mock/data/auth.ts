// 认证相关的数据和配置
export interface User {
  id: number;
  username: string;
  password: string; // 在实际应用中应该是加密的
  email: string;
  name: string;
  avatar?: string;
  role: string;
  permissions: string[];
  status: 'active' | 'inactive' | 'locked';
  loginAttempts: number;
  lastLoginTime?: string;
  lockUntil?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface JWTPayload {
  sub: string; // user id
  username: string;
  role: string;
  permissions: string[];
  iat: number; // issued at
  exp: number; // expires at
  jti: string; // JWT ID
}

// JWT 配置
export const JWT_CONFIG = {
  SECRET: 'mock-jwt-secret-key-for-development-only',
  ACCESS_TOKEN_EXPIRES_IN: 60 * 60, // 1 hour in seconds
  REFRESH_TOKEN_EXPIRES_IN: 7 * 24 * 60 * 60, // 7 days in seconds
  ISSUER: 'glass4-erp-mock-api',
  ALGORITHM: 'HS256' as const
};

// 登录安全配置
export const LOGIN_SECURITY = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes in milliseconds
  PASSWORD_MIN_LENGTH: 6
};

// 模拟用户数据
export const MOCK_USERS: User[] = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123', // 实际应用中应该是 bcrypt 加密
    email: '<EMAIL>',
    name: '系统管理员',
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
    role: 'admin',
    permissions: [
      'user:read', 'user:write', 'user:delete',
      'system:read', 'system:write', 'system:config',
      'report:read', 'report:write',
      'dashboard:read'
    ],
    status: 'active',
    loginAttempts: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'manager',
    password: 'manager123',
    email: '<EMAIL>',
    name: '部门经理',
    avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
    role: 'manager',
    permissions: [
      'user:read', 'user:write',
      'report:read', 'report:write',
      'dashboard:read'
    ],
    status: 'active',
    loginAttempts: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    username: 'user',
    password: 'user123',
    email: '<EMAIL>',
    name: '普通用户',
    avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
    role: 'user',
    permissions: [
      'dashboard:read',
      'report:read'
    ],
    status: 'active',
    loginAttempts: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 4,
    username: 'locked_user',
    password: 'locked123',
    email: '<EMAIL>',
    name: '被锁定用户',
    role: 'user',
    permissions: ['dashboard:read'],
    status: 'locked',
    loginAttempts: 5,
    lockUntil: new Date(Date.now() + LOGIN_SECURITY.LOCKOUT_DURATION).toISOString(),
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  }
];

// 存储活跃的 refresh tokens (在实际应用中应该使用数据库或 Redis)
export const ACTIVE_REFRESH_TOKENS = new Set<string>();

// 存储被撤销的 access tokens (JWT 黑名单)
export const REVOKED_ACCESS_TOKENS = new Set<string>();

// 用户查找辅助函数
export function findUserByUsername(username: string): User | undefined {
  return MOCK_USERS.find(user => user.username === username);
}

export function findUserById(id: number): User | undefined {
  return MOCK_USERS.find(user => user.id === id);
}

// 验证密码
export function validatePassword(plainPassword: string, hashedPassword: string): boolean {
  // 在实际应用中，这里应该使用 bcrypt.compare
  return plainPassword === hashedPassword;
}

// 检查用户是否被锁定
export function isUserLocked(user: User): boolean {
  if (user.status === 'locked' && user.lockUntil) {
    const lockUntil = new Date(user.lockUntil);
    return Date.now() < lockUntil.getTime();
  }
  return user.status === 'locked';
}

// 增加登录失败次数
export function incrementLoginAttempts(user: User): void {
  user.loginAttempts += 1;
  user.updatedAt = new Date().toISOString();
  
  if (user.loginAttempts >= LOGIN_SECURITY.MAX_LOGIN_ATTEMPTS) {
    user.status = 'locked';
    user.lockUntil = new Date(Date.now() + LOGIN_SECURITY.LOCKOUT_DURATION).toISOString();
  }
}

// 重置登录失败次数
export function resetLoginAttempts(user: User): void {
  user.loginAttempts = 0;
  user.status = 'active';
  user.lockUntil = undefined;
  user.lastLoginTime = new Date().toISOString();
  user.updatedAt = new Date().toISOString();
}
