# Glass4 ERP Mock API 服务

基于 Nitro 的独立 Mock API 服务，用于 Glass4 ERP 前端开发阶段的 API 模拟。

## 🚀 快速开始

### 启动服务

```bash
# 在项目根目录启动完整开发环境（前端 + Mock API）
pnpm dev

# 或者单独启动 Mock API 服务
cd mock
pnpm dev
```

Mock API 服务将在 `http://localhost:3001` 启动。

### 验证服务

访问健康检查接口验证服务是否正常运行：
```bash
curl http://localhost:3001/api/health
```

## 📁 项目结构

```
mock/
├── data/              # 静态 JSON Mock 数据
│   ├── users.json     # 用户数据
│   └── products.json  # 产品数据
├── routes/            # Nitro 路由定义
│   └── api/
│       ├── users/
│       │   ├── index.get.ts    # GET /api/users
│       │   └── [id].get.ts     # GET /api/users/:id
│       ├── products/
│       │   ├── index.get.ts    # GET /api/products
│       │   ├── index.post.ts   # POST /api/products
│       │   └── [id].get.ts     # GET /api/products/:id
│       ├── test/
│       │   ├── error.get.ts    # 错误模拟测试
│       │   └── delay.get.ts    # 延迟模拟测试
│       └── health.get.ts       # 健康检查
├── utils/             # 辅助函数
│   └── helpers.ts     # 通用工具函数
├── nitro.config.ts    # Nitro 配置
├── package.json       # 依赖配置
└── README.md          # 本文档
```

## 🔌 API 接口

### 用户管理

#### 获取用户列表
```http
GET /api/users
```

**查询参数：**
- `department` - 按部门筛选
- `status` - 按状态筛选 (active/inactive)
- `role` - 按角色筛选 (admin/manager/user)
- `search` - 搜索用户名或邮箱
- `page` - 页码 (默认: 1)
- `limit` - 每页数量 (默认: 10)

**示例：**
```bash
curl "http://localhost:3001/api/users?department=IT部门&status=active&page=1&limit=5"
```

#### 获取单个用户
```http
GET /api/users/:id
```

**示例：**
```bash
curl http://localhost:3001/api/users/1
```

### 产品管理

#### 获取产品列表
```http
GET /api/products
```

**查询参数：**
- `category` - 按分类筛选
- `status` - 按状态筛选 (available/out_of_stock)
- `supplier` - 按供应商筛选
- `search` - 搜索产品名称或编码
- `minPrice` - 最低价格
- `maxPrice` - 最高价格
- `page` - 页码 (默认: 1)
- `limit` - 每页数量 (默认: 10)

#### 创建产品
```http
POST /api/products
```

**请求体：**
```json
{
  "name": "产品名称",
  "code": "PRODUCT-001",
  "category": "产品分类",
  "specification": "规格说明",
  "price": 100.00,
  "stock": 50,
  "unit": "个",
  "supplier": "供应商名称",
  "description": "产品描述"
}
```

## 🧪 高级功能

### 模拟网络延迟

所有接口都内置了模拟延迟（200-600ms），也可以通过测试接口自定义延迟：

```bash
# 模拟 2 秒延迟
curl "http://localhost:3001/api/test/delay?ms=2000"
```

### 模拟错误状态

#### 方法 1：查询参数
```bash
# 模拟 500 错误
curl "http://localhost:3001/api/users?error=true&errorCode=500&errorMessage=服务器内部错误"

# 模拟 404 错误
curl "http://localhost:3001/api/users?error=true&errorCode=404&errorMessage=资源未找到"
```

#### 方法 2：请求头
```bash
curl -H "X-Simulate-Error: true" \
     -H "X-Error-Code: 403" \
     -H "X-Error-Message: 权限不足" \
     http://localhost:3001/api/users
```

#### 方法 3：专用测试接口
```bash
# 模拟指定错误
curl "http://localhost:3001/api/test/error?code=401&message=未授权访问&delay=1000"
```

## 🔧 添加新的 Mock 接口

### 1. 创建路由文件

在 `routes/api/` 目录下创建新的路由文件，文件命名规则：
- `filename.get.ts` - GET 请求
- `filename.post.ts` - POST 请求
- `filename.put.ts` - PUT 请求
- `filename.delete.ts` - DELETE 请求
- `[param].get.ts` - 动态路由参数

### 2. 实现路由处理器

```typescript
import { simulateDelay, createSuccessResponse, shouldSimulateError } from '../../utils/helpers'

export default defineEventHandler(async (event) => {
  // 错误模拟检查
  const errorCheck = shouldSimulateError(event)
  if (errorCheck.shouldError) {
    await simulateDelay(200)
    throw createError({
      statusCode: errorCheck.errorCode!,
      statusMessage: errorCheck.errorMessage!
    })
  }

  // 模拟延迟
  await simulateDelay(300)

  // 业务逻辑
  const data = { message: 'Hello World' }
  
  return createSuccessResponse(data, 'Success')
})
```

### 3. 添加数据文件（可选）

如果需要静态数据，在 `data/` 目录下创建 JSON 文件：

```json
[
  {
    "id": 1,
    "name": "示例数据"
  }
]
```

## 🌐 环境配置

项目支持通过环境变量切换 API 源：

```bash
# .env.development
VITE_USE_MOCK=true              # 启用 Mock API
VITE_API_BASE_URL=http://localhost:5173/api
VITE_MOCK_API_URL=http://localhost:3001/api
```

## 📝 注意事项

1. **端口冲突**：确保 3001 端口未被占用
2. **CORS 配置**：已在 `nitro.config.ts` 中配置 CORS 支持
3. **数据持久化**：当前版本不支持数据持久化，重启服务后数据重置
4. **性能考虑**：仅用于开发环境，不适用于生产环境

## 🔍 故障排除

### Mock API 无法启动
1. 检查端口 3001 是否被占用
2. 确认 `mock/package.json` 中的依赖已安装
3. 查看控制台错误信息

### 前端无法访问 Mock API
1. 确认 Vite 代理配置正确
2. 检查 `.env.development` 中的配置
3. 验证 Mock API 服务是否正常运行

### 接口返回 404
1. 检查路由文件命名是否正确
2. 确认文件位置是否在 `routes/api/` 目录下
3. 验证 Nitro 服务是否正确加载路由
