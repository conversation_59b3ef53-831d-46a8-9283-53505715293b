import { defineNitroConfig } from 'nitropack/config'

export default defineNitroConfig({
  // 兼容性日期
  compatibilityDate: '2025-08-03',

  // 开发服务器配置
  devServer: {
    port: 3002,
    host: 'localhost'
  },
  
  // 路由目录
  srcDir: '.',
  
  // API 路由目录
  apiDir: './routes',
  
  // 启用 CORS 支持
  cors: true,
  
  // 开发模式配置
  dev: true,
  
  // 实验性功能
  experimental: {
    wasm: false
  },
  
  // 路由规则
  routeRules: {
    '/api/**': {
      cors: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    }
  }
})
