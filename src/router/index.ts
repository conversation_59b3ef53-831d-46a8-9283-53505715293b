import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginPage.vue'),
    meta: {
      requiresAuth: false,
      title: '登录'
    }
  },
  // 主应用路由
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/DashboardPage.vue'),
    meta: {
      requiresAuth: true,
      title: '仪表盘'
    }
  },
  // 用户管理
  {
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('@/views/user-management.vue'),
    meta: {
      requiresAuth: true,
      title: '用户管理',
      permissions: ['user:view'] // 需要的权限
    }
  },
  // 角色管理
  {
    path: '/role-management',
    name: 'RoleManagement',
    component: () => import('@/views/RoleManagement.vue'),
    meta: {
      requiresAuth: true,
      title: '角色管理',
      permissions: ['role:view'] // 需要的权限
    }
  },
  // 测试页面
  {
    path: '/test-auth',
    name: 'TestAuth',
    component: () => import('@/views/TestAuthPage.vue'),
    meta: {
      requiresAuth: false,
      title: '认证测试'
    }
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundPage.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 玻璃生产管理系统`
  }

  // 等待认证状态初始化完成
  if (!authStore.isInitialized) {
    console.log('[Router] 等待认证状态初始化...')
    // 等待初始化完成
    await new Promise<void>((resolve) => {
      const checkInitialized = () => {
        if (authStore.isInitialized) {
          resolve()
        } else {
          setTimeout(checkInitialized, 50)
        }
      }
      checkInitialized()
    })
    console.log('[Router] 认证状态初始化完成')
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    console.log('[Router] 检查认证状态:', {
      isAuthenticated: authStore.isAuthenticated,
      hasToken: !!authStore.accessToken,
      hasUser: !!authStore.user
    })
    
    // 如果没有登录，跳转到登录页
    if (!authStore.isAuthenticated) {
      console.log('[Router] 未认证，跳转到登录页')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 注意：在路由守卫中不主动验证token
    // 让API请求自然失败，避免误判
    // 只有在用户主动操作时才进行token验证
  } else {
    // 如果已经登录，访问登录页时跳转到首页
    if (to.path === '/login' && authStore.isAuthenticated) {
      console.log('[Router] 已认证用户访问登录页，跳转到首页')
      next('/')
      return
    }
  }

  console.log('[Router] 路由检查通过，继续导航')
  next()
})

export default router