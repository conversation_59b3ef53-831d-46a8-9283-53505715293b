import { reactive, computed } from 'vue'

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

export interface ValidationSchema {
  [field: string]: ValidationRule
}

export function useFormValidation<T extends Record<string, any>>(
  formData: T,
  schema: ValidationSchema
) {
  const errors = reactive<Record<keyof T, string>>({} as Record<keyof T, string>)
  const touched = reactive<Record<keyof T, boolean>>({} as Record<keyof T, boolean>)

  // 验证单个字段
  const validateField = (fieldName: keyof T): boolean => {
    const value = formData[fieldName]
    const rule = schema[fieldName as string]
    
    if (!rule) {
      errors[fieldName] = ''
      return true
    }

    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[fieldName] = `${String(fieldName)}是必填项`
      return false
    }

    // 最小长度验证
    if (rule.minLength && value && value.length < rule.minLength) {
      errors[fieldName] = `${String(fieldName)}至少需要${rule.minLength}个字符`
      return false
    }

    // 最大长度验证
    if (rule.maxLength && value && value.length > rule.maxLength) {
      errors[fieldName] = `${String(fieldName)}不能超过${rule.maxLength}个字符`
      return false
    }

    // 正则表达式验证
    if (rule.pattern && value && !rule.pattern.test(value)) {
      errors[fieldName] = `${String(fieldName)}格式不正确`
      return false
    }

    // 自定义验证
    if (rule.custom) {
      const customError = rule.custom(value)
      if (customError) {
        errors[fieldName] = customError
        return false
      }
    }

    errors[fieldName] = ''
    return true
  }

  // 验证所有字段
  const validateAll = (): boolean => {
    let isValid = true
    for (const fieldName in schema) {
      const fieldValid = validateField(fieldName as keyof T)
      if (!fieldValid) {
        isValid = false
      }
      touched[fieldName as keyof T] = true
    }
    return isValid
  }

  // 清除指定字段的错误
  const clearError = (fieldName: keyof T) => {
    errors[fieldName] = ''
  }

  // 清除所有错误
  const clearAllErrors = () => {
    for (const fieldName in errors) {
      errors[fieldName] = ''
    }
  }

  // 设置字段为已触摸
  const setTouched = (fieldName: keyof T) => {
    touched[fieldName] = true
  }

  // 检查表单是否有效
  const isValid = computed(() => {
    return Object.values(errors).every(error => !error)
  })

  // 检查表单是否有错误
  const hasErrors = computed(() => {
    return Object.values(errors).some(error => !!error)
  })

  // 获取错误数量
  const errorCount = computed(() => {
    return Object.values(errors).filter(error => !!error).length
  })

  return {
    errors,
    touched,
    validateField,
    validateAll,
    clearError,
    clearAllErrors,
    setTouched,
    isValid,
    hasErrors,
    errorCount
  }
}

// 常用验证规则
export const commonRules = {
  required: { required: true },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value: string) => {
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return '请输入有效的邮箱地址'
      }
      return null
    }
  },
  username: {
    required: true,
    minLength: 3,
    maxLength: 20,
    custom: (value: string) => {
      if (value && !/^[a-zA-Z0-9_@.-]+$/.test(value)) {
        return '用户名只能包含字母、数字、下划线、@、点和减号'
      }
      return null
    }
  },
  password: {
    required: true,
    minLength: 6,
    maxLength: 50,
    custom: (value: string) => {
      if (value && value.length >= 6) {
        // 可选：添加密码强度验证
        const hasLetter = /[a-zA-Z]/.test(value)
        const hasNumber = /\d/.test(value)
        if (!hasLetter || !hasNumber) {
          return '密码应包含字母和数字'
        }
      }
      return null
    }
  }
}