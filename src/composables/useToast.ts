import { ref } from 'vue'

export interface ToastOptions {
  title: string
  description?: string
  variant?: 'default' | 'destructive' | 'success'
  duration?: number
}

export interface Toast extends ToastOptions {
  id: string
  timestamp: number
}

const toasts = ref<Toast[]>([])

export function useToast() {
  const toast = (options: ToastOptions) => {
    const id = Date.now().toString()
    const newToast: Toast = {
      id,
      timestamp: Date.now(),
      duration: 5000,
      variant: 'default',
      ...options
    }
    
    toasts.value.push(newToast)
    
    // 自动移除toast
    setTimeout(() => {
      removeToast(id)
    }, newToast.duration)
    
    return id
  }
  
  const removeToast = (id: string) => {
    const index = toasts.value.findIndex(t => t.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }
  
  const clearAllToasts = () => {
    toasts.value = []
  }
  
  return {
    toast,
    toasts,
    removeToast,
    clearAllToasts
  }
}