<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <div class="space-y-6 pb-6">
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900" :class="titleClass">
          {{ title }}
        </h3>
        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors" @click="closeDialog">
          <XIcon class="w-5 h-5" />
        </button>
      </div>

      <div class="p-6">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center" :class="iconContainerClass">
            <AlertTriangleIcon v-if="type === 'warning'" class="w-6 h-6" />
            <XCircleIcon v-else-if="type === 'danger'" class="w-6 h-6" />
            <InfoIcon v-else-if="type === 'info'" class="w-6 h-6" />
            <CheckCircleIcon v-else class="w-6 h-6" />
          </div>

          <div class="flex-1 min-w-0">
            <p class="text-sm text-gray-900" v-html="message"></p>
            
            <div v-if="details" class="mt-4">
              <button
                class="flex items-center text-sm text-blue-600 hover:text-blue-700"
                @click="showDetails = !showDetails"
              >
                {{ showDetails ? '隐藏详情' : '显示详情' }}
                <ChevronDownIcon
                  class="w-4 h-4 ml-1 transition-transform"
                  :class="{ 'rotate-180': showDetails }"
                />
              </button>

              <div v-if="showDetails" class="mt-3 p-3 bg-gray-50 rounded-md">
                <p v-for="(detail, index) in details" :key="index" class="text-sm text-gray-600 mb-1 last:mb-0">
                  {{ detail }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
        @click="$emit('cancel')"
        :disabled="loading"
      >
        {{ cancelText }}
      </button>
      <button
        class="px-4 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
        :class="confirmButtonClass"
        @click="handleConfirm"
        :disabled="loading"
      >
        {{ loading ? loadingText : confirmText }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  XIcon, 
  AlertTriangleIcon, 
  XCircleIcon, 
  InfoIcon, 
  CheckCircleIcon,
  ChevronDownIcon
} from 'lucide-vue-next'

interface Props {
  title?: string
  message: string
  details?: string[]
  type?: 'info' | 'warning' | 'danger' | 'success'
  confirmText?: string
  cancelText?: string
  loadingText?: string
  loading?: boolean
}

interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认操作',
  type: 'info',
  confirmText: '确认',
  cancelText: '取消',
  loadingText: '处理中...',
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const showDetails = ref(false)

// 计算属性
const titleClass = computed(() => {
  switch (props.type) {
    case 'warning': return 'text-yellow-600'
    case 'danger': return 'text-red-600'
    case 'success': return 'text-green-600'
    default: return 'text-blue-600'
  }
})

const iconContainerClass = computed(() => {
  switch (props.type) {
    case 'warning': return 'bg-yellow-100 text-yellow-600'
    case 'danger': return 'bg-red-100 text-red-600'
    case 'success': return 'bg-green-100 text-green-600'
    default: return 'bg-blue-100 text-blue-600'
  }
})

const confirmButtonClass = computed(() => {
  switch (props.type) {
    case 'warning': return 'bg-yellow-600 text-white border-yellow-600 hover:bg-yellow-700'
    case 'danger': return 'bg-red-600 text-white border-red-600 hover:bg-red-700'
    case 'success': return 'bg-green-600 text-white border-green-600 hover:bg-green-700'
    default: return 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700'
  }
})

// 方法
function handleConfirm() {
  emit('confirm')
}


</script>

