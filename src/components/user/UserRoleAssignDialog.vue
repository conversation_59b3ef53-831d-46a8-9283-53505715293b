<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <div class="space-y-6 pb-6">
        <!-- 用户信息 -->
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-blue-100">
              <UserIcon class="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">
                角色分配
              </h3>
              <p class="text-sm text-gray-500">
                为用户 "{{ user?.name }}" 分配系统角色
              </p>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span class="ml-3 text-gray-600">加载中...</span>
        </div>

        <!-- 角色分配内容 -->
        <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 已分配角色 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h4 class="text-base font-medium text-gray-900">已分配角色</h4>
              <div class="text-sm text-gray-500">
                {{ assignedRoles.length }} 个角色
              </div>
            </div>

            <div v-if="assignedRoles.length > 0" class="space-y-3 max-h-80 overflow-y-auto">
              <div 
                v-for="role in assignedRoles" 
                :key="role.role_id"
                class="flex items-center justify-between p-4 border border-green-200 bg-green-50 rounded-lg hover:border-green-300 transition-colors"
              >
                <div class="flex-1 space-y-1">
                  <div class="font-medium text-gray-900">{{ role.role_name }}</div>
                  <div class="text-sm text-blue-600 font-mono">{{ role.role_code }}</div>
                  <div class="text-sm text-gray-600" v-if="role.description">
                    {{ role.description }}
                  </div>
                </div>
                <button
                  @click="removeRole(role)"
                  class="ml-3 p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full transition-colors"
                  title="移除角色"
                >
                  <MinusIcon class="h-4 w-4" />
                </button>
              </div>
            </div>

            <div v-else class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-3">👤</div>
              <div>暂无分配角色</div>
            </div>
          </div>

          <!-- 可分配角色 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h4 class="text-base font-medium text-gray-900">可分配角色</h4>
              <div class="text-sm text-gray-500">
                {{ availableRoles.length }} 个角色
              </div>
            </div>

            <!-- 搜索框 -->
            <div class="relative">
              <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索角色..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div v-if="filteredAvailableRoles.length > 0" class="space-y-3 max-h-80 overflow-y-auto">
              <div 
                v-for="role in filteredAvailableRoles" 
                :key="role.role_id"
                class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div class="flex-1 space-y-1">
                  <div class="font-medium text-gray-900">{{ role.role_name }}</div>
                  <div class="text-sm text-blue-600 font-mono">{{ role.role_code }}</div>
                  <div class="text-sm text-gray-600" v-if="role.description">
                    {{ role.description }}
                  </div>
                </div>
                <button
                  @click="assignRole(role)"
                  class="ml-3 p-2 text-green-600 hover:text-green-800 hover:bg-green-100 rounded-full transition-colors"
                  title="分配角色"
                >
                  <PlusIcon class="h-4 w-4" />
                </button>
              </div>
            </div>

            <div v-else-if="searchQuery" class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-3">🔍</div>
              <div>未找到匹配的角色</div>
            </div>

            <div v-else class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-3">✅</div>
              <div>所有角色都已分配</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button 
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" 
        @click="$emit('cancel')"
      >
        关闭
      </button>
      <button 
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        :disabled="submitting"
        @click="handleSave"
      >
        <div v-if="submitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
        {{ submitting ? '保存中...' : '保存' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/stores/user.store'
import { useRoleStore } from '@/stores/role.store'
import type { User, Role } from '@/types/user.types'
import { UserIcon, SearchIcon, PlusIcon, MinusIcon } from 'lucide-vue-next'

interface Props {
  user?: User | null
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()
const roleStore = useRoleStore()

// 状态管理
const loading = ref(false)
const submitting = ref(false)
const searchQuery = ref('')
const assignedRoles = ref<Role[]>([])
const allRoles = ref<Role[]>([])

// 计算属性
const availableRoles = computed(() => {
  const assignedRoleIds = new Set(assignedRoles.value.map(role => role.role_id))
  return allRoles.value.filter(role => !assignedRoleIds.has(role.role_id))
})

const filteredAvailableRoles = computed(() => {
  if (!searchQuery.value) return availableRoles.value
  
  const query = searchQuery.value.toLowerCase()
  return availableRoles.value.filter(role =>
    role.role_name.toLowerCase().includes(query) ||
    role.role_code.toLowerCase().includes(query) ||
    role.description?.toLowerCase().includes(query)
  )
})

// 方法
async function loadData() {
  if (!props.user) return

  try {
    loading.value = true
    
    // 并行加载用户角色和所有角色
    const [userRoles, roles] = await Promise.all([
      userStore.fetchUserRoles(props.user.user_id),
      roleStore.fetchRoles()
    ])
    
    assignedRoles.value = userRoles.map(userRole => {
      const role = roles.find(r => r.role_id === userRole.role_id)
      return role || {
        role_id: userRole.role_id,
        role_name: userRole.role_name,
        role_code: userRole.role_code,
        description: '',
        status: 'ACTIVE' as const,
        created_date: '',
        updated_date: ''
      }
    })
    
    allRoles.value = roles
  } catch (error) {
    console.error('加载角色数据失败:', error)
  } finally {
    loading.value = false
  }
}

function assignRole(role: Role) {
  assignedRoles.value.push(role)
}

function removeRole(role: Role) {
  const index = assignedRoles.value.findIndex(r => r.role_id === role.role_id)
  if (index > -1) {
    assignedRoles.value.splice(index, 1)
  }
}

async function handleSave() {
  if (!props.user) return

  try {
    submitting.value = true
    const roleIds = assignedRoles.value.map(role => role.role_id)
    await userStore.assignUserRoles(props.user.user_id, roleIds)
    emit('success')
  } catch (error) {
    console.error('保存角色分配失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听用户变化
watch(() => props.user, (newUser) => {
  if (newUser) {
    loadData()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.user) {
    loadData()
  }
})
</script>
