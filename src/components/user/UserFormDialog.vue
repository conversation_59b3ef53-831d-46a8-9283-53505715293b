<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <form id="user-form" @submit.prevent="handleSubmit" class="space-y-6 pb-6">
        <div class="form-grid">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>

            <div class="form-field">
              <label class="form-label" for="username">
                用户名 <span class="required">*</span>
              </label>
              <input id="username" v-model="formData.username" type="text" class="form-input"
                :class="{ 'error': errors.username }" :disabled="isEditing" placeholder="请输入用户名"
                @blur="validateUsername" />
              <div v-if="errors.username" class="form-error">
                {{ errors.username }}
              </div>
              <div v-if="usernameChecking" class="form-help">
                正在检查用户名...
              </div>
            </div>

            <div class="form-field" v-if="!isEditing">
              <label class="form-label" for="password">
                密码 <span class="required">*</span>
              </label>
              <div class="password-input">
                <input id="password" v-model="formData.password" :type="showPassword ? 'text' : 'password'"
                  class="form-input" :class="{ 'error': errors.password }" placeholder="请输入密码" />
                <button type="button" class="password-toggle" @click="showPassword = !showPassword">
                  <EyeIcon v-if="!showPassword" class="w-4 h-4" />
                  <EyeOffIcon v-else class="w-4 h-4" />
                </button>
              </div>
              <div v-if="errors.password" class="form-error">
                {{ errors.password }}
              </div>
              <div class="form-help">
                密码长度至少8位，包含字母和数字
              </div>
            </div>

            <div class="form-field">
              <label class="form-label" for="name">
                姓名 <span class="required">*</span>
              </label>
              <input id="name" v-model="formData.name" type="text" class="form-input" :class="{ 'error': errors.name }"
                placeholder="请输入真实姓名" />
              <div v-if="errors.name" class="form-error">
                {{ errors.name }}
              </div>
            </div>

            <div class="form-field">
              <label class="form-label" for="employee_id">
                员工编号
              </label>
              <input id="employee_id" v-model="formData.employee_id" type="text" class="form-input"
                placeholder="请输入员工编号" />
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="form-section">
            <h4 class="section-title">联系信息</h4>

            <div class="form-field">
              <label class="form-label" for="email">
                邮箱
              </label>
              <input id="email" v-model="formData.email" type="email" class="form-input"
                :class="{ 'error': errors.email }" placeholder="请输入邮箱地址" />
              <div v-if="errors.email" class="form-error">
                {{ errors.email }}
              </div>
            </div>

            <div class="form-field">
              <label class="form-label" for="phone">
                手机号
              </label>
              <input id="phone" v-model="formData.phone" type="tel" class="form-input"
                :class="{ 'error': errors.phone }" placeholder="请输入手机号" />
              <div v-if="errors.phone" class="form-error">
                {{ errors.phone }}
              </div>
            </div>

            <div class="form-field">
              <label class="form-label" for="department">
                所属部门
              </label>
              <select id="department" v-model="formData.department_id" class="form-select">
                <option value="">请选择部门</option>
                <option v-for="dept in organizations" :key="dept.org_id" :value="dept.org_id">
                  {{ '　'.repeat(dept.level) }}{{ dept.org_name }}
                </option>
              </select>
            </div>

            <div class="form-field">
              <label class="form-label" for="status">
                状态
              </label>
              <select id="status" v-model="formData.status" class="form-select">
                <option value="ACTIVE">启用</option>
                <option value="INACTIVE">禁用</option>
              </select>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        @click="$emit('cancel')">
        取消
      </button>
      <button type="submit" form="user-form"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        :disabled="loading || !isFormValid" @click="handleSubmit">
        {{ loading ? '保存中...' : (isEditing ? '保存' : '创建') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useUserStore } from '@/stores/user.store'
import type { UserStatus } from '@/types/user.types'
import type { User, CreateUserRequest, UpdateUserRequest, Organization } from '@/types/user.types'
import { EyeIcon, EyeOffIcon } from 'lucide-vue-next'

interface Props {
  user?: User | null
  organizations: Organization[]
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  user: null
})

const emit = defineEmits<Emits>()

// Store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const usernameChecking = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  name: '',
  employee_id: '',
  email: '',
  phone: '',
  department_id: '',
  status: 'ACTIVE' as UserStatus
})

// 错误信息
const errors = reactive({
  username: '',
  password: '',
  name: '',
  email: '',
  phone: ''
})

// 计算属性
const isEditing = computed(() => !!props.user)

const isFormValid = computed(() => {
  const hasRequiredFields = formData.username && formData.name && (isEditing.value || formData.password)
  const hasNoErrors = !Object.values(errors).some(error => error)
  return hasRequiredFields && hasNoErrors
})

// 监听器
watch(() => props.user, (user) => {
  resetForm()
  if (user) {
    fillForm(user)
  }
  nextTick(() => {
    const firstInput = document.querySelector('.form-input') as HTMLInputElement
    firstInput?.focus()
  })
}, { immediate: true })

watch(() => formData.username, () => {
  if (errors.username) {
    errors.username = ''
  }
})

// 方法
function resetForm() {
  Object.assign(formData, {
    username: '',
    password: '',
    name: '',
    employee_id: '',
    email: '',
    phone: '',
    department_id: '',
    status: 'ACTIVE' as UserStatus
  })

  Object.assign(errors, {
    username: '',
    password: '',
    name: '',
    email: '',
    phone: ''
  })
}

function fillForm(user: User) {
  Object.assign(formData, {
    username: user.username,
    password: '', // 编辑时不显示密码
    name: user.name,
    employee_id: user.employee_id || '',
    email: user.email || '',
    phone: user.phone || '',
    department_id: user.department_id || '',
    status: user.status
  })
}

async function validateUsername() {
  if (!formData.username || isEditing.value) return

  // 基本格式验证
  if (formData.username.length < 3) {
    errors.username = '用户名长度至少3位'
    return
  }

  if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    return
  }

  // 检查用户名是否可用
  try {
    usernameChecking.value = true
    const available = await userStore.checkUsernameAvailable(formData.username)
    if (!available) {
      errors.username = '用户名已存在'
    }
  } catch (error) {
    console.error('检查用户名失败:', error)
  } finally {
    usernameChecking.value = false
  }
}

function validateForm() {
  let isValid = true

  // 用户名验证
  if (!formData.username) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (formData.username.length < 3) {
    errors.username = '用户名长度至少3位'
    isValid = false
  } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    isValid = false
  }

  // 密码验证（仅创建时）
  if (!isEditing.value) {
    if (!formData.password) {
      errors.password = '请输入密码'
      isValid = false
    } else if (formData.password.length < 8) {
      errors.password = '密码长度至少8位'
      isValid = false
    } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(formData.password)) {
      errors.password = '密码必须包含字母和数字'
      isValid = false
    }
  }

  // 姓名验证
  if (!formData.name) {
    errors.name = '请输入姓名'
    isValid = false
  } else if (formData.name.length < 2) {
    errors.name = '姓名长度至少2位'
    isValid = false
  }

  // 邮箱验证
  if (formData.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址'
      isValid = false
    }
  }

  // 手机号验证
  if (formData.phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.phone)) {
      errors.phone = '请输入有效的手机号'
      isValid = false
    }
  }

  return isValid
}

async function handleSubmit() {
  if (!validateForm()) return

  try {
    loading.value = true

    if (isEditing.value && props.user) {
      // 更新用户
      const updateData: UpdateUserRequest = {
        name: formData.name,
        employee_id: formData.employee_id || undefined,
        email: formData.email || undefined,
        phone: formData.phone || undefined,
        department_id: formData.department_id || undefined,
        status: formData.status
      }
      await userStore.editUser(props.user.user_id, updateData)
    } else {
      // 创建用户
      const createData: CreateUserRequest = {
        username: formData.username,
        password: formData.password,
        name: formData.name,
        employee_id: formData.employee_id || undefined,
        email: formData.email || undefined,
        phone: formData.phone || undefined,
        department_id: formData.department_id || undefined,
        status: formData.status
      }
      await userStore.addUser(createData)
    }

    emit('success')
  } catch (error) {
    console.error('保存用户失败:', error)
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
/* 对话框遮罩层 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* 对话框主体 */
.dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.dialog-close:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 对话框内容 */
.dialog-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

/* 表单区域 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

/* 表单字段 */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.required {
  color: #ef4444;
}

/* 表单输入 */
.form-input,
.form-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
  background-color: white;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.form-input.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.password-toggle:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.password-input .form-input {
  padding-right: 40px;
}

/* 错误信息 */
.form-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 2px;
}

/* 帮助信息 */
.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-outline {
  background-color: white;
  border-color: #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-overlay {
    padding: 10px;
  }

  .dialog {
    max-width: 100%;
    max-height: 95vh;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 16px;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .dialog-overlay {
    padding: 5px;
  }

  .dialog {
    max-height: 98vh;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 12px;
  }
}
</style>
