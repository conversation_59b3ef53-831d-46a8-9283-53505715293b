<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <div class="space-y-6 pb-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 当前角色 -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h4 class="text-base font-medium text-gray-900">当前角色</h4>
          <div class="text-sm text-gray-500">
            {{ userRoles.length }} 个角色
          </div>
        </div>

        <div v-if="userRoles.length > 0" class="space-y-3 max-h-96 overflow-y-auto">
          <div v-for="role in userRoles" :key="role.role_id"
            class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
            <div class="flex-1 space-y-1">
              <div class="font-medium text-gray-900">{{ role.role_name }}</div>
              <div class="text-sm text-blue-600 font-mono">{{ role.role_code }}</div>
              <div class="text-sm text-gray-600" v-if="role.description">
                {{ role.description }}
              </div>
            </div>
            <button
              class="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              @click="removeRole(role)" :disabled="loading" title="移除角色">
              <TrashIcon class="w-4 h-4" />
            </button>
          </div>
        </div>

        <div v-else class="text-center py-8">
          <div class="text-4xl mb-3">👤</div>
          <div class="text-gray-500">该用户暂未分配任何角色</div>
        </div>
      </div>

      <!-- 可分配角色 -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h4 class="text-base font-medium text-gray-900">可分配角色</h4>
          <div class="relative">
            <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input type="text" placeholder="搜索角色..." v-model="searchText"
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-48" />
          </div>
        </div>

        <div v-if="availableRoles.length > 0" class="space-y-3 max-h-96 overflow-y-auto">
          <div v-for="role in filteredAvailableRoles" :key="role.role_id"
            class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
            <div class="flex-1 space-y-1">
              <div class="font-medium text-gray-900">{{ role.role_name }}</div>
              <div class="text-sm text-blue-600 font-mono">{{ role.role_code }}</div>
              <div class="text-sm text-gray-600" v-if="role.description">
                {{ role.description }}
              </div>
            </div>
            <button
              class="p-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              @click="assignRole(role)" :disabled="loading" title="分配角色">
              <PlusIcon class="w-4 h-4" />
            </button>
          </div>
        </div>

        <div v-else-if="searchText" class="text-center py-8">
          <div class="text-4xl mb-3">🔍</div>
          <div class="text-gray-500">没有找到匹配的角色</div>
        </div>

        <div v-else class="text-center py-8">
          <div class="text-4xl mb-3">✅</div>
          <div class="text-gray-500">所有角色都已分配</div>
        </div>
        </div>
      </div>
    </div>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" @click="$emit('cancel')">
        关闭
      </button>
      <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" @click="handleSave">
        保存
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user.store'
import type { User, Role } from '@/types/user.types'
import { SearchIcon, PlusIcon, TrashIcon } from 'lucide-vue-next'

interface Props {
  user?: User | null
  roles: Role[]
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  user: null
})

const emit = defineEmits<Emits>()

// Store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const userRoles = ref<Role[]>([])
const searchText = ref('')

// 计算属性
const availableRoles = computed(() => {
  const userRoleIds = userRoles.value.map(role => role.role_id)
  return props.roles.filter(role =>
    !userRoleIds.includes(role.role_id) && role.status === 'ACTIVE'
  )
})

const filteredAvailableRoles = computed(() => {
  if (!searchText.value) return availableRoles.value

  const query = searchText.value.toLowerCase()
  return availableRoles.value.filter(role =>
    role.role_name.toLowerCase().includes(query) ||
    role.role_code.toLowerCase().includes(query) ||
    role.description?.toLowerCase().includes(query)
  )
})

// 方法
async function loadUserRoles() {
  if (!props.user) return

  try {
    loading.value = true
    const roleList = await userStore.fetchUserRoles(props.user.user_id)
    // 将UserRole转换为Role类型
    userRoles.value = roleList.map(userRole => {
      const role = props.roles.find(r => r.role_id === userRole.role_id)
      return role || {
        role_id: userRole.role_id,
        role_name: userRole.role_name,
        role_code: userRole.role_code,
        description: '',
        status: 'ACTIVE' as const,
        created_date: '',
        updated_date: ''
      }
    })
  } catch (error) {
    console.error('获取用户角色失败:', error)
    userRoles.value = []
  } finally {
    loading.value = false
  }
}

async function assignRole(role: Role) {
  if (!props.user) return

  try {
    loading.value = true
    await userStore.assignRole(props.user.user_id, role.role_id)

    // 添加到当前角色列表
    userRoles.value.push(role)

    emit('success')
  } catch (error) {
    console.error('分配角色失败:', error)
  } finally {
    loading.value = false
  }
}

async function removeRole(role: Role) {
  if (!props.user) return

  const confirmed = confirm(`确定要移除角色"${role.role_name}"吗？`)
  if (!confirmed) return

  try {
    loading.value = true
    await userStore.removeRole(props.user.user_id, role.role_id)

    // 从当前角色列表移除
    const index = userRoles.value.findIndex(r => r.role_id === role.role_id)
    if (index !== -1) {
      userRoles.value.splice(index, 1)
    }

    emit('success')
  } catch (error) {
    console.error('移除角色失败:', error)
  } finally {
    loading.value = false
  }
}

function handleSave() {
  emit('success')
}
</script>
