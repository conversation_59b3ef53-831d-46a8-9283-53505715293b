<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <!-- 加载状态 -->
      <div v-if="userStore.detailLoading" class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">加载中...</span>
      </div>

    <div v-else-if="userDetail" class="user-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label class="detail-label">用户名</label>
            <div class="detail-value">{{ userDetail.username }}</div>
          </div>
          <div class="detail-item">
            <label class="detail-label">姓名</label>
            <div class="detail-value">{{ userDetail.name }}</div>
          </div>
          <div class="detail-item">
            <label class="detail-label">员工编号</label>
            <div class="detail-value">{{ userDetail.employee_id || '-' }}</div>
          </div>
          <div class="detail-item">
            <label class="detail-label">状态</label>
            <div class="detail-value">
              <span :class="statusClass">
                {{ userDetail.status === 'ACTIVE' ? '活跃' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="detail-section">
        <h4 class="section-title">联系信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label class="detail-label">邮箱</label>
            <div class="detail-value">{{ userDetail.email || '-' }}</div>
          </div>
          <div class="detail-item">
            <label class="detail-label">手机号</label>
            <div class="detail-value">{{ userDetail.phone || '-' }}</div>
          </div>
          <div class="detail-item">
            <label class="detail-label">部门</label>
            <div class="detail-value">{{ userDetail.department_name || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 角色信息 -->
      <div class="detail-section">
        <div class="section-header">
          <h4 class="section-title">角色信息</h4>
          <button v-if="userStore.permissions.canAssignRole" @click="$emit('manage-roles', userDetail.user_id)"
            class="btn btn-sm btn-outline">
            <UserPlusIcon class="w-4 h-4" />
            管理角色
          </button>
        </div>
        <div v-if="userDetail.roles && userDetail.roles.length > 0" class="roles-list">
          <div v-for="role in userDetail.roles" :key="role.role_id" class="role-item">
            <div class="role-info">
              <div class="role-name">{{ role.role_name }}</div>
              <div class="role-code">{{ role.role_code }}</div>
            </div>
            <div class="role-date">
              分配时间: {{ formatDate(role.assigned_date) }}
            </div>
          </div>
        </div>
        <div v-else class="empty-roles">
          <UserXIcon class="w-12 h-12 text-gray-400" />
          <p class="empty-text">暂无分配角色</p>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="detail-section">
        <h4 class="section-title">系统信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label class="detail-label">最后登录</label>
            <div class="detail-value">
              {{ userDetail.last_login_date ? formatDate(userDetail.last_login_date) : '从未登录' }}
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">创建时间</label>
            <div class="detail-value">{{ formatDate(userDetail.created_date) }}</div>
          </div>
          <div class="detail-item">
            <label class="detail-label">更新时间</label>
            <div class="detail-value">{{ formatDate(userDetail.updated_date) }}</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="error-container">
      <AlertCircleIcon class="w-12 h-12 text-red-400" />
      <p class="error-text">加载用户详情失败</p>
      </div>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button v-if="userStore.permissions.canEdit && userDetail" @click="handleEdit"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
        <PencilIcon class="w-4 h-4 mr-2" />
        编辑
      </button>
      <button v-if="userStore.permissions.canResetPassword && userDetail" @click="handleResetPassword"
        class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
        <KeyIcon class="w-4 h-4 mr-2" />
        重置密码
      </button>
      <button v-if="userDetail" @click="handleManageRoles"
        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
        管理角色
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/stores/user.store'
import type { UserDetail } from '@/types/user.types'
import {
  UserPlusIcon,
  UserXIcon,
  AlertCircleIcon,
  PencilIcon,
  KeyIcon
} from 'lucide-vue-next'

interface Props {
  userId: string
}

interface Emits {
  (e: 'edit-user', user: UserDetail): void
  (e: 'reset-password', user: UserDetail): void
  (e: 'manage-roles', userId: string): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Store
const userStore = useUserStore()

// 响应式数据
const userDetail = ref<UserDetail | null>(null)

// 计算属性
const statusClass = computed(() => {
  if (!userDetail.value) return ''
  return userDetail.value.status === 'ACTIVE'
    ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800'
    : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800'
})

// 监听器
watch(() => props.userId, async (userId) => {
  if (userId) {
    try {
      userDetail.value = await userStore.fetchUserDetail(userId)
    } catch (error) {
      console.error('获取用户详情失败:', error)
      userDetail.value = null
    }
  } else {
    userDetail.value = null
  }
}, { immediate: true })

// 方法
function formatDate(dateStr: string) {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 事件处理函数
function handleEdit() {
  if (userDetail.value) {
    emit('edit-user', userDetail.value)
  }
}

function handleResetPassword() {
  if (userDetail.value) {
    emit('reset-password', userDetail.value)
  }
}

function handleManageRoles() {
  if (props.userId) {
    emit('manage-roles', props.userId)
  }
}
</script>

<style scoped>
/* 对话框遮罩层 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* 对话框主体 */
.dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.dialog-close:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 对话框内容 */
.dialog-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 16px;
  color: #6b7280;
  font-size: 14px;
}

/* 用户详情 */
.user-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 详情区域 */
.detail-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

/* 详情网格 */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* 详情项 */
.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

/* 状态样式 */
.status-active {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background-color: #dcfce7;
  color: #166534;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-inactive {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background-color: #fef2f2;
  color: #991b1b;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 角色列表 */
.roles-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.role-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.role-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.role-code {
  font-size: 12px;
  color: #6b7280;
}

.role-date {
  font-size: 12px;
  color: #6b7280;
}

/* 空状态 */
.empty-roles {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
}

.empty-text {
  margin-top: 12px;
  color: #6b7280;
  font-size: 14px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-outline {
  background-color: white;
  border-color: #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-overlay {
    padding: 10px;
  }

  .dialog {
    max-width: 100%;
    max-height: 95vh;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .role-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .dialog-overlay {
    padding: 5px;
  }

  .dialog {
    max-height: 98vh;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 12px;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    width: 100%;
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
