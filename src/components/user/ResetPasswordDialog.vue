<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <form id="reset-password-form" @submit.prevent="handleSubmit" class="space-y-6 pb-6">
      <div class="password-reset-form">
        <div class="user-info">
          <div class="info-item">
            <span class="info-label">用户名:</span>
            <span class="info-value">{{ user?.username }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">姓名:</span>
            <span class="info-value">{{ user?.name }}</span>
          </div>
          <div class="info-item" v-if="user?.department_name">
            <span class="info-label">部门:</span>
            <span class="info-value">{{ user.department_name }}</span>
          </div>
        </div>

        <div class="password-section">
          <div class="form-field">
            <label class="form-label" for="new_password">
              新密码 <span class="required">*</span>
            </label>
            <div class="password-input">
              <input id="new_password" v-model="formData.new_password" :type="showPassword ? 'text' : 'password'"
                class="form-input" :class="{ 'error': errors.new_password }" placeholder="请输入新密码"
                autocomplete="new-password" />
              <button type="button" class="password-toggle" @click="showPassword = !showPassword">
                <EyeIcon v-if="!showPassword" class="w-4 h-4" />
                <EyeOffIcon v-else class="w-4 h-4" />
              </button>
            </div>
            <div v-if="errors.new_password" class="form-error">
              {{ errors.new_password }}
            </div>
          </div>

          <div class="form-field">
            <label class="form-label" for="confirm_password">
              确认密码 <span class="required">*</span>
            </label>
            <div class="password-input">
              <input id="confirm_password" v-model="formData.confirm_password"
                :type="showConfirmPassword ? 'text' : 'password'" class="form-input"
                :class="{ 'error': errors.confirm_password }" placeholder="请再次输入新密码" autocomplete="new-password" />
              <button type="button" class="password-toggle" @click="showConfirmPassword = !showConfirmPassword">
                <EyeIcon v-if="!showConfirmPassword" class="w-4 h-4" />
                <EyeOffIcon v-else class="w-4 h-4" />
              </button>
            </div>
            <div v-if="errors.confirm_password" class="form-error">
              {{ errors.confirm_password }}
            </div>
          </div>

          <div class="password-strength">
            <div class="strength-label">密码强度:</div>
            <div class="strength-bar">
              <div class="strength-fill" :class="strengthClass" :style="{ width: strengthPercentage + '%' }"></div>
            </div>
            <div class="strength-text" :class="strengthClass">
              {{ strengthText }}
            </div>
          </div>

          <div class="password-requirements">
            <div class="requirements-title">密码要求:</div>
            <ul class="requirements-list">
              <li :class="{ 'valid': hasMinLength }">
                <CheckIcon v-if="hasMinLength" class="w-4 h-4" />
                <XIcon v-else class="w-4 h-4" />
                至少8个字符
              </li>
              <li :class="{ 'valid': hasLetters }">
                <CheckIcon v-if="hasLetters" class="w-4 h-4" />
                <XIcon v-else class="w-4 h-4" />
                包含字母
              </li>
              <li :class="{ 'valid': hasNumbers }">
                <CheckIcon v-if="hasNumbers" class="w-4 h-4" />
                <XIcon v-else class="w-4 h-4" />
                包含数字
              </li>
              <li :class="{ 'valid': hasSpecialChars }">
                <CheckIcon v-if="hasSpecialChars" class="w-4 h-4" />
                <XIcon v-else class="w-4 h-4" />
                包含特殊字符(推荐)
              </li>
            </ul>
          </div>
        </div>

        <div class="warning-section">
          <div class="warning-box">
            <AlertTriangleIcon class="warning-icon" />
            <div class="warning-content">
              <div class="warning-title">重要提醒</div>
              <div class="warning-text">
                重置密码后，用户需要使用新密码重新登录。请确保及时通知用户新密码。
              </div>
            </div>
          </div>
        </div>
        </div>
      </form>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        @click="$emit('cancel')">
        取消
      </button>
      <button type="submit" form="reset-password-form" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        :disabled="loading || !isFormValid" @click="handleSubmit">
        {{ loading ? '重置中...' : '确认重置' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useUserStore } from '@/stores/user.store'
import type { User } from '@/types/user.types'
import {
  EyeIcon,
  EyeOffIcon,
  CheckIcon,
  AlertTriangleIcon
} from 'lucide-vue-next'

interface Props {
  user?: User | null
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  user: null
})

const emit = defineEmits<Emits>()

// Store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 表单数据
const formData = reactive({
  new_password: '',
  confirm_password: ''
})

// 错误信息
const errors = reactive({
  new_password: '',
  confirm_password: ''
})

// 密码强度计算
const hasMinLength = computed(() => formData.new_password.length >= 8)
const hasLetters = computed(() => /[a-zA-Z]/.test(formData.new_password))
const hasNumbers = computed(() => /\d/.test(formData.new_password))
const hasSpecialChars = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(formData.new_password))

const passwordStrength = computed(() => {
  let score = 0
  if (hasMinLength.value) score += 25
  if (hasLetters.value) score += 25
  if (hasNumbers.value) score += 25
  if (hasSpecialChars.value) score += 25
  return score
})

const strengthPercentage = computed(() => passwordStrength.value)

const strengthClass = computed(() => {
  if (passwordStrength.value >= 75) return 'strong'
  if (passwordStrength.value >= 50) return 'medium'
  if (passwordStrength.value >= 25) return 'weak'
  return 'very-weak'
})

const strengthText = computed(() => {
  if (passwordStrength.value >= 75) return '强'
  if (passwordStrength.value >= 50) return '中等'
  if (passwordStrength.value >= 25) return '弱'
  return '很弱'
})

const isFormValid = computed(() => {
  return formData.new_password &&
    formData.confirm_password &&
    formData.new_password === formData.confirm_password &&
    hasMinLength.value &&
    hasLetters.value &&
    hasNumbers.value &&
    !errors.new_password &&
    !errors.confirm_password
})

// 监听器
watch(() => props.user, (user) => {
  resetForm()
  if (user) {
    nextTick(() => {
      const firstInput = document.querySelector('#new_password') as HTMLInputElement
      firstInput?.focus()
    })
  }
}, { immediate: true })

watch(() => formData.new_password, () => {
  validatePassword()
  if (formData.confirm_password) {
    validateConfirmPassword()
  }
})

watch(() => formData.confirm_password, () => {
  validateConfirmPassword()
})

// 方法
function resetForm() {
  formData.new_password = ''
  formData.confirm_password = ''
  errors.new_password = ''
  errors.confirm_password = ''
}

function validatePassword() {
  if (!formData.new_password) {
    errors.new_password = '请输入新密码'
    return false
  }

  if (formData.new_password.length < 8) {
    errors.new_password = '密码长度至少8位'
    return false
  }

  if (!hasLetters.value) {
    errors.new_password = '密码必须包含字母'
    return false
  }

  if (!hasNumbers.value) {
    errors.new_password = '密码必须包含数字'
    return false
  }

  errors.new_password = ''
  return true
}

function validateConfirmPassword() {
  if (!formData.confirm_password) {
    errors.confirm_password = '请确认密码'
    return false
  }

  if (formData.new_password !== formData.confirm_password) {
    errors.confirm_password = '两次输入的密码不一致'
    return false
  }

  errors.confirm_password = ''
  return true
}

function validateForm() {
  const isPasswordValid = validatePassword()
  const isConfirmPasswordValid = validateConfirmPassword()
  return isPasswordValid && isConfirmPasswordValid
}

async function handleSubmit() {
  if (!validateForm() || !props.user) return

  try {
    loading.value = true
    await userStore.resetPassword(props.user.user_id, formData.new_password)
    emit('success')
  } catch (error) {
    console.error('重置密码失败:', error)
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
/* 对话框遮罩层 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* 对话框主体 */
.dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.dialog-close:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 对话框内容 */
.dialog-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* 密码重置表单 */
.password-reset-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 用户信息 */
.user-info {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

/* 密码部分 */
.password-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 表单字段 */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.form-input.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-toggle {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.password-toggle:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.password-input .form-input {
  padding-right: 40px;
}

/* 错误信息 */
.form-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

/* 密码强度 */
.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 6px;
}

.strength-label {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.strength-bar {
  flex: 1;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background-color: #ef4444;
}

.strength-fill.fair {
  background-color: #f59e0b;
}

.strength-fill.good {
  background-color: #10b981;
}

.strength-fill.strong {
  background-color: #059669;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.strength-text.weak {
  color: #ef4444;
}

.strength-text.fair {
  color: #f59e0b;
}

.strength-text.good {
  color: #10b981;
}

.strength-text.strong {
  color: #059669;
}

/* 密码要求 */
.password-requirements {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
}

.requirements-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
  transition: all 0.2s;
}

.requirements-list li.valid {
  color: #10b981;
}

.requirements-list li svg {
  flex-shrink: 0;
}

/* 警告部分 */
.warning-section {
  margin-top: 8px;
}

.warning-box {
  display: flex;
  gap: 12px;
  padding: 16px;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
}

.warning-icon {
  flex-shrink: 0;
  color: #f59e0b;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 4px;
}

.warning-text {
  font-size: 12px;
  color: #92400e;
  line-height: 1.4;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-outline {
  background-color: white;
  border-color: #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-overlay {
    padding: 10px;
  }

  .dialog {
    max-width: 100%;
    max-height: 95vh;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 16px;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .dialog-overlay {
    padding: 5px;
  }

  .dialog {
    max-height: 98vh;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 12px;
  }
}
</style>
