<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <form id="role-form" @submit.prevent="handleSubmit" class="space-y-6 pb-6">
    <!-- 角色名称 -->
    <div>
      <label for="role-name" class="block text-sm font-medium text-gray-700">
        角色名称 <span class="text-red-500">*</span>
      </label>
      <input id="role-name" v-model="formData.role_name" type="text" required :class="[
        'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
        errors.role_name ? 'border-red-300' : 'border-gray-300'
      ]" placeholder="请输入角色名称" @blur="validateRoleName" />
      <p v-if="errors.role_name" class="mt-1 text-sm text-red-600">{{ errors.role_name }}</p>
    </div>

    <!-- 角色编码 -->
    <div>
      <label for="role-code" class="block text-sm font-medium text-gray-700">
        角色编码 <span class="text-red-500">*</span>
      </label>
      <input id="role-code" v-model="formData.role_code" type="text" required :class="[
        'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
        errors.role_code ? 'border-red-300' : 'border-gray-300'
      ]" placeholder="请输入角色编码，如：ADMIN, USER" @blur="validateRoleCode" />
      <p v-if="errors.role_code" class="mt-1 text-sm text-red-600">{{ errors.role_code }}</p>
      <p class="mt-1 text-xs text-gray-500">角色编码用于系统识别，建议使用大写字母和下划线</p>
    </div>

    <!-- 描述 -->
    <div>
      <label for="description" class="block text-sm font-medium text-gray-700">
        描述
      </label>
      <textarea id="description" v-model="formData.description" rows="3"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        placeholder="请输入角色描述"></textarea>
    </div>

    <!-- 状态 -->
    <div>
      <label for="status" class="block text-sm font-medium text-gray-700">
        状态
      </label>
      <select id="status" v-model="formData.status"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
        <option value="ACTIVE">启用</option>
        <option value="INACTIVE">禁用</option>
      </select>
      </div>
      </form>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button type="button" @click="$emit('cancel')"
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
        取消
      </button>
      <button type="submit" form="role-form" :disabled="loading || !isFormValid"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
        @click="handleSubmit">
        <div v-if="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
        {{ isEdit ? '更新' : '创建' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRoleStore } from '@/stores/role.store'
import { useFormValidation } from '@/composables/useFormValidation'
import type { RoleListItem, CreateRoleRequest, UpdateRoleRequest } from '@/types/user.types'

interface Props {
  role?: RoleListItem | null
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  role: null
})

const emit = defineEmits<Emits>()

const roleStore = useRoleStore()

// 响应式数据
const loading = ref(false)
const formData = reactive({
  role_name: '',
  role_code: '',
  description: '',
  status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE'
})

// 表单验证规则
const validationSchema = {
  role_name: { required: true, minLength: 2, maxLength: 50 },
  role_code: { required: true, minLength: 2, maxLength: 20, pattern: /^[A-Z_]+$/ }
}

const {
  errors,
  validateField
} = useFormValidation(formData, validationSchema)

// 计算属性
const isEdit = computed(() => !!props.role)
const isFormValid = computed(() => {
  return formData.role_name &&
    formData.role_code &&
    !errors.role_name &&
    !errors.role_code
})

// 验证方法
const validateRoleName = async () => {
  // 使用 form validation 的验证方法
  const isValid = validateField('role_name')

  // 额外的重复性验证
  if (isValid && formData.role_name) {
    try {
      const available = await roleStore.checkRoleNameAvailable(
        formData.role_name,
        props.role?.role_id
      )
      if (!available) {
        errors.role_name = '角色名称已存在'
        return false
      }
    } catch (error) {
      console.error('验证角色名称失败:', error)
    }
  }

  return isValid
}

const validateRoleCode = async () => {
  // 使用 form validation 的验证方法
  const isValid = validateField('role_code')

  // 额外的重复性验证
  if (isValid && formData.role_code) {
    try {
      const available = await roleStore.checkRoleCodeAvailable(
        formData.role_code,
        props.role?.role_id
      )
      if (!available) {
        errors.role_code = '角色编码已存在'
        return false
      }
    } catch (error) {
      console.error('验证角色编码失败:', error)
    }
  }

  return isValid
}

// 表单处理
const handleSubmit = async () => {
  // 验证表单
  const isNameValid = await validateRoleName()
  const isCodeValid = await validateRoleCode()

  if (!isNameValid || !isCodeValid) {
    return
  }

  loading.value = true
  try {
    if (isEdit.value && props.role) {
      const updateData: UpdateRoleRequest = {
        role_name: formData.role_name,
        role_code: formData.role_code,
        description: formData.description || undefined,
        status: formData.status
      }
      await roleStore.editRole(props.role.role_id, updateData)
    } else {
      const createData: CreateRoleRequest = {
        role_name: formData.role_name,
        role_code: formData.role_code,
        description: formData.description || undefined,
        status: formData.status
      }
      await roleStore.addRole(createData)
    }

    emit('success')
  } catch (error) {
    console.error('保存角色失败:', error)
  } finally {
    loading.value = false
  }
}



// 初始化表单数据
const initFormData = () => {
  if (props.role) {
    formData.role_name = props.role.role_name
    formData.role_code = props.role.role_code
    formData.description = props.role.description || ''
    formData.status = props.role.status
  } else {
    formData.role_name = ''
    formData.role_code = ''
    formData.description = ''
    formData.status = 'ACTIVE'
  }

  // 清空错误
  errors.role_name = ''
  errors.role_code = ''
}

// 监听角色数据变化
watch(() => props.role, () => {
  initFormData()
}, { immediate: true })
</script>

<style scoped>
/* 对话框动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>