<template>
  <div>
    <div 
      :class="[
        'flex items-center p-3 hover:bg-gray-50',
        shouldHighlight ? 'bg-yellow-50' : '',
        level > 0 ? 'ml-6' : ''
      ]"
    >
      <div class="flex items-center space-x-3 flex-1">
        <!-- 展开/收起按钮 -->
        <button
          v-if="hasChildren"
          @click="handleToggleExpand"
          class="text-gray-400 hover:text-gray-600"
        >
          <ChevronRightIcon 
            :class="['h-4 w-4 transition-transform', isExpanded ? 'rotate-90' : '']"
          />
        </button>
        <div v-else class="w-4"></div>
        
        <!-- 选择框 -->
        <input
          type="checkbox"
          :checked="isSelected"
          @change="handleSelectionChange"
          class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
        />
        
        <!-- 权限信息 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ permission.permission_name }}
            </p>
            <span 
              :class="['inline-flex px-2 py-1 text-xs font-semibold rounded-full', getResourceTypeClass(permission.resource_type)]"
            >
              {{ getResourceTypeName(permission.resource_type) }}
            </span>
          </div>
          <div class="flex items-center space-x-4 mt-1">
            <p class="text-xs text-gray-500">{{ permission.permission_code }}</p>
            <p v-if="permission.description" class="text-xs text-gray-500 truncate">
              {{ permission.description }}
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 子权限 -->
    <div v-if="hasChildren && isExpanded">
      <PermissionTreeNode
        v-for="child in permission.children"
        :key="child.permission_id"
        :permission="child"
        :selected-permissions="selectedPermissions"
        :expanded-nodes="expandedNodes"
        :search-query="searchQuery"
        :level="level + 1"
        @toggle-selection="handleChildToggleSelection"
        @toggle-expand="handleChildToggleExpand"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChevronRightIcon } from '@heroicons/vue/24/outline'
import type { Permission } from '@/types/user.types'

interface Props {
  permission: Permission
  selectedPermissions: string[]
  expandedNodes: Set<string>
  searchQuery: string
  level?: number
}

interface Emits {
  (e: 'toggle-selection', permissionId: string, selected: boolean): void
  (e: 'toggle-expand', permissionId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  level: 0
})

const emit = defineEmits<Emits>()

// 计算属性
const isSelected = computed(() => 
  props.selectedPermissions.includes(props.permission.permission_id)
)

const isExpanded = computed(() => 
  props.expandedNodes.has(props.permission.permission_id)
)

const hasChildren = computed(() => 
  props.permission.children && props.permission.children.length > 0
)

const shouldHighlight = computed(() => {
  if (!props.searchQuery) return false
  const query = props.searchQuery.toLowerCase()
  return props.permission.permission_name.toLowerCase().includes(query) ||
         props.permission.permission_code.toLowerCase().includes(query) ||
         (props.permission.description && props.permission.description.toLowerCase().includes(query))
})

// 方法
const getResourceTypeClass = (type: string) => {
  const classMap: Record<string, string> = {
    'menu': 'bg-blue-100 text-blue-800',
    'action': 'bg-green-100 text-green-800',
    'data': 'bg-yellow-100 text-yellow-800',
    'api': 'bg-purple-100 text-purple-800',
    'page': 'bg-indigo-100 text-indigo-800'
  }
  return classMap[type] || 'bg-gray-100 text-gray-800'
}

const getResourceTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'menu': '菜单',
    'action': '操作',
    'data': '数据',
    'api': '接口',
    'page': '页面'
  }
  return typeMap[type] || type
}

const handleSelectionChange = (event: Event) => {
  const checked = (event.target as HTMLInputElement).checked
  emit('toggle-selection', props.permission.permission_id, checked)
}

const handleToggleExpand = () => {
  if (hasChildren.value) {
    emit('toggle-expand', props.permission.permission_id)
  }
}

const handleChildToggleSelection = (permissionId: string, selected: boolean) => {
  emit('toggle-selection', permissionId, selected)
}

const handleChildToggleExpand = (permissionId: string) => {
  emit('toggle-expand', permissionId)
}
</script>

<style scoped>
/* 树形结构的缩进线条 */
.ml-6 {
  border-left: 1px solid #e5e7eb;
}
</style>