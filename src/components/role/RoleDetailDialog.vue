<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <!-- 加载状态 -->
      <div v-if="roleStore.detailLoading" class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="!roleStore.detailLoading && !roleDetail" class="flex flex-col items-center justify-center py-8">
        <div class="text-gray-400 mb-2">
          <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z">
            </path>
          </svg>
        </div>
        <p class="text-gray-500 text-center">
          无法加载角色详情<br>
          <span class="text-sm">请检查网络连接或稍后重试</span>
        </p>
      </div>

      <!-- 角色详情内容 -->
      <div v-else-if="roleDetail" class="space-y-6">
        <!-- 基本信息 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-medium text-gray-900 mb-4">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">角色名称</label>
              <p class="mt-1 text-sm text-gray-900">{{ roleDetail.role_name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">角色编码</label>
              <p class="mt-1 text-sm text-gray-900">{{ roleDetail.role_code }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">状态</label>
              <span
                :class="roleDetail.status === 'ACTIVE' ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800' : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800'">
                {{ roleDetail.status === 'ACTIVE' ? '启用' : '禁用' }}
              </span>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">创建时间</label>
              <p class="mt-1 text-sm text-gray-900">{{ formatDate(roleDetail.created_date) }}</p>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700">描述</label>
              <p class="mt-1 text-sm text-gray-900">{{ roleDetail.description || '暂无描述' }}</p>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <UsersIcon class="h-8 w-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-blue-900">分配用户</p>
                <p class="text-2xl font-bold text-blue-600">{{ roleDetail.users?.length || 0 }}</p>
              </div>
            </div>
          </div>

          <div class="bg-green-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <KeyIcon class="h-8 w-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-green-900">权限数量</p>
                <p class="text-2xl font-bold text-green-600">{{ roleDetail.permissions?.length || 0 }}</p>
              </div>
            </div>
          </div>

          <div class="bg-purple-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <ClockIcon class="h-8 w-8 text-purple-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-purple-900">最后更新</p>
                <p class="text-sm font-bold text-purple-600">{{ formatDate(roleDetail.updated_date) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 权限列表 -->
        <div>
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-medium text-gray-900">权限列表</h4>
            <button @click="handleManagePermissions"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
              <KeyIcon class="h-4 w-4 mr-1" />
              管理权限
            </button>
          </div>

          <div v-if="roleDetail.permissions && roleDetail.permissions.length > 0"
            class="bg-white border rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限名称</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限编码</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资源类型</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="permission in roleDetail.permissions" :key="permission.permission_id"
                    class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {{ permission.permission_name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ permission.permission_code }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        :class="getResourceTypeClass(permission.resource_type)">
                        {{ getResourceTypeName(permission.resource_type) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {{ permission.description || '-' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div v-else class="text-center py-8 bg-gray-50 rounded-lg">
            <KeyIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无权限</h3>
            <p class="mt-1 text-sm text-gray-500">该角色尚未分配任何权限</p>
            <div class="mt-6">
              <button @click="handleManagePermissions"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <KeyIcon class="h-4 w-4 mr-2" />
                分配权限
              </button>
            </div>
          </div>
        </div>

        <!-- 用户列表 -->
        <div>
          <h4 class="text-lg font-medium text-gray-900 mb-4">使用该角色的用户</h4>

          <div v-if="roleDetail.users && roleDetail.users.length > 0"
            class="bg-white border rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="user in roleDetail.users" :key="user.user_id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {{ user.username }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ user.name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ user.department_name || '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="user.status === 'ACTIVE' ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800' : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800'">
                        {{ user.status === 'ACTIVE' ? '活跃' : '禁用' }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div v-else class="text-center py-8 bg-gray-50 rounded-lg">
            <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无用户</h3>
            <p class="mt-1 text-sm text-gray-500">该角色尚未分配给任何用户</p>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="text-center py-12">
        <ExclamationTriangleIcon class="mx-auto h-12 w-12 text-red-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">加载失败</h3>
        <p class="mt-1 text-sm text-gray-500">无法加载角色详情信息</p>
      </div>
    </div>

    <!-- 操作按钮区域 - 固定在底部 -->
    <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
      <button v-if="roleDetail" @click="handleEdit"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
        编辑角色
      </button>
      <button @click="handleManagePermissions"
        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
        管理权限
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoleStore } from '@/stores/role.store'
import type { RoleDetail } from '@/types/user.types'
import {
  UsersIcon,
  KeyIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

interface Props {
  roleId: string
}

interface Emits {
  (e: 'edit-role', role: RoleDetail): void
  (e: 'manage-permissions', roleId: string): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const roleStore = useRoleStore()

// 响应式数据
const roleDetail = ref<RoleDetail | null>(null)

// 资源类型映射
const getResourceTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'menu': '菜单',
    'action': '操作',
    'data': '数据',
    'api': '接口',
    'page': '页面'
  }
  return typeMap[type] || type
}

const getResourceTypeClass = (type: string) => {
  const classMap: Record<string, string> = {
    'menu': 'bg-blue-100 text-blue-800',
    'action': 'bg-green-100 text-green-800',
    'data': 'bg-yellow-100 text-yellow-800',
    'api': 'bg-purple-100 text-purple-800',
    'page': 'bg-indigo-100 text-indigo-800'
  }
  return classMap[type] || 'bg-gray-100 text-gray-800'
}

// 加载角色详情
const loadRoleDetail = async () => {
  if (!props.roleId) {
    roleDetail.value = null
    return
  }

  try {
    const detail = await roleStore.fetchRoleDetail(props.roleId)
    roleDetail.value = detail
  } catch (error) {
    console.error('加载角色详情失败:', error)
    roleDetail.value = null
  }
}

// 事件处理
const handleEdit = () => {
  if (roleDetail.value) {
    emit('edit-role', roleDetail.value)
  }
}

const handleManagePermissions = () => {
  if (props.roleId) {
    emit('manage-permissions', props.roleId)
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听角色ID变化
watch(() => props.roleId, () => {
  if (props.roleId) {
    loadRoleDetail()
  }
}, { immediate: true })
</script>

<style scoped>
/* 表格样式优化 */
.table-container {
  max-height: 400px;
  overflow-y: auto;
}

/* 滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>