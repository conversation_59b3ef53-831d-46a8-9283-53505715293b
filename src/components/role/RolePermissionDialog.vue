<template>
  <div class="h-full flex flex-col">
    <!-- 内容区域 - 可滚动 -->
    <div class="flex-1 overflow-y-auto px-1">
      <div class="space-y-6 pb-6">
        <!-- 角色信息 -->
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-green-100">
              <KeyIcon class="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">
                权限管理
              </h3>
              <p class="text-sm text-gray-500">
                为角色 "{{ role?.role_name }}" 分配权限
              </p>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span class="ml-2 text-gray-600">加载权限数据...</span>
        </div>

        <!-- 权限分配界面 -->
        <div v-else class="space-y-6">
          <!-- 操作工具栏 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-4">
              <div class="text-sm text-gray-600">
                已选择 {{ selectedPermissions.length }} 个权限
              </div>
              <button @click="clearSelection" class="text-sm text-blue-600 hover:text-blue-700">
                清空选择
              </button>
            </div>

            <div class="flex items-center space-x-2">
              <button @click="expandAll" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                全部展开
              </button>
              <button @click="collapseAll" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                全部收起
              </button>
            </div>
          </div>

          <!-- 权限搜索 -->
          <div class="flex items-center space-x-4">
            <div class="flex-1">
              <input v-model="searchQuery" type="text" placeholder="搜索权限名称或编码..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500" />
            </div>
            <select v-model="filterResourceType"
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500">
              <option value="">全部类型</option>
              <option value="menu">菜单</option>
              <option value="action">操作</option>
              <option value="data">数据</option>
              <option value="api">接口</option>
              <option value="page">页面</option>
            </select>
          </div>

          <!-- 权限树 -->
          <div class="border rounded-lg overflow-hidden">
            <div class="max-h-96 overflow-y-auto">
              <div v-if="filteredPermissions.length === 0" class="text-center py-8 text-gray-500">
                暂无匹配的权限
              </div>

              <div v-else class="divide-y divide-gray-200">
                <PermissionTreeNode v-for="permission in filteredPermissions" :key="permission.permission_id"
                  :permission="permission" :selected-permissions="selectedPermissions" :expanded-nodes="expandedNodes"
                  :search-query="searchQuery" @toggle-selection="handleToggleSelection"
                  @toggle-expand="handleToggleExpand" />
              </div>
            </div>
          </div>

          <!-- 已选权限汇总 -->
          <div v-if="selectedPermissions.length > 0" class="bg-blue-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-blue-900 mb-3">已选择的权限 ({{ selectedPermissions.length }})</h4>
            <div class="flex flex-wrap gap-2">
              <span v-for="permId in selectedPermissions" :key="permId"
                class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                {{ getPermissionName(permId) }}
                <button @click="removePermission(permId)" class="ml-1 text-blue-600 hover:text-blue-800">
                  <XMarkIcon class="h-3 w-3" />
                </button>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 操作按钮区域 - 固定在底部 -->
  <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 border-t bg-white">
    <button type="button" @click="$emit('cancel')"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
      取消
    </button>
    <button type="button" :disabled="submitting" @click="handleSave"
      class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center">
      <div v-if="submitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      保存权限
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoleStore } from '@/stores/role.store'
import type { RoleListItem, Permission } from '@/types/user.types'
import { KeyIcon, XMarkIcon } from '@heroicons/vue/24/outline'
import PermissionTreeNode from './PermissionTreeNode.vue'

interface Props {
  role?: RoleListItem | null
  permissions: Permission[]
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  role: null
})

const emit = defineEmits<Emits>()

const roleStore = useRoleStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const selectedPermissions = ref<string[]>([])
const expandedNodes = ref<Set<string>>(new Set())
const searchQuery = ref('')
const filterResourceType = ref('')
const currentRolePermissions = ref<string[]>([])

// 计算属性
const filteredPermissions = computed(() => {
  let filtered = props.permissions

  // 按资源类型过滤
  if (filterResourceType.value) {
    filtered = filtered.filter(p => p.resource_type === filterResourceType.value)
  }

  // 按搜索词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(p =>
      p.permission_name.toLowerCase().includes(query) ||
      p.permission_code.toLowerCase().includes(query) ||
      (p.description && p.description.toLowerCase().includes(query))
    )
  }

  return filtered
})

// 权限名称映射
const permissionMap = computed(() => {
  const map = new Map<string, Permission>()
  const traverse = (perms: Permission[]) => {
    perms.forEach(p => {
      map.set(p.permission_id, p)
      if (p.children) {
        traverse(p.children)
      }
    })
  }
  traverse(props.permissions)
  return map
})

// 方法
const getPermissionName = (permissionId: string) => {
  return permissionMap.value.get(permissionId)?.permission_name || permissionId
}

const loadCurrentPermissions = async () => {
  if (!props.role) return

  loading.value = true
  try {
    const permissions = await roleStore.fetchRolePermissions(props.role.role_id)
    currentRolePermissions.value = permissions.map(p => p.permission_id)
    selectedPermissions.value = [...currentRolePermissions.value]
  } catch (error) {
    console.error('加载角色权限失败:', error)
  } finally {
    loading.value = false
  }
}

const handleToggleSelection = (permissionId: string, selected: boolean) => {
  if (selected) {
    if (!selectedPermissions.value.includes(permissionId)) {
      selectedPermissions.value.push(permissionId)
    }
  } else {
    const index = selectedPermissions.value.indexOf(permissionId)
    if (index !== -1) {
      selectedPermissions.value.splice(index, 1)
    }
  }
}

const handleToggleExpand = (permissionId: string) => {
  if (expandedNodes.value.has(permissionId)) {
    expandedNodes.value.delete(permissionId)
  } else {
    expandedNodes.value.add(permissionId)
  }
}

const removePermission = (permissionId: string) => {
  const index = selectedPermissions.value.indexOf(permissionId)
  if (index !== -1) {
    selectedPermissions.value.splice(index, 1)
  }
}

const clearSelection = () => {
  selectedPermissions.value = []
}

const expandAll = () => {
  const collectIds = (perms: Permission[]): string[] => {
    const ids: string[] = []
    perms.forEach(p => {
      ids.push(p.permission_id)
      if (p.children && p.children.length > 0) {
        ids.push(...collectIds(p.children))
      }
    })
    return ids
  }

  const allIds = collectIds(props.permissions)
  expandedNodes.value = new Set(allIds)
}

const collapseAll = () => {
  expandedNodes.value.clear()
}

const handleSave = async () => {
  if (!props.role) return

  submitting.value = true
  try {
    await roleStore.assignPermissions(props.role.role_id, selectedPermissions.value)
    emit('success')
  } catch (error) {
    console.error('保存权限失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听角色变化
watch(() => props.role, (role) => {
  if (role) {
    loadCurrentPermissions()
    // 默认展开第一层
    const firstLevelIds = props.permissions.map(p => p.permission_id)
    expandedNodes.value = new Set(firstLevelIds)
  } else {
    // 重置状态
    selectedPermissions.value = []
    currentRolePermissions.value = []
    expandedNodes.value.clear()
    searchQuery.value = ''
    filterResourceType.value = ''
  }
}, { immediate: true })
</script>


<style scoped>
/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>