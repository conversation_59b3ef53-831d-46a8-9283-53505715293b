import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import { useAuthStore } from './stores/auth'
import './style.css'
import App from './App.vue'

const app = createApp(App)
const pinia = createPinia()

// 安装插件
app.use(pinia)
app.use(router)

// 初始化认证状态并等待完成
const initializeApp = async () => {
  try {
    console.log('[App] 开始初始化应用...')
    const authStore = useAuthStore()
    
    // 等待认证状态初始化完成
    await authStore.initializeAuth()
    
    console.log('[App] 认证状态初始化完成，挂载应用...')
    app.mount('#app')
    console.log('[App] 应用挂载完成')
  } catch (error) {
    console.error('[App] 应用初始化失败:', error)
    // 即使初始化失败也要挂载应用，让用户看到错误页面
    app.mount('#app')
  }
}

// 启动应用
initializeApp()
