<template>
  <div class="user-management p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
        <p class="text-gray-600">管理系统用户账户、角色和权限</p>
      </div>
      <div class="flex items-center space-x-3">
        <button 
          v-if="userStore.permissions.canCreate"
          @click="showCreateDialog = true"
          class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <PlusIcon class="h-4 w-4" />
          <span>新增用户</span>
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4" v-if="statistics">
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <UsersIcon class="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-bold">{{ statistics.total_users }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircleIcon class="h-6 w-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">活跃用户</p>
            <p class="text-2xl font-bold">{{ statistics.active_users }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-red-100 rounded-lg">
            <XCircleIcon class="h-6 w-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">禁用用户</p>
            <p class="text-2xl font-bold">{{ statistics.inactive_users }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-purple-100 rounded-lg">
            <BuildingOfficeIcon class="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">部门数量</p>
            <p class="text-2xl font-bold">{{ statistics.users_by_department?.length || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg border p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="search-username" class="block text-sm font-medium text-gray-700">用户名</label>
          <input
            id="search-username"
            v-model="searchForm.username"
            placeholder="请输入用户名"
            @input="debouncedSearch"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label for="search-name" class="block text-sm font-medium text-gray-700">姓名</label>
          <input
            id="search-name"
            v-model="searchForm.name"
            placeholder="请输入姓名"
            @input="debouncedSearch"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label for="search-department" class="block text-sm font-medium text-gray-700">部门</label>
          <select v-model="searchForm.department_id" @change="handleSearch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option value="">全部部门</option>
            <option 
              v-for="dept in flatDepartments" 
              :key="dept.org_id" 
              :value="dept.org_id"
            >
              {{ '　'.repeat(dept.level) }}{{ dept.org_name }}
            </option>
          </select>
        </div>
        
        <div>
          <label for="search-status" class="block text-sm font-medium text-gray-700">状态</label>
          <select v-model="searchForm.status" @change="handleSearch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option value="">全部状态</option>
            <option value="ACTIVE">活跃</option>
            <option value="INACTIVE">禁用</option>
          </select>
        </div>
      </div>
      
      <div class="flex items-center justify-between mt-4">
        <div class="flex items-center space-x-2">
          <button 
            @click="handleReset"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
          >
            重置
          </button>
        </div>
        
        <div class="text-sm text-gray-600">
          共 {{ userStore.total }} 条记录
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div 
      v-if="userStore.hasSelectedUsers"
      class="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg"
    >
      <div class="flex items-center space-x-4">
        <span class="text-sm text-blue-700">
          已选择 {{ userStore.selectedUserIds.length }} 个用户
        </span>
        <button 
          @click="userStore.clearSelection"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
        >
          取消选择
        </button>
      </div>
      
      <div class="flex items-center space-x-2">
        <button 
          v-if="userStore.permissions.canBatchOperation"
          @click="handleBatchActivate"
          :disabled="!userStore.canBatchActivate"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
        >
          批量启用
        </button>
        <button 
          v-if="userStore.permissions.canBatchOperation"
          @click="handleBatchDeactivate"
          :disabled="!userStore.canBatchDeactivate"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
        >
          批量禁用
        </button>
        <button 
          v-if="userStore.permissions.canDelete"
          @click="handleBatchDelete"
          :disabled="!userStore.canBatchDelete"
          class="px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          批量删除
        </button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg border">
      <div class="relative">
        <!-- 加载状态 -->
        <div v-if="userStore.loading" class="absolute top-0 left-0 right-0 bottom-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
          <div class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-600">加载中...</span>
          </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input 
                    type="checkbox"
                    :checked="isAllSelected"
                    @change="handleSelectAll"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工编号</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr 
                v-for="user in userStore.users" 
                :key="user.user_id"
                class="hover:bg-gray-50"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <input 
                    type="checkbox"
                    :checked="userStore.selectedUserIds.includes(user.user_id)"
                    @change="userStore.toggleUserSelection(user.user_id)"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ user.username }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.employee_id || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.email || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.phone || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.department_name || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="user.status === 'ACTIVE' ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800' : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800'">
                    {{ user.status === 'ACTIVE' ? '活跃' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ user.last_login_date ? formatDate(user.last_login_date) : '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatDate(user.created_date) }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <button 
                      v-if="userStore.permissions.canViewDetail"
                      @click="handleViewUser(user)"
                      class="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      查看
                    </button>
                    <button 
                      v-if="userStore.permissions.canEdit"
                      @click="handleEditUser(user)"
                      class="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      编辑
                    </button>
                    <button
                      v-if="userStore.permissions.canResetPassword"
                      @click="handleResetPassword(user)"
                      class="text-orange-600 hover:text-orange-900 text-sm"
                    >
                      重置密码
                    </button>
                    <button
                      v-if="userStore.permissions.canEdit"
                      @click="handleAssignRoles(user)"
                      class="text-green-600 hover:text-green-900 text-sm"
                    >
                      分配角色
                    </button>
                    <button
                      v-if="userStore.permissions.canDelete"
                      @click="handleDeleteUser(user)"
                      class="text-red-600 hover:text-red-900 text-sm"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          
          <!-- 空状态 -->
          <div v-if="!userStore.loading && userStore.users.length === 0" class="text-center py-12">
            <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无用户</h3>
            <p class="mt-1 text-sm text-gray-500">开始创建第一个用户吧</p>
            <div class="mt-6">
              <button @click="showCreateDialog = true" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">新增用户</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="userStore.total > 0" class="flex items-center justify-between">
      <div class="text-sm text-gray-600">
        显示第 {{ (userStore.currentPage - 1) * userStore.pageSize + 1 }} - 
        {{ Math.min(userStore.currentPage * userStore.pageSize, userStore.total) }} 条，
        共 {{ userStore.total }} 条记录
      </div>
      
      <div class="flex items-center space-x-2">
        <select 
          :value="userStore.pageSize.toString()" 
          @change="handlePageSizeChange"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="10">10</option>
          <option value="20">20</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
        
        <div class="flex items-center space-x-1">
          <button 
            :disabled="userStore.currentPage <= 1"
            @click="handlePageChange(userStore.currentPage - 1)"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          
          <div class="flex items-center space-x-1">
            <button
              v-for="page in visiblePages"
              :key="page"
              :class="page === userStore.currentPage ? 'px-3 py-2 text-sm bg-blue-600 text-white rounded-md' : 'px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50'"
              @click="handlePageChange(Number(page))"
            >
              {{ page }}
            </button>
          </div>
          
          <button 
            :disabled="userStore.currentPage >= userStore.totalPages"
            @click="handlePageChange(userStore.currentPage + 1)"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 用户表单对话框 -->
    <Dialog :open="showCreateDialog" @update:open="showCreateDialog = $event">
      <DialogContent class="w-[75vw] max-w-[1024px] h-[42.2vw] max-h-[576px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>{{ editingUser ? '编辑用户' : '新增用户' }}</DialogTitle>
          <DialogDescription>
            {{ editingUser ? '修改用户信息和角色设置' : '创建新的系统用户账户' }}
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <UserFormDialog
            :user="editingUser"
            :organizations="flatDepartments"
            @success="handleFormSuccess"
            @cancel="showCreateDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 用户详情对话框 -->
    <Dialog :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <DialogContent class="w-[75vw] max-w-[1200px] h-[42.2vw] max-h-[675px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>用户详情</DialogTitle>
          <DialogDescription>
            查看用户的详细信息、角色分配和登录记录
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <UserDetailDialog
            :user-id="viewingUserId"
            @edit-user="handleEditFromDetail"
            @reset-password="handleResetPasswordFromDetail"
            @manage-roles="handleManageRolesFromDetail"
            @close="showDetailDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 重置密码对话框 -->
    <Dialog :open="showResetPasswordDialog" @update:open="showResetPasswordDialog = $event">
      <DialogContent class="w-[75vw] max-w-[800px] h-[42.2vw] max-h-[450px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>重置密码</DialogTitle>
          <DialogDescription>
            为用户 "{{ resettingPasswordUser?.name }}" 重置登录密码
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <ResetPasswordDialog
            :user="resettingPasswordUser"
            @success="handleResetPasswordSuccess"
            @cancel="showResetPasswordDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 角色管理对话框 -->
    <Dialog :open="showRoleDialog" @update:open="showRoleDialog = $event">
      <DialogContent class="w-[75vw] max-w-[1024px] h-[42.2vw] max-h-[576px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>角色管理</DialogTitle>
          <DialogDescription>
            为用户 "{{ managingRoleUser?.name }}" 分配系统角色
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <UserRoleDialog
            :user="managingRoleUser"
            :roles="userStore.availableRoles"
            @success="handleRoleManagementSuccess"
            @cancel="showRoleDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 删除确认对话框 -->
    <Dialog :open="showDeleteDialog" @update:open="showDeleteDialog = $event">
      <DialogContent class="w-[75vw] max-w-[600px] h-[42.2vw] max-h-[338px] flex flex-col">
        <DialogHeader>
          <DialogTitle>确认删除用户</DialogTitle>
          <DialogDescription>
            此操作将永久删除用户账户，请谨慎操作
          </DialogDescription>
        </DialogHeader>
        
        <div class="py-4">
          <p class="text-sm text-gray-600">
            确定要删除用户 <span class="font-semibold text-gray-900">{{ deletingUser?.name }}</span> 吗？
          </p>
          <ul class="mt-3 text-sm text-red-600 space-y-1">
            <li>• 此操作不可撤销</li>
            <li>• 用户的所有数据将被永久删除</li>
          </ul>
        </div>
        
        <DialogFooter>
          <DialogClose as-child>
            <button class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
              取消
            </button>
          </DialogClose>
          <button 
            @click="confirmDelete"
            :disabled="userStore.submitting"
            class="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {{ userStore.submitting ? '删除中...' : '删除' }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    
    <!-- 批量删除确认对话框 -->
    <Dialog :open="showBatchDeleteDialog" @update:open="showBatchDeleteDialog = $event">
      <DialogContent class="w-[75vw] max-w-[600px] h-[42.2vw] max-h-[338px] flex flex-col">
        <DialogHeader>
          <DialogTitle>确认批量删除</DialogTitle>
          <DialogDescription>
            此操作将永久删除多个用户账户，请谨慎操作
          </DialogDescription>
        </DialogHeader>
        
        <div class="py-4">
          <p class="text-sm text-gray-600">
            确定要删除选中的 <span class="font-semibold text-gray-900">{{ userStore.selectedUserIds.length }}</span> 个用户吗？
          </p>
          <ul class="mt-3 text-sm text-red-600 space-y-1">
            <li>• 此操作不可撤销</li>
            <li>• 所有选中用户的数据将被永久删除</li>
          </ul>
        </div>
        
        <DialogFooter>
          <DialogClose as-child>
            <button class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
              取消
            </button>
          </DialogClose>
          <button 
            @click="confirmBatchDelete"
            :disabled="userStore.submitting"
            class="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {{ userStore.submitting ? '删除中...' : '批量删除' }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 角色分配对话框 -->
    <Dialog :open="showRoleAssignDialog" @update:open="showRoleAssignDialog = $event">
      <DialogContent class="w-[75vw] max-w-[1024px] h-[42.2vw] max-h-[576px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>角色分配</DialogTitle>
          <DialogDescription>
            为用户分配系统角色和权限
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <UserRoleAssignDialog
            :user="assigningRoleUser"
            @success="handleRoleAssignSuccess"
            @cancel="showRoleAssignDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { debounce } from 'lodash-es'
import { useUserStore } from '@/stores/user.store'
import { useToast } from '@/composables/useToast'
import type { UserListItem, Organization, UserDetail } from '@/types/user.types'

// 导入对话框组件
import UserFormDialog from '@/components/user/UserFormDialog.vue'
import UserDetailDialog from '@/components/user/UserDetailDialog.vue'
import ResetPasswordDialog from '@/components/user/ResetPasswordDialog.vue'
import UserRoleDialog from '@/components/user/UserRoleDialog.vue'
import UserRoleAssignDialog from '@/components/user/UserRoleAssignDialog.vue'

// 导入 shadcn-ui Dialog 组件
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'

// 图标导入
import { 
  PlusIcon, 
  UsersIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  BuildingOfficeIcon
} from '@heroicons/vue/24/outline'

// 状态管理
const userStore = useUserStore()
const { toast } = useToast()

// 响应式状态
const statistics = ref<any>(null)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showResetPasswordDialog = ref(false)
const showDeleteDialog = ref(false)
const showBatchDeleteDialog = ref(false)
const showRoleDialog = ref(false)
const showRoleAssignDialog = ref(false)

const editingUser = ref<UserListItem | null>(null)
const viewingUserId = ref<string>('')
const resettingPasswordUser = ref<UserListItem | null>(null)
const deletingUser = ref<UserListItem | null>(null)
const managingRoleUser = ref<UserListItem | null>(null)
const assigningRoleUser = ref<UserListItem | null>(null)

const dialogMode = ref<'create' | 'edit'>('create')

// 搜索表单
const searchForm = reactive({
  username: '',
  name: '',
  department_id: '',
  status: ''
})

// 部门数据扁平化
const flatDepartments = computed(() => {
  const flatten = (deps: Organization[], level = 0): Organization[] => {
    const result: Organization[] = []
    for (const dep of deps) {
      result.push({ ...dep, level })
      if (dep.children && dep.children.length > 0) {
        result.push(...flatten(dep.children, level + 1))
      }
    }
    return result
  }
  return flatten(userStore.organizationTree)
})

// 全选状态
const isAllSelected = computed(() => {
  return userStore.users.length > 0 && 
         userStore.users.every(user => userStore.selectedUserIds.includes(user.user_id))
})

// 可见页码
const visiblePages = computed(() => {
  const current = userStore.currentPage
  const total = userStore.totalPages
  const delta = 2
  const range = []
  const rangeWithDots = []

  for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
    range.push(i)
  }

  if (current - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }

  rangeWithDots.push(...range)

  if (current + delta < total - 1) {
    rangeWithDots.push('...', total)
  } else if (total > 1) {
    rangeWithDots.push(total)
  }

  return rangeWithDots.filter(page => page !== 1 || total === 1)
})

// 防抖搜索
const debouncedSearch = debounce(() => {
  handleSearch()
}, 500)

// 方法定义
const handleSearch = async () => {
  const params = Object.fromEntries(
    Object.entries(searchForm).filter(([_, value]) => value !== '')
  )
  userStore.setQueryParams(params)
  await userStore.fetchUsers()
}

const handleReset = async () => {
  Object.assign(searchForm, {
    username: '',
    name: '',
    department_id: '',
    status: ''
  })
  userStore.resetQueryParams()
  await userStore.fetchUsers()
}

const handleSelectAll = (event: Event) => {
  const checked = (event.target as HTMLInputElement).checked
  if (checked) {
    userStore.selectAllUsers()
  } else {
    userStore.clearSelection()
  }
}

const handlePageChange = async (page: number) => {
  userStore.setPage(page)
  await userStore.fetchUsers()
}

const handlePageSizeChange = async (event: Event) => {
  const size = (event.target as HTMLSelectElement).value
  userStore.setPageSize(parseInt(size))
  await userStore.fetchUsers()
}

const handleViewUser = (user: UserListItem) => {
  viewingUserId.value = user.user_id
  showDetailDialog.value = true
}

const handleEditUser = (user: UserListItem) => {
  editingUser.value = user
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const handleDeleteUser = (user: UserListItem) => {
  deletingUser.value = user
  showDeleteDialog.value = true
}

const handleResetPassword = (user: UserListItem) => {
  resettingPasswordUser.value = user
  showResetPasswordDialog.value = true
}

// const handleManageRoles = (user: UserListItem) => {
//   managingRoleUserId.value = user.user_id
//   showRoleDialog.value = true
// }

const handleBatchActivate = async () => {
  try {
    await userStore.batchOperation('activate')
    toast({
      title: '操作成功',
      description: '用户已批量启用'
    })
  } catch (error) {
    toast({
      title: '操作失败',
      description: '批量启用用户失败',
      variant: 'destructive'
    })
  }
}

const handleBatchDeactivate = async () => {
  try {
    await userStore.batchOperation('deactivate')
    toast({
      title: '操作成功',
      description: '用户已批量禁用'
    })
  } catch (error) {
    toast({
      title: '操作失败',
      description: '批量禁用用户失败',
      variant: 'destructive'
    })
  }
}

const handleBatchDelete = () => {
  showBatchDeleteDialog.value = true
}

const confirmDelete = async () => {
  if (!deletingUser.value) return
  
  try {
    await userStore.removeUser(deletingUser.value.user_id)
    showDeleteDialog.value = false
    deletingUser.value = null
    toast({
      title: '删除成功',
      description: '用户已删除',
      variant: 'success'
    })
  } catch (error) {
    toast({
      title: '删除失败',
      description: '删除用户失败',
      variant: 'destructive'
    })
  }
}

const confirmBatchDelete = async () => {
  try {
    await userStore.batchOperation('delete')
    showBatchDeleteDialog.value = false
    toast({
      title: '删除成功',
      description: '用户已批量删除',
      variant: 'success'
    })
  } catch (error) {
    toast({
      title: '删除失败',
      description: '批量删除用户失败',
      variant: 'destructive'
    })
  }
}

const handleFormSuccess = async () => {
  const isEdit = dialogMode.value === 'edit'
  showCreateDialog.value = false
  editingUser.value = null
  dialogMode.value = 'create'
  toast({
    title: '操作成功',
    description: isEdit ? '用户更新成功' : '用户创建成功',
    variant: 'success'
  })
  // 刷新用户列表
  await userStore.fetchUsers()
}

const handleResetPasswordSuccess = () => {
  showResetPasswordDialog.value = false
  resettingPasswordUser.value = null
  toast({
    title: '重置成功',
    description: '密码已重置',
    variant: 'success'
  })
}

const handleRoleManagementSuccess = () => {
  showRoleDialog.value = false
  managingRoleUser.value = null
  toast({
    title: '操作成功',
    description: '角色分配已更新',
    variant: 'success'
  })
}

const handleEditFromDetail = (user: UserDetail) => {
  showDetailDialog.value = false
  editingUser.value = user as any // UserDetail extends User
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const handleResetPasswordFromDetail = (user: UserDetail) => {
  showDetailDialog.value = false
  resettingPasswordUser.value = user as any // UserDetail extends User
  showResetPasswordDialog.value = true
}

const handleManageRolesFromDetail = (userId: string) => {
  const user = userStore.users.find(u => u.user_id === userId)
  if (user) {
    showDetailDialog.value = false
    managingRoleUser.value = user
    showRoleDialog.value = true
  }
}

const handleAssignRoles = (user: UserListItem) => {
  assigningRoleUser.value = user
  showRoleAssignDialog.value = true
}

const handleRoleAssignSuccess = () => {
  showRoleAssignDialog.value = false
  assigningRoleUser.value = null
  toast({
    title: '操作成功',
    description: '角色分配已更新',
    variant: 'success'
  })
  // 刷新用户列表
  userStore.fetchUsers()
}

// const handleManageRoles = (user: UserListItem) => {
//   managingRoleUser.value = user
//   showRoleDialog.value = true
// }

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听弹窗关闭，重置状态
watch(showCreateDialog, (newVal) => {
  if (!newVal) {
    editingUser.value = null
    dialogMode.value = 'create'
  }
})

// 组件挂载
onMounted(async () => {
  try {
    // 并行加载数据
    await Promise.all([
      userStore.fetchUsers(),
      userStore.fetchOrganizationTree(),
      userStore.fetchAvailableRoles(),
      userStore.fetchStatistics().then(data => statistics.value = data)
    ])
  } catch (error) {
    console.error('初始化用户管理页面失败:', error)
    toast({
      title: '加载失败',
      description: '初始化页面数据失败',
      variant: 'destructive'
    })
  }
})
</script>

<style scoped>
.user-management {
  max-width: 100%;
  margin: 0 auto;
}

/* 自定义滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格响应式样式 */
@media (max-width: 768px) {
  .user-management {
    padding: 1rem;
  }
  
  .grid-cols-1.md\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-cols-1.md\\:grid-cols-4 > :nth-child(n+3) {
    grid-column: span 2;
  }
}
</style>