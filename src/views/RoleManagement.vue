<template>
  <div class="role-management p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">角色管理</h1>
        <p class="text-gray-600">管理系统角色和权限分配</p>
      </div>
      <div class="flex items-center space-x-3">
        <button 
          v-if="roleStore.permissions.canCreate"
          @click="showCreateDialog = true"
          class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <PlusIcon class="h-4 w-4" />
          <span>新增角色</span>
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4" v-if="statistics">
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <ShieldCheckIcon class="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总角色数</p>
            <p class="text-2xl font-bold">{{ statistics.total_roles }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircleIcon class="h-6 w-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">启用角色</p>
            <p class="text-2xl font-bold">{{ statistics.active_roles }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-red-100 rounded-lg">
            <XCircleIcon class="h-6 w-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">禁用角色</p>
            <p class="text-2xl font-bold">{{ statistics.inactive_roles }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-purple-100 rounded-lg">
            <KeyIcon class="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600">权限类型</p>
            <p class="text-2xl font-bold">{{ statistics.roles_by_type?.length || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg border p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="search-role-name" class="block text-sm font-medium text-gray-700">角色名称</label>
          <input
            id="search-role-name"
            v-model="searchForm.role_name"
            placeholder="请输入角色名称"
            @input="debouncedSearch"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label for="search-role-code" class="block text-sm font-medium text-gray-700">角色编码</label>
          <input
            id="search-role-code"
            v-model="searchForm.role_code"
            placeholder="请输入角色编码"
            @input="debouncedSearch"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label for="search-status" class="block text-sm font-medium text-gray-700">状态</label>
          <select v-model="searchForm.status" @change="handleSearch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option value="">全部状态</option>
            <option value="ACTIVE">启用</option>
            <option value="INACTIVE">禁用</option>
          </select>
        </div>
        
        <div>
          <label for="search-description" class="block text-sm font-medium text-gray-700">描述</label>
          <input
            id="search-description"
            v-model="searchForm.description"
            placeholder="请输入描述关键词"
            @input="debouncedSearch"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      
      <div class="flex items-center justify-between mt-4">
        <div class="flex items-center space-x-2">
          <button 
            @click="handleReset"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
          >
            重置
          </button>
        </div>
        
        <div class="text-sm text-gray-600">
          共 {{ roleStore.total }} 条记录
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div 
      v-if="roleStore.hasSelectedRoles"
      class="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg"
    >
      <div class="flex items-center space-x-4">
        <span class="text-sm text-blue-700">
          已选择 {{ roleStore.selectedRoleIds.length }} 个角色
        </span>
        <button 
          @click="roleStore.clearSelection"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
        >
          取消选择
        </button>
      </div>
      
      <div class="flex items-center space-x-2">
        <button 
          v-if="roleStore.permissions.canBatchOperation"
          @click="handleBatchActivate"
          :disabled="!roleStore.canBatchActivate"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
        >
          批量启用
        </button>
        <button 
          v-if="roleStore.permissions.canBatchOperation"
          @click="handleBatchDeactivate"
          :disabled="!roleStore.canBatchDeactivate"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
        >
          批量禁用
        </button>
        <button 
          v-if="roleStore.permissions.canDelete"
          @click="handleBatchDelete"
          :disabled="!roleStore.canBatchDelete"
          class="px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          批量删除
        </button>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="bg-white rounded-lg border">
      <div class="relative">
        <!-- 加载状态 -->
        <div v-if="roleStore.loading" class="absolute top-0 left-0 right-0 bottom-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
          <div class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-600">加载中...</span>
          </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input 
                    type="checkbox"
                    :checked="isAllSelected"
                    @change="handleSelectAll"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色名称</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色编码</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户数</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限数</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr 
                v-for="role in roleStore.roles" 
                :key="role.role_id"
                class="hover:bg-gray-50"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <input 
                    type="checkbox"
                    :checked="roleStore.selectedRoleIds.includes(role.role_id)"
                    @change="roleStore.toggleRoleSelection(role.role_id)"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ role.role_name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ role.role_code }}</td>
                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">{{ role.description || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="role.status === 'ACTIVE' ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800' : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800'">
                    {{ role.status === 'ACTIVE' ? '启用' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ role.user_count || 0 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ role.permission_count || 0 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatDate(role.created_date) }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <button 
                      v-if="roleStore.permissions.canViewDetail"
                      @click="handleViewRole(role)"
                      class="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      查看
                    </button>
                    <button 
                      v-if="roleStore.permissions.canEdit"
                      @click="handleEditRole(role)"
                      class="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      编辑
                    </button>
                    <button 
                      v-if="roleStore.permissions.canAssignPermission"
                      @click="handleManagePermissions(role)"
                      class="text-green-600 hover:text-green-900 text-sm"
                    >
                      权限
                    </button>
                    <button 
                      v-if="roleStore.permissions.canDelete"
                      @click="handleDeleteRole(role)"
                      class="text-red-600 hover:text-red-900 text-sm"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          
          <!-- 空状态 -->
          <div v-if="!roleStore.loading && roleStore.roles.length === 0" class="text-center py-12">
            <ShieldCheckIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无角色</h3>
            <p class="mt-1 text-sm text-gray-500">开始创建第一个角色吧</p>
            <div class="mt-6">
              <button @click="showCreateDialog = true" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">新增角色</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="roleStore.total > 0" class="flex items-center justify-between">
      <div class="text-sm text-gray-600">
        显示第 {{ (roleStore.currentPage - 1) * roleStore.pageSize + 1 }} - 
        {{ Math.min(roleStore.currentPage * roleStore.pageSize, roleStore.total) }} 条，
        共 {{ roleStore.total }} 条记录
      </div>
      
      <div class="flex items-center space-x-2">
        <select 
          :value="roleStore.pageSize.toString()" 
          @change="handlePageSizeChange"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="10">10</option>
          <option value="20">20</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
        
        <div class="flex items-center space-x-1">
          <button 
            :disabled="roleStore.currentPage <= 1"
            @click="handlePageChange(roleStore.currentPage - 1)"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          
          <div class="flex items-center space-x-1">
            <button
              v-for="page in visiblePages"
              :key="page"
              :class="page === roleStore.currentPage ? 'px-3 py-2 text-sm bg-blue-600 text-white rounded-md' : 'px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50'"
              @click="handlePageChange(Number(page))"
            >
              {{ page }}
            </button>
          </div>
          
          <button 
            :disabled="roleStore.currentPage >= roleStore.totalPages"
            @click="handlePageChange(roleStore.currentPage + 1)"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <Dialog :open="showCreateDialog" @update:open="showCreateDialog = $event">
      <DialogContent class="w-[75vw] max-w-[900px] h-[42.2vw] max-h-[506px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>{{ editingRole ? '编辑角色' : '新增角色' }}</DialogTitle>
          <DialogDescription>
            {{ editingRole ? '修改角色信息和权限设置' : '创建新的系统角色' }}
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <RoleFormDialog
            :role="editingRole"
            @success="handleFormSuccess"
            @cancel="showCreateDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 角色详情对话框 -->
    <Dialog :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <DialogContent class="w-[75vw] max-w-[1200px] h-[42.2vw] max-h-[675px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>角色详情</DialogTitle>
          <DialogDescription>
            查看角色的详细信息、权限分配和关联用户
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <RoleDetailDialog
            :role-id="viewingRoleId"
            @edit-role="handleEditFromDetail"
            @manage-permissions="handleManagePermissionsFromDetail"
            @close="showDetailDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 权限管理对话框 -->
    <Dialog :open="showPermissionDialog" @update:open="showPermissionDialog = $event">
      <DialogContent class="w-[75vw] max-w-[1200px] h-[42.2vw] max-h-[675px] flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle>权限管理</DialogTitle>
          <DialogDescription>
            为角色 "{{ managingPermissionRole?.role_name }}" 分配系统权限
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <RolePermissionDialog
            :role="managingPermissionRole"
            :permissions="roleStore.availablePermissions"
            @success="handlePermissionManagementSuccess"
            @cancel="showPermissionDialog = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 删除确认对话框 -->
    <Dialog :open="showDeleteDialog" @update:open="showDeleteDialog = $event">
      <DialogContent class="w-[75vw] max-w-[600px] h-[42.2vw] max-h-[338px] flex flex-col">
        <DialogHeader>
          <DialogTitle>确认删除角色</DialogTitle>
          <DialogDescription>
            此操作将永久删除角色，请谨慎操作
          </DialogDescription>
        </DialogHeader>
        
        <div class="py-4">
          <p class="text-sm text-gray-600">
            确定要删除角色 <span class="font-semibold text-gray-900">{{ deletingRole?.role_name }}</span> 吗？
          </p>
          <ul class="mt-3 text-sm text-red-600 space-y-1">
            <li>• 此操作不可撤销</li>
            <li>• 角色的所有权限关联将被清除</li>
          </ul>
        </div>
        
        <DialogFooter>
          <DialogClose as-child>
            <button class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
              取消
            </button>
          </DialogClose>
          <button 
            @click="confirmDelete"
            :disabled="roleStore.submitting"
            class="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {{ roleStore.submitting ? '删除中...' : '删除' }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    
    <!-- 批量删除确认对话框 -->
    <Dialog :open="showBatchDeleteDialog" @update:open="showBatchDeleteDialog = $event">
      <DialogContent class="w-[75vw] max-w-[600px] h-[42.2vw] max-h-[338px] flex flex-col">
        <DialogHeader>
          <DialogTitle>确认批量删除</DialogTitle>
          <DialogDescription>
            此操作将永久删除多个角色，请谨慎操作
          </DialogDescription>
        </DialogHeader>
        
        <div class="py-4">
          <p class="text-sm text-gray-600">
            确定要删除选中的 <span class="font-semibold text-gray-900">{{ roleStore.selectedRoleIds.length }}</span> 个角色吗？
          </p>
          <ul class="mt-3 text-sm text-red-600 space-y-1">
            <li>• 此操作不可撤销</li>
            <li>• 所有选中角色的权限关联将被清除</li>
          </ul>
        </div>
        
        <DialogFooter>
          <DialogClose as-child>
            <button class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
              取消
            </button>
          </DialogClose>
          <button 
            @click="confirmBatchDelete"
            :disabled="roleStore.submitting"
            class="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {{ roleStore.submitting ? '删除中...' : '批量删除' }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { debounce } from 'lodash-es'
import { useRoleStore } from '@/stores/role.store'
import { useToast } from '@/composables/useToast'
import type { RoleListItem, RoleDetail } from '@/types/user.types'

// 导入对话框组件
import RoleFormDialog from '@/components/role/RoleFormDialog.vue'
import RoleDetailDialog from '@/components/role/RoleDetailDialog.vue'
import RolePermissionDialog from '@/components/role/RolePermissionDialog.vue'

// 导入 shadcn-ui Dialog 组件
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'

// 图标导入
import { 
  PlusIcon, 
  ShieldCheckIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  KeyIcon
} from '@heroicons/vue/24/outline'

// 状态管理
const roleStore = useRoleStore()
const { toast } = useToast()

// 响应式状态
const statistics = ref<any>(null)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showPermissionDialog = ref(false)
const showDeleteDialog = ref(false)
const showBatchDeleteDialog = ref(false)

const editingRole = ref<RoleListItem | null>(null)
const viewingRoleId = ref<string>('')
const deletingRole = ref<RoleListItem | null>(null)
const managingPermissionRole = ref<RoleListItem | null>(null)

const dialogMode = ref<'create' | 'edit'>('create')

// 搜索表单
const searchForm = reactive({
  role_name: '',
  role_code: '',
  status: '',
  description: ''
})

// 全选状态
const isAllSelected = computed(() => {
  return roleStore.roles.length > 0 && 
         roleStore.roles.every(role => roleStore.selectedRoleIds.includes(role.role_id))
})

// 可见页码
const visiblePages = computed(() => {
  const current = roleStore.currentPage
  const total = roleStore.totalPages
  const delta = 2
  const range = []
  const rangeWithDots = []

  for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
    range.push(i)
  }

  if (current - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }

  rangeWithDots.push(...range)

  if (current + delta < total - 1) {
    rangeWithDots.push('...', total)
  } else if (total > 1) {
    rangeWithDots.push(total)
  }

  return rangeWithDots.filter(page => page !== 1 || total === 1)
})

// 防抖搜索
const debouncedSearch = debounce(() => {
  handleSearch()
}, 500)

// 方法定义
const handleSearch = async () => {
  const params = Object.fromEntries(
    Object.entries(searchForm).filter(([_, value]) => value !== '')
  )
  roleStore.setQueryParams(params)
  await roleStore.fetchRoles()
}

const handleReset = async () => {
  Object.assign(searchForm, {
    role_name: '',
    role_code: '',
    status: '',
    description: ''
  })
  roleStore.resetQueryParams()
  await roleStore.fetchRoles()
}

const handleSelectAll = (event: Event) => {
  const checked = (event.target as HTMLInputElement).checked
  if (checked) {
    roleStore.selectAllRoles()
  } else {
    roleStore.clearSelection()
  }
}

const handlePageChange = async (page: number) => {
  roleStore.setPage(page)
  await roleStore.fetchRoles()
}

const handlePageSizeChange = async (event: Event) => {
  const size = (event.target as HTMLSelectElement).value
  roleStore.setPageSize(parseInt(size))
  await roleStore.fetchRoles()
}

const handleViewRole = (role: RoleListItem) => {
  viewingRoleId.value = role.role_id
  showDetailDialog.value = true
}

const handleEditRole = (role: RoleListItem) => {
  editingRole.value = role
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const handleDeleteRole = (role: RoleListItem) => {
  deletingRole.value = role
  showDeleteDialog.value = true
}

const handleManagePermissions = (role: RoleListItem) => {
  managingPermissionRole.value = role
  showPermissionDialog.value = true
}

const handleBatchActivate = async () => {
  try {
    await roleStore.batchOperation('activate')
    toast({
      title: '操作成功',
      description: '角色已批量启用'
    })
  } catch (error) {
    toast({
      title: '操作失败',
      description: '批量启用角色失败',
      variant: 'destructive'
    })
  }
}

const handleBatchDeactivate = async () => {
  try {
    await roleStore.batchOperation('deactivate')
    toast({
      title: '操作成功',
      description: '角色已批量禁用'
    })
  } catch (error) {
    toast({
      title: '操作失败',
      description: '批量禁用角色失败',
      variant: 'destructive'
    })
  }
}

const handleBatchDelete = () => {
  showBatchDeleteDialog.value = true
}

const confirmDelete = async () => {
  if (!deletingRole.value) return
  
  try {
    await roleStore.removeRole(deletingRole.value.role_id)
    showDeleteDialog.value = false
    deletingRole.value = null
    toast({
      title: '删除成功',
      description: '角色已删除',
      variant: 'success'
    })
  } catch (error) {
    toast({
      title: '删除失败',
      description: '删除角色失败',
      variant: 'destructive'
    })
  }
}

const confirmBatchDelete = async () => {
  try {
    await roleStore.batchOperation('delete')
    showBatchDeleteDialog.value = false
    toast({
      title: '删除成功',
      description: '角色已批量删除',
      variant: 'success'
    })
  } catch (error) {
    toast({
      title: '删除失败',
      description: '批量删除角色失败',
      variant: 'destructive'
    })
  }
}

const handleFormSuccess = async () => {
  const isEdit = dialogMode.value === 'edit'
  showCreateDialog.value = false
  editingRole.value = null
  dialogMode.value = 'create'
  toast({
    title: '操作成功',
    description: isEdit ? '角色更新成功' : '角色创建成功',
    variant: 'success'
  })
  // 刷新角色列表
  await roleStore.fetchRoles()
}

const handlePermissionManagementSuccess = () => {
  showPermissionDialog.value = false
  managingPermissionRole.value = null
  toast({
    title: '操作成功',
    description: '权限分配已更新',
    variant: 'success'
  })
}

const handleEditFromDetail = (role: RoleDetail) => {
  showDetailDialog.value = false
  editingRole.value = role as any // RoleDetail extends Role
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const handleManagePermissionsFromDetail = (roleId: string) => {
  const role = roleStore.roles.find(r => r.role_id === roleId)
  if (role) {
    showDetailDialog.value = false
    managingPermissionRole.value = role
    showPermissionDialog.value = true
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听弹窗关闭，重置状态
watch(showCreateDialog, (newVal) => {
  if (!newVal) {
    editingRole.value = null
    dialogMode.value = 'create'
  }
})

// 组件挂载
onMounted(async () => {
  try {
    // 并行加载数据
    await Promise.all([
      roleStore.fetchRoles(),
      roleStore.fetchAvailablePermissions(),
      roleStore.fetchStatistics().then(data => statistics.value = data)
    ])
  } catch (error) {
    console.error('初始化角色管理页面失败:', error)
    toast({
      title: '加载失败',
      description: '初始化页面数据失败',
      variant: 'destructive'
    })
  }
})
</script>

<style scoped>
.role-management {
  max-width: 100%;
  margin: 0 auto;
}

/* 自定义滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格响应式样式 */
@media (max-width: 768px) {
  .role-management {
    padding: 1rem;
  }
  
  .grid-cols-1.md\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-cols-1.md\:grid-cols-4 > :nth-child(n+3) {
    grid-column: span 2;
  }
}
</style>