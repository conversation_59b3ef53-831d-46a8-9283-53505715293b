<template>
  <div class="dashboard p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-3xl font-bold text-gray-900">仪表盘</h1>
      <p class="text-gray-600 mt-1">欢迎使用玉璃生产管理系统</p>
    </div>

    <!-- 快速导航 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- 用户管理 -->
      <router-link
        to="/user-management"
        class="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200"
      >
        <div class="flex items-center space-x-4">
          <div class="p-3 bg-blue-100 rounded-lg">
            <UsersIcon class="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">用户管理</h3>
            <p class="text-sm text-gray-600">管理系统用户账户</p>
          </div>
        </div>
      </router-link>

      <!-- 角色管理 -->
      <router-link
        to="/role-management"
        class="block p-6 bg-white rounded-lg border border-gray-200 hover:border-green-300 hover:shadow-lg transition-all duration-200"
      >
        <div class="flex items-center space-x-4">
          <div class="p-3 bg-green-100 rounded-lg">
            <ShieldCheckIcon class="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">角色管理</h3>
            <p class="text-sm text-gray-600">管理系统角色权限</p>
          </div>
        </div>
      </router-link>

      <!-- 产品管理 -->
      <div class="block p-6 bg-white rounded-lg border border-gray-200 opacity-50 cursor-not-allowed">
        <div class="flex items-center space-x-4">
          <div class="p-3 bg-purple-100 rounded-lg">
            <CubeIcon class="h-8 w-8 text-purple-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">产品管理</h3>
            <p class="text-sm text-gray-600">管理产品信息（待开发）</p>
          </div>
        </div>
      </div>

      <!-- 订单管理 -->
      <div class="block p-6 bg-white rounded-lg border border-gray-200 opacity-50 cursor-not-allowed">
        <div class="flex items-center space-x-4">
          <div class="p-3 bg-orange-100 rounded-lg">
            <DocumentTextIcon class="h-8 w-8 text-orange-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">订单管理</h3>
            <p class="text-sm text-gray-600">管理客户订单（待开发）</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">系统信息</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">版本 2.1</div>
          <div class="text-sm text-gray-600 mt-1">系统版本</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">运行中</div>
          <div class="text-sm text-gray-600 mt-1">系统状态</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">Glass ERP</div>
          <div class="text-sm text-gray-600 mt-1">玉璃生产管理</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  UsersIcon, 
  ShieldCheckIcon,
  CubeIcon,
  DocumentTextIcon
} from '@heroicons/vue/24/outline'
</script>

<style scoped>
.dashboard {
  max-width: 100%;
  margin: 0 auto;
}

/* 卡片悬停效果 */
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 过渡动画 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }
}
</style>