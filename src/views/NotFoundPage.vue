<script setup lang="ts">
import { useRouter } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { Home, ArrowLeft } from 'lucide-vue-next'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-gray-300">404</h1>
        <h2 class="text-2xl font-semibold text-gray-900 mt-4">页面未找到</h2>
        <p class="text-gray-600 mt-2">
          抱歉，您访问的页面不存在或已被移除。
        </p>
      </div>

      <div class="space-y-4">
        <Button
          @click="goHome"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Home class="h-4 w-4 mr-2" />
          返回首页
        </Button>

        <Button
          @click="goBack"
          variant="outline"
          class="w-full"
        >
          <ArrowLeft class="h-4 w-4 mr-2" />
          返回上一页
        </Button>
      </div>
    </div>
  </div>
</template>