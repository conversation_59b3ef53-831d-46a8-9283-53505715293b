<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Lock, User, Eye, EyeOff } from 'lucide-vue-next'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const formData = reactive({
  username: 'user',
  password: '123456',
  remember: false
})

// 状态
const showPassword = ref(false)
const isSubmitting = ref(false)
const errorMessage = ref('')

// 表单验证
const validateForm = () => {
  if (!formData.username.trim()) {
    errorMessage.value = '请输入用户名'
    return false
  }
  if (!formData.password.trim()) {
    errorMessage.value = '请输入密码'
    return false
  }
  if (formData.password.length < 6) {
    errorMessage.value = '密码长度不能少于6位'
    return false
  }
  return true
}

// 登录处理
const handleLogin = async () => {
  errorMessage.value = ''

  if (!validateForm()) {
    return
  }

  try {
    isSubmitting.value = true

    const result = await authStore.login({
      username: formData.username,
      password: formData.password
    })

    if (result.success) {
      // 登录成功，跳转到主页
      router.push('/')
    } else {
      errorMessage.value = result.message || '登录失败'
    }
  } catch (error: any) {
    errorMessage.value = error.message || '登录失败，请稍后重试'
  } finally {
    isSubmitting.value = false
  }
}

// 切换密码显示
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 处理回车键登录
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleLogin()
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          MES 生产执行系统
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          请登录您的账户
        </p>
      </div>

      <!-- 登录表单 -->
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="space-y-4">
          <!-- 用户名输入 -->
          <div>
            <label for="username" class="sr-only">用户名</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User class="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="username"
                v-model="formData.username"
                type="text"
                placeholder="用户名"
                required
                class="pl-10"
                @keydown="handleKeyPress"
              />
            </div>
          </div>

          <!-- 密码输入 -->
          <div>
            <label for="password" class="sr-only">密码</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock class="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="密码"
                required
                class="pl-10 pr-10"
                @keypress="handleKeyPress"
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="togglePasswordVisibility"
              >
                <Eye v-if="!showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" />
                <EyeOff v-else class="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        <!-- 记住我 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember"
              v-model="formData.remember"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="remember" class="ml-2 block text-sm text-gray-900">
              记住我
            </label>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="errorMessage" class="text-red-600 text-sm text-center">
          {{ errorMessage }}
        </div>

        <!-- 登录按钮 -->
        <div>
          <Button
            type="submit"
            :disabled="isSubmitting"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isSubmitting">登录中...</span>
            <span v-else>登录</span>
          </Button>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>