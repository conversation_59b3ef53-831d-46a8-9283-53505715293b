# Auth 模块文档

## LoginPage.vue 组件说明

### 概述
LoginPage.vue 是 Glass ERP V2.1 系统的用户认证入口页面，提供现代化、响应式的登录体验。

### 功能特性

#### 🔐 核心认证功能
- **用户名/邮箱登录**: 支持用户名或邮箱地址登录
- **密码输入保护**: 密码输入框支持显示/隐藏切换
- **记住我功能**: 可选择记住用户名以便下次登录
- **自动跳转**: 登录成功后自动跳转到目标页面

#### 🛡️ 安全机制
- **登录失败限制**: 5次失败后账户锁定15分钟
- **实时验证**: 表单字段实时验证和错误提示
- **安全倒计时**: 账户锁定期间显示剩余解锁时间
- **防暴力破解**: 自动安全机制防止恶意登录尝试

#### 📱 响应式设计
- **移动端优先**: 采用移动端优先的响应式设计
- **24列栅格系统**: 遵循项目设计规范
- **多设备适配**: 完美适配从手机到超大屏的所有设备
- **现代化UI**: 使用渐变背景和卡片布局

#### ♿ 无障碍支持
- **键盘导航**: 支持Tab键和Enter键操作
- **屏幕阅读器**: 适当的ARIA标签和语义化标签
- **高对比度**: 支持高对比度模式
- **减少动画**: 支持prefers-reduced-motion

### 技术实现

#### 依赖组件
```typescript
// UI组件
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Card } from '@/components/ui/card'

// 图标
import { Lock, User, Eye, EyeOff, Loader2, AlertCircle, CheckCircle, Shield } from 'lucide-vue-next'
```

#### 状态管理
```typescript
// 表单数据
const formData = reactive({
  username: '',
  password: '',
  remember: false
})

// 验证状态
const { errors, validateField, validateAll, isValid, clearError } = useFormValidation(formData, validationSchema)

// 安全状态
const loginAttempts = ref(0)
const isBlocked = ref(false)
const blockEndTime = ref<Date | null>(null)
```

#### 表单验证规则
- **用户名**: 必填，最少3个字符，支持字母、数字、@、.、-、_
- **密码**: 必填，最少6个字符
- **实时验证**: 输入时清除错误，失焦时验证
- **提交验证**: 提交前完整验证所有字段

### 样式特性

#### 设计系统
- **颜色方案**: 蓝色渐变主题，符合ERP系统专业形象
- **间距系统**: 基于8px的间距体系
- **圆角**: 现代化的圆角设计（8px-16px）
- **阴影**: 层次感的阴影效果

#### 响应式断点
```css
/* 移动端 */
@media (max-width: 640px) { }

/* 小屏平板 */
@media (min-width: 768px) { }

/* 中屏桌面 */
@media (min-width: 1024px) { }

/* 大屏桌面 */
@media (min-width: 1440px) { }

/* 超大屏 */
@media (min-width: 1920px) { }
```

#### 动画效果
- **过渡动画**: 所有交互元素的平滑过渡
- **加载状态**: 提交时的加载动画
- **状态反馈**: 错误和成功状态的视觉反馈

### API 集成

#### 认证接口
```typescript
// 登录
const result = await authStore.login({
  username: formData.username.trim(),
  password: formData.password
})

// 自动获取用户信息
// 在authStore中自动调用getCurrentUser()
```

#### 路由处理
```typescript
// 登录成功跳转
const redirectUrl = computed(() => {
  return (route.query.redirect as string) || '/'
})

// 自动跳转
router.push(redirectUrl.value)
```

### 使用方法

#### 基本使用
```vue
<!-- 在路由中使用 -->
<template>
  <router-view />
</template>
```

#### 路由配置
```typescript
{
  path: '/login',
  name: 'Login',
  component: () => import('@/views/auth/LoginPage.vue'),
  meta: {
    requiresAuth: false,
    title: '登录'
  }
}
```

### 错误处理

#### 验证错误
- 实时字段验证
- 统一错误提示样式
- 清晰的错误信息

#### 登录错误
- 网络错误处理
- 业务错误处理
- 安全锁定提示

#### 安全机制
- 登录失败次数统计
- 自动账户锁定
- 解锁倒计时显示

### 性能优化

#### 代码分割
- 路由级别的懒加载
- 组件按需引入

#### 缓存策略
- 记住用户名的本地存储
- 认证状态的持久化

#### 用户体验
- 快速响应的表单验证
- 平滑的过渡动画
- 清晰的状态反馈

### 最佳实践

#### 安全性
- 密码不在客户端明文存储
- 安全的表单提交
- 适当的错误信息展示

#### 用户体验
- 清晰的视觉层次
- 直观的交互反馈
- 友好的错误提示

#### 可维护性
- 组件化的UI结构
- 可复用的验证逻辑
- 清晰的代码组织

### 扩展指南

#### 添加新的验证规则
```typescript
// 在useFormValidation中添加自定义规则
const customRule = {
  custom: (value: string) => {
    // 自定义验证逻辑
    return validationMessage || null
  }
}
```

#### 修改UI样式
```vue
<!-- 更新CSS类名或添加新样式 -->
<Card class="custom-login-card">
  <!-- 内容 -->
</Card>
```

#### 集成第三方认证
```typescript
// 添加OAuth等第三方登录方式
const handleOAuthLogin = (provider: string) => {
  // 第三方登录逻辑
}
```

### 故障排除

#### 常见问题
1. **表单验证不生效**: 检查validation规则配置
2. **样式显示异常**: 检查Tailwind CSS配置
3. **路由跳转失败**: 检查路由权限配置
4. **API调用失败**: 检查网络连接和API端点

#### 调试技巧
- 使用Vue DevTools查看组件状态
- 检查控制台错误信息
- 验证API响应格式
- 测试不同设备的兼容性

### 更新日志

#### v2.1.0
- ✅ 完成现代化登录界面设计
- ✅ 实现安全登录机制
- ✅ 添加响应式设计支持
- ✅ 集成表单验证系统
- ✅ 优化用户体验和无障碍支持