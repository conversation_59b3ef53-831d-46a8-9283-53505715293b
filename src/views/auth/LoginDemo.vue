<script setup lang="ts">
import { ref } from 'vue'
import LoginPage from './LoginPage.vue'

const showDemo = ref(true)
const demoInfo = ref({
  title: 'Glass ERP V2.1 登录页面演示',
  features: [
    '🎨 现代化渐变背景设计',
    '📱 完全响应式布局（移动端优先）',
    '🔐 安全机制（5次失败锁定15分钟）',
    '✅ 实时表单验证',
    '♿ 无障碍支持（键盘导航、屏幕阅读器）',
    '🌙 深色模式支持',
    '⚡ 高性能动画和过渡效果',
    '💾 记住用户名功能',
    '🔄 自动解锁倒计时',
    '🛡️ 防暴力破解保护'
  ],
  testCredentials: {
    username: 'demo',
    password: 'demo123'
  }
})
</script>

<template>
  <div>
    <!-- 演示信息面板 -->
    <div v-if="showDemo" class="fixed top-4 right-4 z-50 max-w-sm">
      <div class="bg-white rounded-lg shadow-lg border p-4">
        <div class="flex items-start justify-between mb-3">
          <h3 class="font-semibold text-gray-900">{{ demoInfo.title }}</h3>
          <button 
            @click="showDemo = false"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div class="space-y-3">
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">功能特性：</h4>
            <ul class="text-xs text-gray-600 space-y-1">
              <li v-for="feature in demoInfo.features" :key="feature">
                {{ feature }}
              </li>
            </ul>
          </div>
          
          <div class="pt-2 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-1">测试凭据：</h4>
            <p class="text-xs text-gray-600">
              用户名: <code class="bg-gray-100 px-1 rounded">{{ demoInfo.testCredentials.username }}</code><br>
              密码: <code class="bg-gray-100 px-1 rounded">{{ demoInfo.testCredentials.password }}</code>
            </p>
          </div>
          
          <div class="pt-2 border-t border-gray-200">
            <p class="text-xs text-gray-500">
              💡 尝试输入错误密码5次以测试安全锁定功能
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 实际登录页面 -->
    <LoginPage />
    
    <!-- 显示/隐藏演示面板按钮 -->
    <button 
      v-if="!showDemo"
      @click="showDemo = true"
      class="fixed top-4 right-4 z-50 bg-blue-600 text-white p-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
      title="显示演示信息"
    >
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>
</template>

<style scoped>
code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

@media (max-width: 640px) {
  .fixed.top-4.right-4 {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
}
</style>