<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useFormValidation, commonRules } from '@/composables/useFormValidation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Card } from '@/components/ui/card'
import { Lock, User, Eye, EyeOff, Loader2, AlertCircle, CheckCircle, Shield } from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单数据
const formData = reactive({
  username: 'admin',
  password: 'admin123',
  remember: false
})

// 表单验证
const { errors, validateField, validateAll, isValid, clearError } = useFormValidation(formData, {
  username: {
    ...commonRules.username,
    custom: (value: string) => {
      if (!value || !value.trim()) {
        return '请输入用户名或邮箱'
      }
      if (value.length < 2) {
        return '用户名或邮箱至少2个字符'
      }
      return null
    }
  },
  password: {
    ...commonRules.password,
    custom: (value: string) => {
      if (!value || !value.trim()) {
        return '请输入密码'
      }
      if (value.length < 6) {
        return '密码至少6个字符'
      }
      return null
    }
  }
})

// 状态管理
const showPassword = ref(false)
const isSubmitting = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const loginAttempts = ref(0)
const isBlocked = ref(false)
const blockEndTime = ref<Date | null>(null)

// 计算属性
const isFormValid = computed(() => {
  return formData.username.trim() && 
         formData.password.trim() && 
         formData.password.length >= 6 &&
         isValid.value &&
         !isBlocked.value
})

const redirectUrl = computed(() => {
  return (route.query.redirect as string) || '/'
})

const remainingTime = computed(() => {
  if (!blockEndTime.value) return 0
  const remaining = Math.max(0, blockEndTime.value.getTime() - Date.now())
  return Math.ceil(remaining / 1000)
})

// 实时验证
const handleFieldBlur = (field: 'username' | 'password') => {
  validateField(field)
}

const handleFieldInput = (field: 'username' | 'password') => {
  clearError(field)
  errorMessage.value = ''
}

// 安全机制：登录失败次数限制
const handleLoginFailure = () => {
  loginAttempts.value++
  
  if (loginAttempts.value >= 5) {
    isBlocked.value = true
    blockEndTime.value = new Date(Date.now() + 15 * 60 * 1000) // 15分钟后解锁
    
    // 设置定时器自动解锁
    setTimeout(() => {
      isBlocked.value = false
      loginAttempts.value = 0
      blockEndTime.value = null
    }, 15 * 60 * 1000)
    
    errorMessage.value = '登录失败次数过多，账户已被锁定15分钟'
  } else {
    errorMessage.value = `登录失败，还可以尝试 ${5 - loginAttempts.value} 次`
  }
}

// 登录处理
const handleLogin = async () => {
  errorMessage.value = ''
  successMessage.value = ''

  if (isBlocked.value) {
    errorMessage.value = `账户已被锁定，请在 ${remainingTime.value} 秒后重试`
    return
  }

  if (!validateAll()) {
    return
  }

  try {
    isSubmitting.value = true

    const result = await authStore.login({
      username: formData.username.trim(),
      password: formData.password
    })

    if (result.success) {
      // 重置登录失败计数
      loginAttempts.value = 0
      successMessage.value = '登录成功，正在跳转...'
      
      // 保存记住我状态
      if (formData.remember) {
        localStorage.setItem('remembered_username', formData.username.trim())
      } else {
        localStorage.removeItem('remembered_username')
      }
      
      // 短暂显示成功消息后跳转
      setTimeout(() => {
        router.push(redirectUrl.value)
      }, 800)
    } else {
      handleLoginFailure()
    }
  } catch (error: any) {
    console.error('Login error:', error)
    handleLoginFailure()
  } finally {
    isSubmitting.value = false
  }
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 处理键盘事件
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && isFormValid.value && !isSubmitting.value) {
    handleLogin()
  }
}

// 重置表单
const resetForm = () => {
  formData.username = ''
  formData.password = ''
  formData.remember = false
  // 清除验证错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
  errorMessage.value = ''
  successMessage.value = ''
}

// 组件挂载时的初始化
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    router.push(redirectUrl.value)
  }
  
  // 从 localStorage 恢复记住我状态
  const rememberedUsername = localStorage.getItem('remembered_username')
  if (rememberedUsername) {
    formData.username = rememberedUsername
    formData.remember = true
  }
})

// 监听记住我状态
const handleRememberChange = (checked: boolean) => {
  formData.remember = checked
  if (!checked) {
    localStorage.removeItem('remembered_username')
  }
}

// 定时器更新剩余时间
if (typeof window !== 'undefined') {
  setInterval(() => {
    if (isBlocked.value && remainingTime.value <= 0) {
      isBlocked.value = false
      loginAttempts.value = 0
      blockEndTime.value = null
    }
  }, 1000)
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/20 rounded-full blur-3xl"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400/20 rounded-full blur-3xl"></div>
    </div>

    <div class="relative w-full max-w-md">
      <!-- 主登录卡片 -->
      <Card class="px-8 py-10 backdrop-blur-sm bg-white/80 shadow-2xl border-0">
        <!-- Logo 和标题区域 -->
        <div class="text-center mb-8">
          <!-- Logo 占位符 - 可以替换为实际的公司Logo -->
          <div class="mx-auto mb-6 w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          
          <h1 class="text-2xl font-bold text-gray-900 mb-2">
            Glass ERP V2.1
          </h1>
          <p class="text-gray-600 text-sm">
            玻璃生产管理系统
          </p>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 用户名输入 -->
          <div class="space-y-2">
            <Label for="username" class="text-sm font-medium text-gray-700">
              用户名
            </Label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User class="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="username"
                v-model="formData.username"
                type="text"
                placeholder="请输入用户名或邮箱"
                autocomplete="username"
                :disabled="isBlocked"
                :class="[
                  'pl-10 transition-all duration-200',
                  errors.username 
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20' 
                    : 'focus:border-blue-500 focus:ring-blue-500/20',
                  isBlocked ? 'bg-gray-100 cursor-not-allowed' : ''
                ]"
                @input="handleFieldInput('username')"
                @blur="handleFieldBlur('username')"
                @keydown="handleKeyPress"
              />
            </div>
            <p v-if="errors.username" class="text-xs text-red-600 flex items-center gap-1">
              <AlertCircle class="h-3 w-3" />
              {{ errors.username }}
            </p>
          </div>

          <!-- 密码输入 -->
          <div class="space-y-2">
            <Label for="password" class="text-sm font-medium text-gray-700">
              密码
            </Label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock class="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                autocomplete="current-password"
                :disabled="isBlocked"
                :class="[
                  'pl-10 pr-10 transition-all duration-200',
                  errors.password 
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20' 
                    : 'focus:border-blue-500 focus:ring-blue-500/20',
                  isBlocked ? 'bg-gray-100 cursor-not-allowed' : ''
                ]"
                @input="handleFieldInput('password')"
                @blur="handleFieldBlur('password')"
                @keydown="handleKeyPress"
              />
              <button
                type="button"
                :disabled="isBlocked"
                class="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors duration-200 disabled:cursor-not-allowed"
                @click="togglePasswordVisibility"
              >
                <Eye 
                  v-if="!showPassword" 
                  class="h-5 w-5 text-gray-400 hover:text-gray-600" 
                />
                <EyeOff 
                  v-else 
                  class="h-5 w-5 text-gray-400 hover:text-gray-600" 
                />
              </button>
            </div>
            <p v-if="errors.password" class="text-xs text-red-600 flex items-center gap-1">
              <AlertCircle class="h-3 w-3" />
              {{ errors.password }}
            </p>
          </div>

          <!-- 记住我选项 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <Checkbox
                id="remember"
                :checked="formData.remember"
                @update:checked="handleRememberChange"
              />
              <Label 
                for="remember" 
                class="text-sm text-gray-600 cursor-pointer select-none"
              >
                记住我
              </Label>
            </div>
            
            <a 
              href="#" 
              class="text-sm text-blue-600 hover:text-blue-800 transition-colors duration-200"
              @click.prevent="$router.push('/forgot-password')"
            >
              忘记密码？
            </a>
          </div>

          <!-- 安全警告 -->
          <div v-if="isBlocked" class="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div class="flex items-start gap-3">
              <Shield class="h-5 w-5 text-orange-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 class="text-sm font-medium text-orange-800 mb-1">
                  账户安全保护
                </h4>
                <p class="text-sm text-orange-700">
                  由于多次登录失败，您的账户已被临时锁定。
                  <br>
                  <span class="font-medium">{{ remainingTime }} 秒后自动解锁</span>
                </p>
              </div>
            </div>
          </div>

          <!-- 错误和成功消息 -->
          <div v-if="errorMessage && !isBlocked" class="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-sm text-red-700 flex items-center gap-2">
              <AlertCircle class="h-4 w-4 flex-shrink-0" />
              {{ errorMessage }}
            </p>
          </div>

          <div v-if="successMessage" class="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p class="text-sm text-green-700 flex items-center gap-2">
              <CheckCircle class="h-4 w-4 flex-shrink-0" />
              {{ successMessage }}
            </p>
          </div>

          <!-- 登录按钮 -->
          <Button
            type="submit"
            :disabled="!isFormValid || isSubmitting"
            class="w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
          >
            <Loader2 v-if="isSubmitting" class="h-4 w-4 mr-2 animate-spin" />
            <span v-if="isSubmitting">登录中...</span>
            <span v-else>登录</span>
          </Button>
        </form>

        <!-- 底部信息 -->
        <div class="mt-8 text-center">
          <p class="text-xs text-gray-500">
            © 2024 Glass ERP V2.1. 保留所有权利.
          </p>
        </div>
      </Card>

      <!-- 响应式帮助信息 -->
      <div class="mt-6 text-center">
        <p class="text-sm text-gray-600">
          需要帮助？请联系 
          <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
            技术支持
          </a>
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

/* 响应式断点优化 */
@media (max-width: 640px) {
  .backdrop-blur-sm {
    backdrop-filter: none;
    background: white;
  }
}

/* 深色模式支持 (可选) */
@media (prefers-color-scheme: dark) {
  .bg-gradient-to-br {
    background: linear-gradient(to bottom right, #0f172a, #1e293b, #334155);
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .text-gray-400 {
    color: #6b7280;
  }
  
  .text-gray-600 {
    color: #374151;
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .min-h-screen {
    min-height: auto;
  }
  
  .bg-gradient-to-br {
    background: white;
  }
  
  .shadow-2xl {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hover\:bg-blue-700:hover {
    background-color: rgb(29 78 216);
  }
  
  .hover\:text-blue-800:hover {
    color: rgb(30 64 175);
  }
}
</style>