<template>
  <div class="test-auth-page">
    <h1>认证状态测试页面</h1>
    
    <div class="auth-status">
      <h2>认证状态</h2>
      <p><strong>已初始化:</strong> {{ authStore.isInitialized }}</p>
      <p><strong>已认证:</strong> {{ authStore.isAuthenticated }}</p>
      <p><strong>有Token:</strong> {{ !!authStore.accessToken }}</p>
      <p><strong>有用户信息:</strong> {{ !!authStore.user }}</p>
      <p><strong>用户:</strong> {{ authStore.user?.username || '无' }}</p>
    </div>

    <div class="local-storage">
      <h2>LocalStorage 状态</h2>
      <p><strong>Access Token:</strong> {{ localStorageStatus.accessToken }}</p>
      <p><strong>Refresh Token:</strong> {{ localStorageStatus.refreshToken }}</p>
      <p><strong>用户信息:</strong> {{ localStorageStatus.userInfo }}</p>
    </div>

    <div class="actions">
      <h2>操作</h2>
      <button @click="testApiCall" :disabled="!authStore.isAuthenticated">
        测试API调用
      </button>
      <button @click="clearAuth">清除认证</button>
      <button @click="refreshPage">刷新页面</button>
    </div>

    <div class="logs" v-if="logs.length > 0">
      <h2>日志</h2>
      <div v-for="(log, index) in logs" :key="index" class="log-item">
        <span class="timestamp">{{ log.timestamp }}</span>
        <span class="message">{{ log.message }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const logs = ref<Array<{timestamp: string, message: string}>>([])

// 计算localStorage状态
const localStorageStatus = computed(() => {
  if (typeof window === 'undefined') {
    return {
      accessToken: '无法访问',
      refreshToken: '无法访问',
      userInfo: '无法访问'
    }
  }
  
  return {
    accessToken: localStorage.getItem('access_token') ? '存在' : '不存在',
    refreshToken: localStorage.getItem('refresh_token') ? '存在' : '不存在',
    userInfo: localStorage.getItem('user_info') ? '存在' : '不存在'
  }
})

const addLog = (message: string) => {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message
  })
}

const testApiCall = async () => {
  try {
    addLog('开始测试API调用...')
    // 这里可以调用任何需要认证的API
    addLog('API调用成功')
  } catch (error: any) {
    addLog(`API调用失败: ${error.message}`)
  }
}

const clearAuth = () => {
  authStore.clearAuthData()
  addLog('认证数据已清除')
}

const refreshPage = () => {
  addLog('页面即将刷新...')
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

onMounted(() => {
  addLog('页面已加载')
  addLog(`认证状态: ${authStore.isAuthenticated ? '已认证' : '未认证'}`)
})
</script>

<style scoped>
.test-auth-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.auth-status, .local-storage, .actions, .logs {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.actions button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.actions button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.actions button:hover:not(:disabled) {
  background: #0056b3;
}

.log-item {
  margin: 5px 0;
  padding: 5px;
  background: #f8f9fa;
  border-radius: 4px;
}

.timestamp {
  color: #666;
  font-size: 0.9em;
  margin-right: 10px;
}

.message {
  font-family: monospace;
}
</style> 