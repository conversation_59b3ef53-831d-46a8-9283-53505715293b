import type { ModelBase, QueryParams } from './api.types'

// 用户状态枚举
export type UserStatus = 'ACTIVE' | 'INACTIVE'

// 用户模型
export interface User extends ModelBase {
  user_id: string
  username: string
  password_hash?: string // 仅在创建时使用，查询时不返回
  employee_id?: string
  name: string
  email?: string
  phone?: string
  department_id?: string
  department_name?: string
  status: UserStatus
  last_login_date?: string
  created_date: string
  updated_date: string
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  password: string
  employee_id?: string
  name: string
  email?: string
  phone?: string
  department_id?: string
  status?: UserStatus
}

// 更新用户请求
export interface UpdateUserRequest {
  employee_id?: string
  name?: string
  email?: string
  phone?: string
  department_id?: string
  status?: UserStatus
}

// 重置密码请求
export interface ResetPasswordRequest {
  new_password: string
}

// 角色模型
export interface Role extends ModelBase {
  role_id: string
  role_name: string
  role_code: string
  description?: string
  status: UserStatus
  created_date: string
  updated_date: string
}

// 用户角色关联
export interface UserRole {
  user_id: string
  role_id: string
  role_name: string
  role_code: string
  assigned_date: string
}

// 分配角色请求
export interface AssignRoleRequest {
  role_id: string
}

// 组织架构模型
export interface Organization extends ModelBase {
  org_id: string
  org_code: string
  org_name: string
  parent_id?: string
  level: number
  path?: string
  status: UserStatus
  children?: Organization[]
  created_date: string
  updated_date: string
}

// 用户查询参数
export interface UserQueryParams extends QueryParams {
  username?: string
  name?: string
  department_id?: string
  status?: UserStatus
  employee_id?: string
  email?: string
  phone?: string
}

// 用户列表项（用于表格显示）
export interface UserListItem {
  user_id: string
  username: string
  employee_id?: string
  name: string
  email?: string
  phone?: string
  department_id?: string
  department_name?: string
  status: UserStatus
  last_login_date?: string
  created_date: string
  updated_date: string
  role_count?: number // 用户拥有的角色数量
}

// 用户详情（包含角色信息）
export interface UserDetail extends User {
  roles: UserRole[]
  department?: Organization
}

// 批量操作类型
export type BatchAction = 'activate' | 'deactivate' | 'delete'

// 批量操作请求
export interface BatchOperationRequest {
  user_ids: string[]
  action: BatchAction
}

// 用户表单验证规则
export interface UserFormValidation {
  username: {
    required: boolean
    min: number
    max: number
    pattern?: RegExp
  }
  password: {
    required: boolean
    min: number
    max: number
    pattern?: RegExp
  }
  name: {
    required: boolean
    min: number
    max: number
  }
  email: {
    pattern?: RegExp
  }
  phone: {
    pattern?: RegExp
  }
}

// 用户权限检查
export interface UserPermissions {
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canResetPassword: boolean
  canAssignRole: boolean
  canViewList: boolean
  canViewDetail: boolean
  canBatchOperation: boolean
}

// 角色查询参数
export interface RoleQueryParams extends QueryParams {
  role_name?: string
  role_code?: string
  status?: UserStatus
  description?: string
}

// 创建角色请求
export interface CreateRoleRequest {
  role_name: string
  role_code: string
  description?: string
  status?: UserStatus
}

// 更新角色请求
export interface UpdateRoleRequest {
  role_name?: string
  role_code?: string
  description?: string
  status?: UserStatus
}

// 角色列表项（用于表格显示）
export interface RoleListItem {
  role_id: string
  role_name: string
  role_code: string
  description?: string
  status: UserStatus
  user_count?: number // 使用该角色的用户数量
  permission_count?: number // 角色拥有的权限数量
  created_date: string
  updated_date: string
}

// 角色详情（包含权限信息）
export interface RoleDetail extends Role {
  permissions: Permission[]
  users: UserListItem[]
}

// 权限模型
export interface Permission {
  permission_id: string
  permission_name: string
  permission_code: string
  resource_type: string // 资源类型：menu, action, data
  resource_path?: string
  description?: string
  parent_id?: string
  level: number
  children?: Permission[]
  created_date: string
  updated_date: string
}

// 角色权限关联
export interface RolePermission {
  role_id: string
  permission_id: string
  permission_name: string
  permission_code: string
  resource_type: string
  assigned_date: string
}

// 分配权限请求
export interface AssignPermissionRequest {
  permission_ids: string[]
}

// 角色统计信息
export interface RoleStatistics {
  total_roles: number
  active_roles: number
  inactive_roles: number
  roles_by_type: Array<{
    resource_type: string
    role_count: number
  }>
  permission_usage: Array<{
    permission_id: string
    permission_name: string
    usage_count: number
  }>
}

// 角色权限检查
export interface RolePermissions {
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canAssignPermission: boolean
  canViewList: boolean
  canViewDetail: boolean
  canBatchOperation: boolean
}