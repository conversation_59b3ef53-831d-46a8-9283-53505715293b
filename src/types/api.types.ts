/**
 * 模型基类
 */
export interface ModelBase {
}

/**
 * API 统一响应格式
 */
export interface ResponseModel<T = any> {
  code: number
  msg: string
  data: T
}

/**
 * API 分页响应格式
 */
export interface ResponseListModel<T = any> {
  code: number
  msg: string
  data: {
    items: T[]
    has_more: boolean
    total: number
  }
}

/**
 * API 统一查询参数
 */
export enum FilterOperator {
  EQUAL = 'eq', // 等于
  NOT_EQUAL = 'ne', // 不等于
  LIKE = 'ilike', // 模糊匹配
  GT = 'gt', // 大于
  GE = 'ge', // 大于等于
  LT = 'lt', // 小于
  LE = 'le', // 小于等于
  IN = 'in', // 包含
  BETWEEN = 'between', // 介于
  IS_NULL = 'is_null', // 为 null
  NOT_NULL = 'not_null', // 不为 null
}

// 基础操作符类型
export type FilterOperatorKey =
  | 'eq'
  | 'ne'
  | 'gt'
  | 'ge'
  | 'lt'
  | 'le'
  | 'ilike'
  | 'in'
  | 'between'
  | 'is_null'
  | 'not_null'

/**
 * 过滤器操作符映射
 */
export const FilterOperatorMap = {
  [FilterOperator.EQUAL]:     '等于',
  [FilterOperator.NOT_EQUAL]: '不等于',
  [FilterOperator.LIKE]:      '模糊匹配',
  [FilterOperator.GT]:        '大于',
  [FilterOperator.GE]:        '大于等于',
  [FilterOperator.LT]:        '小于',
  [FilterOperator.LE]:        '小于等于',
  [FilterOperator.IN]:        '包含',
  [FilterOperator.BETWEEN]:   '介于',
  [FilterOperator.IS_NULL]:   '为 null',
  [FilterOperator.NOT_NULL]:  '不为 null',
} as const

// 排序方向
export type SortOrder = 'asc' | 'desc'

// 条件单元
export interface FilterCondition {
  id?: string
  field: string
  op: FilterOperator | FilterOperatorKey
  value?: any
  [key: string]: any
}

// 嵌套条件组
export interface FilterGroup {
  id?: string
  couple?: 'and' | 'or'
  conditions: Array<FilterCondition | FilterGroup>
}

// 排序字段
export interface SortField {
  field: string
  order: SortOrder
}

// 完整查询参数
export interface QueryParams {
  filters?: FilterGroup // 最外层只接收FilterGroup
  sort?: SortField[]
  offset: number
  limit: number
}