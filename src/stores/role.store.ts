import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { 
  queryRoles,
  getRoleDetail,
  createRole,
  updateRole,
  deleteRole,
  batchOperateRoles,
  getRolePermissions,
  assignPermissionsToRole,
  removePermissionFromRole,
  getAvailablePermissions,
  getPermissionsFlat,
  getRoleStatistics,
  getRoleUsage,
  validateRoleName,
  validateRoleCode
} from '@/api/role.api'
import type { 
  RoleListItem,
  RoleDetail,
  CreateRoleRequest,
  UpdateRoleRequest,
  Permission,
  RoleQueryParams,
  RoleStatistics,
  RolePermissions
} from '@/types'

export const useRoleStore = defineStore('role', () => {
  // 状态
  const roles = ref<RoleListItem[]>([])
  const roleDetail = ref<RoleDetail | null>(null)
  const selectedRoleIds = ref<string[]>([])
  const availablePermissions = ref<Permission[]>([])
  const permissionsFlat = ref<Permission[]>([])
  const statistics = ref<RoleStatistics | null>(null)
  const roleUsage = ref<any>(null)
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const hasMore = ref(false)
  
  // 加载状态
  const loading = ref(false)
  const detailLoading = ref(false)
  const submitting = ref(false)
  
  // 查询参数
  const queryParams = ref<RoleQueryParams>({
    offset: 0,
    limit: 20
  })

  // 计算属性
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
  const hasSelectedRoles = computed(() => selectedRoleIds.value.length > 0)
  const canBatchDelete = computed(() => selectedRoleIds.value.length > 0)
  const canBatchActivate = computed(() => selectedRoleIds.value.length > 0)
  const canBatchDeactivate = computed(() => selectedRoleIds.value.length > 0)

  // 权限检查 (从认证store或后端获取实际权限)
  const permissions = computed<RolePermissions>(() => ({
    canCreate: true, // TODO: 从实际权限系统获取
    canEdit: true,
    canDelete: true,
    canAssignPermission: true,
    canViewList: true,
    canViewDetail: true,
    canBatchOperation: true
  }))

  // 扁平化权限树
  const flatPermissions = computed(() => {
    const flatten = (perms: Permission[], level = 0): Permission[] => {
      const result: Permission[] = []
      for (const perm of perms) {
        result.push({ ...perm, level })
        if (perm.children && perm.children.length > 0) {
          result.push(...flatten(perm.children, level + 1))
        }
      }
      return result
    }
    return flatten(availablePermissions.value)
  })

  // 动作
  const fetchRoles = async (params?: Partial<RoleQueryParams>) => {
    loading.value = true
    try {
      const queryData = {
        ...queryParams.value,
        ...params,
        offset: ((currentPage.value - 1) * pageSize.value),
        limit: pageSize.value
      }
      
      const response = await queryRoles(queryData)
      if (response.code === 200) {
        roles.value = response.data.items
        total.value = response.data.total
        hasMore.value = response.data.has_more
        
        // 更新查询参数
        Object.assign(queryParams.value, queryData)
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchRoleDetail = async (roleId: string) => {
    detailLoading.value = true
    try {
      const response = await getRoleDetail(roleId)
      if (response.code === 200) {
        roleDetail.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取角色详情失败:', error)
      throw error
    } finally {
      detailLoading.value = false
    }
  }

  const addRole = async (roleData: CreateRoleRequest) => {
    submitting.value = true
    try {
      const response = await createRole(roleData)
      if (response.code === 200) {
        // 刷新角色列表
        await fetchRoles()
      }
      return response
    } catch (error) {
      console.error('创建角色失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }

  const editRole = async (roleId: string, roleData: UpdateRoleRequest) => {
    submitting.value = true
    try {
      const response = await updateRole(roleId, roleData)
      if (response.code === 200) {
        // 更新列表中的角色信息
        const index = roles.value.findIndex(r => r.role_id === roleId)
        if (index !== -1) {
          Object.assign(roles.value[index], response.data)
        }
        // 如果当前正在查看该角色详情，也更新详情
        if (roleDetail.value?.role_id === roleId) {
          Object.assign(roleDetail.value, response.data)
        }
      }
      return response
    } catch (error) {
      console.error('更新角色失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }

  const removeRole = async (roleId: string) => {
    try {
      const response = await deleteRole(roleId)
      if (response.code === 200) {
        // 从列表中移除角色
        const index = roles.value.findIndex(r => r.role_id === roleId)
        if (index !== -1) {
          roles.value.splice(index, 1)
          total.value--
        }
        // 从选中列表中移除
        const selectedIndex = selectedRoleIds.value.indexOf(roleId)
        if (selectedIndex !== -1) {
          selectedRoleIds.value.splice(selectedIndex, 1)
        }
      }
      return response
    } catch (error) {
      console.error('删除角色失败:', error)
      throw error
    }
  }

  const batchOperation = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedRoleIds.value.length === 0) return
    
    try {
      const response = await batchOperateRoles({
        user_ids: selectedRoleIds.value,
        action
      })
      
      if (response.code === 200) {
        if (action === 'delete') {
          // 删除操作：从列表中移除
          roles.value = roles.value.filter(r => !selectedRoleIds.value.includes(r.role_id))
          total.value -= selectedRoleIds.value.length
        } else {
          // 激活/禁用操作：更新状态
          const newStatus = action === 'activate' ? 'ACTIVE' : 'INACTIVE'
          roles.value.forEach(role => {
            if (selectedRoleIds.value.includes(role.role_id)) {
              role.status = newStatus
            }
          })
        }
        // 清空选中列表
        selectedRoleIds.value = []
      }
      return response
    } catch (error) {
      console.error('批量操作失败:', error)
      throw error
    }
  }

  // 权限管理相关
  const fetchRolePermissions = async (roleId: string) => {
    try {
      const response = await getRolePermissions(roleId)
      return response.data
    } catch (error) {
      console.error('获取角色权限失败:', error)
      throw error
    }
  }

  const assignPermissions = async (roleId: string, permissionIds: string[]) => {
    try {
      const response = await assignPermissionsToRole(roleId, { permission_ids: permissionIds })
      if (response.code === 200 && roleDetail.value?.role_id === roleId) {
        // 重新获取角色详情以更新权限列表
        await fetchRoleDetail(roleId)
      }
      return response
    } catch (error) {
      console.error('分配权限失败:', error)
      throw error
    }
  }

  const removePermission = async (roleId: string, permissionId: string) => {
    try {
      const response = await removePermissionFromRole(roleId, permissionId)
      if (response.code === 200 && roleDetail.value?.role_id === roleId) {
        // 从详情中移除权限
        roleDetail.value.permissions = roleDetail.value.permissions.filter(p => p.permission_id !== permissionId)
      }
      return response
    } catch (error) {
      console.error('移除权限失败:', error)
      throw error
    }
  }

  const fetchAvailablePermissions = async () => {
    try {
      const response = await getAvailablePermissions()
      if (response.code === 200) {
        availablePermissions.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取可用权限失败:', error)
      throw error
    }
  }

  const fetchPermissionsFlat = async () => {
    try {
      const response = await getPermissionsFlat()
      if (response.code === 200) {
        permissionsFlat.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取权限列表失败:', error)
      throw error
    }
  }

  // 统计信息
  const fetchStatistics = async () => {
    try {
      const response = await getRoleStatistics()
      if (response.code === 200) {
        statistics.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw error
    }
  }

  const fetchRoleUsage = async (roleId: string) => {
    try {
      const response = await getRoleUsage(roleId)
      if (response.code === 200) {
        roleUsage.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取角色使用情况失败:', error)
      throw error
    }
  }

  // 验证相关
  const checkRoleNameAvailable = async (roleName: string, excludeRoleId?: string) => {
    try {
      const response = await validateRoleName(roleName, excludeRoleId)
      return response.data.available
    } catch (error) {
      console.error('验证角色名称失败:', error)
      return false
    }
  }

  const checkRoleCodeAvailable = async (roleCode: string, excludeRoleId?: string) => {
    try {
      const response = await validateRoleCode(roleCode, excludeRoleId)
      return response.data.available
    } catch (error) {
      console.error('验证角色编码失败:', error)
      return false
    }
  }

  // 选择管理
  const toggleRoleSelection = (roleId: string) => {
    const index = selectedRoleIds.value.indexOf(roleId)
    if (index === -1) {
      selectedRoleIds.value.push(roleId)
    } else {
      selectedRoleIds.value.splice(index, 1)
    }
  }

  const selectAllRoles = () => {
    selectedRoleIds.value = roles.value.map(r => r.role_id)
  }

  const clearSelection = () => {
    selectedRoleIds.value = []
  }

  // 分页管理
  const setPage = (page: number) => {
    currentPage.value = page
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1 // 重置到第一页
  }

  // 搜索和筛选
  const setQueryParams = (params: Partial<RoleQueryParams>) => {
    Object.assign(queryParams.value, params)
    currentPage.value = 1 // 重置到第一页
  }

  const resetQueryParams = () => {
    queryParams.value = {
      offset: 0,
      limit: pageSize.value
    }
    currentPage.value = 1
  }

  // 重置状态
  const resetState = () => {
    roles.value = []
    roleDetail.value = null
    selectedRoleIds.value = []
    currentPage.value = 1
    total.value = 0
    hasMore.value = false
    loading.value = false
    detailLoading.value = false
    submitting.value = false
    resetQueryParams()
  }

  return {
    // 状态
    roles,
    roleDetail,
    selectedRoleIds,
    availablePermissions,
    permissionsFlat,
    statistics,
    roleUsage,
    currentPage,
    pageSize,
    total,
    hasMore,
    loading,
    detailLoading,
    submitting,
    queryParams,
    
    // 计算属性
    totalPages,
    hasSelectedRoles,
    canBatchDelete,
    canBatchActivate,
    canBatchDeactivate,
    permissions,
    flatPermissions,
    
    // 动作
    fetchRoles,
    fetchRoleDetail,
    addRole,
    editRole,
    removeRole,
    batchOperation,
    fetchRolePermissions,
    assignPermissions,
    removePermission,
    fetchAvailablePermissions,
    fetchPermissionsFlat,
    fetchStatistics,
    fetchRoleUsage,
    checkRoleNameAvailable,
    checkRoleCodeAvailable,
    toggleRoleSelection,
    selectAllRoles,
    clearSelection,
    setPage,
    setPageSize,
    setQueryParams,
    resetQueryParams,
    resetState
  }
})