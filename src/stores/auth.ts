import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { login as apiLogin, logout as apiLogout, getCurrentUser, refreshToken as apiRefreshToken } from '@/api/auth'
import type { LoginParams, User, LoginToken } from '@/api/auth'
import { TOKEN, CACHE_KEYS, BUSINESS_CODE } from '@/constants/api'

const TOKEN_KEY = TOKEN.STORAGE_KEY
const REFRESH_TOKEN_KEY = TOKEN.REFRESH_TOKEN_STORAGE_KEY
const USER_KEY = CACHE_KEYS.USER_INFO

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false) // 新增：标记是否已初始化

  // 计算属性
  const isAuthenticated = computed(() => {
    // 确保已初始化且token存在
    return isInitialized.value && !!accessToken.value && !!user.value
  })
  const hasRole = (role: string) => user.value?.roles?.includes(role) ?? false
  const hasPermission = (permission: string) => user.value?.permissions?.includes(permission) ?? false

  // 初始化 - 从 localStorage 恢复状态
  const initializeAuth = async () => {
    if (isInitialized.value) return // 避免重复初始化
    
    try {
      console.log('[Auth] 开始初始化认证状态...')
      
      const storedToken = localStorage.getItem(TOKEN_KEY)
      const storedRefreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)
      const storedUser = localStorage.getItem(USER_KEY)

      console.log('[Auth] 从localStorage读取的数据:', {
        hasToken: !!storedToken,
        hasRefreshToken: !!storedRefreshToken,
        hasUser: !!storedUser
      })

      if (storedToken) {
        accessToken.value = storedToken
      }
      if (storedRefreshToken) {
        refreshToken.value = storedRefreshToken
      }
      if (storedUser) {
        try {
          user.value = JSON.parse(storedUser)
        } catch (parseError) {
          console.error('[Auth] 解析用户信息失败:', parseError)
          localStorage.removeItem(USER_KEY)
        }
      }

      // 如果有token但没有用户信息，尝试获取用户信息
      if (accessToken.value && !user.value) {
        console.log('[Auth] 有token但无用户信息，尝试获取用户信息...')
        try {
          await fetchCurrentUser()
        } catch (error) {
          console.warn('[Auth] 获取用户信息失败，清除认证数据:', error)
          clearAuthData()
        }
      }

      // 注意：在初始化阶段，不要主动验证token有效性
      // 让API请求自然失败，避免误判
      // 只有在用户主动操作时才进行token验证

      isInitialized.value = true
      console.log('[Auth] 认证状态初始化完成:', {
        isAuthenticated: isAuthenticated.value,
        hasUser: !!user.value,
        hasToken: !!accessToken.value
      })
    } catch (error) {
      console.error('[Auth] 初始化认证状态失败:', error)
      clearAuthData()
      isInitialized.value = true
    }
  }

  // 清除认证数据
  const clearAuthData = () => {
    console.log('[Auth] 清除认证数据')
    user.value = null
    accessToken.value = null
    refreshToken.value = null
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(USER_KEY)
  }

  // 保存认证数据
  const saveAuthData = (loginToken: LoginToken, userInfo?: User) => {
    console.log('[Auth] 保存认证数据:', {
      hasAccessToken: !!loginToken.access_token,
      hasRefreshToken: !!loginToken.refresh_token,
      hasUserInfo: !!userInfo
    })
    
    accessToken.value = loginToken.access_token
    refreshToken.value = loginToken.refresh_token || null

    localStorage.setItem(TOKEN_KEY, loginToken.access_token)
    if (loginToken.refresh_token) {
      localStorage.setItem(REFRESH_TOKEN_KEY, loginToken.refresh_token)
    }

    // 如果有用户信息，保存用户信息
    if (userInfo) {
      user.value = userInfo
      localStorage.setItem(USER_KEY, JSON.stringify(userInfo))
    }
  }

  // 登录
  const login = async (params: LoginParams) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await apiLogin(params)

      if (response.code === BUSINESS_CODE.SUCCESS || response.code === 200) {
        // 保存 token 信息
        saveAuthData(response.data)

        // 获取用户信息
        try {
          const userResponse = await getCurrentUser()
          if (userResponse.code === BUSINESS_CODE.SUCCESS || userResponse.code === 200) {
            user.value = userResponse.data
            localStorage.setItem(USER_KEY, JSON.stringify(userResponse.data))
          }
        } catch (userErr) {
          console.warn('Failed to fetch user info after login:', userErr)
        }

        return { success: true }
      } else {
        error.value = response.msg || '登录失败'
        return { success: false, message: error.value }
      }
    } catch (err: any) {
      error.value = err.message || '登录失败'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      isLoading.value = true

      // 调用后端登出接口
      if (accessToken.value) {
        await apiLogout()
      }
    } catch (err) {
      console.error('Logout API call failed:', err)
    } finally {
      clearAuthData()
      isLoading.value = false
    }
  }

  // 刷新 token
  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await apiRefreshToken(refreshToken.value)

      if (response.code === BUSINESS_CODE.SUCCESS || response.code === 200) {
        accessToken.value = response.data.access_token
        localStorage.setItem(TOKEN_KEY, response.data.access_token)
        return true
      } else {
        throw new Error(response.msg || 'Token refresh failed')
      }
    } catch (error) {
      clearAuthData()
      throw error
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!accessToken.value) {
      throw new Error('No access token available')
    }

    try {
      isLoading.value = true
      const response = await getCurrentUser()

      if (response.code === BUSINESS_CODE.SUCCESS || response.code === 200) {
        user.value = response.data
        localStorage.setItem(USER_KEY, JSON.stringify(response.data))
        return response.data
      } else {
        throw new Error(response.msg || 'Failed to fetch user info')
      }
    } catch (error) {
      console.error('Failed to fetch current user:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 检查 token 是否即将过期并自动刷新
  const checkAndRefreshToken = async () => {
    if (!accessToken.value || !refreshToken.value) {
      return false
    }

    try {
      // 这里可以添加 token 过期检查逻辑
      // 如果 token 即将过期，则刷新
      await refreshAccessToken()
      return true
    } catch (error) {
      console.error('Token refresh failed:', error)
      return false
    }
  }

  return {
    // 状态
    user: readonly(user),
    accessToken: readonly(accessToken),
    refreshToken: readonly(refreshToken),
    isLoading: readonly(isLoading),
    error: readonly(error),
    isInitialized: readonly(isInitialized),

    // 计算属性
    isAuthenticated,
    hasRole,
    hasPermission,

    // 方法
    initializeAuth,
    login,
    logout,
    refreshAccessToken,
    fetchCurrentUser,
    checkAndRefreshToken,
    clearAuthData
  }
})