import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { 
  queryUsers, 
  getUserDetail, 
  createUser, 
  updateUser, 
  deleteUser, 
  resetUserPassword,
  batchOperateUsers,
  getUserRoles,
  assignRoleToUser,
  removeRoleFromUser,
  getAvailableRoles,
  getOrganizationTree,
  getUserStatistics,
  validateUsername,
  validateEmployeeId,
  validateEmail
} from '@/api/user.api'
import type { 
  UserListItem, 
  UserDetail, 
  CreateUserRequest, 
  UpdateUserRequest,
  Role,
  Organization,
  UserQueryParams,
  UserPermissions
} from '@/types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const users = ref<UserListItem[]>([])
  const userDetail = ref<UserDetail | null>(null)
  const selectedUserIds = ref<string[]>([])
  const availableRoles = ref<Role[]>([])
  const organizationTree = ref<Organization[]>([])
  const statistics = ref<any>(null)
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const hasMore = ref(false)
  
  // 加载状态
  const loading = ref(false)
  const detailLoading = ref(false)
  const submitting = ref(false)
  
  // 查询参数
  const queryParams = ref<UserQueryParams>({
    offset: 0,
    limit: 20
  })

  // 计算属性
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
  const hasSelectedUsers = computed(() => selectedUserIds.value.length > 0)
  const canBatchDelete = computed(() => selectedUserIds.value.length > 0)
  const canBatchActivate = computed(() => selectedUserIds.value.length > 0)
  const canBatchDeactivate = computed(() => selectedUserIds.value.length > 0)

  // 权限检查 (这里应该从认证store或后端获取实际权限)
  const permissions = computed<UserPermissions>(() => ({
    canCreate: true, // TODO: 从实际权限系统获取
    canEdit: true,
    canDelete: true,
    canResetPassword: true,
    canAssignRole: true,
    canViewList: true,
    canViewDetail: true,
    canBatchOperation: true
  }))

  // 动作
  const fetchUsers = async (params?: Partial<UserQueryParams>) => {
    loading.value = true
    try {
      const queryData = {
        ...queryParams.value,
        ...params,
        offset: ((currentPage.value - 1) * pageSize.value),
        limit: pageSize.value
      }
      
      const response = await queryUsers(queryData)
      if (response.code === 200) {
        users.value = response.data.items
        total.value = response.data.total
        hasMore.value = response.data.has_more
        
        // 更新查询参数
        Object.assign(queryParams.value, queryData)
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchUserDetail = async (userId: string) => {
    detailLoading.value = true
    try {
      const response = await getUserDetail(userId)
      if (response.code === 200) {
        userDetail.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw error
    } finally {
      detailLoading.value = false
    }
  }

  const addUser = async (userData: CreateUserRequest) => {
    submitting.value = true
    try {
      const response = await createUser(userData)
      if (response.code === 200) {
        // 刷新用户列表
        await fetchUsers()
      }
      return response
    } catch (error) {
      console.error('创建用户失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }

  const editUser = async (userId: string, userData: UpdateUserRequest) => {
    submitting.value = true
    try {
      const response = await updateUser(userId, userData)
      if (response.code === 200) {
        // 更新列表中的用户信息
        const index = users.value.findIndex(u => u.user_id === userId)
        if (index !== -1) {
          Object.assign(users.value[index], response.data)
        }
        // 如果当前正在查看该用户详情，也更新详情
        if (userDetail.value?.user_id === userId) {
          Object.assign(userDetail.value, response.data)
        }
      }
      return response
    } catch (error) {
      console.error('更新用户失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }

  const removeUser = async (userId: string) => {
    try {
      const response = await deleteUser(userId)
      if (response.code === 200) {
        // 从列表中移除用户
        const index = users.value.findIndex(u => u.user_id === userId)
        if (index !== -1) {
          users.value.splice(index, 1)
          total.value--
        }
        // 从选中列表中移除
        const selectedIndex = selectedUserIds.value.indexOf(userId)
        if (selectedIndex !== -1) {
          selectedUserIds.value.splice(selectedIndex, 1)
        }
      }
      return response
    } catch (error) {
      console.error('删除用户失败:', error)
      throw error
    }
  }

  const resetPassword = async (userId: string, newPassword: string) => {
    try {
      const response = await resetUserPassword(userId, { new_password: newPassword })
      return response
    } catch (error) {
      console.error('重置密码失败:', error)
      throw error
    }
  }

  const batchOperation = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedUserIds.value.length === 0) return
    
    try {
      const response = await batchOperateUsers({
        user_ids: selectedUserIds.value,
        action
      })
      
      if (response.code === 200) {
        if (action === 'delete') {
          // 删除操作：从列表中移除
          users.value = users.value.filter(u => !selectedUserIds.value.includes(u.user_id))
          total.value -= selectedUserIds.value.length
        } else {
          // 激活/禁用操作：更新状态
          const newStatus = action === 'activate' ? 'ACTIVE' : 'INACTIVE'
          users.value.forEach(user => {
            if (selectedUserIds.value.includes(user.user_id)) {
              user.status = newStatus
            }
          })
        }
        // 清空选中列表
        selectedUserIds.value = []
      }
      return response
    } catch (error) {
      console.error('批量操作失败:', error)
      throw error
    }
  }

  // 角色管理相关
  const fetchUserRoles = async (userId: string) => {
    try {
      const response = await getUserRoles(userId)
      return response.data
    } catch (error) {
      console.error('获取用户角色失败:', error)
      throw error
    }
  }

  const assignRole = async (userId: string, roleId: string) => {
    try {
      const response = await assignRoleToUser(userId, { role_id: roleId })
      if (response.code === 200 && userDetail.value?.user_id === userId) {
        // 重新获取用户详情以更新角色列表
        await fetchUserDetail(userId)
      }
      return response
    } catch (error) {
      console.error('分配角色失败:', error)
      throw error
    }
  }

  const removeRole = async (userId: string, roleId: string) => {
    try {
      const response = await removeRoleFromUser(userId, roleId)
      if (response.code === 200 && userDetail.value?.user_id === userId) {
        // 从详情中移除角色
        userDetail.value.roles = userDetail.value.roles.filter(r => r.role_id !== roleId)
      }
      return response
    } catch (error) {
      console.error('移除角色失败:', error)
      throw error
    }
  }

  const assignUserRoles = async (userId: string, roleIds: string[]) => {
    try {
      // 先获取用户当前的角色
      const currentRoles = await fetchUserRoles(userId)
      const currentRoleIds = currentRoles.map(role => role.role_id)

      // 计算需要添加和移除的角色
      const rolesToAdd = roleIds.filter(roleId => !currentRoleIds.includes(roleId))
      const rolesToRemove = currentRoleIds.filter(roleId => !roleIds.includes(roleId))

      // 批量操作
      const promises = []

      // 添加新角色
      for (const roleId of rolesToAdd) {
        promises.push(assignRoleToUser(userId, { role_id: roleId }))
      }

      // 移除不需要的角色
      for (const roleId of rolesToRemove) {
        promises.push(removeRoleFromUser(userId, roleId))
      }

      await Promise.all(promises)

      // 如果是当前查看的用户详情，重新获取
      if (userDetail.value?.user_id === userId) {
        await fetchUserDetail(userId)
      }

      return { code: 200, message: '角色分配成功' }
    } catch (error) {
      console.error('批量分配角色失败:', error)
      throw error
    }
  }

  const fetchAvailableRoles = async () => {
    try {
      const response = await getAvailableRoles()
      if (response.code === 200) {
        availableRoles.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取可用角色失败:', error)
      throw error
    }
  }

  // 组织架构相关
  const fetchOrganizationTree = async () => {
    try {
      const response = await getOrganizationTree()
      if (response.code === 200) {
        organizationTree.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取组织架构失败:', error)
      throw error
    }
  }

  // 统计信息
  const fetchStatistics = async () => {
    try {
      const response = await getUserStatistics()
      if (response.code === 200) {
        statistics.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw error
    }
  }

  // 验证相关
  const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {
    try {
      const response = await validateUsername(username, excludeUserId)
      return response.data.available
    } catch (error) {
      console.error('验证用户名失败:', error)
      return false
    }
  }

  const checkEmployeeIdAvailable = async (employeeId: string, excludeUserId?: string) => {
    try {
      const response = await validateEmployeeId(employeeId, excludeUserId)
      return response.data.available
    } catch (error) {
      console.error('验证员工编号失败:', error)
      return false
    }
  }

  const checkEmailAvailable = async (email: string, excludeUserId?: string) => {
    try {
      const response = await validateEmail(email, excludeUserId)
      return response.data.available
    } catch (error) {
      console.error('验证邮箱失败:', error)
      return false
    }
  }

  // 选择管理
  const toggleUserSelection = (userId: string) => {
    const index = selectedUserIds.value.indexOf(userId)
    if (index === -1) {
      selectedUserIds.value.push(userId)
    } else {
      selectedUserIds.value.splice(index, 1)
    }
  }

  const selectAllUsers = () => {
    selectedUserIds.value = users.value.map(u => u.user_id)
  }

  const clearSelection = () => {
    selectedUserIds.value = []
  }

  // 分页管理
  const setPage = (page: number) => {
    currentPage.value = page
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1 // 重置到第一页
  }

  // 搜索和筛选
  const setQueryParams = (params: Partial<UserQueryParams>) => {
    Object.assign(queryParams.value, params)
    currentPage.value = 1 // 重置到第一页
  }

  const resetQueryParams = () => {
    queryParams.value = {
      offset: 0,
      limit: pageSize.value
    }
    currentPage.value = 1
  }

  // 重置状态
  const resetState = () => {
    users.value = []
    userDetail.value = null
    selectedUserIds.value = []
    currentPage.value = 1
    total.value = 0
    hasMore.value = false
    loading.value = false
    detailLoading.value = false
    submitting.value = false
    resetQueryParams()
  }

  return {
    // 状态
    users,
    userDetail,
    selectedUserIds,
    availableRoles,
    organizationTree,
    statistics,
    currentPage,
    pageSize,
    total,
    hasMore,
    loading,
    detailLoading,
    submitting,
    queryParams,
    
    // 计算属性
    totalPages,
    hasSelectedUsers,
    canBatchDelete,
    canBatchActivate,
    canBatchDeactivate,
    permissions,
    
    // 动作
    fetchUsers,
    fetchUserDetail,
    addUser,
    editUser,
    removeUser,
    resetPassword,
    batchOperation,
    fetchUserRoles,
    assignRole,
    removeRole,
    assignUserRoles,
    fetchAvailableRoles,
    fetchOrganizationTree,
    fetchStatistics,
    checkUsernameAvailable,
    checkEmployeeIdAvailable,
    checkEmailAvailable,
    toggleUserSelection,
    selectAllUsers,
    clearSelection,
    setPage,
    setPageSize,
    setQueryParams,
    resetQueryParams,
    resetState
  }
})