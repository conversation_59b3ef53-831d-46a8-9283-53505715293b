/**
 * API 相关常量定义
 */

// API 基础路径
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// API 版本
export const API_VERSION = 'v1'

// 请求超时时间 (毫秒)
export const REQUEST_TIMEOUT = 30000

// 响应状态码
export const HTTP_STATUS = {
  /** 成功 */
  OK: 200,
  /** 创建成功 */
  CREATED: 201,
  /** 删除成功 */
  NO_CONTENT: 204,
  /** 请求参数错误 */
  BAD_REQUEST: 400,
  /** 未认证 */
  UNAUTHORIZED: 401,
  /** 无权限 */
  FORBIDDEN: 403,
  /** 资源不存在 */
  NOT_FOUND: 404,
  /** 方法不允许 */
  METHOD_NOT_ALLOWED: 405,
  /** 请求冲突 */
  CONFLICT: 409,
  /** 请求实体过大 */
  PAYLOAD_TOO_LARGE: 413,
  /** 请求过于频繁 */
  TOO_MANY_REQUESTS: 429,
  /** 服务器内部错误 */
  INTERNAL_SERVER_ERROR: 500,
  /** 服务不可用 */
  SERVICE_UNAVAILABLE: 503,
  /** 网关超时 */
  GATEWAY_TIMEOUT: 504
} as const

// 业务状态码
export const BUSINESS_CODE = {
  /** 成功 */
  SUCCESS: 0,
  /** 通用错误 */
  ERROR: -1,
  /** 参数错误 */
  PARAM_ERROR: 1001,
  /** 认证失败 */
  AUTH_FAILED: 1002,
  /** 权限不足 */
  PERMISSION_DENIED: 1003,
  /** 资源不存在 */
  RESOURCE_NOT_FOUND: 1004,
  /** 资源已存在 */
  RESOURCE_EXISTS: 1005,
  /** 操作失败 */
  OPERATION_FAILED: 1006,
  /** 系统繁忙 */
  SYSTEM_BUSY: 1007
} as const

// 请求方法
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
} as const

// 内容类型
export const CONTENT_TYPE = {
  JSON: 'application/json',
  FORM: 'application/x-www-form-urlencoded',
  MULTIPART: 'multipart/form-data',
  TEXT: 'text/plain'
} as const

// 请求头字段名
export const HEADER_KEYS = {
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
  X_REQUESTED_WITH: 'X-Requested-With',
  X_CSRF_TOKEN: 'X-CSRF-Token'
} as const

// Token 相关
export const TOKEN = {
  /** Token 前缀 */
  PREFIX: 'Bearer ',
  /** Token 存储键名 */
  STORAGE_KEY: 'access_token',
  /** 刷新 Token 存储键名 */
  REFRESH_TOKEN_STORAGE_KEY: 'refresh_token',
  /** Token 过期时间存储键名 */
  EXPIRES_AT_STORAGE_KEY: 'token_expires_at'
} as const

// 缓存键名
export const CACHE_KEYS = {
  /** 用户信息 */
  USER_INFO: 'user_info',
  /** 权限列表 */
  PERMISSIONS: 'permissions',
  /** 菜单列表 */
  MENUS: 'menus',
  /** 字典数据 */
  DICT_DATA: 'dict_data'
} as const
