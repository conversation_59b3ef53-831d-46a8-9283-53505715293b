import type { ModelBase } from "@/types/api.types"
import type { DemoItem } from "../demo_item/types";

/**
 * 状态
 */
export enum DocumentType {
  SALE_ORDER = '销售订单',
  SALE_QUOTATION = '销售报价',
  SALE_CONTRACT = '销售合同',
  SALE_DELIVERY = '销售送货',
  SALE_RETURN = '销售退货',
  PURCHASE_APPLY = '采购申请',
  PURCHASE_ORDER = '采购订单',
  PURCHASE_RECEIPT = '采购收货',
  PURCHASE_RETURN = '采购退货',
  PURCHASE_CONTRACT = '采购合同',
  INVENTORY_LOCATION_INFO = '仓位资料',
  INVENTORY_INFO = '仓库资料',
  INBOUND = '入库',
  OUTBOUND = '出库',
  ADJUSTMENT = '调拨',
  RETURN = '退货',
  FIN_RECEIVABLE = '应收账单',
  FIN_COLLECTION = '收款单',
  FIN_PAYABLE = '应付账单',
  FIN_PAYMENT = '付款单',
  FIN_ACCOUNTING_ENTRY = '财务分录',
  FIN_VOUCHER = '财务凭证',
  FIN_ANALYSIS = '财务分析',
  FIN_BUDGET = '财务预算',
  FIN_FORECAST = '财务预测',
  MES_PRODUCTION_NOTICE = '生产通知单',
  MES_FLOW_CARD = '流程卡',
  MES_OPTIMIZATION = '切割优化',
  BAS_WORKMANSHIP_ROUTE = '工艺路线',
  OPS_MATERIAL = '物料',
  OPS_PRODUCT = '产品',
  OPS_PROJECT = '项目',
  OPS_BOM = 'BOM',
  OPS_PROJECT_QUOTATION = '项目报价单',
  OPS_PROJECT_CONTRACT = '项目合同',
  OPS_PROJECT_DESIGN = '项目设计',
  OTHER = '其他'
}

// Demo 数据接口定义
export interface Demo extends ModelBase {
  id: number;
  version: number; // 乐观锁版本号
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  tenant_id?: number; // 租户ID
  order_no: string; // 订单编号
  customer_name: string;
  email?: string;
  phone?: string;
  address?: string;
  sales_id?: number;
  total_amount: number; // 总金额
  tax_amount: number; // 税额
  discount_amount: number; // 折扣金额
  tax_rate: number; // 税率
  discount_rate: number; // 折扣率
  status: DocumentType; // 状态
  sales?: any; // sales
  demo_items: DemoItem[]; // demo_items
}

export interface DemoCreate extends Omit<Demo, 'id'> {

}

export interface DemoUpdate extends Demo {

}

// API 响应接口定义
