import type { ModelBase } from "@/types/api.types"
import type { Demo } from "../demo/types";

// DemoItem 数据接口定义
export interface DemoItem extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  demo_id: number;
  product_name: string;
  product_code: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  soft_delete: boolean;
  demo: Demo; // demo
}

export interface DemoItemCreate extends Omit<DemoItem, 'id'> {

}

export interface DemoItemUpdate extends DemoItem {

}

// API 响应接口定义
