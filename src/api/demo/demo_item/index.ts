import { alovaInstance } from '@/api';
import type { ResponseModel, ResponseListModel, QueryParams } from '@/types/api.types';

import type {
  DemoItem,
  DemoItemCreate,
  DemoItemUpdate,
} from './types';

/**
 * @description 获取DemoItem元数据
 * @returns {Promise<ResponseModel<any>>} 返回元数据信息
 */
const getDemoItemMetadata = () => {
  return alovaInstance.Get<ResponseModel<any>>('/v1/v1/demo/demo_item/get_metadata');
};

/**
 * @description 获取DemoItem列表
 * @param {DemoItemQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<DemoItem>>} 返回包含DemoItem信息的Promise对象
 * @example
 * // 使用示例
 * const demoItemList = await getDemoItemList({ start: 1, limit: 20 });
 */
const getDemoItemList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<DemoItem>>('/v1/v1/demo/demo_item/query', params);
};

/**
 * @description 获取DemoItem详情
 * @param {number} id DemoItem ID
 * @returns {Promise<ResponseModel<DemoItem>>} 返回DemoItem详情信息
 */
const getDemoItem = (id?: number) => {
  const url = id ? `/v1/v1/demo/demo_item/get/${id}` : '/v1/v1/demo/demo_item/get';
  return alovaInstance.Get<ResponseModel<DemoItem>>(url);
};

/**
 * @description 创建DemoItem
 * @param {DemoItemCreate} data 创建数据
 * @returns {Promise<ResponseModel<DemoItem>>} 返回创建的DemoItem信息
 */
const createDemoItem = (data: DemoItemCreate) => {
  return alovaInstance.Post<ResponseModel<DemoItem>>('/v1/v1/demo/demo_item/create', data);
};

/**
 * @description 更新DemoItem
 * @param {DemoItemUpdate} data 更新数据
 * @returns {Promise<ResponseModel<DemoItem>>} 返回更新后的DemoItem信息
 */
const updateDemoItem = (data: DemoItemUpdate) => {
  return alovaInstance.Put<ResponseModel<DemoItem>>('/v1/v1/demo/demo_item/update', data);
};

/**
 * @description 删除DemoItem
 * @param {number} id DemoItem ID
 * @returns {Promise<ResponseModel<any>>} 返回删除结果
 */
const removeDemoItem = (id: number) => {
  return alovaInstance.Delete<ResponseModel<any>>(`/v1/v1/demo/demo_item/delete/${id}`);
};

/**
 * @description 批量删除DemoItem
 * @param {number[]} ids  ID数组
 * @returns {Promise<ResponseModel<any>>} 返回批量删除结果
 */
const bulkDeleteDemoItem = (ids: number[]) => {
  return alovaInstance.Delete<ResponseModel<any>>('/v1/v1/demo/demo_item/bulk_delete', ids);
};

// /**
//  * @description 导出DemoItem数据
//  * @param {DemoItemQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportDemoItem = (params?: DemoItemQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/v1/demo/demo_item/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入DemoItem数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importDemoItem = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/v1/demo/demo_item/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getDemoItemMetadata,
  getDemoItemList,
  getDemoItem,
  createDemoItem,
  updateDemoItem,
  removeDemoItem,
  bulkDeleteDemoItem,
  // exportDemoItem,
  // importDemoItem,
};