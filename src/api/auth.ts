import { alovaInstance } from './client'
import type { ResponseModel } from '@/types/api.types'

/**
 * 登录参数 - 根据 API 文档 AuthLoginParam
 */
export interface LoginParams {
  username: string
  password: string
}

/**
 * 登录响应数据 - 根据实际 API 响应
 */
export interface LoginToken {
  access_token: string
  token_type: string
  expires_in?: number
  refresh_token?: string
  user_id?: string
}

/**
 * 用户信息类型 - 根据 /me 接口响应
 */
export interface User {
  id: string
  username: string
  email?: string
  nickname?: string
  avatar?: string
  phone?: string
  status?: string
  is_active?: boolean
  is_superuser?: boolean
  created_at?: string
  updated_at?: string
  // 角色和权限信息
  roles?: string[]
  permissions?: string[]
}

/**
 * 用户登录
 */
export const login = (params: LoginParams) => {
  return alovaInstance.Post<ResponseModel<LoginToken>>('/v1/sys/user/login', params)
}

/**
 * 用户登出
 */
export const logout = () => {
  return alovaInstance.Post<ResponseModel<null>>('/v1/sys/user/logout')
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = () => {
  return alovaInstance.Get<ResponseModel<User>>('/v1/sys/user/me')
}

/**
 * 刷新 Token (如果后端支持)
 */
export const refreshToken = (refreshToken: string) => {
  return alovaInstance.Post<ResponseModel<Pick<LoginToken, 'access_token' | 'expires_in'>>>(
    '/v1/sys/user/refresh',
    { refresh_token: refreshToken }
  )
}
