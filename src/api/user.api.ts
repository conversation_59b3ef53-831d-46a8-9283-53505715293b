import { alovaInstance } from './client'
import type { 
  User, 
  UserListItem, 
  UserDetail, 
  CreateUserRequest, 
  UpdateUserRequest, 
  ResetPasswordRequest,
  Role,
  UserRole,
  AssignRoleRequest,
  Organization,
  UserQueryParams,
  BatchOperationRequest,
  ResponseModel, 
  ResponseListModel 
} from '@/types'

/**
 * 用户管理 API
 */

// 查询用户列表
export const queryUsers = (params: UserQueryParams) => {
  return alovaInstance.Get<ResponseListModel<UserListItem>>('/users/query', {
    params
  })
}

// 获取用户详情
export const getUserDetail = (userId: string) => {
  return alovaInstance.Get<ResponseModel<UserDetail>>(`/users/get/${userId}`)
}

// 创建用户
export const createUser = (data: CreateUserRequest) => {
  return alovaInstance.Post<ResponseModel<User>>('/users/create', data)
}

// 更新用户信息
export const updateUser = (userId: string, data: UpdateUserRequest) => {
  return alovaInstance.Put<ResponseModel<User>>(`/users/update/${userId}`, data)
}

// 删除用户
export const deleteUser = (userId: string) => {
  return alovaInstance.Delete<ResponseModel<void>>(`/users/delete/${userId}`)
}

// 重置用户密码
export const resetUserPassword = (userId: string, data: ResetPasswordRequest) => {
  return alovaInstance.Post<ResponseModel<void>>(`/users/${userId}/reset-password`, data)
}

// 批量操作用户
export const batchOperateUsers = (data: BatchOperationRequest) => {
  return alovaInstance.Post<ResponseModel<void>>('/users/batch-operation', data)
}

/**
 * 用户角色管理 API
 */

// 获取用户的角色列表
export const getUserRoles = (userId: string) => {
  return alovaInstance.Get<ResponseModel<UserRole[]>>(`/users/${userId}/roles`)
}

// 为用户分配角色
export const assignRoleToUser = (userId: string, data: AssignRoleRequest) => {
  return alovaInstance.Post<ResponseModel<void>>(`/users/${userId}/assign-role`, data)
}

// 移除用户角色
export const removeRoleFromUser = (userId: string, roleId: string) => {
  return alovaInstance.Delete<ResponseModel<void>>(`/users/${userId}/remove-role/${roleId}`)
}

// 获取所有可分配的角色
export const getAvailableRoles = () => {
  return alovaInstance.Get<ResponseModel<Role[]>>('/roles/available')
}

/**
 * 组织架构 API
 */

// 获取组织架构树
export const getOrganizationTree = () => {
  return alovaInstance.Get<ResponseModel<Organization[]>>('/organizations/tree')
}

// 获取部门下的用户
export const getDepartmentUsers = (departmentId: string) => {
  return alovaInstance.Get<ResponseModel<UserListItem[]>>(`/organizations/users/${departmentId}`)
}

// 获取部门信息
export const getDepartmentInfo = (departmentId: string) => {
  return alovaInstance.Get<ResponseModel<Organization>>(`/organizations/get/${departmentId}`)
}

/**
 * 用户统计信息 API
 */

// 获取用户统计信息
export const getUserStatistics = () => {
  return alovaInstance.Get<ResponseModel<{
    total_users: number
    active_users: number
    inactive_users: number
    users_by_department: Array<{
      department_id: string
      department_name: string
      user_count: number
    }>
    recent_logins: Array<{
      user_id: string
      username: string
      name: string
      login_date: string
    }>
  }>>('/users/statistics')
}

/**
 * 用户验证 API
 */

// 验证用户名是否可用
export const validateUsername = (username: string, excludeUserId?: string) => {
  return alovaInstance.Post<ResponseModel<{ available: boolean }>>('/users/validate-username', {
    username,
    exclude_user_id: excludeUserId
  })
}

// 验证员工编号是否可用
export const validateEmployeeId = (employeeId: string, excludeUserId?: string) => {
  return alovaInstance.Post<ResponseModel<{ available: boolean }>>('/users/validate-employee-id', {
    employee_id: employeeId,
    exclude_user_id: excludeUserId
  })
}

// 验证邮箱是否可用
export const validateEmail = (email: string, excludeUserId?: string) => {
  return alovaInstance.Post<ResponseModel<{ available: boolean }>>('/users/validate-email', {
    email,
    exclude_user_id: excludeUserId
  })
}