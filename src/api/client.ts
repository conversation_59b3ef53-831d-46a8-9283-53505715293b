import { create<PERSON>lova } from 'alova'
import adapterFetch from 'alova/fetch'
import VueHook from 'alova/vue'
import { 
  API_BASE_URL, 
  REQUEST_TIMEOUT, 
  HTTP_STATUS,
  BUSINESS_CODE,
  TOKEN,
  HEADER_KEYS,  
  CONTENT_TYPE
} from '@/constants/api'
import type { ResponseModel } from '@/types/api.types'

/**
 * 获取存储的 Token
 */
function getToken(): string | null {
  try {
    // 首先尝试从localStorage获取
    const token = localStorage.getItem(TOKEN.STORAGE_KEY)
    if (token) {
      console.log('[API] 从localStorage获取到token')
      return token
    }
    
    // 如果localStorage中没有，尝试从Pinia store获取
    // 注意：这里需要确保store已经初始化
    console.log('[API] localStorage中没有token，尝试从store获取')
    return null
  } catch (error) {
    console.error('[API] 获取token失败:', error)
    return null
  }
}

/**
 * 清除认证信息
 */
function clearAuth(): void {
  console.log('[API] 清除认证信息')
  localStorage.removeItem(TOKEN.STORAGE_KEY)
  localStorage.removeItem(TOKEN.REFRESH_TOKEN_STORAGE_KEY)
  localStorage.removeItem(TOKEN.EXPIRES_AT_STORAGE_KEY)
}

/**
 * 处理 API 错误
 */
function handleApiError(error: any): never {
  console.error('[API] 处理API错误:', error)
  
  // 网络错误
  if (!navigator.onLine) {
    throw new Error('网络连接失败，请检查网络设置')
  }
  
  // HTTP 错误
  if (error instanceof Response) {
    switch (error.status) {
      case HTTP_STATUS.UNAUTHORIZED:
        console.log('[API] 401错误，但不立即清除认证信息')
        // 不在这里清除认证信息，让认证存储来处理
        throw new Error('登录已过期，请重新登录')
      case HTTP_STATUS.FORBIDDEN:
        throw new Error('权限不足，无法访问该资源')
      case HTTP_STATUS.NOT_FOUND:
        throw new Error('请求的资源不存在')
      case HTTP_STATUS.TOO_MANY_REQUESTS:
        throw new Error('请求过于频繁，请稍后再试')
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        throw new Error('服务器内部错误，请稍后再试')
      case HTTP_STATUS.SERVICE_UNAVAILABLE:
        throw new Error('服务暂时不可用，请稍后再试')
      default:
        throw new Error(`请求失败 (${error.status})`)
    }
  }
  
  // 其他错误
  throw new Error(error.message || '请求失败，请稍后再试')
}

/**
 * 创建 Alova 实例
 */
export const alovaInstance = createAlova({
  // 基础 URL
  baseURL: API_BASE_URL,
  
  // 超时时间
  timeout: REQUEST_TIMEOUT,
  
  // 使用 Fetch 适配器
  requestAdapter: adapterFetch(),
  
  // 使用 Vue Hook
  statesHook: VueHook,
  
  // 请求前拦截器
  beforeRequest(method) {
    // 添加认证头
    const token = getToken()
    if (token) {
      method.config.headers = {
        ...method.config.headers,
        [HEADER_KEYS.AUTHORIZATION]: `${TOKEN.PREFIX}${token}`
      }
      console.log(`[API] 请求 ${method.type} ${method.url} 已添加认证头`)
    } else {
      console.log(`[API] 请求 ${method.type} ${method.url} 无认证头`)
    }
    
    // 设置默认 Content-Type
    if (!method.config.headers?.[HEADER_KEYS.CONTENT_TYPE]) {
      method.config.headers = {
        ...method.config.headers,
        [HEADER_KEYS.CONTENT_TYPE]: CONTENT_TYPE.JSON
      }
    }
    
    // 设置默认 Accept
    method.config.headers = {
      ...method.config.headers,
      [HEADER_KEYS.ACCEPT]: CONTENT_TYPE.JSON
    }
    
    // 添加请求标识
    method.config.headers = {
      ...method.config.headers,
      [HEADER_KEYS.X_REQUESTED_WITH]: 'XMLHttpRequest'
    }
    
    console.log(`[API] ${method.type} ${method.url}`, {
      data: method.data,
      headers: method.config.headers
    })
  },
  
  // 响应拦截器
  responded: {
    // 成功响应处理
    onSuccess: async (response, method) => {
      console.log(`[API] Response ${method.type} ${method.url}`, response)

      const data = await response.json() as ResponseModel<any>
      console.log(`[API] Response Data ${method.type} ${method.url}`, data)

      // 检查业务状态码 - 支持多种成功状态码
      if (data.code !== BUSINESS_CODE.SUCCESS && data.code !== 200) {
        const error: ResponseModel<any> = {
          code: data.code,
          msg: data.msg || '请求失败',
          data: data.data
        }
        
        // 处理特定业务错误
        switch (data.code) {
          case BUSINESS_CODE.AUTH_FAILED:
            console.log('[API] 认证失败，但不立即清除认证信息')
            // 不在这里清除认证信息，让认证存储来处理
            throw new Error('认证失败，请重新登录')
          case BUSINESS_CODE.PERMISSION_DENIED:
            throw new Error('权限不足，无法执行该操作')
          case BUSINESS_CODE.RESOURCE_NOT_FOUND:
            throw new Error('请求的资源不存在')
          case BUSINESS_CODE.SYSTEM_BUSY:
            throw new Error('系统繁忙，请稍后再试')
          default:
            throw new Error(error.msg)
        }
      }
      
      return data
    },
    
    // 错误响应处理
    onError: (error, method) => {
      console.error(`[API] Error ${method.type} ${method.url}`, error)
      handleApiError(error)
    }
  },
  
  // 缓存配置使用默认设置
  // L1 缓存 (内存缓存) 和 L2 缓存 (持久化缓存) 使用默认配置
})

/**
 * 导出常用的请求方法
 */
export const {
  Get,
  Post,
  Put,
  Delete,
  Patch
} = alovaInstance

/**
 * 默认导出 Alova 实例
 */
export default alovaInstance
