/**
 * 中心化API服务注册表
 * 提供统一的方式获取模型API实例和元数据
 */

import type { ResponseListModel, ResponseModel, QueryParams } from '@/types/api.types'

/**
 * 标准模型API接口
 */
export interface ModelApi<T = any, C = any, U = any, CMetadata = any> {
  /** 获取元数据 */
  getMetadata: () => Promise<ResponseModel<CMetadata>>
  
  /** 获取列表 */
  getList: (params?: QueryParams) => Promise<ResponseListModel<T>>
  
  /** 获取详情 */
  getDetail?: (id: string | number) => Promise<T>
  
  /** 创建 */
  create?: (data: C) => Promise<T>
  
  /** 更新 */
  update?: (id: string | number, data: U) => Promise<T>
  
  /** 删除 */
  delete?: (id: string | number) => Promise<void>
  
  /** 批量删除 */
  bulkDelete?: (ids: (string | number)[]) => Promise<void>
  
  /** 自定义方法 */
  [method: string]: any
}

/**
 * API服务注册表接口
 */
export interface ApiRegistry {
  [key: string]: ModelApi
}

/**
 * 将kebab-case转换为PascalCase
 * @param str kebab-case字符串
 * @returns PascalCase字符串
 */
function toPascalCase(str: string): string {
  return str
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('')
}

/**
 * API服务中心类
 * 负责管理和获取各个模块的API接口
 */
class ApiServiceCenter {
  private apiCache = new Map<string, ModelApi>()
  private loadingPromises = new Map<string, Promise<ModelApi>>()

  /**
   * 获取API接口
   * @param moduleModel 格式为 "module/model" 的字符串，如 "demo/demo", "demo/demo_item"
   * @returns ModelApi实例
   */
  async getApi(moduleModel: string): Promise<ModelApi> {
    // 检查缓存
    if (this.apiCache.has(moduleModel)) {
      return this.apiCache.get(moduleModel)!
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(moduleModel)) {
      return this.loadingPromises.get(moduleModel)!
    }

    // 开始加载
    const loadPromise = this.loadApi(moduleModel)
    this.loadingPromises.set(moduleModel, loadPromise)

    try {
      const api = await loadPromise
      this.apiCache.set(moduleModel, api)
      return api
    } finally {
      this.loadingPromises.delete(moduleModel)
    }
  }

  /**
   * 动态加载API模块
   * @param moduleModel 模块/模型路径
   * @returns ModelApi实例
   */
  private async loadApi(moduleModel: string): Promise<ModelApi> {
    const [module, model] = moduleModel.split('/')
    
    if (!module || !model) {
      throw new Error(`Invalid module/model format: ${moduleModel}. Expected format: "module/model"`)
    }

    try {
      // 动态导入API模块
      const apiModule = await import(`./${module}/${model}/index.ts`)
      console.log('apiModule:', apiModule)
      // 生成函数名称
      const modelPascalCase = toPascalCase(model)
      
      // 构建标准化的API对象
      const api: ModelApi = {
        // 获取元数据 - 查找 get{Model}Metadata 函数
        getMetadata: apiModule[`get${modelPascalCase}Metadata`] || 
                    (() => Promise.reject(new Error(`getMetadata method not found for ${moduleModel}`))),
        
        // 获取列表 - 查找 get{Model}List 函数
        getList: apiModule[`get${modelPascalCase}List`] || 
                (() => Promise.reject(new Error(`getList method not found for ${moduleModel}`))),
        
        // 获取详情 - 查找 get{Model} 函数
        getDetail: apiModule[`get${modelPascalCase}`],
        
        // 创建 - 查找 create{Model} 函数
        create: apiModule[`create${modelPascalCase}`],
        
        // 更新 - 查找 update{Model} 函数
        update: apiModule[`update${modelPascalCase}`],
        
        // 删除 - 查找 remove{Model} 或 delete{Model} 函数
        delete: apiModule[`remove${modelPascalCase}`] || apiModule[`delete${modelPascalCase}`],
        
        // 批量删除 - 查找 bulkDelete{Model} 函数
        bulkDelete: apiModule[`bulkDelete${modelPascalCase}`]
      }

      // 添加所有导出的方法到API对象中，以支持自定义方法
      Object.keys(apiModule).forEach(key => {
        if (typeof apiModule[key] === 'function' && !api[key]) {
          api[key] = apiModule[key]
        }
      })

      return api
    } catch (error) {
      throw new Error(`Failed to load API for ${moduleModel}: ${error}`)
    }
  }

  /**
   * 预加载指定的API接口
   * @param moduleModels 要预加载的模块/模型数组
   */
  async preloadApis(moduleModels: string[]): Promise<void> {
    const loadPromises = moduleModels.map(moduleModel => this.getApi(moduleModel))
    await Promise.allSettled(loadPromises)
  }

  /**
   * 清除缓存
   * @param moduleModel 可选，指定要清除的模块/模型，不传则清除所有
   */
  clearCache(moduleModel?: string): void {
    if (moduleModel) {
      this.apiCache.delete(moduleModel)
      this.loadingPromises.delete(moduleModel)
    } else {
      this.apiCache.clear()
      this.loadingPromises.clear()
    }
  }

  /**
   * 获取已缓存的API列表
   * @returns 已缓存的模块/模型数组
   */
  getCachedApis(): string[] {
    return Array.from(this.apiCache.keys())
  }

  /**
   * 检查API是否已缓存
   * @param moduleModel 模块/模型路径
   * @returns 是否已缓存
   */
  isCached(moduleModel: string): boolean {
    return this.apiCache.has(moduleModel)
  }
}

// 创建全局单例实例
const apiServiceCenter = new ApiServiceCenter()

/**
 * 获取API接口的便捷函数
 * @param moduleModel 格式为 "module/model" 的字符串
 * @returns ModelApi实例
 * 
 * @example
 * ```typescript
 * // 获取demo模块的demo接口
 * const demoApi = await getApi('demo/demo')
 * const list = await demoApi.getList({ start: 1, limit: 20 })
 * 
 * // 获取demo模块的demo_item接口
 * const demoItemApi = await getApi('demo/demo_item')
 * const metadata = await demoItemApi.getMetadata()
 * ```
 */
export const getApi = (moduleModel: string): Promise<ModelApi> => {
  return apiServiceCenter.getApi(moduleModel)
}

/**
 * 预加载API接口
 * @param moduleModels 要预加载的模块/模型数组
 */
export const preloadApis = (moduleModels: string[]): Promise<void> => {
  return apiServiceCenter.preloadApis(moduleModels)
}

/**
 * 清除API缓存
 * @param moduleModel 可选，指定要清除的模块/模型
 */
export const clearApiCache = (moduleModel?: string): void => {
  return apiServiceCenter.clearCache(moduleModel)
}

/**
 * 获取已缓存的API列表
 */
export const getCachedApis = (): string[] => {
  return apiServiceCenter.getCachedApis()
}

/**
 * 检查API是否已缓存
 * @param moduleModel 模块/模型路径
 */
export const isApiCached = (moduleModel: string): boolean => {
  return apiServiceCenter.isCached(moduleModel)
}

// 导出API服务中心实例（用于高级用法）
export { apiServiceCenter as ApiServiceCenter }

// 默认导出便捷函数
export const ApiService = {
  getApi,
  getCachedApis,
  isApiCached,
  clearApiCache,
  preloadApis,
}
