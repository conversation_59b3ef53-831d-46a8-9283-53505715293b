import type { ModelBase } from "@/types/api.types"

/**
 * 用户类型
 */
export enum UserType {
  OUTER = '外部用户',
  INNER = '内部用户'
}

// User 数据接口定义
export interface User extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  name: string; // 真实姓名
  username?: string; // 用户名
  avatar?: string; // 头像
  status: boolean; // 用户状态
  user_type: UserType; // 用户类型
  email?: string; // 邮箱
  phone?: string; // 手机号
  is_user: boolean; // 是否用户
  is_superuser: boolean; // 是否超级用户
  is_multi_login: boolean; // 是否多端登录
  last_login?: string; // 最后登录时间
  home_path?: string; // 首页路径
  uuid: string; // UUID
  password?: string;
  salt?: string;
  roles: any; // roles
  factories: any; // factories
  tenants: any; // tenants
}

export interface UserCreate extends Omit<User, 'id'> {

}

export interface UserUpdate extends User {

}

// API 响应接口定义
