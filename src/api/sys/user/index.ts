import { alovaInstance } from '@/api';
import type { ResponseModel, ResponseListModel, QueryParams } from '@/types/api.types';

import type {
  User,
  UserCreate,
  UserUpdate,
} from './types';

/**
 * @description 获取User元数据
 * @returns {Promise<ResponseModel<any>>} 返回元数据信息
 */
const getUserMetadata = () => {
  return alovaInstance.Get<ResponseModel<any>>('/v1/v1/sys/user/get_metadata');
};

/**
 * @description 获取User列表
 * @param {UserQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<User>>} 返回包含User信息的Promise对象
 * @example
 * // 使用示例
 * const userList = await getUserList({ start: 1, limit: 20 });
 */
const getUserList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<User>>('/v1/v1/sys/user/query', params);
};

/**
 * @description 获取User详情
 * @param {number} id User ID
 * @returns {Promise<ResponseModel<User>>} 返回User详情信息
 */
const getUser = (id?: number) => {
  const url = id ? `/v1/v1/sys/user/get/${id}` : '/v1/v1/sys/user/get';
  return alovaInstance.Get<ResponseModel<User>>(url);
};

/**
 * @description 创建User
 * @param {UserCreate} data 创建数据
 * @returns {Promise<ResponseModel<User>>} 返回创建的User信息
 */
const createUser = (data: UserCreate) => {
  return alovaInstance.Post<ResponseModel<User>>('/v1/v1/sys/user/create', data);
};

/**
 * @description 更新User
 * @param {UserUpdate} data 更新数据
 * @returns {Promise<ResponseModel<User>>} 返回更新后的User信息
 */
const updateUser = (data: UserUpdate) => {
  return alovaInstance.Put<ResponseModel<User>>('/v1/v1/sys/user/update', data);
};

/**
 * @description 删除User
 * @param {number} id User ID
 * @returns {Promise<ResponseModel<any>>} 返回删除结果
 */
const removeUser = (id: number) => {
  return alovaInstance.Delete<ResponseModel<any>>(`/v1/v1/sys/user/delete/${id}`);
};

/**
 * @description 批量删除User
 * @param {number[]} ids  ID数组
 * @returns {Promise<ResponseModel<any>>} 返回批量删除结果
 */
const bulkDeleteUser = (ids: number[]) => {
  return alovaInstance.Delete<ResponseModel<any>>('/v1/v1/sys/user/bulk_delete', ids);
};

// /**
//  * @description 导出User数据
//  * @param {UserQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportUser = (params?: UserQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/v1/sys/user/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入User数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importUser = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/v1/sys/user/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getUserMetadata,
  getUserList,
  getUser,
  createUser,
  updateUser,
  removeUser,
  bulkDeleteUser,
  // exportUser,
  // importUser,
};