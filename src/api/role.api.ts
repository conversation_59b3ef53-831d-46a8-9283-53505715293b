import { alovaInstance } from './client'
import type { 
  Role,
  RoleListItem,
  RoleDetail,
  CreateRoleRequest,
  UpdateRoleRequest,
  Permission,
  RolePermission,
  AssignPermissionRequest,
  RoleQueryParams,
  RoleStatistics,
  BatchOperationRequest,
  ResponseModel, 
  ResponseListModel 
} from '@/types'

/**
 * 角色管理 API
 */

// 查询角色列表
export const queryRoles = (params: RoleQueryParams) => {
  return alovaInstance.Get<ResponseListModel<RoleListItem>>('/roles/query', {
    params
  })
}

// 获取角色详情
export const getRoleDetail = (roleId: string) => {
  return alovaInstance.Get<ResponseModel<RoleDetail>>(`/roles/get/${roleId}`)
}

// 创建角色
export const createRole = (data: CreateRoleRequest) => {
  return alovaInstance.Post<ResponseModel<Role>>('/roles/create', data)
}

// 更新角色信息
export const updateRole = (roleId: string, data: UpdateRoleRequest) => {
  return alovaInstance.Put<ResponseModel<Role>>(`/roles/update/${roleId}`, data)
}

// 删除角色
export const deleteRole = (roleId: string) => {
  return alovaInstance.Delete<ResponseModel<void>>(`/roles/delete/${roleId}`)
}

// 批量操作角色
export const batchOperateRoles = (data: BatchOperationRequest) => {
  return alovaInstance.Post<ResponseModel<void>>('/roles/batch-operation', data)
}

/**
 * 角色权限管理 API
 */

// 获取角色的权限列表
export const getRolePermissions = (roleId: string) => {
  return alovaInstance.Get<ResponseModel<RolePermission[]>>(`/roles/${roleId}/permissions`)
}

// 为角色分配权限
export const assignPermissionsToRole = (roleId: string, data: AssignPermissionRequest) => {
  return alovaInstance.Post<ResponseModel<void>>(`/roles/${roleId}/assign-permissions`, data)
}

// 移除角色权限
export const removePermissionFromRole = (roleId: string, permissionId: string) => {
  return alovaInstance.Delete<ResponseModel<void>>(`/roles/${roleId}/remove-permission/${permissionId}`)
}

// 获取所有可分配的权限(树形结构)
export const getAvailablePermissions = () => {
  return alovaInstance.Get<ResponseModel<Permission[]>>('/permissions/tree')
}

// 获取权限树（扁平化）
export const getPermissionsFlat = () => {
  return alovaInstance.Get<ResponseModel<Permission[]>>('/permissions/flat')
}

/**
 * 角色统计信息 API
 */

// 获取角色统计信息
export const getRoleStatistics = () => {
  return alovaInstance.Get<ResponseModel<RoleStatistics>>('/roles/statistics')
}

// 获取角色使用情况
export const getRoleUsage = (roleId: string) => {
  return alovaInstance.Get<ResponseModel<{
    role_id: string
    role_name: string
    user_count: number
    users: Array<{
      user_id: string
      username: string
      name: string
      department_name?: string
      assigned_date: string
    }>
  }>>(`/roles/${roleId}/usage`)
}

/**
 * 角色验证 API
 */

// 验证角色名称是否可用
export const validateRoleName = (roleName: string, excludeRoleId?: string) => {
  return alovaInstance.Post<ResponseModel<{ available: boolean }>>('/roles/validate-name', {
    role_name: roleName,
    exclude_role_id: excludeRoleId
  })
}

// 验证角色编码是否可用
export const validateRoleCode = (roleCode: string, excludeRoleId?: string) => {
  return alovaInstance.Post<ResponseModel<{ available: boolean }>>('/roles/validate-code', {
    role_code: roleCode,
    exclude_role_id: excludeRoleId
  })
}