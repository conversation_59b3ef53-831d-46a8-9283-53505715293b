const axios = require('axios');

// 测试配置
const API_BASE_URL = 'http://localhost:3003/api';
const TEST_USER = {
  username: 'user',
  password: 'user123'
};

async function testBatchOperation() {
  console.log('🔍 开始测试批量操作功能...');
  console.log(`📍 API 地址: ${API_BASE_URL}`);
  
  try {
    // 1. 登录获取 token
    console.log('\n1️⃣ 登录获取 token...');
    const loginResponse = await axios.post(`${API_BASE_URL}/v1/sys/user/login`, TEST_USER, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (loginResponse.data.code !== 200) {
      throw new Error('登录失败: ' + loginResponse.data.msg);
    }
    
    const token = loginResponse.data.data.access_token;
    console.log('✅ 登录成功，获取到 token');
    
    // 2. 查询用户列表
    console.log('\n2️⃣ 查询用户列表...');
    const usersResponse = await axios.get(`${API_BASE_URL}/users/query`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      params: {
        limit: 10,
        offset: 0
      }
    });
    
    if (usersResponse.data.code !== 200) {
      throw new Error('查询用户失败: ' + usersResponse.data.msg);
    }
    
    const users = usersResponse.data.data.items;
    console.log(`✅ 查询到 ${users.length} 个用户`);
    
    if (users.length === 0) {
      console.log('⚠️ 没有用户数据，无法测试批量操作');
      return;
    }
    
    // 选择前两个用户进行批量操作
    const userIds = users.slice(0, 2).map(user => user.user_id);
    console.log(`📋 选择用户: ${userIds.join(', ')}`);
    
    // 3. 测试批量禁用
    console.log('\n3️⃣ 测试批量禁用...');
    const deactivateResponse = await axios.post(`${API_BASE_URL}/users/batch-operation`, {
      user_ids: userIds,
      action: 'deactivate'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (deactivateResponse.data.code !== 200) {
      throw new Error('批量禁用失败: ' + deactivateResponse.data.msg);
    }
    
    console.log('✅ 批量禁用成功:', deactivateResponse.data.msg);
    
    // 4. 测试批量启用
    console.log('\n4️⃣ 测试批量启用...');
    const activateResponse = await axios.post(`${API_BASE_URL}/users/batch-operation`, {
      user_ids: userIds,
      action: 'activate'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (activateResponse.data.code !== 200) {
      throw new Error('批量启用失败: ' + activateResponse.data.msg);
    }
    
    console.log('✅ 批量启用成功:', activateResponse.data.msg);
    
    // 5. 验证用户状态
    console.log('\n5️⃣ 验证用户状态...');
    const verifyResponse = await axios.get(`${API_BASE_URL}/users/query`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      params: {
        limit: 10,
        offset: 0
      }
    });
    
    if (verifyResponse.data.code !== 200) {
      throw new Error('验证用户状态失败: ' + verifyResponse.data.msg);
    }
    
    const updatedUsers = verifyResponse.data.data.items;
    const targetUsers = updatedUsers.filter(user => userIds.includes(user.user_id));
    
    console.log('📊 用户状态验证结果:');
    targetUsers.forEach(user => {
      console.log(`  - ${user.username}: ${user.status}`);
    });
    
    console.log('\n🎉 批量操作功能测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testBatchOperation(); 