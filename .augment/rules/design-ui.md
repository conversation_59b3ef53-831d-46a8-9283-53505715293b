---
type: "manual"
---

# 需求文档转UI设计生成规则

## 目的
本规则用于根据用户指定的需求文档，生成对应的UI设计并输出为单个HTML文件。

## 步骤
1. 读取用户指定的需求文档文件
2. 分析需求文档中的功能需求和用户流程
3. 根据需求生成适合的UI设计
4. 将设计转换为单个HTML文件并输出到output目录

## UI设计生成指南

### 基本原则
- 必须基于 Vue 3 (Composition API) + TypeScript + shadcn/ui + Tailwind CSS 4+
- 组件采用 <script setup lang="ts">，类型安全，优先使用组合式 API
- 组件命名、props、emits、状态管理（Pinia）、API 交互（Alova）等需符合 glass_erp 项目风格
- 表单、交互、自动化表单（如 AutoForm）等应自动推断类型和依赖，支持字段联动、校验
- 样式只用 Tailwind 工具类，避免自定义 CSS，类名顺序参照：布局 → 间距 → 尺寸 → 颜色 → 状态
- 响应式设计，优先使用 Tailwind 响应式前缀（如 sm: md: lg:）
- 组件拆分与目录映射应参考 src/views、src/components/ui、src/stores、src/api、src/types 等结构
- 界面应简洁清晰，符合现代设计趋势，优先考虑用户体验和易用性
- 遵循需求文档中描述的用户流程，色彩方案应符合产品定位和目标用户群体

### 输出HTML要求
- 单文件HTML，使用 Tailwind CSS 4+ 样式，不要使用 Style Scope 的标签
- 生成的 HTML 结构应可直接迁移为 Vue 单文件组件（SFC），并便于后续拆分
- 交互逻辑建议用 TypeScript 注释说明，或以 <script> 标签内伪代码形式给出
- 表单、按钮、弹窗、导航等应直接用 shadcn/ui 组件风格
- 不依赖外部资源（除非特别指定）
- 注释清晰，代码结构合理
- 实现需求文档中描述的核心功能，提供基本交互效果展示

### 设计元素
- 导航结构
- 布局框架
- 色彩方案
- 字体选择
- 按钮和表单样式
- 图标和视觉元素
- 表单自动化（如 AutoForm）、字段依赖、校验
- 状态管理（Pinia）、API 交互（Alova）
- 权限控制、响应式数据

## 代码风格与目录映射
- 页面（views）：每个核心功能/流程对应一个页面组件，放于 src/views
- 复用组件（components/ui）：基础UI组件、表单控件、弹窗、导航等，放于 src/components/ui
- 通用逻辑（composables）：可复用的组合式函数，放于 src/composables
- 状态管理（stores）：每个业务域一个 Pinia store，放于 src/stores，命名为 use[Name]Store
- API 请求（api）：每个业务域一个 API 文件，统一响应类型，放于 src/api
- 类型定义（types）：所有接口、数据结构、表单字段类型，放于 src/types
- 常量（constants）：API 路径、枚举、全局常量，放于 src/constants

## 类型与状态管理
- 所有数据、props、emits、store、API 响应等必须有完整 TypeScript 类型定义
- 类型优先用 interface，禁止 any，推荐 unknown 或具体类型
- Pinia store 采用组合式语法，状态、getters、actions 分离，状态只读暴露
- API 响应类型统一为 ResponseModel<T>，分页为 ResponseListModel<T>
- 响应式数据优先用 ref()，避免深层 reactive 嵌套

## 表单与交互自动化
- 表单应自动推断字段类型、校验规则、默认值，支持字段依赖、联动、禁用、隐藏、选项动态变更
- 推荐使用 AutoForm 组件自动渲染表单，字段类型与组件自动映射
- 校验规则基于 zod 或 vee-validate，自动生成
- 交互逻辑（如按钮禁用、弹窗、切换、Tab、分页等）应用 shadcn/ui 组件风格
- 所有交互建议用 TypeScript 注释或伪代码说明，便于后续迁移为 Vue 组件

## 模板结构
生成的HTML应包含以下基本结构：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[项目名称] UI设计</title>
    <style>
        /* 此处包含所有CSS样式 */
    </style>
</head>
<body>
    <!-- 页面结构 -->
    <header>
        <!-- 导航栏 -->
    </header>
    
    <main>
        <!-- 主要内容区域 -->
    </main>
    
    <footer>
        <!-- 页脚 -->
    </footer>
    
    <script>
        // 此处包含所有JavaScript代码
    </script>
</body>
</html>
```
