---
type: "manual"
---

# 需求文档生成规则（增强版）

## 文档格式要求
生成的需求文档应包含以下部分，并为 UI 原型自动化开发提供结构化、类型化、可交互的详细信息：

1. **项目概述**
   - 项目背景
   - 项目目标
   - 目标用户

2. **页面与功能映射**
   - 列出所有核心页面，每个页面下细分功能模块。
   - 每个功能模块需包含：
     - 功能描述
     - 所属页面
     - 主要界面元素（如表单、表格、按钮、导航等）
     - 交互流程（如点击、切换、弹窗、校验等）
     - 输入/输出字段（含字段名、类型、校验、选项、依赖等）
     - 相关 API 交互（接口名、请求参数、响应结构、错误处理）
     - 权限控制（如角色、可见性、可操作性）

3. **用户场景与流程**
   - 描述典型用户场景，每个场景需包含：
     - 用户角色
     - 场景描述
     - 操作步骤（与页面、功能、界面元素关联）
     - 状态变化与交互说明（如表单校验、按钮禁用、数据加载、权限变化等）
     - 期望结果
   - 绘制用户操作流程图，清晰展示页面跳转、分支、判断条件。

4. **非功能需求**
   - 性能需求（响应时间、并发数等）
   - 兼容性需求（操作系统、浏览器、设备等）
   - 安全性需求（数据加密、权限、认证等）
   - 可用性需求（易用性、可访问性等）

5. **Product Backlog（SCRUM）**
   - 史诗（Epic）：高层次需求，需注明对应页面、功能模块。
   - 用户故事（User Story）：
     - 遵循“作为[用户角色]，我希望[完成某功能]，以便[实现某价值]”结构
     - 明确界面元素、交互动作、输入输出、状态变化、API 交互、权限控制等
     - 具备验收标准、工作量估算、归属史诗
   - 任务（Task）：
     - 具体操作内容，明确负责人、预计完成时间
     - 需与用户故事、功能模块、界面元素、交互动作等关联

## 标准模板
用户提供信息时，请按照以下结构生成文档：

```markdown
# 项目名称：[项目名]
## 1. 项目概述
### 1.1 项目背景
    [根据用户输入填写]
### 1.2 项目目标
    [根据用户输入填写]
### 1.3 目标用户
    [根据用户输入填写]

## 2. 页面与功能映射
### 2.1 页面列表
- [页面1]
- [页面2]

### 2.2 页面功能明细
#### 2.2.1 [页面1]
- 功能模块：[功能1]
  - 功能描述：[描述]
  - 主要界面元素：
    - [元素类型] [元素名称]（如：表单 登录表单、按钮 提交按钮、表格 用户列表等）
  - 输入/输出字段：
    - 字段名：[字段1]
    - 类型：[string/number/boolean/enum/date/array/object]
    - 校验规则：[必填/长度/格式/范围等]
    - 选项（如下拉/单选）：[选项1, 选项2]
    - 依赖关系（如联动/禁用/隐藏）：[描述]
  - 交互流程：
    - [如：点击提交按钮 -> 校验表单 -> 调用登录API -> 跳转主页]
  - 相关API：
    - 接口名：[接口名]
    - 请求参数：[参数结构]
    - 响应结构：[响应结构]
    - 错误处理：[描述]
  - 权限控制：[如仅管理员可见/可操作]

#### 2.2.2 [页面2]
- 功能模块：[功能2]
  ...

## 3. 用户场景与流程
### 3.1 [场景1]
    - 用户角色：[角色]
    - 场景描述：[描述]
    - 操作步骤：[步骤1 -> 步骤2 -> ...]
    - 状态变化与交互说明：[如：表单校验、按钮禁用、数据加载、权限变化等]
    - 期望结果：[描述]
### 3.2 [场景2]
    ...

## 4. 非功能需求
### 4.1 性能需求
    [响应时间、并发数等]
### 4.2 兼容性要求
    [操作系统、浏览器、设备等]
### 4.3 安全要求
    [数据安全、身份验证等]
### 4.4 可用性要求
    [易用性、可访问性等]

## 5. Product Backlog
### 5.1 史诗 [史诗1]
    - 关联页面：[页面名]
    - 关联功能模块：[功能模块名]
    - 史诗描述：[描述]
#### 5.1.1 用户故事 [史诗1故事1]
    - 角色：[角色]
    - 目标：[目标]
    - 关联页面/功能/界面元素：[页面/功能/元素]
    - 交互动作：[如：点击、输入、切换等]
    - 输入/输出字段：[字段及类型]
    - 状态变化/权限控制/数据交互：[描述]
    - 验收标准：[标准]
    - 工作量估算：[故事点/人天]
##### 5.1.1.1 任务 [史诗1故事1任务1]
    - 操作内容：[描述]
    - 负责人：[姓名]
    - 预计完成时间：[时间]
    - 关联用户故事/功能/界面元素/交互动作：[描述]
##### 5.1.1.2 任务 [史诗1故事1任务2]
    ...
### 5.N 史诗 [史诗N]
    ...
```