/**
 * 用户管理功能测试脚本
 * 用于验证API接口和Mock数据的正确性
 */

// 测试用户管理API
async function testUserManagementAPIs() {
  const baseURL = 'http://localhost:3002/api'
  
  console.log('🧪 开始测试用户管理API...')
  
  try {
    // 1. 测试用户列表查询
    console.log('\n1. 测试用户列表查询...')
    const queryResponse = await fetch(`${baseURL}/users/query?limit=5`)
    const queryData = await queryResponse.json()
    console.log('✅ 用户列表查询成功:', queryData)
    
    if (queryData.data && queryData.data.items.length > 0) {
      const testUserId = queryData.data.items[0].user_id
      
      // 2. 测试用户详情获取
      console.log('\n2. 测试用户详情获取...')
      const detailResponse = await fetch(`${baseURL}/users/get/${testUserId}`)
      const detailData = await detailResponse.json()
      console.log('✅ 用户详情获取成功:', detailData)
      
      // 3. 测试用户角色获取
      console.log('\n3. 测试用户角色获取...')
      const rolesResponse = await fetch(`${baseURL}/users/${testUserId}/roles`)
      const rolesData = await rolesResponse.json()
      console.log('✅ 用户角色获取成功:', rolesData)
    }
    
    // 4. 测试组织架构获取
    console.log('\n4. 测试组织架构获取...')
    const orgResponse = await fetch(`${baseURL}/organizations/tree`)
    const orgData = await orgResponse.json()
    console.log('✅ 组织架构获取成功:', orgData)
    
    // 5. 测试可用角色获取
    console.log('\n5. 测试可用角色获取...')
    const availableRolesResponse = await fetch(`${baseURL}/roles/available`)
    const availableRolesData = await availableRolesResponse.json()
    console.log('✅ 可用角色获取成功:', availableRolesData)
    
    // 6. 测试用户统计
    console.log('\n6. 测试用户统计...')
    const statsResponse = await fetch(`${baseURL}/users/statistics`)
    const statsData = await statsResponse.json()
    console.log('✅ 用户统计获取成功:', statsData)
    
    // 7. 测试用户名验证
    console.log('\n7. 测试用户名验证...')
    const validateResponse = await fetch(`${baseURL}/users/validate-username`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'test_user_' + Date.now() })
    })
    const validateData = await validateResponse.json()
    console.log('✅ 用户名验证成功:', validateData)
    
    // 8. 测试创建用户
    console.log('\n8. 测试创建用户...')
    const newUser = {
      username: 'test_user_' + Date.now(),
      password: 'Test123456',
      name: '测试用户',
      employee_id: 'TEST' + Date.now(),
      email: `test${Date.now()}@glass4.com`,
      phone: '13800000000',
      department_id: 'dept_001',
      status: 'ACTIVE'
    }
    
    const createResponse = await fetch(`${baseURL}/users/create`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newUser)
    })
    const createData = await createResponse.json()
    console.log('✅ 用户创建成功:', createData)
    
    if (createData.data) {
      const newUserId = createData.data.user_id
      
      // 9. 测试更新用户
      console.log('\n9. 测试更新用户...')
      const updateResponse = await fetch(`${baseURL}/users/update/${newUserId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: '测试用户_已更新' })
      })
      const updateData = await updateResponse.json()
      console.log('✅ 用户更新成功:', updateData)
      
      // 10. 测试重置密码
      console.log('\n10. 测试重置密码...')
      const resetPasswordResponse = await fetch(`${baseURL}/users/${newUserId}/reset-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ new_password: 'NewPassword123' })
      })
      const resetPasswordData = await resetPasswordResponse.json()
      console.log('✅ 密码重置成功:', resetPasswordData)
      
      // 11. 测试删除用户
      console.log('\n11. 测试删除用户...')
      const deleteResponse = await fetch(`${baseURL}/users/delete/${newUserId}`, {
        method: 'DELETE'
      })
      const deleteData = await deleteResponse.json()
      console.log('✅ 用户删除成功:', deleteData)
    }
    
    console.log('\n🎉 所有API测试完成！')
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

// 运行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  const fetch = require('node-fetch')
  testUserManagementAPIs()
} else {
  // 浏览器环境
  testUserManagementAPIs()
}

// 导出测试函数供浏览器使用
if (typeof window !== 'undefined') {
  window.testUserManagementAPIs = testUserManagementAPIs
}