# 🚀 Vue 业务代码生成器套件

一套完整的 Vue.js 业务开发代码生成工具，帮助快速生成 TypeScript API 代码和 Vue 表格组件。

## 📋 工具概览

### 🎯 **表格代码生成器** (`generate-table.js`)
从后端元数据自动生成 Vue 表格组件代码
- 智能字段类型映射
- 复合列支持 (name/code 字段组合)
- 关系字段处理
- 枚举状态配置

### 📡 **API 代码生成器** (`generate-api.js`) 
从后端元数据自动生成 TypeScript API 请求代码
- 完整的 CRUD API 函数
- TypeScript 类型定义
- 符合项目规范的代码格式
- 使用 alovaInstance 和 ResponseListModel

### 🧪 **API 测试工具** (`test-generated-api.js`)
测试生成的 API 接口文件质量和功能
- 文件结构验证
- TypeScript 语法检查
- API 函数完整性测试
- 类型定义检查
- 实际 API 调用测试


## 🛠️ 安装和配置

### 环境要求
- Node.js 18+
- 项目中已配置 alovaInstance
- 已安装项目依赖

### 配置环境变量
确保 `.env` 文件中配置了后端 API 地址：
```bash
VITE_API_BASE_URL=http://localhost:8111/api
```

## 📖 使用方法

### 🎯 表格组件生成
```bash
pnpm generate:table
```

交互式输入：
1. 后端元数据接口地址 (例: `/sys/user/get_metadata`)
2. 后端数据查询接口地址 (例: `/sys/user/query`)
3. 认证 TOKEN
4. 组件名称
5. 输出文件路径

### 📡 API 代码生成
```bash
pnpm generate:api
```

生成内容：
- `types.ts` - TypeScript 接口定义
- `index.ts` - API 请求函数

### 🧪 API 测试
```bash
# 交互式测试
pnpm test:api

# 指定模块测试
pnpm test:api --module demo/user

# 自动化测试
pnpm test:api --auto --module demo/user --verbose
```

测试功能：
- ✅ 检查文件结构和语法
- ✅ 验证 API 函数完整性
- ✅ 确认类型定义正确性
- 🌐 可选的实际 API 调用测试


## 📊 支持的字段类型

### 基础类型映射
- `string/varchar/text` → TypeScript `string`
- `integer/int/bigint` → TypeScript `number`
- `float/decimal` → TypeScript `number`
- `boolean` → TypeScript `boolean`
- `datetime/timestamp` → TypeScript `string`

### 特殊字段识别
- **金额字段**: `total_amount`, `tax_amount` → 货币组件
- **百分比字段**: `tax_rate`, `discount_rate` → 百分比组件
- **状态字段**: `status` + 枚举 → 状态组件
- **用户字段**: `created_by`, `updated_by` → 用户映射组件
- **时间字段**: `created_at`, `updated_at` → 时间组件

### 关系字段
- `ONETOMANY` → 一对多组件
- `MANYTOONE` → 多对一组件

## 🎨 生成的 API 代码格式

基于您的项目 `demo.ts` 格式：

```typescript
import alovaInstance from '@/api';
import { ResponseListModel } from '@/types/core';

/**
 * @description 获取User列表
 * @param {UserQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<User>>} 返回包含User信息的Promise对象
 */
const getList = (params: UserQueryParams) => {
  return alovaInstance.Post<ResponseListModel<User>>('/api/v1/sys/user/query', params);
};

const getMetadata = () => {
  return alovaInstance.Get<any>('/api/v1/sys/user/get_metadata');
};

const create = (data: UserCreateParams) => {
  return alovaInstance.Post<User>('/api/v1/sys/user/create', data);
};

// ... 其他 CRUD 函数

export { getMetadata, getList, create, update, remove };
```

## 🔧 生成的 API 函数

所有生成器都包含以下完整的 API 函数：

| 函数名 | 功能 | HTTP 方法 | 路径 |
|--------|------|-----------|------|
| `getMetadata()` | 获取元数据 | GET | `/get_metadata` |
| `getList(params)` | 查询列表 | POST | `/query` |
| `get(id?)` | 获取详情 | GET | `/get` 或 `/get/{id}` |
| `create(data)` | 创建记录 | POST | `/create` |
| `update(data)` | 更新记录 | PUT | `/update` |
| `remove(id)` | 删除记录 | DELETE | `/delete/{id}` |
| `batchDelete(ids)` | 批量删除 | POST | `/batch-delete` |
| `exportData(params?)` | 导出数据 | POST | `/export` |
| `importData(file)` | 导入数据 | POST | `/import` |


## 📝 元数据格式要求

生成器支持以下元数据格式：

```json
{
  "code": 200,
  "msg": "请求成功",
  "data": {
    "name": "ModelName",
    "fields": [
      {
        "name": "field_name",
        "type": "string",
        "nullable": false,
        "comment": "字段注释",
        "enum_info": {
          "enum_values": {
            "VALUE1": "显示文本1",
            "VALUE2": "显示文本2"
          }
        },
        "relation_info": {
          "related_model": "RelatedModel",
          "direction": "RelationshipDirection.ONETOMANY"
        }
      }
    ]
  }
}
```

## 🔄 与现有项目集成

### 在 Vue 组件中使用生成的 API
```vue
<script setup lang="ts">
import { getMetadata, getList, create } from '@/api/sys/user';

// 获取列表数据
const userList = await getList({ page: 1, size: 20 });

// 创建新用户
const newUser = await create({ username: 'test', email: '<EMAIL>' });
</script>
```

### 在表格组件中使用
```vue
<script setup lang="ts">
import { getMetadata, getList } from '@/api/sys/user';
import { VTable, useTable } from '@/components/common/Table';

const [register, tableApi] = useTable({
  fetchApi: async (params) => getList(params),
  columnsApi: () => getMetadata(),
  // ... 其他配置
});
</script>

<template>
  <VTable @register="register" />
</template>
```

## 🚀 高级功能

### 复合列生成
自动识别 `name` 和 `code` 字段组合，生成复合列：
- `customer_name` + `customer_code` → 客户信息复合列
- `product_name` + `product_code` → 产品信息复合列

### 智能字段排序
1. **ID 列** - 固定左侧
2. **业务字段** - 主要内容
3. **关系字段** - 关联数据
4. **用户字段** - 创建者、更新者
5. **时间字段** - 创建时间、更新时间

### 枚举状态配置
自动为枚举字段生成状态映射：
```typescript
statusMap: {
  'ACTIVE': { text: '激活', color: '#10b981' },
  'INACTIVE': { text: '禁用', color: '#ef4444' }
}
```

## ⚠️ 注意事项

1. **后端 API 规范**: 确保后端接口遵循图片中显示的标准格式
2. **认证配置**: 确保 TOKEN 有效且具有相应权限
3. **类型安全**: 生成的 TypeScript 代码提供完整的类型支持
4. **项目依赖**: 确保项目中已正确配置 alovaInstance 和相关类型

## 🐛 故障排除

### 常见问题

1. **网络连接错误**
   ```bash
   Error: ECONNREFUSED
   ```
   - 检查 `VITE_GLOB_API_URL` 环境变量
   - 确认后端服务正在运行

2. **认证失败**
   ```bash
   Error: 401 Unauthorized
   ```
   - 检查 TOKEN 是否正确
   - 确认 TOKEN 未过期

3. **元数据格式错误**
   ```bash
   Error: 元数据格式不正确
   ```
   - 检查后端返回的元数据格式
   - 确认 `fields` 数组存在

### 调试模式
在生成过程中，工具会输出详细的调试信息：
- 📡 API 请求状态
- 📊 字段解析结果
- 🔗 复合列识别
- ✅ 代码生成进度

## 📞 技术支持

如果遇到问题：
1. 查看控制台输出的详细错误信息
2. 检查元数据接口返回格式
3. 验证网络连接和认证配置
4. 参考生成的示例代码

---

🎉 **使用愉快！这套工具将大大提升您的 Vue.js 业务开发效率！** 