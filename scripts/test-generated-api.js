#!/usr/bin/env node

/**
 * API 接口测试脚本
 * 用于测试由 generate-api.js 生成的 API 接口文件
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';
import { readFile } from 'fs/promises';
import { createInterface } from 'readline';
import { createAlova } from 'alova';
import adapterFetch from 'alova/fetch';
import { config } from 'dotenv';

// 加载环境变量
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 命令行接口
const rl = createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: true,
});

// 获取用户输入的辅助函数
function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, (answer) => {
            resolve(answer.trim());
        });
    });
}

// 显示帮助信息
function showHelp() {
    console.log(`
🧪 API 接口测试工具

用法:
  pnpm test:api [options]

选项:
  --help, -h        显示帮助信息
  --module <name>   指定要测试的模块 (例: demo/user)
  --auto           自动运行所有测试
  --verbose        显示详细输出

示例:
  pnpm test:api
  pnpm test:api --module demo/demo
  pnpm test:api --auto --verbose

测试内容包括：
  ✅ 文件结构验证
  ✅ TypeScript 语法检查
  ✅ API 函数可用性测试
  ✅ 类型定义完整性检查
  ✅ 实际 API 调用测试 (可选)
`);
}

// 创建测试用的 Alova 实例
const testAlovaInstance = createAlova({
    baseURL: process.env.VITE_API_BASE_URL || 'http://localhost:8111/api',
    requestAdapter: adapterFetch(),
    timeout: 5000,
    beforeRequest(method) {
        method.config.headers = method.config.headers || {};
        method.config.headers['Content-Type'] = 'application/json';
    },
    responded: {
        onSuccess: async (response) => {
            return await response.json();
        },
        onError: (error) => {
            throw error;
        }
    }
});

// 测试结果统计
class TestResults {
    constructor() {
        this.total = 0;
        this.passed = 0;
        this.failed = 0;
        this.skipped = 0;
        this.results = [];
    }

    addResult(name, status, message = '', details = null) {
        this.total++;
        const result = {
            name,
            status,
            message,
            details,
            timestamp: new Date().toISOString()
        };
        this.results.push(result);
        // 恢复统计量累加
        switch (status) {
            case 'PASS':
                this.passed++;
                break;
            case 'FAIL':
                this.failed++;
                break;
            case 'SKIP':
                this.skipped++;
                break;
        }
    }

    printSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('🧪 测试结果汇总');
        console.log('='.repeat(60));
        // 详细打印每一条
        this.results.forEach(r => {
            let icon = r.status === 'PASS' ? '✅' : r.status === 'FAIL' ? '❌' : '⏭️ ';
            let detail = r.details ? ` | 详情: ${r.details}` : '';
            console.log(`${icon} ${r.name} - ${r.message}${detail}`);
        });
        // 汇总
        console.log(`总计: ${this.total}`);
        console.log(`✅ 通过: ${this.passed}`);
        console.log(`❌ 失败: ${this.failed}`);
        console.log(`⏭️  跳过: ${this.skipped}`);
        const successRate = this.total > 0 ? ((this.passed / this.total) * 100).toFixed(1) : 0;
        console.log(`📊 成功率: ${successRate}%`);
        if (this.failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.results
                .filter(r => r.status === 'FAIL')
                .forEach(r => console.log(`   - ${r.name}: ${r.message}`));
        }
    }
}

// 检查文件结构
async function testFileStructure(modulePath) {
    const results = new TestResults();
    
    console.log(`\n📁 检查文件结构: ${modulePath}`);
    console.log('-'.repeat(40));
    
    const basePath = join(process.cwd(), 'src/api', modulePath);
    
    // 检查目录是否存在
    if (!existsSync(basePath)) {
        results.addResult('目录存在性', 'FAIL', `目录不存在: ${basePath}`);
        return results;
    }
    results.addResult('目录存在性', 'PASS', '目录存在');
    
    // 检查必需文件
    const requiredFiles = ['index.ts', 'types.ts'];
    for (const file of requiredFiles) {
        const filePath = join(basePath, file);
        if (existsSync(filePath)) {
            results.addResult(`文件存在: ${file}`, 'PASS', '文件存在');
        } else {
            results.addResult(`文件存在: ${file}`, 'FAIL', '文件不存在');
        }
    }
    
    return results;
}

// 检查 TypeScript 语法
async function testTypeScriptSyntax(modulePath) {
    const results = new TestResults();
    
    console.log(`\n📝 检查 TypeScript 语法: ${modulePath}`);
    console.log('-'.repeat(40));
    
    const basePath = join(process.cwd(), 'src/api', modulePath);
    const files = ['index.ts', 'types.ts'];
    
    for (const file of files) {
        const filePath = join(basePath, file);
        
        if (!existsSync(filePath)) {
            results.addResult(`语法检查: ${file}`, 'SKIP', '文件不存在');
            continue;
        }
        
        try {
            const content = await readFile(filePath, 'utf8');
            
            // 基本语法检查
            const syntaxChecks = [
                {
                    name: '导入语句',
                    pattern: /^import\s+.*from\s+['"][^'"]+['"];?$/m,
                    required: true
                },
                {
                    name: '导出语句',
                    pattern: /^export\s+/m,
                    required: true
                },
                {
                    name: '函数定义',
                    pattern: /const\s+\w+\s*=\s*\(/m,
                    required: file === 'index.ts'
                },
                {
                    name: '接口定义',
                    pattern: /^export\s+interface\s+\w+/m,
                    required: file === 'types.ts'
                }
            ];
            
            for (const check of syntaxChecks) {
                if (check.required) {
                    if (check.pattern.test(content)) {
                        results.addResult(`${file}: ${check.name}`, 'PASS', '语法正确');
                    } else {
                        results.addResult(`${file}: ${check.name}`, 'FAIL', '语法错误或缺失');
                    }
                }
            }
            
            // 检查常见语法错误
            const commonErrors = [
                { pattern: /\bany\b(?!\s*>)/, message: '使用了 any 类型' },
                { pattern: /console\.log/, message: '包含调试代码' },
                { pattern: /TODO|FIXME|XXX/i, message: '包含待办事项注释' }
            ];
            
            for (const error of commonErrors) {
                if (error.pattern.test(content)) {
                    results.addResult(`${file}: 代码质量`, 'FAIL', error.message);
                }
            }
            
        } catch (error) {
            results.addResult(`语法检查: ${file}`, 'FAIL', '读取文件失败', error.message);
        }
    }
    
    return results;
}

// 检查 API 函数
async function testApiFunctions(modulePath) {
    const results = new TestResults();
    
    console.log(`\n🔧 检查 API 函数: ${modulePath}`);
    console.log('-'.repeat(40));
    
    const basePath = join(process.cwd(), 'src/api', modulePath);
    const indexPath = join(basePath, 'index.ts');
    
    if (!existsSync(indexPath)) {
        results.addResult('API 函数检查', 'SKIP', 'index.ts 文件不存在');
        return results;
    }
    
    try {
        const content = await readFile(indexPath, 'utf8');
        
        // 提取模型名称
        const modelMatch = content.match(/const\s+get(\w+)Metadata/);
        const modelName = modelMatch ? modelMatch[1] : null;
        
        if (!modelName) {
            results.addResult('模型名称识别', 'FAIL', '无法识别模型名称');
            return results;
        }
        
        results.addResult('模型名称识别', 'PASS', `识别到模型: ${modelName}`);
        
        // 检查标准 CRUD 函数
        const expectedFunctions = [
            `get${modelName}Metadata`,
            `get${modelName}List`,
            `get${modelName}`,
            `create${modelName}`,
            `update${modelName}`,
            `remove${modelName}`,
            `bulkDelete${modelName}`
        ];
        
        for (const funcName of expectedFunctions) {
            const pattern = new RegExp(`const\\s+${funcName}\\s*=`);
            if (pattern.test(content)) {
                results.addResult(`函数定义: ${funcName}`, 'PASS', '函数已定义');
            } else {
                results.addResult(`函数定义: ${funcName}`, 'FAIL', '函数未定义');
            }
        }
        
        // 检查导出语句
        const exportPattern = /export\s*\{([^}]+)\}/;
        const exportMatch = content.match(exportPattern);
        
        if (exportMatch) {
            const exports = exportMatch[1].split(',').map(e => e.trim());
            const exportedFunctions = exports.filter(e => expectedFunctions.includes(e));
            
            results.addResult('函数导出', 'PASS', 
                `导出了 ${exportedFunctions.length}/${expectedFunctions.length} 个函数`);
        } else {
            results.addResult('函数导出', 'FAIL', '未找到导出语句');
        }
        
    } catch (error) {
        results.addResult('API 函数检查', 'FAIL', '文件解析失败', error.message);
    }
    
    return results;
}

// 检查类型定义
async function testTypeDefinitions(modulePath) {
    const results = new TestResults();
    
    console.log(`\n📋 检查类型定义: ${modulePath}`);
    console.log('-'.repeat(40));
    
    const basePath = join(process.cwd(), 'src/api', modulePath);
    const typesPath = join(basePath, 'types.ts');
    
    if (!existsSync(typesPath)) {
        results.addResult('类型定义检查', 'SKIP', 'types.ts 文件不存在');
        return results;
    }
    
    try {
        const content = await readFile(typesPath, 'utf8');
        
        // 检查基础接口
        const requiredInterfaces = [
            { pattern: /export\s+interface\s+\w+\s+extends\s+ModelBase/, name: '主模型接口' },
            { pattern: /export\s+interface\s+\w+Create/, name: '创建接口' },
            { pattern: /export\s+interface\s+\w+Update/, name: '更新接口' }
        ];
        
        for (const interfaceCheck of requiredInterfaces) {
            if (interfaceCheck.pattern.test(content)) {
                results.addResult(`接口定义: ${interfaceCheck.name}`, 'PASS', '接口已定义');
            } else {
                results.addResult(`接口定义: ${interfaceCheck.name}`, 'FAIL', '接口未定义');
            }
        }
        
        // 检查是否有枚举定义
        const enumPattern = /export\s+enum\s+\w+/g;
        const enumMatches = content.match(enumPattern);
        
        if (enumMatches) {
            results.addResult('枚举定义', 'PASS', `发现 ${enumMatches.length} 个枚举`);
        } else {
            results.addResult('枚举定义', 'SKIP', '未发现枚举定义');
        }
        
        // 检查导入语句
        if (content.includes('import type { ModelBase }')) {
            results.addResult('基础类型导入', 'PASS', 'ModelBase 已导入');
        } else {
            results.addResult('基础类型导入', 'FAIL', 'ModelBase 未导入');
        }
        
    } catch (error) {
        results.addResult('类型定义检查', 'FAIL', '文件解析失败', error.message);
    }
    
    return results;
}

// 实际 API 调用测试 (可选)
async function testApiCalls(modulePath, token = null) {
    const results = new TestResults();
    
    console.log(`\n🌐 API 调用测试: ${modulePath}`);
    console.log('-'.repeat(40));
    
    if (!token) {
        results.addResult('API 调用测试', 'SKIP', '未提供认证令牌');
        return results;
    }
    
    try {
        // 尝试获取元数据
        const metadataUrl = `/v1/${modulePath}/get_metadata`;
        
        console.log(`正在测试: ${metadataUrl}`);
        
        const response = await testAlovaInstance.Get(metadataUrl, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        
        if (response && response.code === 200) {
            results.addResult('元数据获取', 'PASS', '成功获取元数据');
            
            // 检查元数据结构
            if (response.data && response.data.fields) {
                response.data.fields.forEach(field => {
                    results.addResult(`字段: ${field.name}`, 'PASS', `字段类型: ${field.type}`);
                });
                results.addResult('元数据结构', 'PASS', 
                    `包含 ${response.data.fields.length} 个字段`);
            } else {
                results.addResult('元数据结构', 'FAIL', '元数据结构不完整');
            }
        } else {
            results.addResult('元数据获取', 'FAIL', 
                `响应码: ${response?.code || 'unknown'}`);
        }
        
    } catch (error) {
        results.addResult('API 调用测试', 'FAIL', 'API 调用失败', error.message);
    }
    
    return results;
}

// 获取 token (用于 API 测试)
async function getTestToken() {
    try {
        console.log('🔑 正在获取测试令牌...');
        
        const response = await testAlovaInstance.Post('/v1/sys/user/login', {
            username: 'user',
            password: '123456'
        });
        
        if (response && response.data && response.data.access_token) {
            console.log('✅ 测试令牌获取成功');
            return response.data.access_token;
        } else {
            console.log('❌ 测试令牌获取失败');
            return null;
        }
    } catch (error) {
        console.log(`❌ 测试令牌获取失败: ${error.message}`);
        return null;
    }
}

// 主测试函数
async function runTests(modulePath, options = {}) {
    console.log('🧪 开始 API 接口测试');
    console.log('='.repeat(60));
    console.log(`📦 测试模块: ${modulePath}`);
    console.log(`🔗 API 基础 URL: ${process.env.VITE_API_BASE_URL || 'http://localhost:8111/api'}`);
    
    const allResults = new TestResults();
    
    // 1. 文件结构测试
    const structureResults = await testFileStructure(modulePath);
    structureResults.results.forEach(r => 
        allResults.addResult(r.name, r.status, r.message, r.details));
    
    // 2. TypeScript 语法测试
    const syntaxResults = await testTypeScriptSyntax(modulePath);
    syntaxResults.results.forEach(r => 
        allResults.addResult(r.name, r.status, r.message, r.details));
    
    // 3. API 函数测试
    const functionResults = await testApiFunctions(modulePath);
    functionResults.results.forEach(r => 
        allResults.addResult(r.name, r.status, r.message, r.details));
    
    // 4. 类型定义测试
    const typeResults = await testTypeDefinitions(modulePath);
    typeResults.results.forEach(r => 
        allResults.addResult(r.name, r.status, r.message, r.details));
    
    // 5. API 调用测试 (可选)
    if (options.testApiCalls) {
        const token = await getTestToken();
        const apiResults = await testApiCalls(modulePath, token);
        apiResults.results.forEach(r => 
            allResults.addResult(r.name, r.status, r.message, r.details));
    }
    
    return allResults;
}

// 主函数
async function main() {
    try {
        const args = process.argv.slice(2);
        
        if (args.includes('--help') || args.includes('-h')) {
            showHelp();
            rl.close();
            return;
        }
        
        const isAutoMode = args.includes('--auto');
        const isVerbose = args.includes('--verbose');
        
        let modulePath = null;
        const moduleIndex = args.indexOf('--module');
        if (moduleIndex !== -1 && moduleIndex + 1 < args.length) {
            modulePath = args[moduleIndex + 1];
        }
        
        // 交互式获取模块路径
        if (!modulePath && !isAutoMode) {
            modulePath = await question('🔍 请输入要测试的模块路径 (例: demo/user): ');
            
            if (!modulePath.trim()) {
                console.log('❌ 模块路径不能为空');
                rl.close();
                return;
            }
        }
        
        // 询问是否进行 API 调用测试
        let testApiCalls = false;
        if (!isAutoMode) {
            const apiTestResponse = await question('🌐 是否进行实际 API 调用测试? (y/n): ');
            testApiCalls = apiTestResponse.toLowerCase().startsWith('y');
        }
        
        const results = await runTests(modulePath, { testApiCalls, verbose: isVerbose });
        
        results.printSummary();
        
        // 返回适当的退出码
        process.exit(results.failed > 0 ? 1 : 0);
        
    } catch (error) {
        console.error('\n❌ 测试过程中出现错误:');
        console.error(error.message);
        if (error.stack && args.includes('--verbose')) {
            console.error(error.stack);
        }
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export {
    main,
    runTests,
    testFileStructure,
    testTypeScriptSyntax,
    testApiFunctions,
    testTypeDefinitions,
    testApiCalls,
    TestResults
};