# 玻璃深加工行业ERP管理平台原型演示项目

一个基于 Vue 3 + TypeScript + Tailwind CSS 构建的适用于**按单生产（Make-to-Order, MTO）**模式的、**玻璃深加工**及**配套铝型材制品**（如防火窗、淋浴房隔断）制造商企业的一站式管理平台。


## 核心特征
- **行业领域**: 采用**按单生产（Make-to-Order, MTO）**模式的**玻璃深加工**及**配套铝型材制品**（如防火窗、淋浴房隔断）的制造商。
- **文档语言**: 中文（简体）
- **目标用户**: 前后端开发团队，作为实现依据

## 🚀 技术栈

- **框架**: Vue 3 (Composition API)
- **语言**: TypeScript (严格模式)
- **样式**: Tailwind CSS 4 (Utility-First)
- **UI 库**: Shadcn Vue
- **状态管理**: Pinia
- **构建工具**: Vite
- **API 请求**: Alova
- **包管理器**: pnpm

## 📁 项目结构

```
src/
├── assets/                   # 静态资源
│   ├── images/               # 图片资源
│   ├── fonts/                # 字体文件
│   └── styles/               # 全局样式文件
├── components/               # 可复用组件
│   ├── ui/                   # 基础 Shadcn Vue UI 组件
│   ├── shared/               # 项目通用组件
│   ├── layout/               # 布局组件
│   └── feature/              # 功能特定组件
├── composables/              # Vue 组合式函数
├── stores/                   # Pinia 状态管理
├── router/                   # 路由配置
├── views/                    # 页面组件
├── api/                      # API 请求层
├── lib/                      # 工具函数
├── types/                    # TypeScript 类型定义
└── constants/                # 常量定义
```

## 🛠️ 开发环境

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
# 使用 pnpm 安装依赖
pnpm install
```

### 开发命令

```bash
# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 预览生产版本
pnpm preview

# 运行类型检查
pnpm type:check
```

## 🎯 功能特性

### 技术特性

- ✅ **响应式设计**: 支持桌面端和移动端
- ✅ **组件化开发**: 高度可复用的组件库
- ✅ **类型安全**: 完整的 TypeScript 类型定义
- ✅ **状态管理**: 基于 Pinia 的响应式状态管理
- ✅ **路由守卫**: 完善的权限控制和路由保护
- ✅ **API 封装**: 统一的 API 请求和错误处理
- ✅ **主题系统**: 支持明暗主题切换
- ✅ **国际化**: 多语言支持

## 🔧 开发规范

### Vue 3 组件规范

```vue
<script setup lang="ts">
interface Props {
  user: User
  showActions?: boolean
}

interface Emits {
  (e: 'edit', user: User): void
  (e: 'delete', userId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

const emit = defineEmits<Emits>()

// 使用组合式函数
const { isLoading, error } = useApi()
</script>

<template>
  <div class="flex items-center justify-between p-4 bg-background border rounded-lg">
    <!-- 组件内容 -->
  </div>
</template>
```

### TypeScript 规范

- 启用严格模式，禁止使用 `any` 类型
- 接口优先使用 `interface` 而非 `type`
- 为所有函数参数和返回值提供类型注解
- 使用 `readonly` 保护只读属性

### Tailwind CSS 规范

- 优先使用 Tailwind 工具类，避免自定义 CSS
- 使用 `cn()` 函数合并条件类名
- 遵循响应式设计原则
- 类名顺序：布局 → 间距 → 尺寸 → 颜色 → 状态

## 🔐 安全规范

- **认证授权**: JWT Token + 刷新令牌机制
- **权限控制**: 基于角色的访问控制 (RBAC)
- **输入验证**: 所有用户输入都需要验证和清理
- **XSS 防护**: 使用内容安全策略 (CSP)
- **CSRF 防护**: 使用 CSRF Token

## 📊 性能优化

- **组件懒加载**: 使用 `defineAsyncComponent`
- **路由懒加载**: 页面级组件按需加载
- **Bundle 分析**: 定期分析打包产物
- **缓存策略**: HTTP 缓存和 Service Worker

## 🚀 部署

### 环境变量配置

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8111/api
VITE_APP_TITLE=开发环境

# .env.production
VITE_API_BASE_URL=https://api.mes.com/api
VITE_APP_TITLE=生产管理系统
```

### 构建部署

```bash
# 构建生产版本
pnpm build

# 部署到服务器
# 将 dist 目录内容部署到 Web 服务器
```

## 🤝 贡献指南
### 提交规范

遵循 Conventional Commits 规范：

```bash
feat(auth): 添加用户登录功能
fix(api): 修复用户信息获取接口错误处理
docs(readme): 更新项目文档
style(ui): 调整按钮样式
refactor(store): 重构用户状态管理
test(user): 添加用户组件单元测试
chore(deps): 更新依赖包版本
```

## 📝 文档

- [项目架构文档](./docs/project-structure.md)
- [Alova API 配置使用指南](./docs/alova-setup-guide.md)
- [用户需求说明书（URD）](./docs/user-requirement.md)
- [原型开发设计规范](./docs/prototype_design_guidelines.md)
- [全局术语表 (Global Glossary)](./docs/glossary.md)

---

**注意**: 代码提交前必须通过类型检查、代码规范检查。
