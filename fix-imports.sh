#!/bin/bash

# Fix import paths for utils/helpers in mock API files

# For files at api/ level (depth 2)
find mock/routes/api -maxdepth 1 -name "*.ts" -exec sed -i '' 's|from '\''../../utils/helpers'\''|from '\''../../utils/helpers'\''|g' {} \;

# For files at api/module/ level (depth 3) 
find mock/routes/api -mindepth 2 -maxdepth 2 -name "*.ts" -exec sed -i '' 's|from '\''../../utils/helpers'\''|from '\''../../../utils/helpers'\''|g' {} \;

# For files at api/module/[id]/ level (depth 4)
find mock/routes/api -mindepth 3 -maxdepth 3 -name "*.ts" -exec sed -i '' 's|from '\''../../utils/helpers'\''|from '\''../../../../utils/helpers'\''|g' {} \;

# For files at api/v1/sys/user/ level (depth 5)
find mock/routes/api -mindepth 4 -maxdepth 4 -name "*.ts" -exec sed -i '' 's|from '\''../../utils/helpers'\''|from '\''../../../../../utils/helpers'\''|g' {} \;

echo "Import paths fixed!"